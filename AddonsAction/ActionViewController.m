//
//  ActionViewController.m
//  AddonsAction
//
//  Created by qingbin on 2023/4/1.
//

#import "ActionViewController.h"
#import <MobileCoreServices/MobileCoreServices.h>
#import <UniformTypeIdentifiers/UniformTypeIdentifiers.h>

@interface ActionViewController ()

@property(strong,nonatomic) IBOutlet UIImageView *imageView;

@end

@implementation ActionViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    [self _handleOpenUrl];
    
    [self.extensionContext completeRequestReturningItems:@[] completionHandler:nil];
}

- (IBAction)done {
    // Return any edited content to the host app.
    // This template doesn't do anything, so we just echo the passed in items.
    [self.extensionContext completeRequestReturningItems:self.extensionContext.inputItems completionHandler:nil];
}

- (void)_handleOpenUrl
{
    __block BOOL foundURL = NO;
    [self.extensionContext.inputItems enumerateObjectsUsingBlock:^(NSExtensionItem * _Nonnull item, NSUInteger idx, BOOL * _Nonnull stop) {
        [item.attachments enumerateObjectsUsingBlock:^(NSItemProvider * _Nonnull itemProvider, NSUInteger idx, BOOL * _Nonnull stop) {
            if ([itemProvider hasItemConformingToTypeIdentifier:@"public.url"]) {
                [itemProvider loadItemForTypeIdentifier:@"public.url"
                                                options:nil
                                      completionHandler:^(id<NSSecureCoding>  _Nullable item, NSError * _Null_unspecified error) {
                                          if ([(NSObject *)item isKindOfClass:[NSURL class]]) {
                                              NSURL* URL = (NSURL*)item;
                                              NSString* jsContent;
                                              BOOL shouldStopAccessing = [URL startAccessingSecurityScopedResource];
                                              @try {
                                                  NSError* error;
                                                  jsContent = [NSString stringWithContentsOfURL:URL encoding:NSUTF8StringEncoding error:&error];
                                              } @catch (NSException *exception) {
                                                  
                                              } @finally {
                                                  if(shouldStopAccessing) {
                                                      [URL stopAccessingSecurityScopedResource];
                                                  }
                                              }
                                              
                                              NSData *data = [jsContent dataUsingEncoding:NSUTF8StringEncoding];
                                              NSString *base64 = [data base64Encoding]; // base64格式的字符串
                                              NSString* scheme = [NSString stringWithFormat:@"addons://installJS?command=22&text=%@", base64];
                                              NSURL* jumpURL = [NSURL URLWithString:scheme];
                                              
//                                              UIResponder *responder = self.parentViewController;
//                                              while(responder){
//                                                  if ([responder respondsToSelector:@selector(openURL:)]){
//                                                      [responder performSelector:@selector(openURL:) withObject:jumpURL];
//                                                      break;
//                                                  }
//                                                  responder = [responder nextResponder];
//                                              }
                                              [self _openUrl:jumpURL];
                                          }
                                      }];

                foundURL = YES;
                *stop = YES;
            }
        }];

        if (foundURL) {
            *stop = YES;
        }
    }];

}

- (void)_openUrl:(NSURL *)URL
{
    UIResponder *responder = self;
        
    while (responder) {
        if ([responder isKindOfClass:[UIApplication class]]) {
            UIApplication *application = (UIApplication *)responder;
            
            if (@available(iOS 18.0, *)) {
                [application openURL:URL options:@{} completionHandler:nil];
            } else {
                #pragma clang diagnostic push
                #pragma clang diagnostic ignored "-Warc-performSelector-leaks"
                if([application respondsToSelector:@selector(openURL:)]) {
                    [application performSelector:@selector(openURL:) withObject:URL];
                }
                #pragma clang diagnostic pop
            }
        }
        
        responder = [responder nextResponder];
    }
}


@end
