//
//  InternalURL.h
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/3.
//

#import <Foundation/Foundation.h>

@interface InternalURL : NSObject

//是否是internal:开头的scheme
+ (BOOL)isValid:(NSURL*)url;

//首页
+ (NSString*)homeUrl;
+ (BOOL)isAboutHomeURL:(NSString*)url;

//session
+ (NSString*)sessionRestoreHistoryItemBaseUrl;
+ (BOOL)isSessionRestore:(NSString*)url;
+ (NSString*)sessionRestoreWrapperUrl:(NSString*)historyJson;

//error
+ (NSString*)errorPageWithUrl:(NSString*)url;
+ (BOOL)isErrorPage:(NSString*)url;

+ (NSString*)uuid;
+ (NSString *)scheme;
+ (NSString *)baseUrl;

@end

