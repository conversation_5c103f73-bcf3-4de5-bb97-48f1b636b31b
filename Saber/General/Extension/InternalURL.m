//
//  InternalURL.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/3.
//

#import "InternalURL.h"

@interface InternalURL ()

@end

@implementation InternalURL

//internal://local/about/home?uuidkey=710413BF-6CDB-4BF3-B2E3-88F270B6CCD6#panel=0

/*
 internal://local/sessionrestore?history={
   "currentPage" : 0,
   "history" : [
     "internal:\/\/local\/about\/home#panel=0",
     "https:\/\/www.wikipedia.org\/"
   ]
 }&uuidkey=9E043546-C3EC-402A-A662-E80618D46C33
 */

+ (BOOL)isValid:(NSURL*)url
{
    return [[self scheme] isEqualToString:url.scheme];
}

+ (NSString *)scheme
{
    return @"internal";
}

+ (NSString *)baseUrl
{
    return [NSString stringWithFormat:@"%@://local", [InternalURL scheme]];
}

//home
+ (NSString*)homeUrlBaseUrl
{
    return [NSString stringWithFormat:@"%@/about/home",[self baseUrl]];
}

+ (NSString*)homeUrl
{
    NSString* url = [NSString stringWithFormat:@"%@",[self homeUrlBaseUrl]];
    return url;
}

+ (BOOL)isAboutHomeURL:(NSString*)url
{
    return [url containsString:[self homeUrlBaseUrl]];
}

//sessionrestore
+ (NSString*)sessionRestoreHistoryItemBaseUrl
{
    return [NSString stringWithFormat:@"%@/sessionrestore",[self baseUrl]];
}

+ (BOOL)isSessionRestore:(NSString*)url
{
    return [url containsString:[self sessionRestoreHistoryItemBaseUrl]];
}

+ (NSString*)sessionRestoreWrapperUrl:(NSString*)historyJson
{
    NSString* url = [NSString stringWithFormat:@"%@?history=%@",[self sessionRestoreHistoryItemBaseUrl], historyJson];
    return url;
}

//error
+ (NSString*)errorPageWithUrl:(NSString*)url
{
    //会有多次执行的情况,如果已经是错误页,那么直接返回
    if([InternalURL isErrorPage:url]) return url;
    
    return [NSString stringWithFormat:@"%@/error?errorUrl=%@",[self baseUrl], url];
}

+ (NSString*)errorPageBaseUrl
{
    return [NSString stringWithFormat:@"%@/error",[self baseUrl]];
}

+ (BOOL)isErrorPage:(NSString*)url
{
    return [url containsString:[self errorPageBaseUrl]];
}

@end
