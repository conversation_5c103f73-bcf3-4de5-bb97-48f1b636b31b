//
//  UITableView+ShowPageNumber.m
//  YSBBusiness
//
//  Created by lu luca<PERSON> on 4/11/2015.
//  Copyright © 2015 lu lucas. All rights reserved.
//

#import "UITableView+ShowPageNumber.h"
#import <objc/runtime.h>
#import "UIView+FrameHelper.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"


@implementation UITableView (ShowPageNumber)

static char PageNumberViewKey;
static char PageNumberPageSize;
static char PageNumberTotalPage;

- (void)setPageNumberView:(UILabel *)pageNumberView
{
    [self willChangeValueForKey:@"PageNumberViewKey"];
    objc_setAssociatedObject(self, &PageNumberViewKey,
                             pageNumberView,
                             OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self didChangeValueForKey:@"PageNumberViewKey"];
}

- (UILabel *)pageNumberView
{
    return objc_getAssociatedObject(self, &PageNumberViewKey);
}

- (void)setPageNumberPageSize:(NSNumber *)pageNumberPageSize
{
    [self willChangeValueForKey:@"PageNumberPageSize"];
    objc_setAssociatedObject(self, &PageNumberPageSize,
                             pageNumberPageSize,
                             OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self didChangeValueForKey:@"PageNumberPageSize"];
}

- (NSNumber *)pageNumberPageSize
{
    return objc_getAssociatedObject(self, &PageNumberPageSize);
}

- (void)setPageNumberTotalPage:(NSNumber *)pageNumberTotalPage
{
    [self willChangeValueForKey:@"PageNumberTotalPage"];
    objc_setAssociatedObject(self, &PageNumberTotalPage,
                             pageNumberTotalPage,
                             OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self didChangeValueForKey:@"PageNumberTotalPage"];
}

- (NSNumber *)pageNumberTotalPage
{
    return objc_getAssociatedObject(self, &PageNumberTotalPage);
}


- (void)showPageNumberViewWithPageSize:(NSInteger)pageSize totolPage:(NSInteger)totolPage affectSection:(NSInteger)section
{
    [self showPageNumberViewWithPageSize:pageSize totolPage:totolPage ignoreIndex:-1 affectSection:section];
}

- (void)showPageNumberViewWithPageSize:(NSInteger)pageSize
                             totolPage:(NSInteger)totolPage
                           ignoreIndex:(NSInteger)ignoreIndex// 这个index是透明项，不参与页码计算
                         affectSection:(NSInteger)section
{
    // 1.创建新的pageNumberView
    if (!self.pageNumberView) {
        UILabel *view = [[UILabel alloc] init];
        view.backgroundColor = [UIColor colorWithWhite:0 alpha:0.4];
        view.textAlignment = NSTextAlignmentCenter;
        view.textColor = [UIColor whiteColor];
        view.font = [UIFont systemFontOfSize:12.0f];
        view.layer.cornerRadius = 6.0f;
        view.layer.masksToBounds = YES;
        
        [self.superview addSubview:view];
        [self.superview insertSubview:view aboveSubview:self];
        [self setPageNumberView:view];
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.superview);
            make.bottom.equalTo(self).with.offset(-20.0f);
            make.height.mas_equalTo(20.0f);
        }];
        
        @weakify(self)
        [RACObserve(self, contentOffset) subscribeNext:^(id x) {
            @strongify(self)

            [NSObject cancelPreviousPerformRequestsWithTarget:self];
            
            // 没有内容就清除掉页码，应对切换了数据源时，数据源清空，还显示页码
            NSArray *indexArray = [self indexPathsForVisibleRows];
            if (indexArray.count == 0) {
                self.pageNumberView.hidden = YES;
                return ;
            }
            
            
            CGPoint translation = [self.panGestureRecognizer translationInView:self.superview];
            // 顶端不显示页码，应对数据源过程中contentoffset为0
            if (translation.y == 0) {
                return ;
            }
            
            if (translation.y>0) {
                // 向上滚，判断数组的第一个元素有没有到达分界
                NSIndexPath *indexPath = indexArray[0];
                // 4.28.0 由于店铺装修的关系，如果不是对应的section就不显示了，因为4.28.0 店铺装修和店铺搜索结果页推荐的关系
                if (indexPath.section == section) {
                    self.pageNumberView.hidden = NO;
                    // 修正index
                    NSInteger index = indexPath.row;
                    if (ignoreIndex > -1 && index > ignoreIndex) {
                        index = indexPath.row - 1;
                    }
                    self.pageNumberView.text = [NSString stringWithFormat:@" %@/%@    ", @((index / self.pageNumberPageSize.integerValue) + 1), self.pageNumberTotalPage];
                }
            }else if(translation.y<0){
                // 向上滚，判断数组的最后一个元素有没有到达分界
                NSIndexPath *indexPath = [indexArray lastObject];
                if (indexPath.section == section) {
                    self.pageNumberView.hidden = NO;
                    // 修正index
                    NSInteger index = indexPath.row;
                    if (ignoreIndex > -1 && index > ignoreIndex) {
                        index = indexPath.row - 1;
                    }
                    self.pageNumberView.text = [NSString stringWithFormat:@" %@/%@    ", @((index / self.pageNumberPageSize.integerValue) + 1), self.pageNumberTotalPage];
                }
            }
            
            
            [self performSelector:@selector(_hiddenPageNumberView) withObject:nil afterDelay:0.5f];
            
        }];
        
        
        // 如果在self 的rac_willDeallocSignal中取消request，会导致崩溃，因为label的dealloc比self的早，讨厌黑魔法。。。
        [[view rac_willDeallocSignal] subscribeCompleted:^{
            @strongify(self)
            [NSObject cancelPreviousPerformRequestsWithTarget:self];

        }];
    }
    
    
    self.pageNumberPageSize = [NSNumber numberWithInteger:pageSize];
    self.pageNumberTotalPage = [NSNumber numberWithInteger:totolPage];
}

- (void)_hiddenPageNumberView
{
    self.pageNumberView.hidden = YES;
}

@end
