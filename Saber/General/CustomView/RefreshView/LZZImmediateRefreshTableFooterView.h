//
//  LZZImmediateRefreshTableFooterView.h
//  YSBBusiness
//
//  Created by lu luca<PERSON> on 17/4/15.
//  Copyright (c) 2015 lu lucas. All rights reserved.
//

#import <UIKit/UIKit.h>

#import <UIKit/UIKit.h>
#import <QuartzCore/QuartzCore.h>

typedef enum{
    LZZImmediatePullRefreshNoMore = 1,
    LZZImmediatePullRefreshLoading,
    LZZImmediatePullRefreshHide,
}   LZZImmediatePullRefreshState;


typedef enum{
    RefreshImmediateFooterTypeText = 0,
    RefreshImmediateFooterTypeButton,
}   RefreshImmediateFooterType;

@interface LZZImmediateRefreshTableFooterView : UIView

@property (nonatomic, assign) NSInteger pageSize;
@property (nonatomic, strong) NSString *text;
@property (nonatomic, copy) void (^beginRefreshingCallback)();
///手动控制加载下一页的时机，不设置这个话，时机是列表显示最后一项的时候加载下一页
@property (nonatomic, copy) BOOL(^manualDetectLoad)();
@property (nonatomic, copy) void (^noMoreCallback)();

- (id)initWithFrame:(CGRect)frame;

- (void)lzzRefreshScrollViewDataSourceDidFinishedLoading:(UIScrollView *)scrollView count:(NSInteger)count;
- (void)configNoMore:(NSInteger)count;

// 4.32.0 修改文字颜色
- (void)setTextColor:(UIColor *)textColor;
@end
