//
//  UITableView+ShowPageNumber.h
//  YSBBusiness
//
//  Created by lu luca<PERSON> on 4/11/2015.
//  Copyright © 2015 lu lucas. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UITableView (ShowPageNumber)

/**
 *  注意！！！这个方法要在tableview被addsubview到父view之后调用
 *  tableview调用这个方法在下方显示当前页码／总页码，
 *
 *  @param pageSize  每页的数量
 *  @param totolPage 一共有多少页
 */
- (void)showPageNumberViewWithPageSize:(NSInteger)pageSize
                             totolPage:(NSInteger)totolPage
                         affectSection:(NSInteger)section;

// 5.11.0 这期首次出现，列表的中途app插入新项，使得index对应的页码失准，这里要增加一个调整index的参数
- (void)showPageNumberViewWithPageSize:(NSInteger)pageSize
                             totolPage:(NSInteger)totolPage
                           ignoreIndex:(NSInteger)ignoreIndex// 这个index是透明项，不参与页码计算
                         affectSection:(NSInteger)section;

@end
