//
//  NaviTitleViewCompatibleView.h
//  Saber
//
//  Created by qingbin on 2023/7/17.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
/* bug ID1072657 从目前找到的情况来看，应该是iOS系统的bug，在早期的iOS16会出现，16.2上没有出现，但由于机型不足，所以具体是什么版本修复好的，不好确认。
 iOS16的导航栏增加了一层_UINavigationBarTitleControl，但它对尺寸的计算好像有问题，所以导致titleview偏移了。现在的做法是titleview为NaviTitleViewCompatibleView（内部设置尽可能大的intrinsicContentSize），然后实际的titleview作为NaviTitleViewCompatibleView的子view，这样_UINavigationBarTitleControl的计算就对了
 
 实际测试下来也很诡异，16.0.2，搜索页第一次进入的时候搜索框会歪，二次进入就好了，然后一直好，连订单列表的搜索框也会好；但直接进入订单列表，搜索框就会一直歪。
 */
@interface NaviTitleViewCompatibleView : UIView

@end

NS_ASSUME_NONNULL_END
