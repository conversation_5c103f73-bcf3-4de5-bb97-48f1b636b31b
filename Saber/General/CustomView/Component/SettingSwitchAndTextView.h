//
//  SettingSwitchAndTextView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2023/11/28.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

/// 上面是标题+开关，下面是文字描述
/// 高度自适应
@interface SettingSwitchAndTextView : UIView

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithShowLine:(BOOL)bShowLine;

- (void)updateWithTitle:(NSString *)title
                 detail:(NSString *)detail
                   isOn:(BOOL)isOn;

@property (nonatomic, copy) void (^didSwithAction)(BOOL isOn);

@property (nonatomic, strong) UISwitch *switchView;

@end
