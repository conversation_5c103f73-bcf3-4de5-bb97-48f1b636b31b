//
//  SettingTextAndSelectView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2023/2/11.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ThemeProtocol.h"

@interface SettingTextAndSelectView : UIView

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithShowLine:(BOOL)bShowLine;

- (void)updateWithTitle:(NSString *)title isSelect:(BOOL)isSelect;

+ (float)height;

@property (nonatomic, copy) void (^didAction)(void);

@end

