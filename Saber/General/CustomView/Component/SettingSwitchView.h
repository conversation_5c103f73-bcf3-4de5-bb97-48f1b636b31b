//
//  SettingSwitchView.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "ThemeProtocol.h"

NS_ASSUME_NONNULL_BEGIN

@interface SettingSwitchView : UIView<ThemeProtocol>

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithShowLine:(BOOL)bShowLine;

- (instancetype)initWithShowLine:(BOOL)bShowLine leftOffset:(float)leftOffset;

+ (float)height;

- (void)updateWithTitle:(NSString *)title isOn:(BOOL)isOn;

@property (nonatomic, copy) void (^didSwithAction)(BOOL isOn);

@property (nonatomic, strong) UISwitch *switchView;

@end

NS_ASSUME_NONNULL_END
