//
//  SettingSegmentView.h
//  Saber
//
//  Created by qing<PERSON> on 2023/3/14.
//

#import <UIKit/UIKit.h>

@interface SettingSegmentView : UIView

- (instancetype)init NS_UNAVAILABLE;
+ (instancetype)new NS_UNAVAILABLE;

- (instancetype)initWithTitle:(NSString *)title
                     showLine:(BOOL)bShowLine
                     segments:(NSArray *)segments;

- (instancetype)initWithTitle:(NSString *)title
                     showLine:(BOOL)bShowLine
                     showInfo:(BOOL)bShowInfo
                   leftOffset:(float)leftOffset
                     segments:(NSArray *)segments;

+ (float)height;

- (void)updateWithSelectIndex:(int)selectIndex;

@property (nonatomic, copy) void (^selectIndexBlock)(int index);

@property (nonatomic, copy) void (^selectInfoBlock)(void);

@end

