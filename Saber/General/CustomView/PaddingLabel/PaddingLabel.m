//
//  PaddingLabel.m
//  AdvertBao
//
//  Created by lu luca<PERSON> on 29/11/2016.
//  Copyright © 2016 lucaslu. All rights reserved.
//

#import "PaddingLabel.h"

@implementation PaddingLabel

- (id)initWithFrame:(CGRect)frame

{
    
    self = [super initWithFrame:frame];
    
    if (self) {
        
        self.edgeInsets = UIEdgeInsetsMake(0, 0, 0, 0);
        self.textAlignment = NSTextAlignmentCenter;
    }
    
    return self;
    
}

- (instancetype)init{
    
    if (self = [super init]) {
        
        self.edgeInsets = UIEdgeInsetsMake(0, 0, 0, 0);
        self.textAlignment = NSTextAlignmentCenter;
        
    }
    
    return self;
    
}

- (void)drawRect:(CGRect)rect

{
    
    [super drawTextInRect:UIEdgeInsetsInsetRect(rect, self.edgeInsets)];
    
}

- (CGSize)intrinsicContentSize

{
    
    CGSize size = [super intrinsicContentSize];
    
    size.width += self.edgeInsets.left + self.edgeInsets.right;
    
    size.height += self.edgeInsets.top + self.edgeInsets.bottom;
    
    return size;
    
}
@end
