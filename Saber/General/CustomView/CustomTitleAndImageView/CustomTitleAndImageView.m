//
//  CustomTitleAndImageView.m
//  YSBPro
//
//  Created by qing<PERSON> on 2024/1/5.
//  Copyright © 2024 lu lucas. All rights reserved.
//

#import "CustomTitleAndImageView.h"

#import "ReactiveCocoa.h"

@interface CustomTitleAndImageView ()

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIImageView *imageView;

@property (nonatomic, copy) void (^block)(CustomTitleAndImageView*, UIImageView*, UILabel*);

@end

@implementation CustomTitleAndImageView

- (instancetype)initWithLayout:(void (^)(CustomTitleAndImageView* view, UIImageView* imageView, UILabel* titleLabel))block
{
    self = [super init];
    if(self) {
        self.block = block;
        
        [self addSubviews];
        [self defineLayout];
        [self setupObservers];
    }
    
    return self;
}

#pragma mark -- 更新
- (void)updateWith:(void(^)(CustomTitleAndImageView* view, UIImageView* imageView, UILabel* titleLabel))block
{
    if(block) {
        block(self, self.imageView, self.titleLabel);
    }
}

#pragma mark -- 事件处理

- (void)setupObservers
{
    UITapGestureRecognizer* tap = [UITapGestureRecognizer new];
    [self addGestureRecognizer:tap];
    
    [tap.rac_gestureSignal subscribeNext:^(id x) {
        if(self.tapAction) {
            self.tapAction();
        }
    }];
}

#pragma mark -- layout

- (void)addSubviews
{
    [self addSubview:self.titleLabel];
    [self addSubview:self.imageView];
}

- (void)defineLayout
{
    if(self.block) {
        self.block(self, self.imageView, self.titleLabel);
    }
}

#pragma mark -- Getters

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        _titleLabel = [UILabel new];
    }
    
    return _titleLabel;
}

- (UIImageView *)imageView
{
    if(!_imageView) {
        _imageView = [UIImageView new];
    }
    
    return _imageView;
}

@end
