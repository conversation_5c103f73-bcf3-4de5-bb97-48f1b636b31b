//
//  CommonGradientView.m
//  PPBrowser
//
//  Created by qingbin on 2025/3/7.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "CommonGradientView.h"

@interface CommonGradientView()

@property(nonatomic) CAGradientLayer *gradientLayer;

@end

@implementation CommonGradientView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.gradientLayer = [CAGradientLayer layer];
        [self.layer insertSublayer:self.gradientLayer atIndex:0];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    _gradientLayer.frame = self.bounds;
}

- (void)updateFromColor:(UIColor *)fromColor toColor:(UIColor *)toColor {
    [self updateFromColor:fromColor toColor:toColor direction:CommonGradientDirectionVertical];
}

- (void)updateFromColor:(UIColor *)fromColor toColor:(UIColor *)toColor direction:(CommonGradientDirection)direction {
    self.gradientLayer.colors = @[
        (__bridge id)fromColor.CGColor,
        (__bridge id)toColor.CGColor
    ];
    if (direction == CommonGradientDirectionVertical) {
        self.gradientLayer.startPoint = CGPointMake(0.5, 0.0);
        self.gradientLayer.endPoint = CGPointMake(0.5, 1.0);
    } else if (direction == CommonGradientDirectionHorizontal) {
        self.gradientLayer.startPoint = CGPointMake(0.0, 0.5);
        self.gradientLayer.endPoint = CGPointMake(1.0, 0.5);
    } else if (direction == CommonGradientDirectionTopLeftToBottomRight) {
        self.gradientLayer.startPoint = CGPointMake(0.0, 0.0);
        self.gradientLayer.endPoint = CGPointMake(1.0, 1.0);
    }
}

@end
