//
//  CustomTitleView.m
//  PPBrowser
//
//  Created by qingbin on 2023/6/10.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import "CustomTitleView.h"

#import "PaddingLabel.h"

#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"
#import "UIView+Helper.h"
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "NSObject+Helper.h"

#import "BrowserUtils.h"
#import "NSString+Helper.h"

@interface CustomTitleView ()

@property (nonatomic, strong) UIStackView* stackView;

@property (nonatomic, strong) PaddingLabel* label;

@property (nonatomic, strong) UILabel* titleLabel;

@end

@implementation CustomTitleView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if(self) {
        [self addSubviews];
        [self defineLayout];
    }
    
    return self;
}

- (void)updateWithTitle:(NSString *)title
                 status:(UpdateStatus)status
{
    self.titleLabel.text = title;
    
    //#1CD8D2 绿色
    //#1FA2FF 蓝色
    //#e52d27 红色
    self.label.hidden = status==UpdateStatusDefault;
    
    if(status == UpdateStatusLoading) {
        //更新中
        self.label.hidden = NO;
        
        self.label.text = NSLocalizedString(@"tips.updating", nil);
        self.label.backgroundColor = [UIColor colorWithHexString:@"#1FA2FF"];
    } else if(status == UpdateStatusFailed) {
        //更新失败
        self.label.hidden = NO;
        
        self.label.text = NSLocalizedString(@"common.update.fail", nil);
        self.label.backgroundColor = [UIColor colorWithHexString:@"#e52d27"];
    } else {
        //初始化或者更新完成
        self.label.hidden = YES;
    }
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.titleLabel.textColor = UIColor.whiteColor;
    } else {
        self.titleLabel.textColor = [UIColor colorWithHexString:@"#1A1A1A"];
    }
}

- (void)addSubviews
{
    [self addSubview:self.stackView];
}

- (void)defineLayout
{
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.label setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.label setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
    [self.titleLabel setContentHuggingPriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
}

- (PaddingLabel *)label
{
    if(!_label) {
        _label = [PaddingLabel new];
        float left = iPadValue(4, 2);
        float top = iPadValue(5, 3);
        _label.edgeInsets = UIEdgeInsetsMake(left, top, left, top);
        
        _label.font = [UIFont systemFontOfSize:iPadValue(15, 12)];
        _label.textColor = UIColor.whiteColor;
        _label.textAlignment = NSTextAlignmentCenter;
        
        
        _label.layer.cornerRadius = 5;
        _label.layer.masksToBounds = YES;
        _label.hidden = YES;
    }
    
    return _label;
}

- (UILabel *)titleLabel
{
    if(!_titleLabel) {
        float font = iPadValue(20, 16);
        _titleLabel = [UIView createLabelWithTitle:@""
                                         textColor:[UIColor colorWithHexString:@"#1A1A1A"]
                                           bgColor:UIColor.clearColor
                                          fontSize:font
                                     textAlignment:NSTextAlignmentLeft
                                             bBold:YES];
    }
    
    return _titleLabel;
}

- (UIStackView *)stackView
{
    if(!_stackView) {
        _stackView = [[UIStackView alloc]initWithArrangedSubviews:@[
            self.label,
            self.titleLabel
        ]];
        
        _stackView.spacing = 5;
        _stackView.axis = UILayoutConstraintAxisHorizontal;
    }
    
    return _stackView;
}

@end
