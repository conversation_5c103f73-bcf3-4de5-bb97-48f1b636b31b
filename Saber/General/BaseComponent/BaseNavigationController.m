//
//  BaseNavigationController.m
//  MaizyClock
//
//  Created by qing<PERSON> on 2022/1/25.
//

#import "BaseNavigationController.h"

#import "ThemeProtocol.h"
#import "PreferenceManager.h"
#import "UIColor+Helper.h"

@interface BaseNavigationController ()

@end

@implementation BaseNavigationController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.interactivePopGestureRecognizer.delegate = self;
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.view.backgroundColor = [UIColor colorWithHexString:kDarkThemeColor222];
    } else {
        self.view.backgroundColor = [UIColor colorWithHexString:@"#f5f5f5"];
    }
}

//如果不添加这两个旋转相关函数,那么播放器全屏后,无法响应相关事件
- (BOOL)shouldAutorotate {
    return self.topViewController.shouldAutorotate;
}

//如果不添加这两个旋转相关函数,那么播放器全屏后,无法响应相关事件
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return self.topViewController.supportedInterfaceOrientations;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return self.topViewController.preferredInterfaceOrientationForPresentation;
}

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    return gestureRecognizer == self.interactivePopGestureRecognizer
        && self.viewControllers.count > 1
        && self.visibleViewController != self.viewControllers.firstObject;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldBeRequiredToFailByGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    return YES;
}

@end
