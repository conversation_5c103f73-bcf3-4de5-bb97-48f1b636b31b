//
//  BaseViewController.m
//  MaizyClock
//
//  Created by q<PERSON><PERSON> on 2022/1/25.
//

#import "BaseViewController.h"
#import "UIColor+Helper.h"
//#import "AnalyticsHelper.h"
#import "PreferenceManager.h"

#import "ThemeProtocol.h"
#import "PPNotifications.h"
#import "UIImage+Extension.h"
#import "BrowserUtils.h"

@interface BaseViewController ()

@end

@implementation BaseViewController

- (void)viewDidLoad
{
    [super viewDidLoad];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(darkThemeDidChangeNotification:)
                                                 name:kDarkThemeDidChangeNotification
                                               object:nil];
}

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    [self configNavigationBarStype];
    
//    [AnalyticsHelper inController:NSStringFromClass([self class])];
}

- (void)viewDidAppear:(BOOL)animated
{
    NSLog(@"进入页面->%@", [self class]);
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    
//    [AnalyticsHelper outController:NSStringFromClass([self class])];
}

#pragma mark -- 设置导航栏样式
- (void)configNavigationBarStype
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];

    BaseNavigationBarStyle barStyle = [self preferredNavigationBarStyle];
    
    switch (barStyle) {
        case BaseNavigationBarStyleDefault:
        {
            [self _setupDefaultNavigationBarWithDarkTheme:isDarkTheme];
        }
            break;
        case BaseNavigationBarStyleNoneWithLightContent:
        {
            [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
            [UIApplication sharedApplication].statusBarHidden = self.prefersStatusBarHidden;
            
            [self.navigationController setNavigationBarHidden:YES animated:YES];
        }
            break;
        case BaseNavigationBarStyleNoneWithDefaultContent:
        {
            // 设置导航栏和状态栏样式
            if(isDarkTheme) {
                //夜间模式
                [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
            } else {
                if (@available(iOS 13.0, *)) {
                    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDarkContent;
                } else {
                    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
                }
            }
            [UIApplication sharedApplication].statusBarHidden = self.prefersStatusBarHidden;
            
            [self.navigationController setNavigationBarHidden:YES animated:YES];
        }
            break;
        case BaseNavigationBarStyleCodeEditLight:
        {
            [self _setupDefaultNavigationBarWithDarkTheme:NO];
        }
            break;
        case BaseNavigationBarStyleCodeEditDark:
        {
            [self _setupDefaultNavigationBarWithDarkTheme:YES];
        }
            break;
        default:
            break;
    }
}

- (void)_setupDefaultNavigationBarWithDarkTheme:(BOOL)isDarkTheme
{
    UIColor* titleColor = [UIColor colorWithHexString:@"#171720"];
    UIColor* backgroundColor = UIColor.whiteColor;
    if(isDarkTheme) {
        titleColor = UIColor.whiteColor;
        backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    }
    
    float font = iPadValue(24, 18);
    [self.navigationController.navigationBar setTitleTextAttributes:@{ NSForegroundColorAttributeName:titleColor,
        NSFontAttributeName:[UIFont systemFontOfSize:font],
        NSShadowAttributeName:[[NSShadow alloc] init] }];
    
    // 4.44.0 去掉导航栏下方默认的一像素shadow
    [self.navigationController.navigationBar setBackgroundImage:[[UIImage alloc] init] forBarMetrics:UIBarMetricsDefault];
    [self.navigationController.navigationBar setShadowImage:[[UIImage alloc] init]];
    self.navigationController.navigationBar.barTintColor = backgroundColor;
    
    if (@available(iOS 15.0, *)) {
        //5.8.5 iOS 15兼容
        UINavigationBarAppearance *navigationBarAppearance = [UINavigationBarAppearance new];
        navigationBarAppearance.backgroundColor = backgroundColor;
        navigationBarAppearance.shadowColor = [UIColor clearColor];
        [navigationBarAppearance setTitleTextAttributes:@{ NSForegroundColorAttributeName:titleColor,
                                                                           NSFontAttributeName:[UIFont systemFontOfSize:font],
                                                                           NSShadowAttributeName:[[NSShadow alloc] init] }];
        self.navigationController.navigationBar.scrollEdgeAppearance = navigationBarAppearance;
        self.navigationController.navigationBar.standardAppearance = navigationBarAppearance;
    } else {
//                self.navigationController.navigationBar.barTintColor = backgroundColor;
    }
    
    // 设置导航栏和状态栏样式
    if(isDarkTheme) {
        //夜间模式
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
    } else {
        if (@available(iOS 13.0, *)) {
            [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDarkContent;
        } else {
            [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
        }
    }
    [UIApplication sharedApplication].statusBarHidden = NO;
    
    [self.navigationController setNavigationBarHidden:NO animated:YES];
}

#pragma mark -- 设置左上角返回按钮
- (void)createCustomLeftBarButtonItem
{
    UIButton* leftButton = [[UIButton alloc] initWithFrame:CGRectMake(0.0, 0.0f, 20.0f, 44.0)];
    [leftButton setBackgroundColor:[UIColor clearColor]];
    
    UIImage* arrow = [UIImage ext_systemImageNamed:@"chevron.backward"
                                         pointSize:20
                                        renderMode:UIImageRenderingModeAlwaysTemplate];
    [leftButton setImage:arrow forState:UIControlStateNormal];
    
    [leftButton addTarget:self action:@selector(leftBarbuttonClick) forControlEvents:UIControlEventTouchUpInside];
    self.leftButton = leftButton;
    self.leftButton.imageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    
    UIBarButtonItem* barItem = [[UIBarButtonItem alloc] initWithCustomView:leftButton];
    
    self.navigationItem.leftBarButtonItems = @[barItem];
}

- (void)leftBarbuttonClick
{
    [self.navigationController popViewControllerAnimated:YES];
}

- (UIStatusBarStyle)preferredStatusBarStyle
{
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        return UIStatusBarStyleLightContent;
    }
    
    return UIStatusBarStyleDefault;
}

- (BaseNavigationBarStyle)preferredNavigationBarStyle
{
    return BaseNavigationBarStyleDefault;
}

#pragma mark -- 暗黑模式
- (void)applyTheme
{
    //子类实现
    
    //左上角返回按钮适配
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        self.leftButton.imageView.tintColor = UIColor.whiteColor;
    } else {
        self.leftButton.imageView.tintColor = [UIColor colorWithHexString:@"#222222"];
    }
}

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection
{
    [super traitCollectionDidChange:previousTraitCollection];
    
    //切换到后台之后,会触发两次,因此会导致下面的脚本重新加载了
    UIApplicationState state = [UIApplication sharedApplication].applicationState;
    if(state == UIApplicationStateBackground) {
        //切换后台没必要触发,通过previousTraitCollection.userInterfaceStyle不正确
        return;
    }
    
    [self configNavigationBarStype];
    
    //iOS16屏幕旋转,也会触发

    //这里的目的是仅仅是夜间模式的时候才触发
    BOOL isDarkTheme = [[PreferenceManager shareInstance].items.isDarkTheme boolValue];
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    if(status == DarkModeStatusAuto) {
        //跟随系统变化
        BOOL needReload = NO;
        
        if(self.traitCollection.userInterfaceStyle == UIUserInterfaceStyleLight) {
            //浅色
            if(isDarkTheme) {
                //当前是黑色,切换浅色
                [PreferenceManager shareInstance].items.isDarkTheme = @(NO);
                [[PreferenceManager shareInstance] encode];
                
                needReload = YES;
            } else {
                //当前是浅色,不用处理
            }
        } else if(self.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            //黑色
            if(isDarkTheme) {
                //当前是黑色,不用处理
            } else {
                //当前是浅色,切换
                [PreferenceManager shareInstance].items.isDarkTheme = @(YES);
                [[PreferenceManager shareInstance] encode];
                
                needReload = YES;
            }
        }
                
        [self themeDidChangeHandler:needReload];
        
        if(needReload) {
            //切换夜间模式
            [[NSNotificationCenter defaultCenter] postNotificationName:kDarkThemeDidChangeNotification object:nil];
        }
    }
}

// 暗黑模式
- (void)themeDidChangeHandler:(BOOL)needReload
{
    //只有当前的controller会触发一次, 其它已存在的controller走通知
}

- (void)darkThemeDidChangeNotification:(NSNotification *)notification
{
    //非最前面的controller/view收到暗黑模式自动切换的通知
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    NSLog(@"页面销毁->%@ dealloc", [self class]);
}

@end
