//
//  ThemeProtocol.m
//  PPBrowser
//
//  Created by qingbin on 2022/9/4.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ThemeProtocol.h"
#import "NSObject+Helper.h"

@implementation ThemeProtocol

//更新当前暗黑模式状态
+ (void)updateDarkModeStatus:(DarkModeStatus)status
{
    [PreferenceManager shareInstance].items.darkModeStatus = @(status);
    
    BOOL isDarkTheme;
    UIWindow* window = YBIBNormalWindow();
    if(status == DarkModeStatusAuto) {
        //跟随系统
        if(window.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            isDarkTheme = YES;
        } else {
            isDarkTheme = NO;
        }
    } else {
        //不跟随系统
        isDarkTheme = status==DarkModeStatusDark;
    }
    
    [PreferenceManager shareInstance].items.isDarkTheme = @(isDarkTheme);
    [[PreferenceManager shareInstance] encode];
    
    [self updateSystemUserInterfaceStyle];
}

//获取当前暗黑模式状态
+ (DarkModeStatus)getDarkModeStatus
{
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    
    return status;
}

//不管什么状态,获取当前是否是暗黑模式
+ (BOOL)isDarkTheme
{
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    BOOL isDarkTheme;
    UIWindow* window = YBIBNormalWindow();
    if(!window) {
        window = [UIApplication sharedApplication].delegate.window;
    }

    if(status == DarkModeStatusAuto) {
        //跟随系统
        if(window.traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
            isDarkTheme = YES;
        } else {
            isDarkTheme = NO;
        }
    } else {
        //不跟随系统
        isDarkTheme = [[PreferenceManager shareInstance].items.isDarkTheme boolValue];
    }

    return isDarkTheme;
}

//更新系统的模式
+ (void)updateSystemUserInterfaceStyle
{
    DarkModeStatus status = [[PreferenceManager shareInstance].items.darkModeStatus intValue];
    UIWindow* window = YBIBNormalWindow();
    if(!window) {
        window = [UIApplication sharedApplication].delegate.window;
    }
    
    if(status == DarkModeStatusAuto) {
        //跟随系统
        [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleUnspecified];
    } else {
        //不跟随系统
        if(status == DarkModeStatusDark) {
            [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleDark];
        } else {
            [window setOverrideUserInterfaceStyle:UIUserInterfaceStyleLight];
        }
    }
}

@end
