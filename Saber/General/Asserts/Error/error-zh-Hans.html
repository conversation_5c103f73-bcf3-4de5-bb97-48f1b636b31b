<!DOCTYPE html>
<html lang="zh-cmn">
<head>
	<meta charset="UTF-8" />
	<title>无法访问此网站</title>
	<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
	<style>
		*{padding: 0;margin: 0;}
		html{font-size: 62.5%; height: 100%;}
		body{height: 100%; background: #f5f5f5;}

		@media (orientation: landscape){
			.content{-webkit-transform:translate(-50%, -100%); transform:translate(-50%, -100%);}
			.opt{text-align: right; padding-right: 3rem; box-sizing: border-box;}
		}
	</style>
	<style id="style">#doc{height: 100%; position: relative; z-index: 1; background:#f5f5f5}body{height: 100%; background: #f5f5f5;}h1{margin-bottom: 10%; font-size: 2rem;color: #333333;}.content{position: absolute; top: 50%; left: 50%; width: 182px;-webkit-transform:translate(-50%, -150%); transform:translate(-50%, -150%); text-align: left;}.cases{padding-left: 1.5rem; color: #333333; font-size: 1.5rem; line-height: 2.5rem;}.opt{position: absolute; bottom: 20%; left: 0; width: 100%; text-align: center;}.opt button{width: 12rem; height: 3.8rem; margin: 0 3%; font-size: 1.5rem; border: none; border-radius: 1.9rem; color: #fff;}.opt .back{background: #767677;}.opt .refresh{background: #2D7AFE;}</style>
	<script type="text/javascript">
		if (window.__firefox__) {
            var dayModeJS = document.getElementById('style').innerHTML;
            var nightModeJS = "#doc{height: 100%; position: relative; z-index: 1; background:#1a1a1a}body{height: 100%; background: #1a1a1a;}h1{margin-bottom: 10%; font-size: 2rem;color: #666;}.content{position: absolute; top: 50%; left: 50%; width: 182px;-webkit-transform:translate(-50%, -150%); transform:translate(-50%, -150%); text-align: left;}.cases{padding-left: 1.5rem; color: #666; font-size: 1.5rem; line-height: 2.5rem;}.opt{position: absolute; bottom: 8%; left: 0; width: 100%; text-align: center;}.opt button{width: 12rem; height: 3.8rem; margin: 0 1%; font-size: 1.5rem; border: none; border-radius: 2px; color: #999;}.opt .back{background: #474747;}.opt .refresh{background: #036a1d;}";
			window.__firefox__.error_nightMode = function() {
				document.getElementById('style').innerHTML = nightModeJS;
			};
			window.__firefox__.error_dayMode = function() {
				document.getElementById('style').innerHTML = dayModeJS;
			};
		}
	</script>
</head>
<body>
    
	<div id="doc">
		<div class="content">
			<h1>无法访问此网站</h1>
			<ul class="cases">
				<li class="item">检查网络连接是否正常</li>
				<li class="item">输入网址不正确</li>
				<li class="item">访问网站可能出现故障</li>
			</ul>
		</div>
		<div class="opt">
			<button class="back" id="js-back">返回</button>
			<button class="refresh" id="js-refresh">重新加载</button>
		</div>
	</div>
	<script>
		document.addEventListener('touchmove', function(e) {
			e.preventDefault();
		});
		document.getElementById('js-back').addEventListener('touchstart', function (e) {
			e.preventDefault();
            //返回
            window.webkit.messageHandlers.errorPageHelper.postMessage({'reload':false});
		})
		document.getElementById('js-refresh').addEventListener('touchstart', function (e) {
			e.preventDefault();
            //重新加载
            window.webkit.messageHandlers.errorPageHelper.postMessage({
                'reload': true,
                'url': window.location.href
            });
		})
	</script>
</body>
</html>
