//
//  UIViewController+Helper.m
//  <PERSON><PERSON><PERSON><PERSON>
//
//  Created by q<PERSON><PERSON> on 2022/1/25.
//

#import "UIViewController+Helper.h"

#import "BaseNavigationController.h"
#import "HomeViewController.h"
#import "SettingViewController.h"

#import "MaizyHeader.h"
#import "UIColor+Helper.h"
#import "NSObject+Helper.h"

#import "ThemeProtocol.h"

@implementation UIViewController (Helper)

+ (UITabBarController *)tabbarController
{
    UIColor* tintColor;
    UIColor* backgroundColor;
    
    BOOL isDarkTheme = [ThemeProtocol isDarkTheme];
    if(isDarkTheme) {
        tintColor = UIColor.whiteColor;
        backgroundColor = [UIColor colorWithHexString:kDarkThemeColor030];
    } else {
        tintColor = [UIColor colorWithHexString:@"#333333"];
        backgroundColor = UIColor.whiteColor;
    }
    
    [[UITabBar appearance] setTintColor:tintColor];
    
    NSArray *imageArray = @[@"tabbar_home_Normal",
                            @"tabbar_settings_Normal"];
    NSArray *titleArray = @[NSLocalizedString(@"tabbar.home", nil), NSLocalizedString(@"tabbar.settings", nil)];
    
    NSArray *controllerArray = @[[[HomeViewController alloc] init],
                                 [[SettingViewController alloc] init]];
    
    UITabBarController *tabBarController = [[UITabBarController alloc] init];
    NSMutableArray *vcArray = [[NSMutableArray alloc] init];
    
    for (NSInteger i = 0; i < imageArray.count; i++) {
        UIViewController *controller = controllerArray[i];
        
        UITabBarItem *item = nil;
        item = [[UITabBarItem alloc] initWithTitle:titleArray[i]
                                             image:[[UIImage imageNamed:imageArray[i]] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate]
                                     selectedImage:[[UIImage imageNamed:imageArray[i]] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate]];
        controller.tabBarItem = item;
        
        BaseNavigationController *nav = [[BaseNavigationController alloc] initWithRootViewController:controller];
        nav.navigationBar.translucent = NO;
        [vcArray addObject:nav];
    }
        
    UITabBarAppearance *appearance = [tabBarController.tabBar.standardAppearance copy];
    appearance.backgroundColor = backgroundColor; // 修改背景颜色 F7F7F7
    appearance.shadowColor = UIColor.clearColor; // 修改横线颜色
    appearance.backgroundImage = [UIColor imageWithColor:backgroundColor size:CGSizeMake(1, 1)];
    appearance.shadowImage = [UIColor imageWithColor:backgroundColor size:CGSizeMake(1, 1)];
    tabBarController.tabBar.standardAppearance = appearance;
    
    //适配iOS15, 背景颜色
    tabBarController.tabBar.backgroundColor = backgroundColor;
    
    //去掉默认的一像素shadow
//    [[UITabBar appearance] setBackgroundImage:[[UIImage alloc] init]];
//    [[UITabBar appearance] setShadowImage:[[UIImage alloc] init]];
    
    tabBarController.viewControllers = vcArray;
    tabBarController.tabBar.translucent = NO;
    return tabBarController;
}

+ (UITabBarController *)rootTabbarController
{
    UIWindow* keywindow = YBIBNormalWindow();
    return (UITabBarController*)keywindow.rootViewController;
}

@end
