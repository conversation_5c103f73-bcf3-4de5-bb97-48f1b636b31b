//
//  UITableView+HintMessage.m
//  YSBBusiness
//
//  Created by <PERSON> on 4/13/15.
//  Copyright (c) 2015 lu lucas. All rights reserved.
//

#import "UITableView+HintMessage.h"
#import <objc/runtime.h>
#import "UIColor+Helper.h"
#import "UIView+FrameHelper.h"
#import "UIView+Helper.h"
#import "MaizyHeader.h"
#import "ReactiveCocoa.h"
#import "Masonry.h"

// 4.16.0 增加新的hint，之前那个也不去掉，继续保留
static const void* kHintMessageViewKey = "kHintMessageViewKey";// hint的backview
static const void* kHintMessageContentViewKey = "kHintMessageContentViewKey";// hint的控件放在这里
static const void* kHintMessageImageKey = "kHintMessageImageKey";
static const void* kHintMessageLabelKey = "kHintMessageLabelKey";
static const void* kHintMessageMarginKey = "kHintMessageMarginKey";

@implementation UITableView (HintMessage)

- (void)hideHintMessage
{    
    [self hintMessageView].hidden = YES;
}

// 4.16.0
// back view
- (UIView *)hintMessageView
{
    UIView *hintMessageView = objc_getAssociatedObject(self, kHintMessageViewKey);
    if (!hintMessageView) {
        hintMessageView = [[UIView alloc] init];
        [self addSubview:hintMessageView];
        // 注意，这一句要放在下方的rac之前，否则会导致死循环，不断地创建view
        objc_setAssociatedObject(self, kHintMessageViewKey, hintMessageView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
        [hintMessageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(self);
            // UIScrollView依靠与其subviews之间的约束来确定ContentSize的大小,设置width和height，使得hintMessageView确定大小
            make.width.equalTo(self);
            make.height.equalTo(self);
        }];
        
        // skip 初始化的那一次
        // 4.27.0 新增，用于处理拼团的tableheaderview会变化的问题
        @weakify(self)
        [[RACObserve(self, tableHeaderView) skip:1] subscribeNext:^(UIView *x) {
            @strongify(self)
            NSInteger realMargin = x.height + [self hintMessageMargin].integerValue;
            [[self hintMessageView] mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self).offset(realMargin);
                // 4.29.0 去掉，若realMargin比self.height还大，则会触发警告，这里其实不设置也ok，无非是hintMessageView的范围超出self罢了
//                make.height.equalTo(self).offset(-realMargin);
            }];
        }];
        
    }
    return hintMessageView;
}

- (UIView *)hintMessageContentView
{
    UIView *hintMessageContentView = objc_getAssociatedObject(self, kHintMessageContentViewKey);
    if (!hintMessageContentView) {
        hintMessageContentView = [[UIView alloc] init];
        [[self hintMessageView] addSubview:hintMessageContentView];
        objc_setAssociatedObject(self, kHintMessageContentViewKey, hintMessageContentView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
        [hintMessageContentView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo([self hintMessageView]).offset(60.0f);
            make.left.right.equalTo([self hintMessageView]);
        }];
        
    }
    return hintMessageContentView;
}

- (NSNumber *)hintMessageMargin
{
    NSNumber *hintMessageMargin = objc_getAssociatedObject(self, kHintMessageMarginKey);
    if (!hintMessageMargin) {
        hintMessageMargin = @(0);
        objc_setAssociatedObject(self, kHintMessageMarginKey, hintMessageMargin, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
    return hintMessageMargin;
}
- (void)setHintMessageMargin:(NSNumber *)margin
{
    objc_setAssociatedObject(self, kHintMessageMarginKey, margin, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (UIImageView *)hintImageView
{
    
    UIImageView* hintImageView = objc_getAssociatedObject(self, kHintMessageImageKey);
    if (!hintImageView) {
        hintImageView = [[UIImageView alloc] init];
        hintImageView.contentMode = UIViewContentModeScaleAspectFit;
        
        [[self hintMessageContentView] addSubview:hintImageView];
        
        [hintImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo([self hintMessageContentView]);
            make.top.equalTo([self hintMessageContentView]);
            make.height.mas_equalTo(108.0f);
            make.width.mas_equalTo(132.0f);
        }];
        
        objc_setAssociatedObject(self, kHintMessageImageKey, hintImageView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
    }
    
    return hintImageView;
}

- (UILabel*)hintMessageLabel
{
    UILabel* hintMessageLabel = objc_getAssociatedObject(self, kHintMessageLabelKey);
    if (!hintMessageLabel) {
        
        hintMessageLabel = [UIView createLabelWithTitle:@""
                                                   textColor:[UIColor colorWithHexString:@"#8a9399"]
                                                     bgColor:[UIColor clearColor]
                                                    fontSize:14
                                               textAlignment:NSTextAlignmentCenter bBold:NO];
        hintMessageLabel.numberOfLines = 0;
        
        [[self hintMessageContentView] addSubview:hintMessageLabel];
        objc_setAssociatedObject(self, kHintMessageLabelKey, hintMessageLabel, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
        [hintMessageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo([self hintMessageContentView]);
            make.top.equalTo([self hintImageView].mas_bottom).offset(15.0f);
            
            make.left.equalTo([self hintMessageContentView]).offset(40.0f);
            make.right.equalTo([self hintMessageContentView]).offset(-40.0f);
        }];
        
    }
    return hintMessageLabel;
}

- (void)showHintMessage:(NSString*)hintMessage
{
    [self showHintMessage:hintMessage image:nil sectionMargin:0];
}

- (void)showHintMessage:(NSString*)hintMessage
                  image:(UIImage *)image
{
    [self showHintMessage:hintMessage image:image sectionMargin:0];
}

- (void)showHintMessage:(NSString*)hintMessage
                  image:(UIImage *)image
          sectionMargin:(CGFloat)margin
{
    // 记录下margin
    [self setHintMessageMargin:@(margin)];
    CGFloat realMargin = self.tableHeaderView.height + margin;
    
    // 如果有offset，则要重新设置contentview的约束,无offset的时候contentview是居中的
    if (realMargin != 0) {
        // 有tableheaderview的话，要设置偏移
        [[self hintMessageView] mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self).offset(realMargin);
            make.height.equalTo(self).offset(-realMargin);
        }];
    }
    
    [self hintMessageView].hidden = NO;
    
    UIImageView *imageView = [self hintImageView];
    UILabel* hintMessageLabel = [self hintMessageLabel];
    
    // 处理有无图片
    if (image != nil) {
        imageView.image = image;
        imageView.hidden = NO;
        [imageView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(108.0f);
        }];
        [hintMessageLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(imageView.mas_bottom).offset(15.0f);
        }];
    } else {
        imageView.hidden = YES;
        [imageView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];
        [hintMessageLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(imageView.mas_bottom).offset(0);
        }];
    }
    
    // 处理有无文字
    hintMessageLabel.text = hintMessage;
}
- (void)updateImageSize:(CGSize)imageSize
                   font:(UIFont *)font
           messageColor:(UIColor *)messageColor messageTopMargin:(CGFloat)topMargin
{
    UIImageView *imageView = [self hintImageView];
    UILabel* hintMessageLabel = [self hintMessageLabel];
    [imageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(imageSize);
    }];
    [hintMessageLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imageView.mas_bottom).offset(topMargin);
    }];
    hintMessageLabel.textColor = messageColor;
    hintMessageLabel.font = font;
}

@end
