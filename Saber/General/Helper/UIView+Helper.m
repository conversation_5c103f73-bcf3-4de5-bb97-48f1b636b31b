//
//  UIView+Helper.m
//  QRCode
//
//  Created by qingbin on 2021/11/7.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import "UIView+Helper.h"
#import "ReactiveCocoa.h"
#import "MBCustomView.h"

#import "NSObject+Helper.h"
#import "Saber-Swift.h"

@implementation UIView (Helper)

+ (UILabel *)createLabelWithTitle:(NSString *)title
                        textColor:(UIColor *)textColor
                          bgColor:(UIColor *)bgColor
                         fontSize:(CGFloat)fontSize
                    textAlignment:(NSTextAlignment)textAlignment
                            bBold:(BOOL)bBold
{
    UILabel *label = [[UILabel alloc] init];
    label.backgroundColor = bgColor;
    label.textColor = textColor;
    if (bBold) {
        label.font = [UIFont boldSystemFontOfSize:fontSize];
    } else {
        label.font = [UIFont systemFontOfSize:fontSize];
    }
    
    label.text = title;
    label.textAlignment = textAlignment;
    
    return label;
}

+ (UIButton *)createButtonWithImage:(UIImage *)image
                              click:(void (^)(id x))nextBlock
{
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    [btn setImage:image forState:UIControlStateNormal];
    if (nextBlock != nil) {
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:nextBlock];
    }
    
    return btn;
}

+ (UIButton *)createButtonWithTitle:(NSString *)title
                          textColor:(UIColor *)textColor
                            bgColor:(UIColor *)bgColor
                           fontSize:(CGFloat)fontSize
                              click:(void (^)(id x))nextBlock
{
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    
    [btn setTitle:title forState:UIControlStateNormal];
    btn.backgroundColor = bgColor;
    [btn setTitleColor:textColor forState:UIControlStateNormal];
    btn.titleLabel.font = [UIFont systemFontOfSize:fontSize];
    if (nextBlock != nil) {
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:nextBlock];
    }

    return btn;
}

- (UIImage*)screenshotWithAspectRatio:(float)aspectRatio offset:(CGPoint)offset quality:(float)quality
{
    if(aspectRatio < 0) return nil;
    
    CGSize size;
    if(aspectRatio > 0) {
        float viewAspectRatio = self.frame.size.width / self.frame.size.height;
        if (viewAspectRatio > aspectRatio) {
            size.height = self.frame.size.height;
            size.width = size.height * aspectRatio;
        } else {
            size.width = self.frame.size.width;
            size.height = size.width / aspectRatio;
        }
    } else {
        size = self.frame.size;
    }
    
    return [self screenshotWithSize:size offset:offset quality:quality];
}

- (UIImage*)screenshotWithSize:(CGSize)size offset:(CGPoint)offset quality:(float)quality
{
    if(quality < 0 || quality > 1) return nil;
    
    UIGraphicsBeginImageContextWithOptions(size, false, [UIScreen mainScreen].scale*quality);
    [self drawViewHierarchyInRect:CGRectMake(offset.x, offset.y, size.width, size.height) afterScreenUpdates:false];
    UIImage* image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    
    return image;
}

//snapshot会报[_UIReplicantView _isSymbolImage]的错误
- (UIImage*)screenshot
{
    UIGraphicsImageRenderer* render = [[UIGraphicsImageRenderer alloc]initWithSize:self.bounds.size];
    @weakify(self)
    UIImage* image = [render imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull rendererContext) {
        @strongify(self)
        [self drawViewHierarchyInRect:self.frame afterScreenUpdates:YES];
    }];
    
    return image;
}

+ (void)showLoading:(UIView *)view animated:(BOOL)animated
{
    UIView* parentView = [self _hudsParentView:view];
    [MBProgressHUD showHUDAddedTo:parentView animated:animated];
}

+ (void)hideLoading:(UIView *)view animated:(BOOL)animated
{
    UIView* parentView = [self _hudsParentView:view];
    [MBProgressHUD hideHUDForView:parentView animated:animated];
}

+ (void)showLongMessage:(UIView *)view message:(NSString *)msg
{
    [self _showMessage:view message:msg time:2.0f];
}

+ (void)showMessage:(UIView *)view message:(NSString *)msg
{
    [self _showMessage:view message:msg time:1.0f];
}

+ (void)showCustomMessage:(UIView *)view
                  message:(NSString *)msg
                actionMsg:(NSString *)actionMsg
                   action:(void(^)(void))actionBlock
{
    MBCustomView* hud = [MBCustomView showCustomMessage:view
                                                message:msg
                                              actionMsg:actionMsg
                                                 action:actionBlock];
    
    [hud hideAfterDelay:5];
}

+ (UIView*)_hudsParentView:(UIView*)view
{
    UIWindow *_keyWindow = [[UIApplication sharedApplication] keyWindow];
    [MBProgressHUD hideAllHUDsForView:_keyWindow animated:NO];
    [MBProgressHUD hideAllHUDsForView:view animated:NO];
    UIView *_parentView = view;
    if (!_parentView) {
        _parentView = _keyWindow;
    }
    
    return _parentView;
}

+ (void)_showMessage:(UIView *)view message:(NSString *)str time:(NSInteger)time
{
    if (str.length <= 0) return;
    // 收键盘
    [view.window endEditing:YES];
    
    UIWindow *_keyWindow = [[UIApplication sharedApplication] keyWindow];
    [MBProgressHUD hideAllHUDsForView:_keyWindow animated:NO];
    [MBProgressHUD hideAllHUDsForView:view animated:NO];
    UIView *_parentView = view;
    if (!_parentView) {
        _parentView = _keyWindow;
    }

    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:_parentView animated:YES];
    hud.mode = MBProgressHUDModeText;
    hud.margin = 10.f;
    hud.labelText = str;
    hud.removeFromSuperViewOnHide = YES;
    [hud hide:YES afterDelay:time];
}

//v2.5
/// 显示toast
+ (void)showToast:(NSString *)msg
{
    [self hideHud:NO];
    if (msg.length <= 0) return;
    
    UIWindow *_keyWindow = [NSObject normalWindow];
    [_keyWindow endEditing:YES];
    
    [MBProgressHUD hideAllHUDsForView:_keyWindow animated:NO];

    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:_keyWindow animated:YES];
    //qingbin
    hud.userInteractionEnabled = NO;
    
    hud.mode = MBProgressHUDModeText;
    hud.margin = 10.f;
    hud.labelText = msg;
    hud.removeFromSuperViewOnHide = YES;
    [hud hide:YES afterDelay:1.0];
}

/// 长时间显示toast
+ (void)showToast:(NSString *)msg
            delay:(float)delay
{
    [self hideHud:NO];
    if (msg.length <= 0) return;
    
    UIWindow *_keyWindow = [NSObject normalWindow];
    [_keyWindow endEditing:YES];
    
    [MBProgressHUD hideAllHUDsForView:_keyWindow animated:NO];

    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:_keyWindow animated:YES];
    //qingbin
    hud.userInteractionEnabled = NO;
    
    hud.mode = MBProgressHUDModeText;
    hud.margin = 10.f;
    hud.labelText = msg;
    hud.removeFromSuperViewOnHide = YES;
    [hud hide:YES afterDelay:delay];
}

/// 进度显示toast
+ (void)showLoading:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD setCustomAnimationType:1];
    [ProgressHUD show:msg interaction:NO];
}

/// 加载中时，需要更新文案（避免闪烁）
+ (void)updateLoadingText:(NSString *)msg
{
    [ProgressHUD updateStatusWithText:msg];
}

/// 隐藏toast
+ (void)hideHud:(BOOL)animated
{
    if(animated) {
        [ProgressHUD dismiss];
    } else {
        [ProgressHUD remove];
    }
}

/// 成功
+ (void)showSucceed:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD showSucceed:msg];
}

/// 失败
+ (void)showFailed:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD showFailed:msg];
}

/// 警告
+ (void)showWarning:(NSString *)msg
{
    [self hideHud:NO];
    [ProgressHUD showWarning:msg];
}

@end
