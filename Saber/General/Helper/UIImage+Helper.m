//
//  UIImage+Helper.m
//  PPBrowser
//
//  Created by qingbin on 2022/4/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UIImage+Helper.h"

@implementation UIImage (Helper)

- (UIImage *)stretchableImage
{
    CGFloat left = floorf((self.size.width + 1) / 2) - 1;
    CGFloat top = floorf((self.size.height + 1) / 2) - 1;
    return [self resizableImageWithCapInsets:UIEdgeInsetsMake(top, left, top, left)];
}

+ (UIImage *)stretchableImageNamed:(NSString *)name
{
    return [[UIImage imageNamed:name] stretchableImage];
}

@end
