//
//  UIView+FrameHelper.h
//  KKBusiness
//
//  Created by qing<PERSON> on 10/23/14.
//  Copyright (c) 2014 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIView (FrameHelper)
///**
// *  顶端
// *
// *  @return <#return value description#>
// */
//- (CGFloat)top;
///**
// *  底部
// *
// *  @return <#return value description#>
// */
//- (CGFloat)bottom;
///**
// *  左边
// *
// *  @return <#return value description#>
// */
//- (CGFloat)left;
///**
// *  右边
// *
// *  @return <#return value description#>
// */
//- (CGFloat)right;
///**
// *  宽
// *
// *  @return <#return value description#>
// */
//- (CGFloat)width;
///**
// *  高
// *
// *  @return <#return value description#>
// */
//- (CGFloat)height;

@property (nonatomic) CGFloat left;        ///< Shortcut for frame.origin.x.
@property (nonatomic) CGFloat top;         ///< Shortcut for frame.origin.y
@property (nonatomic) CGFloat right;       ///< Shortcut for frame.origin.x + frame.size.width
@property (nonatomic) CGFloat bottom;      ///< Shortcut for frame.origin.y + frame.size.height
@property (nonatomic) CGFloat width;       ///< Shortcut for frame.size.width.
@property (nonatomic) CGFloat height;      ///< Shortcut for frame.size.height.
@property (nonatomic) CGFloat centerX;     ///< Shortcut for center.x
@property (nonatomic) CGFloat centerY;     ///< Shortcut for center.y
@property (nonatomic) CGPoint origin;      ///< Shortcut for frame.origin.
@property (nonatomic) CGSize  size;        ///< Shortcut for frame.size.

@end
