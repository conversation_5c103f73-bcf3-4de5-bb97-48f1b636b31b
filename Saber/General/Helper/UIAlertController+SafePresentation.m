//
//  UIAlertController+SafePresentation.m
//  PPBrowser
//
//  Created by qingbin on 2025/4/1.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import "UIAlertController+SafePresentation.h"
#import "BrowserUtils.h"

@implementation UIAlertController (SafePresentation)

- (void)presentSafelyFromViewController:(UIViewController *)viewController
                             sourceView:(UIView *)sourceView
                             sourceRect:(CGRect)sourceRect
                          barButtonItem:(UIBarButtonItem *)barButtonItem
                             completion:(void (^)(void))completion
{
    if (!viewController) {
        return;
    }
    // 只有在iPad和ActionSheet样式时才需要设置弹出框属性
    if ([BrowserUtils isiPad] &&
        self.preferredStyle == UIAlertControllerStyleActionSheet) {
        UIPopoverPresentationController *popover = self.popoverPresentationController;
        if (popover) {
            // 优先使用传入的barButtonItem
            if (barButtonItem) {
                popover.barButtonItem = barButtonItem;
            } else {
                // 其次使用sourceView和sourceRect
                // 设置sourceView，如果未提供则使用控制器的视图
                popover.sourceView = sourceView ?: viewController.view;
                
                // 如果sourceRect设置了非零区域，则使用它
//                if (!CGRectIsEmpty(sourceRect)) {
                if (!CGRectEqualToRect(CGRectZero, sourceRect)) {
                    if (CGRectGetWidth(sourceRect) == 0) {
                        sourceRect.size.width = 1;
                    }
                    
                    if (CGRectGetHeight(sourceRect) == 0) {
                        sourceRect.size.height = 1;
                    }
                    
                    popover.sourceRect = sourceRect;
                } else {
                    // 否则使用视图的中心点
                    UIView *view = popover.sourceView;
                    popover.sourceRect = CGRectMake(CGRectGetMidX(view.bounds),
                                                   CGRectGetMidY(view.bounds),
                                                   1,
                                                   1);
                }
            }
            
            // 设置允许的箭头方向
            popover.permittedArrowDirections = UIPopoverArrowDirectionAny;
        }
    } else {
        //如果设置了barButtonItem，例如iPhone，也要设置
        UIPopoverPresentationController *popover = self.popoverPresentationController;
        if (popover) {
            // 优先使用传入的barButtonItem
            if (barButtonItem) {
                popover.barButtonItem = barButtonItem;
            }
        }
    }
    
    // 展示警告控制器
    [viewController presentViewController:self animated:YES completion:completion];
}

// 只使用 barButtonItem 的简化方法
- (void)presentSafelyFromViewController:(UIViewController *)viewController
                          barButtonItem:(UIBarButtonItem *)barButtonItem
{
    [self presentSafelyFromViewController:viewController
                               sourceView:nil
                               sourceRect:CGRectZero
                            barButtonItem:barButtonItem
                               completion:nil];
}

// 只使用 sourceView 并指定 sourceRect 的简化方法
- (void)presentSafelyFromViewController:(UIViewController *)viewController
                             sourceView:(UIView *)sourceView
                             sourceRect:(CGRect)sourceRect
{
    [self presentSafelyFromViewController:viewController
                               sourceView:sourceView
                               sourceRect:sourceRect
                            barButtonItem:nil
                               completion:nil];
}

// 最简化版本(处理不是actionsheet的情况，例如UIAlertControllerStyleAlert)
- (void)presentSafelyFromViewController:(UIViewController *)viewController
{
    [self presentSafelyFromViewController:viewController
                               sourceView:nil
                               sourceRect:CGRectZero
                            barButtonItem:nil
                               completion:nil];
}

@end
