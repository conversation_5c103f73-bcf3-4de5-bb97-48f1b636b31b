//
//  UITableView+HintMessage.h
//  YSBBusiness
//
//  Created by <PERSON> on 4/13/15.
//  Copyright (c) 2015 lu lucas. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UITableView (HintMessage)

- (void)showHintMessage:(NSString*)hintMessage; // 显示table view没有数据时的信息
- (void)hideHintMessage;                        // 隐藏提示信息

// 支持显示图片
- (void)showHintMessage:(NSString*)hintMessage
                  image:(UIImage *)image;
// 当存在sectionheader时，需要处理间距
- (void)showHintMessage:(NSString*)hintMessage
                  image:(UIImage *)image
          sectionMargin:(CGFloat)margin;
/** 用于处理image大小不一样，以及字体大小颜色不一样的情况
  @param  imageSize 图片大小
  @param  font 字体大小
  @param  messageColor 字体颜色
  @param  topMargin 文字距离图片底部的间距
 */
- (void)updateImageSize:(CGSize)imageSize
                   font:(UIFont *)font
           messageColor:(UIColor *)messageColor messageTopMargin:(CGFloat)topMargin;
@end
