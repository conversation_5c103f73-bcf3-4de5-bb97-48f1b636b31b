//
//  UIAlertController+SafePresentation.h
//  PPBrowser
//
//  Created by qingbin on 2025/4/1.
//  Copyright © 2025 qingbin. All rights reserved.
//

#import <UIKit/UIKit.h>

// 防止UIAlertController在iPad设置为actionsheet时，present时，修复崩溃的问题
@interface UIAlertController (SafePresentation)

// 只使用 barButtonItem 的简化方法
- (void)presentSafelyFromViewController:(UIViewController *)viewController
                          barButtonItem:(UIBarButtonItem *)barButtonItem;

// 只使用 sourceView 并指定 sourceRect 的简化方法
- (void)presentSafelyFromViewController:(UIViewController *)viewController
                             sourceView:(UIView *)sourceView
                             sourceRect:(CGRect)sourceRect;

// 最简化版本(处理不是actionsheet的情况，例如UIAlertControllerStyleAlert)
- (void)presentSafelyFromViewController:(UIViewController *)viewController;

- (void)presentSafelyFromViewController:(UIViewController *)viewController
                             sourceView:(UIView *)sourceView
                             sourceRect:(CGRect)sourceRect
                          barButtonItem:(UIBarButtonItem *)barButtonItem
                             completion:(void (^)(void))completion;
@end
