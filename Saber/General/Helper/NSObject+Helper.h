//
//  NSObject+Helper.h
//  QRCode
//
//  Created by qingbin on 2021/11/25.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <sys/utsname.h>

#import "PPEnums.h"
#import <Photos/Photos.h>

@interface NSObject (Helper)

UIWindow * _Nullable YBIBNormalWindow(void);

+ (UIWindow*)normalWindow;

/// 相册权限检测
+ (void)requestAuthorizationWithAuthorizedBlock:(void(^)(void))authorizedBlock
                                    rejectBlock:(void(^)(void))rejectBlock;

#pragma mark -- 智能检测是否需要带请求头
+ (void)requestForHeader:(NSDictionary*)requestHeader
                     url:(NSString*)url
              completion:(void(^)(BOOL succ, NSDictionary* header, id data))completion;

@end

