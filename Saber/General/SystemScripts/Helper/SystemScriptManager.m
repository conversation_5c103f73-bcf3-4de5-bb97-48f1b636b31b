//
//  SystemScriptManager.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/2.
//

#import "SystemScriptManager.h"

#import "UserScript.h"
#import "Tampermonkey.h"

#import "ReactiveCocoa.h"
#import "NSString+Helper.h"
#import "PPNotifications.h"

#import "ThemeProtocol.h"
#import "ResourceHelper.h"

@interface SystemScriptManager ()

@property(nonatomic,strong) NSString* securityToken;
@property(nonatomic,strong) NSMutableDictionary<NSString*,WKUserScript*>* compiledUserScripts;

@end

@implementation SystemScriptManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static SystemScriptManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [SystemScriptManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self loadAllUserScripts];
    }
    
    return self;
}

#pragma mark -- 预加载(还没注入到webView)
- (void)loadAllUserScripts
{
    // Ensure the first script loaded at document start is __firefox__.js
    // since it defines the `window.__firefox__` global.
    [self loadEncryptionUserScriptWithFileName:@"__firefox__" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:NO];
    
    // Ensure the first script loaded at document end is __firefox__.js
    // since it also defines the `window.__firefox__` global because PDF
    // content does not execute user scripts designated to run at document
    // start for some reason. ¯\_(ツ)_/¯
    [self loadEncryptionUserScriptWithFileName:@"__firefox__" injectTime:WKUserScriptInjectionTimeAtDocumentEnd mainFrameOnly:NO];
    
    [self loadEncryptionUserScriptWithFileName:@"installHelper" injectTime:WKUserScriptInjectionTimeAtDocumentStart mainFrameOnly:YES];
}

- (WKUserScript*)loadUserScriptWithFileName:(NSString*)fileName
                        injectTime:(WKUserScriptInjectionTime)injectTime
                     mainFrameOnly:(BOOL)mainFrameOnly
{
    NSString* key = [self keyForFileName:fileName injectTime:injectTime mainFrameOnly:mainFrameOnly];
    if(self.compiledUserScripts[key]) return self.compiledUserScripts[key];
    
    NSString* path = [[NSBundle mainBundle]pathForResource:fileName ofType:@"js"];
    NSError* error = nil;
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        NSLog(@"error = %@", error.localizedDescription);
    }
    
    WKUserScript* script = [[WKUserScript alloc] initWithSource:source injectionTime:injectTime forMainFrameOnly:mainFrameOnly];

    self.compiledUserScripts[key] = script;
    
    return script;
}

- (WKUserScript*)loadEncryptionUserScriptWithFileName:(NSString*)fileName
                        injectTime:(WKUserScriptInjectionTime)injectTime
                     mainFrameOnly:(BOOL)mainFrameOnly
{
    NSString* key = [self keyForFileName:fileName injectTime:injectTime mainFrameOnly:mainFrameOnly];
    if(self.compiledUserScripts[key]) return self.compiledUserScripts[key];
    
    NSString* source;
    if([fileName.lowercaseString isEqualToString:@"__firefox__"]) {
        source = [ResourceHelper saber].__firefox__;
    } else if([fileName.lowercaseString isEqualToString:@"installhelper"]) {
        source = [ResourceHelper saber].installHelper;
    } else {
        return nil;
    }
    
    WKUserScript* script = [[WKUserScript alloc] initWithSource:source injectionTime:injectTime forMainFrameOnly:mainFrameOnly];

    self.compiledUserScripts[key] = script;
    
    return script;
}

- (NSString*)keyForFileName:(NSString*)fileName
                 injectTime:(WKUserScriptInjectionTime)injectTime
              mainFrameOnly:(BOOL)mainFrameOnly
{
    return [NSString stringWithFormat:@"key-%@-%ld-%d",fileName,(long)injectTime,mainFrameOnly];
}

#pragma mark -- 注入脚本
- (void)injectUserSciptsToWebView:(WKWebView*)webView
{
    // Start off by ensuring that any previously-added user scripts are
    // removed to prevent the same script from being injected twice.
    [webView.configuration.userContentController removeAllUserScripts];

    WKUserScript* script;

    // Inject all pre-compiled user scripts.
    script = [self loadEncryptionUserScriptWithFileName:@"__firefox__"
                                   injectTime:WKUserScriptInjectionTimeAtDocumentStart
                                mainFrameOnly:NO];
    [webView.configuration.userContentController addUserScript:script];

    script = [self loadEncryptionUserScriptWithFileName:@"__firefox__"
                                   injectTime:WKUserScriptInjectionTimeAtDocumentEnd
                                mainFrameOnly:NO];
    [webView.configuration.userContentController addUserScript:script];

    //installHelper,注意加载的时机,不能太早,否则触发不了
    script = [self loadEncryptionUserScriptWithFileName:@"installHelper"
                                   injectTime:WKUserScriptInjectionTimeAtDocumentEnd
                                mainFrameOnly:YES];
    [webView.configuration.userContentController addUserScript:script];
    
    BOOL isDark = [ThemeProtocol isDarkTheme];
    if(isDark) {
        //暗黑模式
        script = [self loadUserScriptWithFileName:@"darkMode"
                                       injectTime:WKUserScriptInjectionTimeAtDocumentEnd
                                    mainFrameOnly:YES];
        [webView.configuration.userContentController addUserScript:script];
    }
}

#pragma mark -- lazy init
- (NSMutableDictionary<NSString *,WKUserScript *> *)compiledUserScripts
{
    if(!_compiledUserScripts) {
        _compiledUserScripts = [NSMutableDictionary dictionary];
    }
    
    return _compiledUserScripts;
}

@end
