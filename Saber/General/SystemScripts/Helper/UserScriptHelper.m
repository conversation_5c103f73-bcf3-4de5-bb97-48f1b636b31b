//
//  UserScriptHelper.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserScriptHelper.h"

#import "PPEnums.h"
#import "Tampermonkey.h"
#import "UIView+Helper.h"

@implementation UserScriptMessageItem
@end

@interface UserScriptHelper ()

@property(nonatomic,weak) WKWebView *webView;

@end

@implementation UserScriptHelper

- (instancetype)initWithWebView:(WKWebView *)webView
{
    self = [super init];
    if(self) {
        self.webView = webView;
    }
    
    return self;
}

- (NSString*)scriptMessageHandlerName
{
    return @"userScriptHelper";
}

- (void)userContentController:(WKUserContentController*)userContentController didReceiveScriptMessage:(WKScriptMessage*)message
{
    NSDictionary* params = message.body;
    UserScriptMessageItem* item = [[UserScriptMessageItem alloc]initWithDictionary:params error:nil];
    
    if(item.command == 21) {
        //via脚本
        if(item.value.length > 0) {
            ViaModel* model = [UserScriptHelper parseSourceCodeFromCommand:item.value];
            item.value = model.code;
        }
    }
    
    //弹窗提示，脚本安装中
    [UIView showLoading:NSLocalizedString(@"tips.handling", nil)];
    
    if(item.value.length > 0) {
        [[Tampermonkey shareInstance] installJSWithJSContent:item.value completion:^(BOOL succ, UserScript* script) {
            [UIView hideHud:NO];
            if(succ) {
                [UIView showSucceed:NSLocalizedString(@"common.install.script.success", nil)];
            } else {
                [UIView showFailed:NSLocalizedString(@"common.install.script.fail", nil)];
            }
        }];
    } else if(item.originalUrl.length > 0) {
        [[Tampermonkey shareInstance] installJS:item.originalUrl completion:^(BOOL succ) {
            [UIView hideHud:NO];
            if(succ) {
                [UIView showSucceed:NSLocalizedString(@"common.install.script.success", nil)];
            } else {
                [UIView showFailed:NSLocalizedString(@"common.install.script.fail", nil)];
            }
        }];
    }
}

+ (ViaModel*)parseSourceCodeFromCommand:(NSString*)commandValue
{
    //via插件管理, 不和油猴脚本混在一起处理, 增加代码的维护难度
    //via中url、originalUrl和code是必有的
    //name和autor可能没有, 而且via插件有可能是油猴的格式
    
    //via插件需要特殊处理
    ViaModel* via;
    //base64解码
    NSData* data = [commandValue dataUsingEncoding:NSUTF8StringEncoding];
    data = [[NSData alloc]initWithBase64EncodedData:data options:0];
    NSString* value = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    
    //code字段再次用base64编码了一次
    via = [[ViaModel alloc]initWithString:value error:nil];
    data = [via.code dataUsingEncoding:NSUTF8StringEncoding];
    data = [[NSData alloc]initWithBase64EncodedData:data options:0];
    via.code = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    
    return via;
}


@end
