//
//  AnalyticsHelper.h
//  MaizyClock
//
//  Created by q<PERSON><PERSON> on 2022/2/15.
//

#import <Foundation/Foundation.h>

//https://apm.umeng.com/apps/list

@interface AnalyticsHelper : NSObject

// 启动【友盟】分析统计
+ (void)startUMengService;

+ (instancetype)shareInstance;

/// 事件统计
+ (void)event:(NSString *)event attributes:(NSDictionary *)attributes;

/// 统计视频缓存失败的事件
- (void)eventDownloadFailWithAttributes:(NSDictionary *)attributes;

/// 统计用户安装了油猴脚本
+ (void)eventAddUserScriptWithAttributes:(NSDictionary *)attributes;

/// 统计用户删除了油猴脚本
+ (void)eventRemoveUserScriptWithAttributes:(NSDictionary *)attributes;

@end

