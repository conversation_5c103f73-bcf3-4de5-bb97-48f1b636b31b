//
//  AnalyticsHelper.m
//  MaizyClock
//
//  Created by qing<PERSON> on 2022/2/15.
//

#import "AnalyticsHelper.h"
#import "MaizyHeader.h"

#if UMENG_ENABLED

#import <UMCommon/MobClick.h>
#import <UMCommon/UMCommon.h>
#import <UMCommonLog/UMCommonLogHeaders.h>

#endif

/// https://developer.umeng.com/docs/119267/detail/119517
@interface AnalyticsHelper ()

@property (nonatomic, strong) NSMutableDictionary *failMap;

@end

@implementation AnalyticsHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static AnalyticsHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [AnalyticsHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.failMap = [NSMutableDictionary dictionary];
    }
    
    return self;
}


+ (void)startUMengService
{
    //开发者需要显式的调用此函数，日志系统才能工作
#if UMENG_ENABLED
    [UMCommonLogManager setUpUMCommonLogManager];
    [UMConfigure setLogEnabled:bLogSwitch];
    [UMConfigure initWithAppkey:kUMengAppKey channel:sChannelId];
    
    //设置为自动采集页面
    [MobClick setAutoPageEnabled:YES];
#endif
}

/// 事件统计
+ (void)event:(NSString *)event
   attributes:(NSDictionary *)attributes
{
#if UMENG_ENABLED
    if(attributes == nil) {
        attributes = @{};
    }
    
    [MobClick event:event attributes:attributes];
#endif
}

#pragma mark -- 统计视频缓存失败的事件

- (void)eventDownloadFailWithAttributes:(NSDictionary *)attributes
{
#if UMENG_ENABLED
    NSString* originalUrl = attributes[@"originalUrl"];
    if(originalUrl.length > 0) {
        //已统计的则不需要重复统计
        if(self.failMap[originalUrl]) return;
    } else {
        //加标志位
        self.failMap[originalUrl] = @1;
    }
    
    [AnalyticsHelper event:@"kEvent_Addons_Download_Fail" attributes:attributes];
#endif
}

#pragma mark -- 统计用户安装了油猴脚本

+ (void)eventAddUserScriptWithAttributes:(NSDictionary *)attributes
{
    [self event:@"kEvent_Addons_Add_UserScript" attributes:attributes];
}

#pragma mark -- 统计用户删除了油猴脚本

+ (void)eventRemoveUserScriptWithAttributes:(NSDictionary *)attributes
{
    [self event:@"kEvent_Addons_Remove_UserScript" attributes:attributes];
}


@end
