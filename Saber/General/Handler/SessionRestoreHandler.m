//
//  SessionRestoreHandler.m
//  PandaBrowser
//
//  Created by qing<PERSON> on 2022/3/10.
//

#import "SessionRestoreHandler.h"
#import "InternalURL.h"
#import "MaizyHeader.h"

/*
 保存历史记录的流程:
 1) 将所有的历史记录都拼接成 internal://local/sessionrestore?history={"history":["internal:\/\/local\/about\/home?uuidkey=89B60688-EF1C-47F3-A9C9-821A076CFFB3","https:\/\/m.baidu.com\/s?from=1000969b&word=App"],"currentPage":0}&uuidkey=08C79369-375B-494D-9B8A-0D78BF4DFA7A
    这样的格式, 其中"history"表示当前所有的堆栈;
 2) 通过sessionRestore.html中的location.reload();那么则会在js中调用加载currentPage指向的url, 格式如下:
 internal://local/sessionrestore?url=https%3A%2F%2Fm.baidu.com%2Fs%3Ffrom%3D1000969b%26word%3DApp&uuidkey=08C79369-375B-494D-9B8A-0D78BF4DFA7A
 3) 再通过判断"url="这个条件,拿到真正的链接(这个链接必须是经过stringByRemovingPercentEncoding处理,这样才能适配webview),
    从而通过location.replace()再次调用。
 */
@implementation SessionRestoreHandler

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.path = @"/sessionrestore";
    }
    
    return self;
}

- (NSData*)generateInvalidSchemeResponseData:(NSString*)urlString
{
    NSArray* schemes = @[[InternalURL scheme], @"http", @"https", @"file", @"about"];
    NSURL* url = [NSURL URLWithString:urlString];
    BOOL isValid = NO;
    for(NSString* scheme in schemes) {
        if([url.scheme containsString:scheme]) {
            isValid = YES;
            break;
        }
    }
    
    if(isValid) return nil;
    
    NSString* tips = NSLocalizedString(@"session.error.tips", nil);
    NSString* html = [NSString stringWithFormat:@"<!DOCTYPE html> \
              <html> \
                  <head> \
                      <meta name=\"referrer\" content=\"no-referrer\"> \
                  </head> \
                  <body> \
                      <h1>%@</h1> \
                  </body> \
              </html>",tips];
    NSData* data = [html dataUsingEncoding:kCFStringEncodingUTF8];
    return data;
}

- (NSData*)generateResponseThatRedirects:(NSURL*)url
{
    NSString* urlString = [url absoluteString];
    NSData* data = [self generateInvalidSchemeResponseData:urlString];
    if(data) return data;
    
    /*
     Printing description of html:
     "<!DOCTYPE html>\n<html>\n    <head>\n        <meta name=\"referrer\" content=\"no-referrer\">\n        <script>\n            location.replace('https://baike.baidu.com/item/%E6%89%8B%E6%9C%BA%E8%BD%AF%E4%BB%B6/7973966?fromtitle=APP&fromid=6133292&fr=aladdin');\n        </script>\n    </head>\n</html>"
     */
    
    NSString* apostropheEncoded = @"%27";
    urlString = [urlString stringByReplacingOccurrencesOfString:@"'" withString:apostropheEncoded];
    NSString* html = [NSString stringWithFormat:@"<!DOCTYPE html>\
                      <html>\
                          <head>\
                              <meta name=\"referrer\" content=\"no-referrer\"> \
                              <script>\
                                  location.replace('%@'); \
                              </script>\
                          </head>\
                      </html>", urlString];
    data = [html dataUsingEncoding:kCFStringEncodingUTF8];
    return data;
}

- (NSData*)defaultResponseData:(NSURL*)url
{
    // Handle the 'url='query param
    NSDictionary* urlParams = [self getQueryWithURL:url];
    if(urlParams[@"url"]) {
        NSString* realURL = urlParams[@"url"];
        return [self generateResponseThatRedirects:[NSURL URLWithString:realURL]];
    }
    
    // From here on, handle 'history=' query param
    NSString* sessionRestorePath = [[NSBundle mainBundle] pathForResource:@"SessionRestore" ofType:@"html"];
    NSError* error;
    NSString* html = [NSString stringWithContentsOfFile:sessionRestorePath encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        LOG_ERROR(@"error = %@", error);
        return nil;
    }
        
    NSData* data = [html dataUsingEncoding:kCFStringEncodingUTF8];
    
    return data;
}

- (NSURLResponse*)defaultResponse:(NSURL*)url
{
    // Handle the 'url='query param
    NSDictionary* urlParams = [self getQueryWithURL:url];
    NSURL* realURL = url;
    if(urlParams[@"url"]) {
        NSString* realURLString = urlParams[@"url"];
        realURL = [NSURL URLWithString:realURLString];
    }
    
    NSURLResponse* response = [[NSURLResponse alloc]initWithURL:realURL
                                                       MIMEType:@"text/html"
                                          expectedContentLength:-1
                                               textEncodingName:nil];
    return response;
}

@end
