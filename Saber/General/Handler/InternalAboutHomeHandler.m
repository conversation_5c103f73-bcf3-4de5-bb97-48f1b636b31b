//
//  InternalAboutHomeHandler.m
//  PandaBrowser
//
//  Created by qingbin on 2022/3/10.
//

#import "InternalAboutHomeHandler.h"

@implementation InternalAboutHomeHandler

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.path = @"/about/home";
    }
    
    return self;
}

- (NSData*)defaultResponseData:(NSURL*)url
{
    NSString* html = @"<!DOCTYPE html> \
    <html> \
      <body style='background-color:#ffffff'></body> \
    </html>";
    NSData* data = [html dataUsingEncoding:kCFStringEncodingUTF8];
    
    return data;
}

@end
