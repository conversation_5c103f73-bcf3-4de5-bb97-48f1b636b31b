//
//  ErrorPageHandler.m
//  PPBrowser
//
//  Created by qingbin on 2022/3/24.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ErrorPageHandler.h"

@implementation ErrorPageHandler

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.path = @"/error";
    }
    
    return self;
}

- (NSData*)defaultResponseData:(NSURL*)url
{
    
    NSString* path = [[NSBundle mainBundle]pathForResource:NSLocalizedString(@"error.fileUrl", nil) ofType:@"html"];
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:nil];
    
    NSData* data = [source dataUsingEncoding:NSUTF8StringEncoding];
    
    return data;
}

@end
