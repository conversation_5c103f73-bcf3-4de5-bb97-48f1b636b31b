//
//  InternalSchemeHandler.m
//  PandaBrowser
//
//  Created by q<PERSON><PERSON> on 2022/3/4.
//

#import "InternalSchemeHandler.h"
#import "InternalAboutHomeHandler.h"
#import "SessionRestoreHandler.h"
#import "ErrorPageHandler.h"

@interface InternalSchemeHandler ()

@property(nonatomic,strong) NSMutableDictionary *hanlders;

@end

@implementation InternalSchemeHandler

- (NSDictionary*)getQueryWithURL:(NSURL*)url
{
    NSMutableDictionary* results = [NSMutableDictionary dictionary];
    NSArray* keyValues = [url.query componentsSeparatedByString:@"&"];
    for(NSString* pair in keyValues) {
        NSArray* kv = [pair componentsSeparatedByString:@"="];
        if(kv.count > 1) {
            //stringByRemovingPercentEncoding - 将百分比编码字符重新转会普通字符
            //在iOS中通过WebView加载Url或者请求HTTP时，若是链接中包含中文、特殊符号&％或是空格等都需要预先进行一下转码才可正常访问。
            results[kv[0]] = [kv[1] stringByRemovingPercentEncoding];
        }
    }
    
    return results;
}

- (NSURLResponse*)defaultResponse:(NSURL*)url
{
    NSURLResponse* response = [[NSURLResponse alloc]initWithURL:url
                                                       MIMEType:@"text/html"
                                          expectedContentLength:-1
                                               textEncodingName:nil];
    return response;
}

- (NSData*)defaultResponseData:(NSURL*)url
{
    NSString* html = @"<!DOCTYPE html> \
    <html> \
      <body style='background-color:#ffffff'></body> \
    </html>";
    NSData* data = [html dataUsingEncoding:kCFStringEncodingUTF8];
    
    return data;
}

- (void)webView:(WKWebView *)webView startURLSchemeTask:(id <WKURLSchemeTask>)urlSchemeTask
{
    NSURL* url = urlSchemeTask.request.URL;
    if(!url) return;
    
    NSString* path = url.path;
    InternalSchemeHandler* handler = self.hanlders[path];

    NSData* data = [handler defaultResponseData:url];
    NSURLResponse* response = [handler defaultResponse:url];
    
    [urlSchemeTask didReceiveResponse:response];
    [urlSchemeTask didReceiveData:data];
    [urlSchemeTask didFinish];
}


- (void)webView:(WKWebView *)webView stopURLSchemeTask:(id <WKURLSchemeTask>)urlSchemeTask{}

- (NSMutableDictionary *)hanlders
{
    if(!_hanlders) {
        _hanlders = [NSMutableDictionary dictionary];

        _hanlders[@"/about/home"] = [InternalAboutHomeHandler new];
        _hanlders[@"/sessionrestore"] = [SessionRestoreHandler new];
        _hanlders[@"/error"] = [ErrorPageHandler new];
    }
    
    return _hanlders;
}

@end
