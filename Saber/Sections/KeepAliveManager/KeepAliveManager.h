//
//  KeepAliveManager.h
//  MaizyClock
//
//  Created by qingbin on 2022/2/11.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

typedef NS_ENUM(NSInteger, KeepAliveStrategy) {
    KeepAliveStrategyNone = 0,           // 不保活
    KeepAliveStrategyBackgroundTask,     // 仅使用后台任务
    KeepAliveStrategyAudioMix,          // 音频混合模式（低功耗）
    KeepAliveStrategyLocationUpdate,     // 位置更新（需要权限）
    KeepAliveStrategyVoIP               // VoIP模式（需要特殊权限）
};

@interface KeepAliveManager : NSObject

+ (instancetype)shareInstance;

/// 开始后台保活
- (void)startKeepAlive;

/// 停止后台保活
- (void)stopKeepAlive;

/// 设置保活策略
- (void)setKeepAliveStrategy:(KeepAliveStrategy)strategy;

/// 获取当前保活状态
- (BOOL)isKeepAliveActive;

/// 获取剩余后台时间
- (NSTimeInterval)remainingBackgroundTime;

@end
