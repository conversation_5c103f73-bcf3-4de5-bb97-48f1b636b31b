//
//  KeepAliveConfig.m
//  Saber
//
//  Created by AI Assistant on 2024/12/19.
//

#import "KeepAliveConfig.h"
#import <AVFoundation/AVFoundation.h>

// 配置键名
static NSString * const kKeepAliveStrategyKey = @"KeepAliveStrategy";
static NSString * const kEnableAutoStrategySwitchKey = @"EnableAutoStrategySwitch";
static NSString * const kEnableLowPowerModeKey = @"EnableLowPowerMode";
static NSString * const kTaskCheckIntervalKey = @"TaskCheckInterval";
static NSString * const kIdleTimeoutIntervalKey = @"IdleTimeoutInterval";
static NSString * const kAudioPlayIntervalKey = @"AudioPlayInterval";

// 默认值
static const KeepAliveStrategy kDefaultStrategy = KeepAliveStrategyAudioMix;
static const BOOL kDefaultAutoStrategySwitch = YES;
static const BOOL kDefaultLowPowerMode = YES;
static const NSTimeInterval kDefaultTaskCheckInterval = 5.0;
static const NSTimeInterval kDefaultIdleTimeoutInterval = 30.0;
static const NSTimeInterval kDefaultAudioPlayInterval = 10.0;

@implementation KeepAliveConfig

+ (instancetype)sharedConfig
{
    static dispatch_once_t onceToken;
    static KeepAliveConfig *sharedInstance;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[KeepAliveConfig alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self loadConfig];
    }
    return self;
}

#pragma mark - Configuration Management

- (void)saveConfig
{
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];

    [defaults setInteger:self.currentStrategy forKey:kKeepAliveStrategyKey];
    [defaults setBool:self.enableAutoStrategySwitch forKey:kEnableAutoStrategySwitchKey];
    [defaults setBool:self.enableLowPowerMode forKey:kEnableLowPowerModeKey];
    [defaults setDouble:self.taskCheckInterval forKey:kTaskCheckIntervalKey];
    [defaults setDouble:self.idleTimeoutInterval forKey:kIdleTimeoutIntervalKey];
    [defaults setDouble:self.audioPlayInterval forKey:kAudioPlayIntervalKey];

    [defaults synchronize];

    NSLog(@"[KeepAliveConfig] 配置已保存");
}

- (void)loadConfig
{
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];

    // 检查是否是首次启动
    if (![defaults objectForKey:kKeepAliveStrategyKey]) {
        [self resetToDefault];
        return;
    }

    self.currentStrategy = [defaults integerForKey:kKeepAliveStrategyKey];
    self.enableAutoStrategySwitch = [defaults boolForKey:kEnableAutoStrategySwitchKey];
    self.enableLowPowerMode = [defaults boolForKey:kEnableLowPowerModeKey];
    self.taskCheckInterval = [defaults doubleForKey:kTaskCheckIntervalKey];
    self.idleTimeoutInterval = [defaults doubleForKey:kIdleTimeoutIntervalKey];
    self.audioPlayInterval = [defaults doubleForKey:kAudioPlayIntervalKey];

    // 验证配置有效性
    [self validateConfig];

    NSLog(@"[KeepAliveConfig] 配置已加载");
}

- (void)resetToDefault
{
    self.currentStrategy = kDefaultStrategy;
    self.enableAutoStrategySwitch = kDefaultAutoStrategySwitch;
    self.enableLowPowerMode = kDefaultLowPowerMode;
    self.taskCheckInterval = kDefaultTaskCheckInterval;
    self.idleTimeoutInterval = kDefaultIdleTimeoutInterval;
    self.audioPlayInterval = kDefaultAudioPlayInterval;

    [self saveConfig];

    NSLog(@"[KeepAliveConfig] 配置已重置为默认值");
}

- (void)validateConfig
{
    // 验证时间间隔
    if (self.taskCheckInterval < 1.0 || self.taskCheckInterval > 60.0) {
        self.taskCheckInterval = kDefaultTaskCheckInterval;
    }

    if (self.idleTimeoutInterval < 10.0 || self.idleTimeoutInterval > 300.0) {
        self.idleTimeoutInterval = kDefaultIdleTimeoutInterval;
    }

    if (self.audioPlayInterval < 5.0 || self.audioPlayInterval > 60.0) {
        self.audioPlayInterval = kDefaultAudioPlayInterval;
    }

    // 验证策略可用性
    if (![self isStrategyAvailable:self.currentStrategy]) {
        self.currentStrategy = [self recommendedStrategy];
    }
}

#pragma mark - Strategy Information

- (NSString *)displayNameForStrategy:(KeepAliveStrategy)strategy
{
    switch (strategy) {
        case KeepAliveStrategyNone:
            return @"无保活";
        case KeepAliveStrategyBackgroundTask:
            return @"后台任务";
        case KeepAliveStrategyAudioMix:
            return @"音频混合";
        default:
            return @"未知策略";
    }
}

- (NSString *)descriptionForStrategy:(KeepAliveStrategy)strategy
{
    switch (strategy) {
        case KeepAliveStrategyNone:
            return @"不执行任何保活操作，依赖系统默认行为";
        case KeepAliveStrategyBackgroundTask:
            return @"使用系统后台任务API，适用于短时间保活（约3分钟）";
        case KeepAliveStrategyAudioMix:
            return @"播放静音音频保活，功耗较低，不影响其他音频应用";
        default:
            return @"未知策略";
    }
}

- (BOOL)isStrategyAvailable:(KeepAliveStrategy)strategy
{
    switch (strategy) {
        case KeepAliveStrategyNone:
        case KeepAliveStrategyBackgroundTask:
            return YES;

        case KeepAliveStrategyAudioMix: {
            // 检查音频权限
            AVAudioSession *session = [AVAudioSession sharedInstance];
            return session != nil;
        }

        default:
            return NO;
    }
}

- (KeepAliveStrategy)recommendedStrategy
{
    // 根据设备状态推荐最佳策略
    if (self.enableLowPowerMode) {
        // 低功耗模式优先选择后台任务
        if ([self isStrategyAvailable:KeepAliveStrategyBackgroundTask]) {
            return KeepAliveStrategyBackgroundTask;
        }
    }

    // 优先选择音频混合模式
    if ([self isStrategyAvailable:KeepAliveStrategyAudioMix]) {
        return KeepAliveStrategyAudioMix;
    }

    // 最后选择后台任务
    return KeepAliveStrategyBackgroundTask;
}

#pragma mark - Setters

- (void)setCurrentStrategy:(KeepAliveStrategy)currentStrategy
{
    if (_currentStrategy != currentStrategy) {
        _currentStrategy = currentStrategy;

        // 自动应用到KeepAliveManager
        [[KeepAliveManager shareInstance] setKeepAliveStrategy:currentStrategy];

        NSLog(@"[KeepAliveConfig] 策略已切换到: %@", [self displayNameForStrategy:currentStrategy]);
    }
}

@end
