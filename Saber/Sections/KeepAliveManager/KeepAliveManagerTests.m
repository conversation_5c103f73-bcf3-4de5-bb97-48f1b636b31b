//
//  KeepAliveManagerTests.m
//  Saber
//
//  Created by AI Assistant on 2024/12/19.
//

#import <XCTest/XCTest.h>
#import "KeepAliveManager.h"
#import "KeepAliveConfig.h"

@interface KeepAliveManagerTests : XCTestCase

@property (nonatomic, strong) KeepAliveManager *keepAliveManager;
@property (nonatomic, strong) KeepAliveConfig *config;

@end

@implementation KeepAliveManagerTests

- (void)setUp
{
    [super setUp];
    self.keepAliveManager = [KeepAliveManager shareInstance];
    self.config = [KeepAliveConfig sharedConfig];
}

- (void)tearDown
{
    [self.keepAliveManager stopKeepAlive];
    [super tearDown];
}

#pragma mark - Basic Functionality Tests

- (void)testKeepAliveManagerSingleton
{
    KeepAliveManager *manager1 = [KeepAliveManager shareInstance];
    KeepAliveManager *manager2 = [KeepAliveManager shareInstance];
    
    XCTAssertEqual(manager1, manager2, @"KeepAliveManager应该是单例");
}

- (void)testKeepAliveConfigSingleton
{
    KeepAliveConfig *config1 = [KeepAliveConfig sharedConfig];
    KeepAliveConfig *config2 = [KeepAliveConfig sharedConfig];
    
    XCTAssertEqual(config1, config2, @"KeepAliveConfig应该是单例");
}

- (void)testStartStopKeepAlive
{
    // 初始状态应该是未激活
    XCTAssertFalse([self.keepAliveManager isKeepAliveActive], @"初始状态应该是未激活");
    
    // 启动保活
    [self.keepAliveManager startKeepAlive];
    XCTAssertTrue([self.keepAliveManager isKeepAliveActive], @"启动后应该是激活状态");
    
    // 停止保活
    [self.keepAliveManager stopKeepAlive];
    XCTAssertFalse([self.keepAliveManager isKeepAliveActive], @"停止后应该是未激活状态");
}

- (void)testStrategySwitch
{
    // 测试策略切换
    KeepAliveStrategy originalStrategy = self.config.currentStrategy;
    
    [self.keepAliveManager setKeepAliveStrategy:KeepAliveStrategyBackgroundTask];
    XCTAssertEqual(self.config.currentStrategy, KeepAliveStrategyBackgroundTask, @"策略应该已切换");
    
    [self.keepAliveManager setKeepAliveStrategy:KeepAliveStrategyAudioMix];
    XCTAssertEqual(self.config.currentStrategy, KeepAliveStrategyAudioMix, @"策略应该已切换");
    
    // 恢复原始策略
    [self.keepAliveManager setKeepAliveStrategy:originalStrategy];
}

#pragma mark - Configuration Tests

- (void)testConfigurationPersistence
{
    // 保存当前配置
    KeepAliveStrategy originalStrategy = self.config.currentStrategy;
    BOOL originalAutoSwitch = self.config.enableAutoStrategySwitch;
    
    // 修改配置
    self.config.currentStrategy = KeepAliveStrategyLocationUpdate;
    self.config.enableAutoStrategySwitch = !originalAutoSwitch;
    [self.config saveConfig];
    
    // 重新加载配置
    [self.config loadConfig];
    
    // 验证配置已保存
    XCTAssertEqual(self.config.currentStrategy, KeepAliveStrategyLocationUpdate, @"策略配置应该已保存");
    XCTAssertEqual(self.config.enableAutoStrategySwitch, !originalAutoSwitch, @"自动切换配置应该已保存");
    
    // 恢复原始配置
    self.config.currentStrategy = originalStrategy;
    self.config.enableAutoStrategySwitch = originalAutoSwitch;
    [self.config saveConfig];
}

- (void)testConfigurationValidation
{
    // 测试无效时间间隔的验证
    self.config.taskCheckInterval = -1.0; // 无效值
    self.config.idleTimeoutInterval = 1000.0; // 无效值
    self.config.audioPlayInterval = 0.5; // 无效值
    
    // 触发验证
    [self.config loadConfig];
    
    // 验证已修正为有效值
    XCTAssertGreaterThan(self.config.taskCheckInterval, 0.0, @"任务检查间隔应该是有效值");
    XCTAssertLessThan(self.config.idleTimeoutInterval, 400.0, @"空闲超时应该是有效值");
    XCTAssertGreaterThan(self.config.audioPlayInterval, 1.0, @"音频播放间隔应该是有效值");
}

#pragma mark - Strategy Information Tests

- (void)testStrategyDisplayNames
{
    NSString *name = [self.config displayNameForStrategy:KeepAliveStrategyAudioMix];
    XCTAssertNotNil(name, @"策略显示名称不应该为空");
    XCTAssertTrue(name.length > 0, @"策略显示名称应该有内容");
}

- (void)testStrategyDescriptions
{
    NSString *description = [self.config descriptionForStrategy:KeepAliveStrategyAudioMix];
    XCTAssertNotNil(description, @"策略描述不应该为空");
    XCTAssertTrue(description.length > 0, @"策略描述应该有内容");
}

- (void)testStrategyAvailability
{
    // 后台任务策略应该总是可用
    BOOL available = [self.config isStrategyAvailable:KeepAliveStrategyBackgroundTask];
    XCTAssertTrue(available, @"后台任务策略应该总是可用");
    
    // 无策略应该总是可用
    available = [self.config isStrategyAvailable:KeepAliveStrategyNone];
    XCTAssertTrue(available, @"无策略应该总是可用");
}

- (void)testRecommendedStrategy
{
    KeepAliveStrategy recommended = [self.config recommendedStrategy];
    XCTAssertTrue(recommended >= KeepAliveStrategyNone && recommended <= KeepAliveStrategyVoIP, 
                  @"推荐策略应该在有效范围内");
    
    BOOL available = [self.config isStrategyAvailable:recommended];
    XCTAssertTrue(available, @"推荐策略应该是可用的");
}

#pragma mark - Performance Tests

- (void)testKeepAlivePerformance
{
    [self measureBlock:^{
        // 测试启动和停止保活的性能
        for (int i = 0; i < 10; i++) {
            [self.keepAliveManager startKeepAlive];
            [self.keepAliveManager stopKeepAlive];
        }
    }];
}

- (void)testConfigurationPerformance
{
    [self measureBlock:^{
        // 测试配置保存和加载的性能
        for (int i = 0; i < 100; i++) {
            [self.config saveConfig];
            [self.config loadConfig];
        }
    }];
}

#pragma mark - Edge Cases Tests

- (void)testMultipleStartCalls
{
    // 多次调用启动应该是安全的
    [self.keepAliveManager startKeepAlive];
    [self.keepAliveManager startKeepAlive];
    [self.keepAliveManager startKeepAlive];
    
    XCTAssertTrue([self.keepAliveManager isKeepAliveActive], @"多次启动后应该仍然是激活状态");
    
    [self.keepAliveManager stopKeepAlive];
    XCTAssertFalse([self.keepAliveManager isKeepAliveActive], @"停止后应该是未激活状态");
}

- (void)testMultipleStopCalls
{
    [self.keepAliveManager startKeepAlive];
    
    // 多次调用停止应该是安全的
    [self.keepAliveManager stopKeepAlive];
    [self.keepAliveManager stopKeepAlive];
    [self.keepAliveManager stopKeepAlive];
    
    XCTAssertFalse([self.keepAliveManager isKeepAliveActive], @"多次停止后应该仍然是未激活状态");
}

- (void)testRemainingBackgroundTime
{
    NSTimeInterval remainingTime = [self.keepAliveManager remainingBackgroundTime];
    XCTAssertGreaterThanOrEqual(remainingTime, 0.0, @"剩余后台时间应该大于等于0");
}

@end
