//
//  KeepAliveConfig.h
//  Saber
//
//  Created by AI Assistant on 2024/12/19.
//

#import <Foundation/Foundation.h>
#import "KeepAliveManager.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * 后台保活配置管理类
 * 提供保活策略的配置和管理功能
 */
@interface KeepAliveConfig : NSObject

/// 单例实例
+ (instancetype)sharedConfig;

/// 当前保活策略
@property (nonatomic, assign) KeepAliveStrategy currentStrategy;

/// 是否启用自动策略切换
@property (nonatomic, assign) BOOL enableAutoStrategySwitch;

/// 是否启用低功耗模式
@property (nonatomic, assign) BOOL enableLowPowerMode;

/// 任务检查间隔（秒）
@property (nonatomic, assign) NSTimeInterval taskCheckInterval;

/// 空闲超时时间（秒）
@property (nonatomic, assign) NSTimeInterval idleTimeoutInterval;

/// 音频播放间隔（秒）
@property (nonatomic, assign) NSTimeInterval audioPlayInterval;

/// 保存配置到本地
- (void)saveConfig;

/// 从本地加载配置
- (void)loadConfig;

/// 重置为默认配置
- (void)resetToDefault;

/// 获取策略的显示名称
- (NSString *)displayNameForStrategy:(KeepAliveStrategy)strategy;

/// 获取策略的描述信息
- (NSString *)descriptionForStrategy:(KeepAliveStrategy)strategy;

/// 检查策略是否可用
- (BOOL)isStrategyAvailable:(KeepAliveStrategy)strategy;

/// 获取推荐的策略
- (KeepAliveStrategy)recommendedStrategy;

@end

NS_ASSUME_NONNULL_END
