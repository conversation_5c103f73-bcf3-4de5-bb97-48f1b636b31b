//
//  KeepAliveManager.m
//  MaizyClock
//
//  Created by q<PERSON><PERSON> on 2022/2/11.
//

#import "KeepAliveManager.h"

#import "WeakProxy.h"

#import <AVFoundation/AVFoundation.h>
#import <MediaPlayer/MediaPlayer.h>
#import <AudioToolbox/AudioToolbox.h>

#import "TaskSessionManager.h"
#import "PlayerManager.h"

@interface KeepAliveManager ()

@property (nonatomic, strong) AVAudioPlayer *player;

@property (nonatomic, strong) NSTimer *timer;

/// 一个3分钟的定时器，主要是已经没有缓存任务，然后等待转换(m3u8转换为mp4)的过程
/// 实测，1G的视频，差不多需要1分钟的时间
//@property (nonatomic, strong) NSTimer *stopAfterIdleTimer;

/// 关掉后台保活定时器（累计15秒后关闭定时器）
@property (nonatomic, strong) NSTimer *closeCountTimer;
// 累计10次关闭
@property (nonatomic, assign) int closeCount;

@property (nonatomic, assign) UIBackgroundTaskIdentifier taskIdentifier;

@end

@implementation KeepAliveManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static KeepAliveManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [KeepAliveManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self setupObservers];
    }
    
    return self;
}

- (void)setupObservers
{
    //监听进入后台通知
    [NSNotificationCenter.defaultCenter addObserver:self
                                           selector:@selector(applicationDidEnterBackground:)
                                               name:UIApplicationDidEnterBackgroundNotification
                                             object:nil];
    
    //监听进入前台通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationWillEnterForeground:)
                                                 name:UIApplicationWillEnterForegroundNotification
                                               object:nil];
}

- (void)applicationDidEnterBackground:(NSNotification *)notification
{
    //检测是否有正在缓存的任务, 如果有那么进入保活模式
    [self keepAlive];
}

- (void)applicationWillEnterForeground:(NSNotification *)notification
{
    //进入前台
    [self stopKeepAlive];
    
    [self.closeCountTimer invalidate];
    self.closeCountTimer = nil;
    self.closeCount = 0;
}

- (void)keepAlive
{
    __block UIBackgroundTaskIdentifier taskIdentifier = [[UIApplication sharedApplication] beginBackgroundTaskWithExpirationHandler:^{
        [[UIApplication sharedApplication] endBackgroundTask:taskIdentifier];
        taskIdentifier = UIBackgroundTaskInvalid;
    }];
    self.taskIdentifier = taskIdentifier;
        
    //降低定时间隔, 可以增大缓存速度
    self.timer = [NSTimer scheduledTimerWithTimeInterval:1
                                                  target:[WeakProxy proxyWithTarget:self]
                                                selector:@selector(playAudio)
                                                userInfo:nil
                                                 repeats:YES];
    
    [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSDefaultRunLoopMode];
    [self.timer fire];
}

- (void)stopKeepAlive
{
    if(self.taskIdentifier != UIBackgroundTaskInvalid) {
        //清除编译器警告
        [[UIApplication sharedApplication] endBackgroundTask:self.taskIdentifier];
        self.taskIdentifier = UIBackgroundTaskInvalid;
    }
    
    [self.player stop];
    self.player = nil;
    
    [self.timer invalidate];
    self.timer = nil;
    
    [self.closeCountTimer invalidate];
    self.closeCountTimer = nil;
    self.closeCount = 0;
    
#if DEBUG
    NSLog(@"关闭后台保活逻辑........");
#endif
}

- (void)playAudio
{
#if DEBUG
    NSLog(@"正在运行后台保活逻辑........");
#endif
    
    //如果有正在运行中的音频模式，那么直接返回，否则音频模式的远程控制会失效
    if([[PlayerManager shareInstance] isRunningHeadPhone]) return;
    
    BOOL isDownloading;
    BOOL isTransforming;
    [[TaskSessionManager shareInstance] totalRestartOnBackground:&isDownloading
                                                        transforming:&isTransforming];

    if(isDownloading || isTransforming) {
        if(![self.player isPlaying]) {
            //如果不定期的播放,戴上耳机会有电流声,非常影响体验
            [self.player play];
        }
        
        if(self.closeCountTimer) {
            [self.closeCountTimer invalidate];
            self.closeCountTimer = nil;
            
            self.closeCount = 0;
        }
    } else {
        //没有缓存任务，交给系统管理
        //或者缓存结束
        //等待3分钟,检测是否有任务,如果没有任务,那么就关闭后台保活
        //v4.4 更加细化，现在改为累计15秒还没有任务，那么则关闭保活，交给后台不靠谱
        //时间要足够长,否则容易出问题
        if(self.closeCountTimer) return;
        self.closeCountTimer = [NSTimer scheduledTimerWithTimeInterval:1
                                         target:[WeakProxy proxyWithTarget:self]
                                       selector:@selector(_handleCloseCountDown)
                                       userInfo:nil
                                        repeats:YES];
    }

//    NSLog(@"time remain:%.1f",[UIApplication sharedApplication].backgroundTimeRemaining);
}

- (void)_handleCloseCountDown
{
    self.closeCount++;
    if(self.closeCount >= 15) {
        [self readyToStopKeepAlive];
    }
}

- (void)readyToStopKeepAlive
{
    [self.closeCountTimer invalidate];
    self.closeCountTimer = nil;
    
    self.closeCount = 0;
    
    [self stopKeepAlive];
}

- (AVAudioPlayer *)player
{
    if(!_player) {
        NSError *error = nil;
#warning -- 注意这里的参数设置，会影响到远程控制的调用
        [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryPlayback
                                         withOptions:AVAudioSessionCategoryOptionMixWithOthers
                                               error:&error];
        if(error) {
            NSLog(@"%d, %s , %@", __LINE__, __func__, error);
        }
        
        [[AVAudioSession sharedInstance] setActive:YES error:&error];
        if(error) {
            NSLog(@"%d, %s , %@", __LINE__, __func__, error);
        }
        
        NSString *path = [[NSBundle mainBundle] pathForResource:@"no_sound" ofType:@"mp3"];
        NSURL *url = [NSURL fileURLWithPath:path];
        
        _player = [[AVAudioPlayer alloc]initWithContentsOfURL:url error:nil];
        _player.numberOfLoops = -1; //循环播放
        _player.volume = 0.0; //静音播放
        [_player prepareToPlay];
    }
    
    return _player;
}

@end
