//
//  KeepAliveManager.m
//  MaizyClock
//
//  Created by qing<PERSON> on 2022/2/11.
//

#import "KeepAliveManager.h"
#import "WeakProxy.h"
#import "TaskSessionManager.h"
#import "PlayerManager.h"
#import "KeepAliveConfig.h"

#import <AVFoundation/AVFoundation.h>
#import <MediaPlayer/MediaPlayer.h>
#import <AudioToolbox/AudioToolbox.h>

// 常量定义
static const NSTimeInterval kTaskCheckInterval = 5.0;          // 任务检查间隔（从1秒改为5秒）
static const NSTimeInterval kIdleTimeoutInterval = 30.0;      // 空闲超时时间（从15秒改为30秒）
static const NSTimeInterval kAudioPlayInterval = 10.0;        // 音频播放间隔
static const NSTimeInterval kMinBackgroundTime = 60.0;        // 最小后台时间要求

@interface KeepAliveManager ()

// 后台任务相关
@property (nonatomic, assign) UIBackgroundTaskIdentifier backgroundTaskId;
@property (nonatomic, strong) NSMutableArray<NSNumber *> *backgroundTaskIds;

// 音频相关
@property (nonatomic, strong) AVAudioPlayer *audioPlayer;
@property (nonatomic, assign) BOOL isAudioSessionActive;

// 定时器相关
@property (nonatomic, strong) NSTimer *taskCheckTimer;
@property (nonatomic, strong) NSTimer *idleTimer;
@property (nonatomic, strong) NSTimer *audioPlayTimer;

// 状态管理
@property (nonatomic, assign) KeepAliveStrategy currentStrategy;
@property (nonatomic, assign) BOOL isKeepAliveActive;
@property (nonatomic, assign) NSInteger idleCount;
@property (nonatomic, assign) NSTimeInterval lastTaskCheckTime;

// 性能监控
@property (nonatomic, assign) NSTimeInterval keepAliveStartTime;
@property (nonatomic, assign) NSInteger backgroundTaskCount;

@end

@implementation KeepAliveManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static KeepAliveManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [KeepAliveManager new];
    });

    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        [self setupInitialState];
        [self setupObservers];
    }

    return self;
}

- (void)setupInitialState
{
    self.backgroundTaskId = UIBackgroundTaskInvalid;
    self.backgroundTaskIds = [NSMutableArray array];
    self.currentStrategy = KeepAliveStrategyBackgroundTask; // 默认策略
    self.isKeepAliveActive = NO;
    self.idleCount = 0;
    self.backgroundTaskCount = 0;
    self.isAudioSessionActive = NO;

    // 启用电池监控
    [UIDevice currentDevice].batteryMonitoringEnabled = YES;
}

- (void)setupObservers
{
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];

    // 应用生命周期通知
    [center addObserver:self
               selector:@selector(applicationDidEnterBackground:)
                   name:UIApplicationDidEnterBackgroundNotification
                 object:nil];

    [center addObserver:self
               selector:@selector(applicationWillEnterForeground:)
                   name:UIApplicationWillEnterForegroundNotification
                 object:nil];

    // 音频会话中断通知
    [center addObserver:self
               selector:@selector(audioSessionInterruption:)
                   name:AVAudioSessionInterruptionNotification
                 object:nil];

    // 内存警告通知
    [center addObserver:self
               selector:@selector(memoryWarningReceived:)
                   name:UIApplicationDidReceiveMemoryWarningNotification
                 object:nil];

    // 电池状态变化通知
    [center addObserver:self
               selector:@selector(batteryStateChanged:)
                   name:UIDeviceBatteryStateDidChangeNotification
                 object:nil];

    // 电池电量变化通知
    [center addObserver:self
               selector:@selector(batteryLevelChanged:)
                   name:UIDeviceBatteryLevelDidChangeNotification
                 object:nil];
}

#pragma mark - Public Methods

- (void)startKeepAlive
{
    if (self.isKeepAliveActive) {
        NSLog(@"[KeepAlive] 保活已经激活，跳过重复启动");
        return;
    }

    self.isKeepAliveActive = YES;
    self.keepAliveStartTime = [[NSDate date] timeIntervalSince1970];

    NSLog(@"[KeepAlive] 开始后台保活，策略: %ld", (long)self.currentStrategy);

    [self executeKeepAliveStrategy];
}

- (void)stopKeepAlive
{
    if (!self.isKeepAliveActive) {
        return;
    }

    NSLog(@"[KeepAlive] 停止后台保活");

    self.isKeepAliveActive = NO;

    [self cleanupAllResources];
    [self logKeepAliveStatistics];
}

- (void)setKeepAliveStrategy:(KeepAliveStrategy)strategy
{
    if (self.currentStrategy == strategy) {
        return;
    }

    NSLog(@"[KeepAlive] 切换保活策略: %ld -> %ld", (long)self.currentStrategy, (long)strategy);

    BOOL wasActive = self.isKeepAliveActive;
    if (wasActive) {
        [self stopKeepAlive];
    }

    self.currentStrategy = strategy;

    if (wasActive) {
        [self startKeepAlive];
    }
}

- (BOOL)isKeepAliveActive
{
    return _isKeepAliveActive;
}

- (NSTimeInterval)remainingBackgroundTime
{
    return [UIApplication sharedApplication].backgroundTimeRemaining;
}

#pragma mark - Application Lifecycle

- (void)applicationDidEnterBackground:(NSNotification *)notification
{
    NSLog(@"[KeepAlive] 应用进入后台");
    [self startKeepAlive];
}

- (void)applicationWillEnterForeground:(NSNotification *)notification
{
    NSLog(@"[KeepAlive] 应用即将进入前台");
    [self stopKeepAlive];
}

#pragma mark - Strategy Execution

- (void)executeKeepAliveStrategy
{
    switch (self.currentStrategy) {
        case KeepAliveStrategyNone:
            // 不执行任何保活操作
            break;

        case KeepAliveStrategyBackgroundTask:
            [self startBackgroundTaskStrategy];
            break;

        case KeepAliveStrategyAudioMix:
            [self startAudioMixStrategy];
            break;
    }

    // 启动任务检查定时器
    [self startTaskCheckTimer];
}

#pragma mark - Background Task Strategy

- (void)startBackgroundTaskStrategy
{
    [self beginBackgroundTask:@"MainKeepAlive"];
}

- (void)beginBackgroundTask:(NSString *)taskName
{
    if (self.backgroundTaskId != UIBackgroundTaskInvalid) {
        return; // 已经有后台任务在运行
    }

    __weak typeof(self) weakSelf = self;
    self.backgroundTaskId = [[UIApplication sharedApplication] beginBackgroundTaskWithName:taskName expirationHandler:^{
        __strong typeof(weakSelf) strongSelf = weakSelf;
        if (strongSelf) {
            NSLog(@"[KeepAlive] 后台任务即将过期: %@", taskName);
            [strongSelf handleBackgroundTaskExpiration];
        }
    }];

    if (self.backgroundTaskId == UIBackgroundTaskInvalid) {
        NSLog(@"[KeepAlive] 创建后台任务失败");
        return;
    }

    self.backgroundTaskCount++;
    [self.backgroundTaskIds addObject:@(self.backgroundTaskId)];

    NSLog(@"[KeepAlive] 创建后台任务成功: %@ (ID: %lu, 剩余时间: %.1fs)",
          taskName, (unsigned long)self.backgroundTaskId, [self remainingBackgroundTime]);
}

- (void)handleBackgroundTaskExpiration
{
    NSTimeInterval remainingTime = [self remainingBackgroundTime];
    NSLog(@"[KeepAlive] 处理后台任务过期，剩余时间: %.1fs", remainingTime);

    if (remainingTime < kMinBackgroundTime) {
        // 剩余时间不足，尝试切换策略
        [self tryFallbackStrategy];
    } else {
        // 创建新的后台任务
        [self endCurrentBackgroundTask];
        [self beginBackgroundTask:@"RenewedKeepAlive"];
    }
}

- (void)endCurrentBackgroundTask
{
    if (self.backgroundTaskId != UIBackgroundTaskInvalid) {
        [[UIApplication sharedApplication] endBackgroundTask:self.backgroundTaskId];
        [self.backgroundTaskIds removeObject:@(self.backgroundTaskId)];
        self.backgroundTaskId = UIBackgroundTaskInvalid;
        NSLog(@"[KeepAlive] 结束后台任务");
    }
}

- (void)tryFallbackStrategy
{
    NSLog(@"[KeepAlive] 尝试降级策略");

    // 根据当前策略选择降级方案
    switch (self.currentStrategy) {
        case KeepAliveStrategyBackgroundTask:
            if ([self canUseAudioMixStrategy]) {
                [self setKeepAliveStrategy:KeepAliveStrategyAudioMix];
            }
            break;

        case KeepAliveStrategyAudioMix:
            // 音频策略失败，回退到纯后台任务
            [self setKeepAliveStrategy:KeepAliveStrategyBackgroundTask];
            break;

        default:
            // 其他策略失败，停止保活
            [self stopKeepAlive];
            break;
    }
}

#pragma mark - Audio Mix Strategy

- (void)startAudioMixStrategy
{
    [self beginBackgroundTask:@"AudioMixKeepAlive"];
    [self setupAudioSession];
    [self startAudioPlayTimer];
}

- (BOOL)canUseAudioMixStrategy
{
    // 检查是否有其他音频应用在播放
    if ([[PlayerManager shareInstance] isRunningHeadPhone]) {
        NSLog(@"[KeepAlive] 检测到音频模式正在运行，跳过音频保活");
        return NO;
    }

    // 检查音频会话权限
    AVAudioSession *session = [AVAudioSession sharedInstance];
    return session.isOtherAudioPlaying == NO;
}

- (void)setupAudioSession
{
    NSError *error = nil;
    AVAudioSession *session = [AVAudioSession sharedInstance];

    // 使用混合模式，不会中断其他音频应用
    BOOL success = [session setCategory:AVAudioSessionCategoryPlayback
                            withOptions:AVAudioSessionCategoryOptionMixWithOthers | AVAudioSessionCategoryOptionDuckOthers
                                  error:&error];

    if (!success || error) {
        NSLog(@"[KeepAlive] 音频会话设置失败: %@", error.localizedDescription);
        return;
    }

    success = [session setActive:YES error:&error];
    if (!success || error) {
        NSLog(@"[KeepAlive] 激活音频会话失败: %@", error.localizedDescription);
        return;
    }

    self.isAudioSessionActive = YES;
    NSLog(@"[KeepAlive] 音频会话设置成功");
}

- (void)startAudioPlayTimer
{
    if (self.audioPlayTimer) {
        [self.audioPlayTimer invalidate];
    }

    // 降低音频播放频率，减少功耗
    self.audioPlayTimer = [NSTimer scheduledTimerWithTimeInterval:kAudioPlayInterval
                                                           target:[WeakProxy proxyWithTarget:self]
                                                         selector:@selector(playAudioIfNeeded)
                                                         userInfo:nil
                                                          repeats:YES];
}

- (void)playAudioIfNeeded
{
    // 检查是否有其他音频在播放
    if ([[PlayerManager shareInstance] isRunningHeadPhone]) {
        NSLog(@"[KeepAlive] 检测到音频模式，暂停保活音频");
        return;
    }

    // 检查是否还有下载任务
    BOOL hasActiveTasks = [self checkForActiveTasks];
    if (!hasActiveTasks) {
        [self handleIdleState];
        return;
    }

    // 播放静音音频
    if (![self.audioPlayer isPlaying]) {
        [self.audioPlayer play];
        NSLog(@"[KeepAlive] 播放保活音频");
    }

    // 重置空闲计数
    self.idleCount = 0;
    if (self.idleTimer) {
        [self.idleTimer invalidate];
        self.idleTimer = nil;
    }
}



#pragma mark - Task Monitoring

- (void)startTaskCheckTimer
{
    if (self.taskCheckTimer) {
        [self.taskCheckTimer invalidate];
    }

    self.taskCheckTimer = [NSTimer scheduledTimerWithTimeInterval:kTaskCheckInterval
                                                           target:[WeakProxy proxyWithTarget:self]
                                                         selector:@selector(checkTaskStatus)
                                                         userInfo:nil
                                                          repeats:YES];

    // 立即执行一次检查
    [self checkTaskStatus];
}

- (void)checkTaskStatus
{
    self.lastTaskCheckTime = [[NSDate date] timeIntervalSince1970];

    BOOL hasActiveTasks = [self checkForActiveTasks];

    NSLog(@"[KeepAlive] 任务状态检查: %@, 剩余后台时间: %.1fs",
          hasActiveTasks ? @"有活跃任务" : @"无活跃任务", [self remainingBackgroundTime]);

    if (!hasActiveTasks) {
        [self handleIdleState];
    } else {
        // 重置空闲状态
        self.idleCount = 0;
        if (self.idleTimer) {
            [self.idleTimer invalidate];
            self.idleTimer = nil;
        }
    }

    // 定期评估策略是否需要调整
    [self evaluateAndAdjustStrategy];
}

- (BOOL)checkForActiveTasks
{
    BOOL isDownloading = NO;
    BOOL isTransforming = NO;

    [[TaskSessionManager shareInstance] totalRestartOnBackground:&isDownloading
                                                    transforming:&isTransforming];

    return isDownloading || isTransforming;
}

- (void)handleIdleState
{
    if (self.idleTimer) {
        return; // 已经在处理空闲状态
    }

    NSLog(@"[KeepAlive] 进入空闲状态，启动倒计时");

    self.idleTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                      target:[WeakProxy proxyWithTarget:self]
                                                    selector:@selector(handleIdleTimeout)
                                                    userInfo:nil
                                                     repeats:YES];
}

- (void)handleIdleTimeout
{
    self.idleCount++;

    if (self.idleCount >= kIdleTimeoutInterval) {
        NSLog(@"[KeepAlive] 空闲超时，停止保活");
        [self stopKeepAlive];
    }
}

#pragma mark - Resource Management

- (void)cleanupAllResources
{
    // 清理定时器
    [self.taskCheckTimer invalidate];
    self.taskCheckTimer = nil;

    [self.idleTimer invalidate];
    self.idleTimer = nil;

    [self.audioPlayTimer invalidate];
    self.audioPlayTimer = nil;

    // 清理音频资源
    [self.audioPlayer stop];
    self.audioPlayer = nil;

    if (self.isAudioSessionActive) {
        [[AVAudioSession sharedInstance] setActive:NO error:nil];
        self.isAudioSessionActive = NO;
    }

    // 清理后台任务
    [self endCurrentBackgroundTask];
    [self endAllBackgroundTasks];

    // 重置状态
    self.idleCount = 0;
}

- (void)endAllBackgroundTasks
{
    for (NSNumber *taskIdNumber in self.backgroundTaskIds) {
        UIBackgroundTaskIdentifier taskId = [taskIdNumber unsignedIntegerValue];
        if (taskId != UIBackgroundTaskInvalid) {
            [[UIApplication sharedApplication] endBackgroundTask:taskId];
        }
    }
    [self.backgroundTaskIds removeAllObjects];
}

#pragma mark - Audio Player

- (AVAudioPlayer *)audioPlayer
{
    if (!_audioPlayer) {
        NSString *path = [[NSBundle mainBundle] pathForResource:@"no_sound" ofType:@"mp3"];
        if (!path) {
            NSLog(@"[KeepAlive] 静音音频文件不存在");
            return nil;
        }

        NSURL *url = [NSURL fileURLWithPath:path];
        NSError *error = nil;

        _audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:url error:&error];
        if (error) {
            NSLog(@"[KeepAlive] 创建音频播放器失败: %@", error.localizedDescription);
            return nil;
        }

        _audioPlayer.numberOfLoops = -1; // 循环播放
        _audioPlayer.volume = 0.01; // 极低音量，避免完全静音被系统优化
        [_audioPlayer prepareToPlay];

        NSLog(@"[KeepAlive] 音频播放器创建成功");
    }

    return _audioPlayer;
}

#pragma mark - Notification Handlers

- (void)audioSessionInterruption:(NSNotification *)notification
{
    NSNumber *interruptionType = notification.userInfo[AVAudioSessionInterruptionTypeKey];

    if (interruptionType.integerValue == AVAudioSessionInterruptionTypeBegan) {
        NSLog(@"[KeepAlive] 音频会话被中断");
        [self.audioPlayer pause];
    } else if (interruptionType.integerValue == AVAudioSessionInterruptionTypeEnded) {
        NSLog(@"[KeepAlive] 音频会话中断结束");
        if (self.currentStrategy == KeepAliveStrategyAudioMix && [self checkForActiveTasks]) {
            [self.audioPlayer play];
        }
    }
}

- (void)memoryWarningReceived:(NSNotification *)notification
{
    NSLog(@"[KeepAlive] 收到内存警告，清理非必要资源");

    // 在内存压力下，切换到更轻量的策略
    if (self.currentStrategy == KeepAliveStrategyAudioMix) {
        [self setKeepAliveStrategy:KeepAliveStrategyBackgroundTask];
    }
}

- (void)batteryStateChanged:(NSNotification *)notification
{
    UIDeviceBatteryState batteryState = [UIDevice currentDevice].batteryState;
    NSLog(@"[KeepAlive] 电池状态变化: %ld", (long)batteryState);

    [self evaluateAndAdjustStrategy];
}

- (void)batteryLevelChanged:(NSNotification *)notification
{
    float batteryLevel = [UIDevice currentDevice].batteryLevel;
    NSLog(@"[KeepAlive] 电池电量变化: %.1f%%", batteryLevel * 100);

    [self evaluateAndAdjustStrategy];
}

#pragma mark - Smart Strategy Management

- (void)evaluateAndAdjustStrategy
{
    // 只有启用自动策略切换时才进行调整
    KeepAliveConfig *config = [KeepAliveConfig sharedConfig];
    if (!config.enableAutoStrategySwitch) {
        return;
    }

    KeepAliveStrategy recommendedStrategy = [self getOptimalStrategy];

    if (recommendedStrategy != self.currentStrategy) {
        NSLog(@"[KeepAlive] 智能切换策略: %@ -> %@",
              [config displayNameForStrategy:self.currentStrategy],
              [config displayNameForStrategy:recommendedStrategy]);

        [self setKeepAliveStrategy:recommendedStrategy];
    }
}

- (KeepAliveStrategy)getOptimalStrategy
{
    KeepAliveConfig *config = [KeepAliveConfig sharedConfig];
    UIDevice *device = [UIDevice currentDevice];

    // 获取设备状态
    float batteryLevel = device.batteryLevel;
    UIDeviceBatteryState batteryState = device.batteryState;
    BOOL isLowPowerModeEnabled = [[NSProcessInfo processInfo] isLowPowerModeEnabled];

    // 低电量模式或电量过低时，优先使用后台任务模式
    if (isLowPowerModeEnabled || batteryLevel < 0.2) {
        NSLog(@"[KeepAlive] 低电量模式，选择后台任务策略");
        return KeepAliveStrategyBackgroundTask;
    }

    // 充电状态下，可以使用音频混合模式
    if (batteryState == UIDeviceBatteryStateCharging || batteryState == UIDeviceBatteryStateFull) {
        if ([self canUseAudioMixStrategy]) {
            NSLog(@"[KeepAlive] 充电状态，选择音频混合策略");
            return KeepAliveStrategyAudioMix;
        }
    }

    // 电量充足且未充电时，根据配置选择
    if (batteryLevel > 0.5) {
        if (config.enableLowPowerMode) {
            return KeepAliveStrategyBackgroundTask;
        } else if ([self canUseAudioMixStrategy]) {
            return KeepAliveStrategyAudioMix;
        }
    }

    // 默认使用后台任务模式
    return KeepAliveStrategyBackgroundTask;
}

#pragma mark - Statistics and Logging

- (void)logKeepAliveStatistics
{
    NSTimeInterval duration = [[NSDate date] timeIntervalSince1970] - self.keepAliveStartTime;
    NSLog(@"[KeepAlive] 保活统计 - 持续时间: %.1fs, 后台任务数: %ld, 策略: %ld",
          duration, (long)self.backgroundTaskCount, (long)self.currentStrategy);
}

#pragma mark - Dealloc

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self cleanupAllResources];
}

@end
