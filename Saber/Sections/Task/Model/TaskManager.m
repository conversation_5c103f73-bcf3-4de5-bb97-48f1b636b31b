//
//  TaskManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/3.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "TaskManager.h"
#import "NSString+MKNetworkKitAdditions.h"
#import "PPNotifications.h"
#import "TaskModel.h"
#import "MaizyHeader.h"
#import "DatabaseUtil.h"

#import "Saber-Swift.h"

#import "BaseNavigationController.h"
#import "UIView+Helper.h"
#import "NSObject+Helper.h"

#import "FFmpegUtil.h"
#import "FFmpegTask.h"
#import "FFmpegManager.h"
#import "FileDetector.h"
#import "ReactiveCocoa.h"

#import "AFNetworking.h"
#import "AFNetworkReachabilityManager.h"
#import "PreferenceManager.h"

#import "PreferenceModel.h"
#import "VIPController.h"
#import "BaseNavigationController.h"

#import "NSURL+Extension.h"

#import "SJM3U8FileParser.h"
#import "TaskSessionManager.h"
#import "NSString+Helper.h"

#import "AppDelegate.h"
#import "BrowserViewController.h"
#import "TaskCacheManager.h"

#import "NSFileManager+Helper.h"
#import "DateHelper.h"
#import "SnifferHelper.h"

#import "KeepAliveManager.h"
#import "KeepAliveConfig.h"

#import "DatabaseUnit+Download.h"
#import "BrowserUtils.h"

#import "PaymentManager.h"

@interface TaskManager ()
/// 最主要的sessionManager，常规缓存的sessionManager(包括mp4和文件缓存)
@property (nonatomic, strong) TRSessionManager* sessionManager;

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, strong) dispatch_queue_t save2FileQueue;

@end

@implementation TaskManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static TaskManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [TaskManager new];
    });

    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.downloadManager", DISPATCH_QUEUE_CONCURRENT);
        self.save2FileQueue = dispatch_queue_create("com.downloadManager.save2file.queue", DISPATCH_QUEUE_SERIAL);

        //保活初始化
        [self setupKeepAliveManager];
        //缓存初始化
        [[TaskCacheManager shareInstance] reloadData];
    }

    return self;
}

- (void)startMonitor
{
    //如果已经初始化
    if(self.sessionManager) {
        return;
    }

    ///必须是异步的，要不然视频缓存太多的情况，会导致开屏非常慢
    dispatch_async(self.queue, ^{
        @synchronized (self) {
            [self setupSessionManager];
        }
    });
}

- (void)setupSessionManager
{
    //注册SessionManager
    [[TaskSessionManager shareInstance] reloadData];
    //需要等TaskSessionManager初始化完毕才能设置
    self.sessionManager = [[TaskSessionManager shareInstance] mainSessionManager];

    //缓存成功监听
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(_downloadSuccessNotification:) name:kDownloadSuccessNotification object:nil];

    //添加普通缓存任务监听
    NSArray* allTasks = [self allTasks];
    for(TRDownloadTask* task in allTasks) {
        [self addObserverToTask:task];
    }

    //m3u8
    NSMutableArray* hlsTasks = [NSMutableArray array];
    [hlsTasks addObjectsFromArray:[[TaskSessionManager shareInstance] allHLSSessionManagers]];
    for(TRSessionManager* item in hlsTasks) {
        [self addObserverToHLSTask:item];
    }
}

- (void)addObserverToTask:(TRDownloadTask *)task
{
    /// 已经缓存成功的视频，如果重新打开APP，那么也会发一次缓存成功的通知(坑)
    /// 所以已经缓存成功的则不再加入监听
    if(task.status == TRStatusSucceeded) return;

    [[[task progressOnMainQueue:YES handler:^(TRDownloadTask * task) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadProgressNotification object:task];
    }] successOnMainQueue:YES handler:^(TRDownloadTask * task) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadSuccessNotification object:task];
    }] failureOnMainQueue:YES handler:^(TRDownloadTask * task) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadFailureNotification object:task];
    }];
}

- (void)addObserverToHLSTask:(TRSessionManager *)sessionManager
{
    /// 已经缓存成功的视频，如果重新打开APP，那么也会发一次缓存成功的通知(坑)
    /// 所以已经缓存成功的则不再加入监听，HLS初始化的时候，sessionManager的状态是waiting，所以不能通过这个来判断
    BOOL isSucc = YES;
    for(TRDownloadTask* task in sessionManager.tasks) {
        if(task.status != TRStatusSucceeded) {
            isSucc = NO;
            break;
        }
    }
    if(isSucc) return;

    NSArray* hlsTasks = sessionManager.tasks;
    [[[sessionManager progressOnMainQueue:YES handler:^(TRSessionManager * manager) {
#if DEBUG
        double totalProgress = 0;
        for(TRDownloadTask* task in hlsTasks) {
            double progress = 0;
            if(task.progress.totalUnitCount != 0) {
                progress = task.progress.completedUnitCount*1.0 / task.progress.totalUnitCount;
            }
            totalProgress += progress;
        }

        double realProgress = totalProgress / hlsTasks.count;
        NSLog(@"m3u8当前缓存进度: %lf", realProgress);
#endif
        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadProgressNotification object:sessionManager];
    }] successOnMainQueue:YES handler:^(TRSessionManager * manager) {
        NSLog(@"m3u8缓存成功...");
        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadSuccessNotification object:sessionManager];
    }] failureOnMainQueue:YES handler:^(TRSessionManager * manager) {
        //缓存暂停也会触发
        if(sessionManager.status == TRStatusSuspended
           || sessionManager.status == TRStatusWillSuspend) {
            NSLog(@"m3u8缓存已暂停...");
        } else {
            NSLog(@"m3u8缓存失败...");
        }
        //这里即使失败也不能清除缓存,因为再次点击下载有可能就能重新触发缓存
        //从而缓存成功
//        [sessionManager.cache clearDiskCache];

        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadFailureNotification object:sessionManager];
    }];
}

#pragma mark -- 缓存成功监听
- (void)_downloadSuccessNotification:(NSNotification*)notification
{
    id item = notification.object;
    if([item isMemberOfClass:TRSessionManager.class]) {
        //m3u8缓存
        TRSessionManager* task = notification.object;
        TRStatus status = task.status;
        if(status == TRStatusSucceeded) {
            //缓存成功,进入转换中,更新状态
            [[DatabaseUtil shareInstance] asyncOnQueue:^{
                TaskModel* item = [[TaskCacheManager shareInstance] queryDownloadWithIdentifier:task.identifier];
                //启动时还没有拉到缓存, 导致获取空数据
                if(!item) return;

                M3U8TransformStatus status = [notification.userInfo[@"transformStatus"] intValue];
                if(status == M3U8TransformStatusSucceed) {
                    //转换成功,更新状态
                    //去掉缓存之后，丢失了文件名，这里修复
                    item.fileName = [NSString stringWithFormat:@"%@.mp4",item.title];

                    NSString* filePath = [[self diskSavePathWithHlsIdentifier:task.identifier]
                                          stringByAppendingPathComponent:item.fileName];
                    item.totalUnitCount = [self fileSizeForPath:filePath];
                    item.transformStatus = M3U8TransformStatusSucceed;

                    DatabaseUnit* unit = [DatabaseUnit updateTransformStatusWithItem:item];
                    [unit setCompleteBlock:^(id result, BOOL success) {
                        //写表后发通知,让列表第一时间刷新
                        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];
                    }];
                    DB_EXEC(unit);

                    NSLog(@"m3u8转mp4 保存地址: %@", filePath);
                    /// 导出逻辑
                } else {
                    //初始化
                    item.totalUnitCount = task.progress.totalUnitCount;
                    item.status = TRStatusSucceeded;

                    DatabaseUnit* unit = [DatabaseUnit updateDownloadFinishWithItem:item];
                    DB_EXEC(unit);

                    //将m3u8转换成mp4
                    dispatch_async(self.queue, ^{
                        [self transformVideoWithSessionManager:task];
                    });
                }
            }];
        }
    } else {
        //mp4
        TRDownloadTask* task = notification.object;
        if(task.status == TRStatusSucceeded) {
            //缓存成功
            [[DatabaseUtil shareInstance] asyncOnQueue:^{
                TaskModel* item = [[TaskCacheManager shareInstance] queryDownloadWithUrl:task.url.absoluteString];
                //启动时还没有拉到缓存, 导致获取空数据
                if(!item) return;

                NSInteger totalUnitCount = task.progress.totalUnitCount;

                NSString* identifier;
                if(task.url) {
                    identifier = [task.url.absoluteString md5];
                } else if(item.url) {
                    identifier = [item.url md5];
                }

                NSString* filePath = [[self diskSavePathWithMainTask:task] stringByAppendingPathComponent:task.fileName];
                if(totalUnitCount <= 0) {
                    //大小获取不正确
                    totalUnitCount = [self fileSizeForPath:filePath];
                }

                item.fileName = task.fileName;
                item.totalUnitCount = totalUnitCount;
                item.status = TRStatusSucceeded;

                DatabaseUnit* unit = [DatabaseUnit updateDownloadFinishWithItem:item];
                DB_EXEC(unit);

                //写表后发通知,让列表第一时间刷新
                [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];

                NSLog(@"mp4 保存地址: %@", filePath);
            }];
            //保留task,可以用于删除本地文件的作用
        }
    }
}

//- (void)_handleSaveToFilesOrAlbumWithFilePath:(NSString *)filePath fileName:(NSString *)fileName
//{
//    /// 导出到相册或者文件
//    if(filePath.length <= 0) return;
//
//    ExportOption option = [[PreferenceManager shareInstance].items.exportOption intValue];
//    if(option == ExportOptionDefault) return;
//
//    dispatch_async(self.save2FileQueue, ^{
//        if(option == ExportOptionAlbum) {
//            //导出到相册
//            if(UIVideoAtPathIsCompatibleWithSavedPhotosAlbum(filePath)) {
//                UISaveVideoAtPathToSavedPhotosAlbum(filePath, self, @selector(video:didFinishSavingWithError:contextInfo:), nil);
//            }
//        } else {
//            //导出到文件
//            NSFileManager* fileManager = [NSFileManager defaultManager];
//            NSURL* url = [NSFileManager readBookMark];
//
//            //特别注意这里，只能获取已存在目录的权限, 否则权限获取失败
//            BOOL didStartAccessing = [url startAccessingSecurityScopedResource];
//
//            NSURL* fileUrl = [url URLByAppendingPathComponent:fileName];
//            @try {
//                NSError* error;
//                [fileManager copyItemAtURL:[NSURL fileURLWithPath:filePath] toURL:fileUrl error:&error];
//
//                if (error) {
//                    NSLog(@"保存视频失败: %@", error.localizedDescription);
//                } else {
//                    NSLog(@"保存视频成功");
//                }
//            } @catch (NSException *exception) {
//            } @finally {
//                if(didStartAccessing) {
//                    [url stopAccessingSecurityScopedResource];
//                }
//            }
//        }
//    });
//}

//保存视频完成之后的回调
//- (void)video:(NSString *)videoPath didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo {
//    if (error) {
//        NSLog(@"保存视频失败%@", error.localizedDescription);
//    } else {
//        NSLog(@"保存视频成功");
//    }
//}

// 缓存完成后存储的目录
// 缓存完成后存储的目录(main session的存储目录)
- (NSString *)diskSavePathWithMainTask:(TRDownloadTask *)task
{
    return [self _diskSavePathWithIdentifier:kDownloadIdentifier];
}

// 缓存完成后存储的目录(main session的存储目录)
- (NSString *)diskSavePathWithMainModel:(TaskModel *)item
{
    return [self _diskSavePathWithIdentifier:kDownloadIdentifier];
}

// 根据hls identifier获取缓存目录
- (NSString *)diskSavePathWithHlsIdentifier:(NSString *)identifier
{
    return [self _diskSavePathWithIdentifier:identifier];
}

- (NSString*)_diskSavePathWithIdentifier:(NSString *)identifier
{
    //SystemUTIsHLS
    NSString* cacheName = [NSString stringWithFormat:@"com.Daniels.Tiercel.Cache.%@", identifier];
    NSString* diskCachePath = [TRCache customDiskCachePathClosure:cacheName];
    NSString* path = [diskCachePath stringByAppendingPathComponent:@"Downloads"];
    NSString* downloadPath = [path stringByAppendingPathComponent:@"File"];

    if(![[NSFileManager defaultManager] fileExistsAtPath:downloadPath]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:downloadPath
                                  withIntermediateDirectories:YES
                                                   attributes:nil
                                                        error:nil];
    }

//    NSLog(@"测试: path = %@", downloadPath);

    return downloadPath;
}

- (void)suspendTask:(TRDownloadTask*)task
{
    if(!task) return;

    [self.sessionManager suspendWithUrl:task.url];
}

- (void)startTask:(TRDownloadTask*)task
{
    if(!task) return;

    [self.sessionManager startWithUrl:task.url];
}

// 开启全部任务
- (void)startAllTask
{
    //添加普通缓存任务
    NSArray* allTasks = [self allTasks];
    for(TRDownloadTask* task in allTasks) {
        [self startTask:task];
    }

    //m3u8任务
    NSMutableArray* hlsTasks = [NSMutableArray array];
    [hlsTasks addObjectsFromArray:[[TaskSessionManager shareInstance] allHLSSessionManagers]];
    for(TRSessionManager* item in hlsTasks) {
        [item totalStart];
    }
}

// 暂停全部任务
- (void)stopAllTask
{
    //添加普通缓存任务
    NSArray* allTasks = [self allTasks];
    for(TRDownloadTask* task in allTasks) {
        [self suspendTask:task];
    }

    //m3u8任务
    NSMutableArray* hlsTasks = [NSMutableArray array];
    [hlsTasks addObjectsFromArray:[[TaskSessionManager shareInstance] allHLSSessionManagers]];
    for(TRSessionManager* item in hlsTasks) {
        [item totalSuspend];
    }
}

#pragma mark -- 视频缓存
- (void)downloadWithUrl:(NSString*)url
            originalUrl:(NSString*)originalUrl
                  title:(NSString*)title
{
    NSURL* URL = [NSURL URLWithString:url];
    if(!URL) return;

    BOOL allowsCellularAccess = [PreferenceManager shareInstance].items.allowsCellularAccess;
    if(!allowsCellularAccess) {
        //移动网络检查
        AFNetworkReachabilityStatus status = [[AFNetworkReachabilityManager sharedManager] networkReachabilityStatus];
        if(status == AFNetworkReachabilityStatusReachableViaWWAN) {
            UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"task.alert.allowsCellular.title", nil) message:nil preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.iknown", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {

            }];
            [alertController addAction:action];

            UIWindow* window = [NSObject normalWindow];
            UIViewController* vc = window.rootViewController;
            [vc presentViewController:alertController animated:YES completion:nil];
        }

        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];

        return;
    }

    BOOL isVip = [[PaymentManager shareInstance] isVip];
    if(!isVip) {
        int downloadCount = [[PreferenceManager shareInstance].items.downloadCount intValue];
        NSDate* date = [PreferenceManager shareInstance].items.today;

        if([DateHelper isToday:date]) {
            if(downloadCount >= 1) {
                //今天缓存次数已经超过1次
                UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"task.alert.vip.title", nil) message:NSLocalizedString(@"task.alert.vip.text", nil) preferredStyle:UIAlertControllerStyleAlert];
                UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    [alertController dismissViewControllerAnimated:YES completion:nil];
                }];
                [alertController addAction:action];

                action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.iknown", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                    [VIPController jumpToVip:nil];
                }];
                [alertController addAction:action];

                UIWindow* window = YBIBNormalWindow();
                UIViewController* root = window.rootViewController;
                [root presentViewController:alertController animated:YES completion:nil];

                [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];

                return;
            } else {
                downloadCount++;
                [PreferenceManager shareInstance].items.downloadCount = @(downloadCount);
                [[PreferenceManager shareInstance] encode];
            }
        } else {
            //初始化
            [PreferenceManager shareInstance].items.today = [NSDate date];
            [PreferenceManager shareInstance].items.downloadCount = @(1);

            [[PreferenceManager shareInstance] encode];
        }
    }

    dispatch_async(self.queue, ^{
        ///必须是异步的，要不然视频缓存太多的情况，会导致开屏非常慢
        @synchronized (self) {
            //主要是冷启动的时候，没有初始化
            if(!self.sessionManager) {
                [self setupSessionManager];
            }
        }

        //重复缓存检测
        TaskModel* item = [[TaskCacheManager shareInstance] queryDownloadWithUrl:url];
        if(item) {
            [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];

            [self showDownloadAlertWithModel:item];
            return;
        }

        SystemUTIs UTIS = SystemUTIsUnknown;
        NSString* fileExtension = nil;
        NSURL* URL = [NSURL URLWithString:url];

        //这里可能非常耗时,注意
        FileDetector* detector = [FileDetector new];
        //必须在异步线程调用
        [detector detectorWithUrl:URL type:&UTIS fileExtension:&fileExtension];

        //去掉title中可能出现的".",从而影响到后缀名的判断
        NSString* itemTitle = [title stringByReplacingOccurrencesOfString:@"." withString:@""];
        itemTitle = [itemTitle stringByReplacingOccurrencesOfString:@" " withString:@""];
        itemTitle = [itemTitle stringByReplacingOccurrencesOfString:@"/" withString:@""];
        itemTitle = [itemTitle stringByReplacingOccurrencesOfString:@"-" withString:@""];

        //如果不指定fileName,那么默认是url的md5
        NSString* fileName = nil;
        if(itemTitle.length > 0) {
            fileName = itemTitle;
        } else {
            fileName = [url md5];
        }

        //重名检测
        TaskModel* renameItem = [[TaskCacheManager shareInstance] queryDownloadWithTitle:fileName];
        if(renameItem) {
            //重名了,加个随机数作为后缀
            NSString* uuid = [[NSUUID UUID] UUIDString];
            uuid = [uuid substringToIndex:4];
            fileName = [NSString stringWithFormat:@"%@_%@", fileName, uuid];
        }

        TaskModel* model = [TaskModel new];
        model.title = fileName;
        model.url = url;
        model.uuid = [[NSUUID UUID] UUIDString];
        model.parentId = [TaskModel rootId];
        model.originalUrl = originalUrl;
        model.UTIS = UTIS;
        //m3u8需要用到这个状态,其它缓存还是采用Tiercel的状态
        model.status = TRStatusRunning;

        //先写表占位,防止一瞬间缓存完成导致无数据
        DatabaseUnit* unit = [DatabaseUnit addDownloadWithItem:model];
        [unit setCompleteBlock:^(id result, BOOL success) {
            if(success) {
                if([BrowserUtils isiPad]) {
                    TaskViewController* controller = ((AppDelegate*)[UIApplication sharedApplication].delegate).taskViewController;
                    [controller reloadAllDownloadTask];
                } else {
                    //写表后发通知,让列表第一时间刷新
                    [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];
                }
            }
        }];
        DB_EXEC(unit);

        if(UTIS == SystemUTIsHLS) {
            [self downloadHttpLiveStreamVideo:model];
        } else {
//            //采用本地缓存
//            //抖音视频有header反而会缓存失败
//            //这里要慎用,设置了http://www.feijisu4.com/acg/28648/4.html反而缓存不了视频
//            //有referer校验反而缓存不了,坑爹
            [[SnifferHelper shareInstance] snifferWithOriginUrl:model.originalUrl
                                                       videoUrl:model.url
                                                     completion:^(NSDictionary *header) {
//                NSMutableDictionary* requestHeader = [NSMutableDictionary dictionary];
//                [requestHeader addEntriesFromDictionary:header];
//
//                //针对听小说这个网站(http://m.ysxs8.vip),特别处理
//                if([model.url rangeOfString:@".m4a"].location != NSNotFound) {
//                    requestHeader[@"accept-encoding"] = @"identity";
//                }
                TRDownloadTask* task = [self.sessionManager downloadWithUrl:url
                                                                    headers:header
                                                                   fileName:fileName];
                //添加监听
                [self addObserverToTask:task];
            }];
        }
    });
}

#pragma mark -- 重复检查
- (void)showDownloadAlertWithModel:(TaskModel *)model
{
    UIAlertController* alertController = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"", nil) message:NSLocalizedString(@"task.taskcheck.text", nil) preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction* action = [UIAlertAction actionWithTitle:NSLocalizedString(@"alert.cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        [alertController dismissViewControllerAnimated:YES completion:nil];

        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];
    }];
    [alertController addAction:action];

    action = [UIAlertAction actionWithTitle:NSLocalizedString(@"common.delete", nil) style:UIAlertActionStyleDestructive handler:^(UIAlertAction * _Nonnull action) {
        [self _deleteDownloadModel:model];
    }];
    [alertController addAction:action];

    UIWindow* window = [NSObject normalWindow];
    UITabBarController* tabbar = (UITabBarController*)window.rootViewController;
    dispatch_async(dispatch_get_main_queue(), ^{
        [tabbar presentViewController:alertController animated:YES completion:nil];
    });
}

- (void)_deleteDownloadModel:(TaskModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit removeDownloadItemsWithIds:@[item.uuid]];
    DB_EXEC(unit);

    if(item.UTIS == SystemUTIsHLS) {
        //新版m3u8
        //在m3u8缓存中移除缓存文件
        [[TaskSessionManager shareInstance] removeSessionManager:item.url];

        //如果是转换中,那么需要删除任务
        FFmpegTask* task = [[FFmpegManager shareInstance] taskForUrl:item.url];
        [[FFmpegManager shareInstance] removeTask:task];
    } else {
        //Tiercel
        TRDownloadTask* task = [[TaskManager shareInstance] fetchTask:item.url];
        [[TaskManager shareInstance] removeTask:task completely:YES];
    }

    [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadReloadNotification object:nil];
}

#pragma mark -- 缓存m3u8
- (void)downloadHttpLiveStreamVideo:(TaskModel *)model
{
    @weakify(self)
    [[SnifferHelper shareInstance] snifferWithOriginUrl:model.originalUrl
                                               videoUrl:model.url
                                             completion:^(NSDictionary *header) {
        @strongify(self)
        //snifferWithOriginUrl的回调变成主线程了？
        @weakify(self)
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_HIGH, 0), ^{
            @strongify(self)
            NSMutableDictionary* requestHeader = [NSMutableDictionary dictionary];
            [requestHeader addEntriesFromDictionary:header];

            //只要点击了缓存,那么就需要创建sessionManager
            TRSessionManager* sessionManager = [[TaskSessionManager shareInstance] appendSessionManager:model.url];
            [sessionManager.cache clearDiskCache];

            @weakify(self)
            [SJM3U8FileParser parseWithUrl:model.url
                    headerFields:requestHeader
                        tryCount:0
                 completionBlock:^(SJM3U8ResultModel *parserResult) {
                @strongify(self)
                [self _handleM3U8ResultWithModel:model
                                          result:parserResult
                                          header:header
                                  sessionManager:sessionManager];
            }];
        });
    }];
}

- (void)_handleM3U8ResultWithModel:(TaskModel *)model
                            result:(SJM3U8ResultModel *)parserResult
                            header:(NSDictionary *)header
                    sessionManager:(TRSessionManager *)sessionManager
{
    if([parserResult isSucc]) {
//        NSLog(@"文件缓存个数是: %d", (int)parser.nodes.count);

        NSMutableArray* headersArray = [NSMutableArray array];

        NSString* sessionId = [[NSUUID UUID] UUIDString];
        NSString* acceptEncoding = @"identity";

        NSArray* nodes = [parserResult nodes];
        for(int i=0;i<nodes.count;i++) {
            //不要犯低级错误
            NSMutableDictionary* _requestHeader = [NSMutableDictionary dictionary];
            [_requestHeader addEntriesFromDictionary:header];
            _requestHeader[@"X-Playback-Session-Id"] = sessionId;
            _requestHeader[@"Accept-Encoding"] = acceptEncoding;

            SJM3U8FileItem* item = nodes[i];
            if(item.nodeType == M3U8NodeTypeKey && ![parserResult needRequestHeaderForKeyURL]) {
                //key节点不需要加请求头
                [headersArray addObject:@{}];
            } else {
                NSURL* URL = [NSURL URLWithString:item.url];
                _requestHeader[@"Host"] = URL.host;
                [headersArray addObject:_requestHeader];
            }
        }

        //检测是否需要带请求头
        [self mutiDownloadWithParser:parserResult
                           originUrl:model.originalUrl
                        headersArray:headersArray
                      sessionManager:sessionManager];
    } else {
        //缓存出错
        NSLog(@"解析文件出错...");
        model.transformStatus = M3U8TransformStatusFailed;

        DatabaseUnit* unit = [DatabaseUnit updateTransformStatusWithItem:model];
        DB_EXEC(unit);

        /// 重置状态
        [sessionManager failedToDownload];

        [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadFailureNotification object:sessionManager];

        return;
    }
}

- (void)mutiDownloadWithParser:(SJM3U8ResultModel *)parserResult
                     originUrl:(NSString*)originUrl
                  headersArray:(NSArray*)headersArray
                sessionManager:(TRSessionManager *)sessionManager

{
    NSMutableArray* urls = [NSMutableArray array];
    NSMutableArray* fileNames = [NSMutableArray array];
    NSArray* nodes = [parserResult nodes];
    for(int i=0;i<nodes.count;i++) {
        SJM3U8FileItem* item = nodes[i];
        //url中含有中文
        NSURL* URL = [NSURL URLWithString:item.url];
        if(!URL) {
            // 解码已编码的部分
            item.url = [item.url stringByRemovingPercentEncoding];
            // url编码
            item.url = [item.url stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        }

        [urls addObject:item.url];
        [fileNames addObject:item.fileName];
    }

    //检测是否需要带请求头
    NSString* url = ((SJM3U8FileItem*)nodes.lastObject).url;
    NSDictionary* header = @{@"Range":@"bytes=0-1"};

    @weakify(self)
    [NSObject requestForHeader:header
                           url:url
                    completion:^(BOOL succ, NSDictionary *header, id data) {
        @strongify(self)
        if(succ) {
            ///一定要保留返回值[TRDownloadTask], 否则sessionManager.tasks获取不正确
            [sessionManager multiDownloadWithUrls:urls headersArray:nil fileNames:fileNames];
            [self addObserverToHLSTask:sessionManager];
            NSLog(@"ts视频缓存不需要请求头..............");
        } else {
            ///一定要保留返回值[TRDownloadTask], 否则sessionManager.tasks获取不正确
            [sessionManager multiDownloadWithUrls:urls headersArray:headersArray fileNames:fileNames];
            [self addObserverToHLSTask:sessionManager];
            NSLog(@"ts视频缓存需要请求头 : %@ ..............", headersArray.firstObject);
        }

        //保存时机(顺序)要特别注意，因为multiDownloadWithUrls会清文件缓存，从而导致保存失败
        //保存索引文件
        NSString* downloadFilePath = [sessionManager downloadFilePath];
        [parserResult saveContentToTaskFilePath:downloadFilePath];
//        if(parser.contents.length > 0) {
//            NSString* indexPath = [downloadFilePath stringByAppendingPathComponent:@"focus_m3u8_index.m3u8"];
//            NSLog(@"m3u8's Path = %@", indexPath);
//            [parser.contents writeToFile:indexPath atomically:YES encoding:NSUTF8StringEncoding error:nil];
//        }

        //保存映射文件
        if(fileNames.count > 0) {
            //转换成json文件
            NSString* content = [NSString convertToJsonString:fileNames options:YES];
            NSString* path = [downloadFilePath stringByAppendingPathComponent:@"focus_tsfilename.json"];
            [content writeToFile:path atomically:YES encoding:NSUTF8StringEncoding error:nil];
        }
    }];
}

#pragma mark -- 检测png/jpeg头部，如果是png头，那么去掉212字节
- (void)removeImageHeaderInTsWithSessionManager:(TRSessionManager *)sessionManager
                                     completion:(void(^)(void))completion
{
    TaskModel* item = [[TaskCacheManager shareInstance] queryDownloadWithIdentifier:sessionManager.identifier];
    if(item.transformStatus == M3U8TransformStatusInit) {
        //开启ffmpeg转换
        NSString* downloadFilePath = [sessionManager downloadFilePath];

        //映射文件路径
        NSString* fileNamePath = [downloadFilePath stringByAppendingPathComponent:@"focus_tsfilename.json"];
        NSData *data = [NSData dataWithContentsOfFile:fileNamePath];
        NSArray* tsFileNames = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];

        dispatch_group_t group = dispatch_group_create();
        dispatch_queue_t queue = dispatch_queue_create("com.m3u8.rearrange.queue", DISPATCH_QUEUE_CONCURRENT);

        BOOL needIterTsFile = NO;
        //最多遍历8个文件，优化速度
        for(int i=0;i<tsFileNames.count;i++) {
            if(i >= 8) {
                break;
            }

            NSString* fileName = tsFileNames[i];

            //png头
            Byte pngHeader[] = {0x89, 0x50, 0x4E, 0x47};
            Byte gifHeader[] = {0x47, 0x49, 0x46, 0x38};
            Byte dataByte[] = {0,0,0,0};
            NSString* fullName = [downloadFilePath stringByAppendingPathComponent:fileName];
            //ts文件
            NSData* data = [NSData dataWithContentsOfFile:fullName];
            //必须大于212字节
            if(data.length <= 212) {
                continue;
            }

            BOOL isPngHeader = YES;
            [data getBytes:dataByte length:4];
            for(int i=0;i<4;i++) {
                if(pngHeader[i] != dataByte[i]) {
                    isPngHeader = NO;
                    break;
                }
            }

            BOOL isGif = YES;
            if(!isPngHeader) {
                //https://www.ikandy.fun/vodplay/13797-1-2/
                //竟然是gif格式,这个212是魔幻数字,具体原因来源于png头
                for(int i=0;i<4;i++) {
                    if(gifHeader[i] != dataByte[i]) {
                        isGif = NO;
                        break;
                    }
                }
            }

            if(isPngHeader || (!isPngHeader && isGif)) {
                needIterTsFile = YES;
                break;
            }
        }

        if(!needIterTsFile) {
            //前面的8个没有检测到PNG/GIF头
            dispatch_group_async(group, queue, ^{
                if(completion) {
                    completion();
                }
            });
            return;
        }

        /*
             https://blog.csdn.net/zhijiandedaima/article/details/81506666
             // 常见的图片 文件头标志：
             JPEG (jpg)，文件头：FFD8FF
             PNG (png)，文件头：89504E47
             GIF (gif)，文件头：47494638
             TIFF (tif)，文件头：49492A00
             Windows Bitmap (bmp)，文件头：424D
         */
        for(NSString* fileName in tsFileNames) {
            dispatch_group_enter(group);
            dispatch_async(queue, ^{
                //png头
                Byte pngHeader[] = {0x89, 0x50, 0x4E, 0x47};
                Byte gifHeader[] = {0x47, 0x49, 0x46, 0x38};
                Byte dataByte[] = {0,0,0,0};
                NSString* fullName = [downloadFilePath stringByAppendingPathComponent:fileName];
                //ts文件
                NSData* data = [NSData dataWithContentsOfFile:fullName];
                //必须大于212字节
                if(data.length <= 212) {
                    dispatch_group_leave(group);
                    return;
                }

                BOOL isPngHeader = YES;
                [data getBytes:dataByte length:4];
                for(int i=0;i<4;i++) {
                    if(pngHeader[i] != dataByte[i]) {
                        isPngHeader = NO;
                        break;
                    }
                }

                BOOL isGif = YES;
                if(!isPngHeader) {
                    //https://www.ikandy.fun/vodplay/13797-1-2/
                    //竟然是gif格式,这个212是魔幻数字,具体原因来源于png头
                    for(int i=0;i<4;i++) {
                        if(gifHeader[i] != dataByte[i]) {
                            isGif = NO;
                            break;
                        }
                    }
                }

                if(isPngHeader || (!isPngHeader && isGif)) {
                    NSMutableData* mutableData = [[NSMutableData alloc] initWithData:data];
                    //https://stackoverflow.com/questions/2345626/nsmutabledata-remove-bytes
                    //https://www.52pojie.cn/thread-1475807-1-1.html
                    //移除伪装png的头部212字节
                    [mutableData replaceBytesInRange:NSMakeRange(0, 212) withBytes:NULL length:0];
                    [mutableData writeToFile:fullName atomically:YES];
                }

                dispatch_group_leave(group);
            });
        }

        dispatch_group_async(group, queue, ^{
            if(completion) {
                completion();
            }
        });
    }
}

#pragma mark -- m3u8转成mp4
- (void)transformVideoWithSessionManager:(TRSessionManager *)sessionManager
{
    @weakify(self)
    [self removeImageHeaderInTsWithSessionManager:sessionManager completion:^{
        @strongify(self)
        [self internal_transformVideoWithSessionManager:sessionManager];
    }];
}

- (void)internal_transformVideoWithSessionManager:(TRSessionManager *)sessionManager
{
    TaskModel* item = [[TaskCacheManager shareInstance] queryDownloadWithIdentifier:sessionManager.identifier];
    if(item.transformStatus == M3U8TransformStatusInit) {
        NSLog(@"开始转换...");
        item.transformStatus = M3U8TransformStatusTransforming;
        item.fileName = [NSString stringWithFormat:@"%@.mp4",item.title];

        NSInteger totalUnitCount = 0;
        for(TRDownloadTask* task in sessionManager.tasks) {
            totalUnitCount += task.progress.totalUnitCount;
        }
        item.totalUnitCount = totalUnitCount;
        item.status = TRStatusSucceeded;

        //更新数据库状态
        DatabaseUnit* unit = [DatabaseUnit updateTransformStatusWithItem:item];
        DB_EXEC(unit);

        //开启ffmpeg转换
        NSFileManager* fileManager = [NSFileManager defaultManager];
        NSString* downloadFilePath = [sessionManager downloadFilePath];
        M3U8FormatType m3u8Type = M3U8FormatTypeDefault;
        NSMutableArray* fullNames = [NSMutableArray array];
        NSString* m3u8Content = nil;

        //映射文件路径
        NSString* fileNamePath = [downloadFilePath stringByAppendingPathComponent:@"focus_tsfilename.json"];
        NSData* data = [NSData dataWithContentsOfFile:fileNamePath];
        NSArray* tsFileNames = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:nil];

        //转换前的文件预处理
        //m3u8索引路径
        NSString* indexPath = [downloadFilePath stringByAppendingPathComponent:@"focus_m3u8_index.m3u8"];
        if([fileManager fileExistsAtPath:indexPath]) {
            //默认/master格式
            m3u8Type = M3U8FormatTypeDefault;

            NSData* data = [NSData dataWithContentsOfFile:indexPath];
            if(!data) {
                NSLog(@"索引文件为空, 转换失败...");
                item.transformStatus = M3U8TransformStatusFailed;

                DatabaseUnit* unit = [DatabaseUnit updateTransformStatusWithItem:item];
                DB_EXEC(unit);

                return;
            }
            m3u8Content = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];

            //映射文件路径
            NSMutableSet* fileNameSet = [NSMutableSet set];
            for(NSString* fileName in tsFileNames) {
                //有重复的情况,会导致文件路径错误
                if([fileNameSet containsObject:fileName]) {
                    continue;
                } else {
                    [fileNameSet addObject:fileName];
                }
                NSString* fullName = [downloadFilePath stringByAppendingPathComponent:fileName];
                m3u8Content = [m3u8Content stringByReplacingOccurrencesOfString:fileName withString:fullName];

                [fullNames addObject:fullName];
            }
        } else {
            //media格式
            indexPath = [downloadFilePath stringByAppendingPathComponent:@"focus_media_m3u8_index.m3u8"];
            m3u8Type = M3U8FormatTypeMedia;

            NSData* data = [NSData dataWithContentsOfFile:indexPath];

            NSString* videoFilePath = [downloadFilePath stringByAppendingPathComponent:[SJM3U8ResultModel media_videoFileName]];
            NSData* videoData = [NSData dataWithContentsOfFile:videoFilePath];

            NSString* audioFilePath = [downloadFilePath stringByAppendingPathComponent:[SJM3U8ResultModel media_audioFileName]];
            NSData* audioData = [NSData dataWithContentsOfFile:audioFilePath];

            if(!data || !videoData || !audioData) {
                NSLog(@"索引文件为空, 转换失败...");
                item.transformStatus = M3U8TransformStatusFailed;

                DatabaseUnit* unit = [DatabaseUnit updateTransformStatusWithItem:item];
                DB_EXEC(unit);

                return;
            }

            //处理media索引文件的真实映射文件路径
            m3u8Content = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
            NSString* videoFullName = [downloadFilePath stringByAppendingPathComponent:[SJM3U8ResultModel media_videoFileName]];
            NSString* audioFullName = [downloadFilePath stringByAppendingPathComponent:[SJM3U8ResultModel media_audioFileName]];
            m3u8Content = [m3u8Content stringByReplacingOccurrencesOfString:[SJM3U8ResultModel media_videoFileName] withString:videoFullName];
            m3u8Content = [m3u8Content stringByReplacingOccurrencesOfString:[SJM3U8ResultModel media_audioFileName] withString:audioFullName];

            //映射文件路径
            //视频/音频索引文件真实路径
            NSString* videoContent = [[NSString alloc]initWithData:videoData encoding:NSUTF8StringEncoding];
            NSString* audioContent = [[NSString alloc]initWithData:audioData encoding:NSUTF8StringEncoding];

            NSMutableSet* fileNameSet = [NSMutableSet set];
            for(NSString* fileName in tsFileNames) {
                //有重复的情况,会导致文件路径错误
                if([fileNameSet containsObject:fileName]) {
                    continue;
                } else {
                    [fileNameSet addObject:fileName];
                }
                NSString* fullName = [downloadFilePath stringByAppendingPathComponent:fileName];
                videoContent = [videoContent stringByReplacingOccurrencesOfString:fileName withString:fullName];
                audioContent = [audioContent stringByReplacingOccurrencesOfString:fileName withString:fullName];

                [fullNames addObject:fullName];
            }
        }

        //写入文件中
        NSString* inputPath = [downloadFilePath stringByAppendingPathComponent:@"index.m3u8"];
        if([fileManager fileExistsAtPath:inputPath]) {
            [fileManager removeItemAtPath:inputPath error:nil];
        }
        [m3u8Content writeToFile:inputPath atomically:YES encoding:NSUTF8StringEncoding error:nil];

        NSString* outputPath = [downloadFilePath stringByAppendingPathComponent:item.fileName];

        FFmpegTask* task = [FFmpegTask new];
        task.url = item.url;
        task.inputPath = inputPath;
        task.outputPath = outputPath;
        [task updateTransformStatus:item.transformStatus];

        [task setDidFailedAction:^(FFmpegTask *task) {
            NSLog(@"m3u8转换失败...");
            item.transformStatus = M3U8TransformStatusFailed;

            DatabaseUnit* unit = [DatabaseUnit updateTransformStatusWithItem:item];
            DB_EXEC(unit);

            [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadFailureNotification object:sessionManager userInfo:@{
                @"transformStatus":@(M3U8TransformStatusFailed)
            }];
        }];

        [task setDidSuccessAction:^(FFmpegTask *task) {
            NSLog(@"m3u8转换成功...");
            item.transformStatus = M3U8TransformStatusSucceed;
            [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadSuccessNotification object:sessionManager userInfo:@{
                @"transformStatus":@(M3U8TransformStatusSucceed)
            }];

            //删除m3u8文件,只保留mp4
            [self removeM3U8WithFileNames:fullNames downloadFilePath:downloadFilePath];
        }];

        [task setDidProgressAction:^(FFmpegTask *task) {
            item.transformStatus = M3U8TransformStatusTransforming; //这里获取的item不一致,bug

            DatabaseUnit* unit = [DatabaseUnit updateTransformStatusWithItem:item];
            DB_EXEC(unit);

            [[NSNotificationCenter defaultCenter] postNotificationName:kDownloadProgressNotification object:sessionManager userInfo:@{
                @"transformStatus":@(M3U8TransformStatusTransforming)
            }];
        }];

        [[FFmpegManager shareInstance] addTask:task];

    } else {
        NSLog(@"不需要转换...");
    }
}

#pragma mark -- 删除转换成功后的m3u8,只保留mp4
- (void)removeM3U8WithFileNames:(NSArray *)fileNames
               downloadFilePath:(NSString *)downloadFilePath
{
    dispatch_queue_t queue = dispatch_queue_create("com.m3u8.remove.queue", DISPATCH_QUEUE_CONCURRENT);
    dispatch_async(queue, ^{
        NSFileManager* fileManager = [NSFileManager defaultManager];
        for(NSString* fileName in fileNames) {
            [fileManager removeItemAtPath:fileName error:nil];
        }

        //删除index.m3u8/focus_m3u8_index.m3u8/focus_tsfilename.json文件
        NSString* filePath = [downloadFilePath stringByAppendingPathComponent:@"index.m3u8"];
        if([fileManager fileExistsAtPath:filePath]) {
            [fileManager removeItemAtPath:filePath error:nil];
        }

        filePath = [downloadFilePath stringByAppendingPathComponent:@"focus_m3u8_index.m3u8"];
        if([fileManager fileExistsAtPath:filePath]) {
            [fileManager removeItemAtPath:filePath error:nil];
        }

        filePath = [downloadFilePath stringByAppendingPathComponent:@"focus_tsfilename.json"];
        if([fileManager fileExistsAtPath:filePath]) {
            [fileManager removeItemAtPath:filePath error:nil];
        }

        filePath = [downloadFilePath stringByAppendingPathComponent:@"focus_media_m3u8_index.json"];
        if([fileManager fileExistsAtPath:filePath]) {
            [fileManager removeItemAtPath:filePath error:nil];
        }

        filePath = [downloadFilePath stringByAppendingPathComponent:@"vid_m3u8_index.json"];
        if([fileManager fileExistsAtPath:filePath]) {
            [fileManager removeItemAtPath:filePath error:nil];
        }

        filePath = [downloadFilePath stringByAppendingPathComponent:@"aud_m3u8_index.json"];
        if([fileManager fileExistsAtPath:filePath]) {
            [fileManager removeItemAtPath:filePath error:nil];
        }
    });
}

//是否删除文件
- (void)removeTask:(TRDownloadTask*)task
        completely:(BOOL)completely
{
    if(!task) return;
    //先暂提任务(如果是边缓存边删除的动作)
    [self.sessionManager suspendWithUrl:task.url];
    //这里不能用cancel,否则删除的时候文件删除不成功
//    [self.sessionManager cancelWithUrl:task.url];
    //再删除任务
    [self.sessionManager removeWithUrl:task.url completely:completely];
}

- (void)removeTasks:(NSArray<TRDownloadTask*>*)tasks
         completely:(BOOL)completely
{
    if(!tasks || tasks.count == 0) return;

    for(TRDownloadTask* task in tasks) {
        [self removeTask:task completely:completely];
    }
}

- (TRDownloadTask*)fetchTask:(id<TRURLConvertible>)url
{
    return [self.sessionManager fetchTaskWithUrl:url];
}

- (NSArray<TRDownloadTask*>*)allTasks
{
    return self.sessionManager.tasks;
}

#pragma mark -- 获取文件大小
- (NSInteger)fileSizeForPath:(NSString *)file
{
    NSFileManager *fileManager = [NSFileManager defaultManager];

    NSDictionary *fileAttributeDict = [fileManager attributesOfItemAtPath:file error:nil];
    NSInteger size = fileAttributeDict.fileSize;

    return size;
}

#pragma mark - Keep Alive Management

- (void)setupKeepAliveManager
{
    // 初始化保活配置
    KeepAliveConfig *config = [KeepAliveConfig sharedConfig];

    // 根据设备状态选择最佳策略
    KeepAliveStrategy recommendedStrategy = [config recommendedStrategy];
    config.currentStrategy = recommendedStrategy;

    // 启用自动策略切换和低功耗模式
    config.enableAutoStrategySwitch = YES;
    config.enableLowPowerMode = YES;

    // 保存配置
    [config saveConfig];

    NSLog(@"[TaskManager] 保活管理器初始化完成，策略: %@",
          [config displayNameForStrategy:recommendedStrategy]);
}

@end
