 (() => {
   
 let isWriting = false;

 async function read(defaults) {
     return new Promise((resolve) => {
         chrome.storage.local.get(defaults, (local) => {
             if (chrome.runtime.lastError) {
                 console.warn(chrome.runtime.lastError.message);
                 resolve(defaults);
                 return;
             }
             resolve(local);
         });
     });
 }

 async function write(values) {
     return new Promise((resolve) => {
         isWriting = true;
         chrome.storage.local.set(values, () => {
             resolve();
             setTimeout(() => (isWriting = false));
         });
     });
 }

 function onChange(callback) {
     chrome.storage.onChanged.addListener(() => {
         if (isWriting) return;
         callback();
     });
 }

 var storage = {read, write, onChange};

 let defaults = {settings: {enabled: true, automation: 'system'}};


 const whiteFlashFixJS = {
   id: 'whiteflashfix-js',
   js: ['whiteflashfix.js'],
   matches: ['<all_urls>' ],
   runAt: 'document_start',
   allFrames: true,
 };

 const whiteFlashFixCSS = {
   id: 'whiteflashfix-css',
   css: ['whiteflashfix.css'],
   matches: ['<all_urls>'],
   runAt: 'document_start',
   allFrames: true,
 };

 const whiteFlashFixObjects = [whiteFlashFixJS, whiteFlashFixCSS];
 const whiteFlashFixIds = whiteFlashFixObjects.map(s => s.id);

 async function runFallback() {
     try {
         await chrome.scripting.registerContentScripts(whiteFlashFixObjects);
     } catch (error) {
         console.log("error = " + error);
     }
 }

 let isColorSchemeDark = undefined;
 async function isExtensionEnabled(settings) {
     //获取暗黑模式的数据
     try {
         //qingbin
         //两个background之间可以通过window通讯
         let darkModel = await window.loadDarkConfig()
         if(darkModel.enableValue === 1) {
             //auto
             if (isColorSchemeDark == null) {
                 return false;
             } else {
                 return isColorSchemeDark;
             }
         } else if(darkModel.enableValue === 2) {
             //enable
             return true;
         } else {
             //disable
             return false;
         }
         
     } catch (error) {
         console.error(error);
         return false;
     }
 }

 async function stopFallback() {
     try {
         await chrome.scripting.unregisterContentScripts({
             ids: whiteFlashFixIds,
         });
     } catch (error) {
         console.log("error = " + error);
     }
 }

 async function isFallbackActive() {
     try {
         const scripts = await chrome.scripting.getRegisteredContentScripts();
         const isWhiteFlashFixElem = (s) => whiteFlashFixIds.includes(s.id);
         return scripts.some?.(isWhiteFlashFixElem);
     } catch (error) {
         console.log("error = " + error);
         return true;
     }
 }

 async function manageFallbackScripts(settings) {
     const enabled = await isExtensionEnabled(settings);
     const fallback = await isFallbackActive();
     
     //qingbin
     try {
         if (enabled) {
             if (!fallback) {
                 await runFallback();
             }
         } else if (fallback) {
             await stopFallback();
         }
     } catch (e) {
         console.log("error = " + e);
     }
 }

 chrome.runtime.onMessage.addListener(({type, data}) => {
     //qingbin
     if (type === 'cs-color-scheme-change') {
         if(data?.isDark != null) {
             isColorSchemeDark = data?.isDark;
         }
         manageFallbackScripts(null);
     }
 });

 chrome.runtime.onInstalled.addListener(() => {
   manageFallbackScripts();
 });

 chrome.runtime.onStartup.addListener(() => {
   manageFallbackScripts();
 });

 storage.onChange(() => {
   manageFallbackScripts();
 });

 window.whiteFlashFixIds = whiteFlashFixIds;

 })();
