/// Note: rn this is duplicated with inj_frame.js because of Edge bug
function blz_chrome_runtime_onMessage_addListener(callback) {
    chrome.runtime.onMessage.addListener((msg, sender) => {
        if (msg.senderFrameURL && document.URL != msg.senderFrameURL)
            return;
        callback(msg, sender);
    });
}

(function dark_content() {
    "use strict";

    if (document.URL === 'about:blank') return;
    if (window != top && !document.documentElement.offsetWidth) {
        if ('loading' == document.readyState) {
            window.addEventListener('DOMContentLoaded', dark_content);
        }
        
        return;
    }
    
    let isFirstLevelSubFrame = (parent == top) && (self != top);
    // firstLevelSubFrame
    // Because executeScript for a frameId runs in that subframe AND all subchildren
    // we only want to send exec. requests for first level subframes
    if (isFirstLevelSubFrame) {
        chrome.runtime.sendMessage({type: "inject-index"});
    }
})();

