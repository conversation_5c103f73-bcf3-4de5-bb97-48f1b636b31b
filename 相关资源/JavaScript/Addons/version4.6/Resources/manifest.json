{"manifest_version": 2, "default_locale": "en", "name": "__MSG_extension_name__", "description": "__MSG_extension_description__", "version": "1.0", "icons": {"48": "images/icon-48.png", "96": "images/icon-96.png", "128": "images/icon-128.png", "256": "images/icon-256.png", "512": "images/icon-512.png"}, "background": {"scripts": ["background.js", "dark_background.js", "dark_whiteflashfix.js"], "persistent": false}, "content_scripts": [{"js": ["jquery-1.9.1.min.js", "PlaylistDetector.js"], "matches": ["<all_urls>"], "all_frames": true, "run_at": "document_start"}, {"css": ["PlaylistDetector.css"], "matches": ["<all_urls>"], "run_at": "document_start", "all_frames": true}, {"js": ["__firefox__.js", "bootstrap.js"], "matches": ["<all_urls>"], "all_frame": true, "run_at": "document_start"}, {"js": ["CheckUserScript.js"], "matches": ["*://*/*.js"], "all_frame": false, "run_at": "document_end"}, {"matches": ["<all_urls>"], "js": ["dark_frame.js"], "run_at": "document_start", "all_frames": true, "match_about_blank": false}, {"matches": ["<all_urls>"], "js": ["dark_content.js"], "run_at": "document_start", "all_frames": false, "match_about_blank": true}], "browser_action": {"default_popup": "popup.html", "default_icon": {"16": "images/toolbar-icon-16.png", "19": "images/toolbar-icon-19.png", "32": "images/toolbar-icon-32.png", "38": "images/toolbar-icon-38.png", "48": "images/toolbar-icon-48.png", "72": "images/toolbar-icon-72.png"}}, "web_accessible_resources": ["*"], "permissions": ["<all_urls>", "clipboardWrite", "menus", "contextMenus", "declarativeNetRequest", "nativeMessaging", "tabs", "webNavigation", "activeTab", "storage", "unlimitedStorage", "alarms", "fontSettings", "scripting"]}