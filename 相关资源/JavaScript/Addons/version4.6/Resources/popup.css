:root {
  color-scheme: light dark;
}

html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

html,
body {
    height: 100%;
    width: 100%;
    overflow-x: hidden;
}

body {
  font-family: system-ui;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin: 0 auto;

  text-align: center;

  /* 不需要body滚动，子内容独立滚动 */
  /* overflow-y: scroll; */
  overflow: hidden;
  
  /*适配iPad页和iOS放大模式*/
  min-width: 375px;
  width: 100%;
  min-height: 300px;
  
  position: absolute; /* 改为absolute避免与内部fixed元素冲突 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  
  /* 防止iOS放大模式下内容溢出 */
  max-width: 100vw;
  overflow-x: hidden;
}

.global-container {
  width: 100%;
  height: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  position: relative;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
    display: flex;
    flex-direction: column;
}

::-webkit-scrollbar  {
    display: none;
}

.header {
    /* 防止压缩,固定高度 */
    flex-shrink: 0;

    font-size: 15px;
    text-align: center;
    font-family: PingFangSC-Regular;
    
    height: 40px;

    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    
    /* 适配iOS放大模式 */
    min-width: 100vw; /* 使用视口宽度单位 */
    max-width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

.content-container {
    flex-grow: 1;

    /* 下面这两句很重要 */
    display: flex;
    flex-direction: column;

/*    background: #f5f5f5;*/

    /* 内容过多时就滚动 */
    overflow: auto;
    
    /* 适配header和footer固定定位 */
    margin-top: 40px;
    margin-bottom: 50px;
    padding-bottom: env(safe-area-inset-bottom);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: auto;
}

/* 油猴脚本相关css */
.script-scrollView {
    flex-grow: 1;
    /* 超出的部分内容滚动 */
    overflow-y: auto;
/*    保留一点间隔*/
    padding-bottom: 15px;
    
    height: 100%;
}

.script-scrollView:last-child {
    border-bottom: none;
}

.content-cell {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    height: 88px;
/*    background: #fff;*/

/*    border-bottom: 1px solid #e6e6e6;*/
}

.content-logo {
    width: 35px;
    height: 35px;

    position: absolute;
    left: 10px;
    top: 10px;
}

.content-default-logo {
    width: 35px;
    height: 35px;
    
    background-size: 35px 35px;
    background-repeat: no-repeat;
    background-position: center;
    
    position: absolute;
    left: 10px;
    top: 10px;
}

.content-control-button {
    position: absolute;
    right: 15px;

    height: 25px;
    width: 25px;

    background-size: 25px 25px;
    background-repeat: no-repeat;
    background-position: center;
}

.content-title {
    font-size: 15px;
    font-weight: bold;
/*    color: #434343;*/
    overflow: hidden;
    text-overflow: ellipsis;

    text-align: left;
    font-family: PingFangSC-Regular;
    
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;

    position: absolute;
    left: 55px;
    top: 10px;
    right: 40px;
}

.content-desc {
    font-size: 12px;
    color: #999999;
    overflow: hidden;
    text-overflow: ellipsis;

    text-align: left;
    font-family: PingFangSC-Regular;
    
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;

    position: absolute;
    left: 55px;
    top: 35px;
    right: 40px;
}

/* 菜单 + 版本号 */
.content-bottom-container {
    display: flex;
    align-items: center;
    justify-content: center;
    
    position: absolute;
    left: 10px;
    bottom: 8px;
}

/* 菜单栏 */
.content-menu {
    font-size: 12px;
/*    color: #333333;*/
    
    text-align: left;
    font-family: PingFangSC-Regular;
    
    display: flex;
    align-items: center;
    justify-content: center;
}

.content-menu-logo {
    width: 18px;
    height: 18px;
    
    background-size: 18px 18px;
    background-repeat: no-repeat;
    background-position: center;
}

.select-container {
    padding: 0px;
}

.content-version {
    font-size: 12px;
    color: #999999;

    text-align: left;
    font-family: PingFangSC-Regular;
    
    padding-right: 12px;
/*    position: absolute;*/
/*    left: 10px;*/
/*    bottom: 10px;*/
}
/* 油猴脚本相关css */


/* 标记模式相关css */
.tagit-scrollView {
    flex-grow: 1;
    /* 超出的部分内容滚动 */
    overflow-y: auto;
/*    保留一点间隔*/
    padding-bottom: 15px;
    
    height: 100%;
}

.tagit-scrollView:last-child {
    border-bottom: none;
}

.tagit-cell {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    height: 64px;
/*    background: #fff;*/

/*    border-bottom: 1px solid #e6e6e6;*/
}

.tagit-host {
    font-size: 15px;
/*    color: #434343;*/
    overflow: hidden;
    text-overflow: ellipsis;

    text-align: left;
    font-family: PingFangSC-Regular;

    position: absolute;
    left: 15px;
    top: 10px;
    right: 90px;
}

.tagit-xpath {
    font-size: 13px;
    color: #999999;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    text-align: left;
    font-family: PingFangSC-Regular;
    
    position: absolute;
    left: 15px;
    bottom: 10px;
    right: 90px;
}

.tagit-active-button {
    position: absolute;
    right: 15px;

    height: 25px;
    width: 25px;

    background-size: 25px 25px;
    background-repeat: no-repeat;
    background-position: center;
}

.tagit-delete-button {
    position: absolute;
    right: 55px;

    height: 25px;
    width: 25px;

    background-size: 25px 25px;
    background-repeat: no-repeat;
    background-position: center;
}

.tagit-add-button {
    /* 下面这句的意思是：如果 scroll-view 内容太多，会挤压其他兄弟的空间，设置自己不允许被其他兄弟挤压空间 */
    flex-shrink: 0;
    
    /* 固定高度 */
    height: 50px;
    width: 60%;

    border: 1px solid #e6e6e6;
    border-radius: 25px;

    background-color:#333;
    color: #fff;
    font-size: 18px;
    text-align: center;
    font-family: PingFangSC-Semibold;
    
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    align-self: center;

    margin: 10px auto 10px;
}
/* 标记模式相关css */

/* 适配相关Mac */

.addons-fix-level1 {
    flex-grow: 1;
    position: relative;
}

.addons-fix-level2 {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    overflow: auto;
}

/* 适配相关Mac */

/* 暗黑模式相关css */
.darkmode-container {
    flex-grow: 1;
    /* 超出的部分内容滚动 */
    overflow-y: auto;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
  
.__dark-row {
    padding: 0 20px;
    height: 50px;
    width: 90%;
    display: flex;
    justify-content: space-between;
    align-items: center;

/*    background-color: #fff;*/
}

.__dark-enable-current-tips {
    width: 90%;
    display: flex;
    align-items: center;
    
    color: #999;
    font-size: 15px;
    text-align: left;
    
    padding-bottom: 8px;
    padding-left: 20px;
}

._dark-enabled {
    margin-top: 20px;
    
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

._dark-light {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    border-bottom: none;
}

._dark-website {
    border-radius: 10px;
}

.__dark-row-label {
    display: flex;
    align-items: center;
    gap: 10px;
    
    flex: 1; /* 允许标签区域伸缩 */
    min-width: 0; /* 允许子元素压缩到比其内容更小 */
}

.__dark-row-up-down-arrow {
    height: 12px;
    padding-right: 1px;
    
    flex-shrink: 0; /* 防止图片被压缩 */
}

#id-scope-span {
    flex: 1; /* 允许文本区域伸缩 */
    min-width: 0; /* 允许文本压缩到比其内容更小 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
    text-align: left;
}

.__dark-row-label-image {
    border-radius: 5px;
    height: 30px;
    width: 30px;

    background-size: 30px;

    background-repeat: no-repeat;
    background-position: center;
}

.__dark-row-enabled {
    background-image: url(./images/dark_enabled.png);
}

.__dark-row-light {
    background-image: url(./images/dark_brightness.png);
}

.__dark-row-website {
    background-image: url(./images/dark_website.png);
}

.__dark-row-label-text {
    color: #999;
    flex-shrink: 1;
}

.__dark-row-detail {
    height: 100%;
    
    flex-shrink: 0; /* 防止右侧详情区域被压缩 */
}

.__dark-row-detail-container {
    position: absolute;
    margin-top: 15px;
}

.__dark-row-detail-container-cover {
    position: absolute;
    right: 0;
    text-align: right;
    color: #666;
    white-space: nowrap;
    pointer-events: none;
}

.__dark-row-select {
    border: none;
    background: none;
    -webkit-appearance: none;
    outline: none;
    position: absolute;
    right: 0;
    text-align: right;
    color: transparent;
}

select:not(:-internal-list-box) {
    overflow: visible !important;
}
/* 暗黑模式相关css */


.content-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    /* 下面这句的意思是：如果 scroll-view 内容太多，会挤压其他兄弟的空间，设置自己不允许被其他兄弟挤压空间 */
    flex-shrink: 0;
    /* 固定高度 */
    height: 50px;
    width: 100%;
    
    /* 适配iOS放大模式 */
    min-width: 100vw; /* 使用视口宽度单位 */
    max-width: 100vw;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    
    padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
    padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
}

.footer-item {
    width: 40px;
    height: 40px;
    background-size: 40px 40px;
    background-repeat: no-repeat;
    background-position: center;
    /* 增加弹性布局适配不同尺寸 */
    flex: 1;
    margin: 0 5px; /* 增加左右间距确保不会紧贴在一起 */
    max-width: 100px; /* 设置最大宽度防止过度拉伸 */
}

/*模块开关*/
.enabled-container {
    flex-grow: 1;
    align-self: center;

    width: 100%;
    display: flex;
    flex-direction: column;

    justify-content: center;
    align-content: center;
    align-items: center;
}

.enabled-text {
    text-align: center;
    color: #999;
    font-size: 15px;
    font-family: PingFangSC-Regular;
    
    margin-bottom: 10px;
}

.enabled-button {
      /* 固定高度 */
      height: 50px;
      width: 60%;
  
      border: 1px solid #e6e6e6;
      border-radius: 25px;
  
      background-color:#333;
      color: #fff;
      font-size: 18px;
      text-align: center;
      font-family: PingFangSC-Semibold;
      
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
  
      align-self: center;
  
      margin: 10px auto 10px;
}

/*空白提示*/
.empty-tips {
    text-align: center;
    color: #666666;
    font-size: 16px;
    font-family: PingFangSC-Regular;
    
    height: 100%;
    
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    align-self: center;
}

@media (prefers-color-scheme: light) {
    html,
    body {
        background-color: #fff;
    }
    
    .header {
        background: white;
        color: #000;
        
        border-bottom: 1px solid #e6e6e6;
    }
    
    .content-container {
        background: #f5f5f5;
    }

    .content-cell {
        background: #fff;
        
        border-bottom: 1px solid #e6e6e6;
    }
    
    .content-title {
        color: #434343;
    }
    
    .content-menu {
        color: #333333;
    }
    
    .tagit-cell {
        background: #fff;

        border-bottom: 1px solid #e6e6e6;
    }
    
    .tagit-host {
        color: #434343;
    }
    
    .__dark-row {
        background-color: #fff;
    }
    
    .content-footer {
        background-color: white;
        border-top: 1px solid #e6e6e6;
    }
}

/* 暗黑模式相关css */
@media (prefers-color-scheme: dark) {
    html,
    body {
        background-color: #303030;
    }
    
  .header {
      background: #222222;
      color: white;
      
      border-bottom: 0.5px solid #666;
  }
  
  .content-container {
      background: #222222;
  }
  
  .content-cell {
      background: #303030;
      
      border-bottom: 0.5px solid #666;
  }
  
  .content-title {
      color: #fff;
  }
  
  .content-menu {
      color: #fff;
  }
  
  .tagit-cell {
      background: #303030;

      border-bottom: 0.5px solid #666;
  }
  
  .tagit-host {
      color: #fff;
  }
  
  .__dark-row {
      background-color: #303030;
  }
  
  .content-footer {
      background-color: #303030;
      border-top: 0.5px solid #666;
  }
}

