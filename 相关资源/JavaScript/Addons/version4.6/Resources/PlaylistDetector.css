.__playlistDetector-video-list-mask {
    /*主要是为了动画显示*/
    display: none;
        
    box-sizing: border-box;
    position: fixed;

    left: 0px;
    top: 0px;
    right: 0px;
    bottom: 0px;

    background-color: rgba(0, 0, 0, .2);

    z-index:2147483646 !important;
}

.__playlistDetector-video-list-dialog {
    /*主要是为了动画显示*/
    display: none;
    
    box-sizing: border-box;
    position: fixed;
    height: 300px;
    
    width: 90%;
    /*适配iPad*/
    max-width: 768px;
    min-width: 375px;
    
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    background-color: #fff;

    border-radius:10px;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, .4);

    z-index:2147483647 !important;
}

.__playlistDetector-video-list-dialog .__playlistDetector-titleLabel {
    box-sizing: border-box;

    position: absolute;
    top: 15px;
    left: 50%;
    transform:translateX(-50%);

    text-align: center;
    font-size: 16px;
    color: #222;
}

.__playlistDetector-video-list-dialog .__playlistDetector-close-logo {
    box-sizing: border-box;

    /* 固定高度 */
    height: 30px;
    width: 30px;

    background-size: 30px 30px;
    position: absolute;
    right: 10px;
    top: 10px;

    background-repeat: no-repeat;
    background-position: center;
}

.__playlistDetector-video-list-dialog .__playlistDetector-video-list {
    box-sizing: border-box;

    position: absolute;
    top: 50px;
    left: 10px;
    right: 10px;
    bottom: 10px;

    border-radius: 10px;
    background-color: #f5f5f5;

    /* 内容过多时就滚动 */
    overflow-y: auto;

    display: flex;
    flex-direction: column;

    padding: 10px;
}

.__playlistDetector-video-list .__playlistDetector-video-list-cell {
    box-sizing: border-box;

    /* 因为父容器使用flex伸缩盒，所以子元素的高度是可伸缩的，空间不够就压缩自己，没挤出滚动条 */
    /* 就是说万一父容器空间不够了，自己不压缩 */
    flex-shrink: 0;

    height: 60px;

    background-color: #fff;
    border-bottom: 0.5px solid #f5f5f5;

    position: relative;

    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

.__playlistDetector-video-list-cell .__playlistDetector-title {
    box-sizing: border-box;

    font-size: 16px;
    color: #333;

    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    text-align: left;

    position: absolute;
    right: 80px;
    left: 15px;
}

.__playlistDetector-video-list-cell .__playlistDetector-downloadButton {
    box-sizing: border-box;

    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-self: center;

    color: #fff;
    background-color: #2D7AFE;

    font-size: 14px;
    border-radius: 12px;

    position: absolute;
    right: 10px;

    padding-left: 12px;
    padding-right: 12px;
    height: 25px;
}

.__playlistDetector-video-list-cell:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.__playlistDetector-video-list-cell:last-child {
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.__playlistDetector-countNumber {
    background-color: #FF2626 !important;
    
    position:absolute;
    right: -6px;
    top: -6px;
    height: 18px;
    line-height: 18px;
    
    border-radius: 9px;
    font-size: 13px;
    color: #fff;
    padding: 0px 6px;
    font-family: PingFangSC-Regular;
        
        
    display: flex;
    justify-content: center;
    align-items: center;
}


/*标记模式*/
.__tagit-mask {
    /*主要是为了动画显示*/
/*    display: none;*/
    
    box-sizing: border-box;
    
    height: 100%;
    width: 100%;
    
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
        
    z-index:2147483647 !important;
}

.__tagit-container {
    /*主要是为了动画显示*/
/*    display: none;*/
    
    box-sizing: border-box;
    
    height: 116px;
    width: 100%;
    /*适配iPad/iMac与iOS放大模式*/
    max-width: min(550px, 100vw - 20px);
    min-width: min(375px, 100vw - 20px);/* 减小最小宽度以适应放大模式 */
    
    position: fixed;
    bottom: 5%;
    left: 50%;
    transform:translateX(-50%);
    
    transition:transform 0.3s;
    
    z-index:2147483647 !important;
    
    /* 防止放大模式下内容溢出 */
    overflow: hidden;
}

.__tagit-buttons {
    height: 36px;
    width: 100%;
    
    display: flex;
    flex-direction: row;
    justify-content: space-around; /* 更均匀的间距分布 */
    
    position: absolute;
    left: 0;
    top: 0;
}

.__tagit-button {
    height: 36px;
    width:70px;
    max-width: 25%; /* 限制最大宽度比例 */
    border-radius: 18px;
    
    background-color:#333;
    opacity: 0.95;
    color: #fff;
    font-size: 15px;
    text-align: center;
    font-family: PingFangSC-Semibold;
      
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.__tagit-bottom-container {
    height: 50px;
    width:calc(65%);
    max-width: calc(100% - 40px); /* 确保在放大模式下不超出容器 */
    
    display:relative;
    
    position: absolute;
    left: 50%;
    transform:translateX(-50%);
    top: 56px;
    
    display: flex;
    flex-direction: row;
}

.__tagit-exit-button {
    /* 固定高度 */
    height: 50px;
    flex-grow: 1;

    border-radius: 10px;

    background-color:#fe0500;
    color: #fff;
    font-size: 18px;
    text-align: center;
    font-family: PingFangSC-Semibold;
    
    margin-right: 20px;
      
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.__tagit-updown_button {
    /* 固定高度 */
    height: 50px;
    width: 50px;
    
    background-size: 48px 48px;
    
    /* 下面这句的意思是：如果 scroll-view 内容太多，会挤压其他兄弟的空间，设置自己不允许被其他兄弟挤压空间 */
    flex-shrink: 0;
    
/*    padding-left: 0px;*/
    
    background-image: url(./images/tagit_up.png);
    background-repeat: no-repeat;
    background-position: center;
}



/*长按识别模式*/
.__contextmenu-mask {
    /*主要是为了动画显示*/
    display: none;
    
    box-sizing: border-box;
    position: fixed;

    left: 0px;
    top: 0px;
    right: 0px;
    bottom: 0px;

    background-color: rgba(0, 0, 0, .2);

    z-index:2147483646 !important;
    
    /*为了去除长按出现的选中效果*/
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

.__contextmenu-container-dialog {
    /*主要是为了动画显示*/
    display: none;
    
    box-sizing: border-box;
    position: fixed;
/*    按钮42px, 标题2行*/
    height: 204px;
    width: calc(100% - 60px);
    
    background-color: #fff;

    border-radius:10px;
    box-shadow: 0px 5px 10px rgba(0, 0, 0, .4);

    z-index: 2147483647 !important;

    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    
    /*为了去除长按出现的选中效果*/
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

.__contextmenu-title-container {
    height: 84px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.__contextmenu-title {
    font-size: 16px;
    color: #222;
    
    padding: 0px 10px 0px 10px;

    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 这里是超出几行省略 */
    overflow: hidden;
}

.__contextmenu-action {
    height: 60px;
    
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    text-align: center;
    font-size: 16px;
    color: #222;
    
    border-top: 1px solid #f5f5f5;
}



/*脚本检测安装*/
.__install-container {
    box-sizing: border-box;
    
    width: 65%;
    height: 60px;
    
    position: fixed;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);

    background-color: #303030;

    border-radius:10px;
    
    z-index:2147483647 !important;
}

.__install-logo {
    width: 30px;
    height: 30px;
    
    background-size: 30px 30px;
    
    position: absolute;
    left: 20px;
    top: 15px;
    
    background-image: url(./images/icon-64.png);
    background-repeat: no-repeat;
    background-position: center;
}

.__install-text {
    position: absolute;
    left: 60px;
    top: 50%;
    transform:translateY(-50%);

    text-align: center;
    font-size: 16px;
/*    color: #222;*/
    color: #fff;
}
