function localizableKey(key) {
    let objs = {
        zh: {
            matchScripts: "已匹配脚本列表",
            tagit: "标记模式",
            darkMode: "暗黑模式",
            openInApp: "打开APP",
            openWithScript: "打开APP，开启油猴脚本功能",
            openWithTagit: "打开APP，开启标记广告功能",
            openWithDarkMode: "打开APP，开启网页暗黑模式",
            emptyScript: "没有匹配到适用的脚本，赶紧去添加吧~",
            emptyData: "暂无数据，空空如也~",
            version: "版本号",
            startTagit: "进入标记模式",
            themeDefault: "默认(跟随系统)",
            themeDark: "开启",
            themeAuto: "跟随系统",
            themeLight: "关闭",
            dimImagesDefault: "默认(100%)",
            scopeDefault: "默认(允许)",
            scopeEnable: "允许",
            scopeDisable: "禁止",
            scopeTips: "是否允许当前网站开启暗黑模式",
            enabled: "启用状态",
            theme: "主题",
            dimImages: "图片明亮度",
            menu: "菜单",
        },
        en: {
            matchScripts: "Match Scripts",
            tagit: "Tagit",
            darkMode: "Dark Mode",
            openInApp: "Open in Addons",
            openWithScript: "Open in Addons and Enable Script",
            openWithTagit: "Open in Addons and Enable Tagit",
            openWithDarkMode: "Open in Addons and Enable DarkMode",
            emptyScript: "No matching script found~",
            emptyData: "empty~",
            version: "version",
            startTagit: "Enter Tagit",
            themeDefault: "Default(Auto)",
            themeDark: "On",
            themeAuto: "Auto",
            themeLight: "Off",
            dimImagesDefault: "Default(100%)",
            scopeDefault: "Default(Global)",
            scopeEnable: "Allow",
            scopeDisable: "Forbidden",
            scopeTips: "Allow dark mode on this website?",
            enabled: "Enabled",
            theme: "Theme",
            dimImages: "ImageBrightness",
            menu: "menu",
        }
    }

    function _current() {
        let e = navigator.languages;
        if(e.length > 0) {
            e = navigator.languages[0];
        } else if(navigator.language) {
            e = navigator.language;
        } else if(navigator.userLanguage) {
            e = navigator.userLanguage;
        } else {
            e = "en";
        }
        e = (e = e.toLowerCase()).replace(/-/, "_");

        if(e.indexOf("zh") > -1) {
            return objs.zh;
        } else {
            return objs.en;
        }
    }
    
    let values = _current();
    return values[key];
}

//添加事件监听
function setupObservers() {
    //脚本
    $("#id-footer-js").click(function (e) {
        e.preventDefault();
        handleToolbarTapAction(e.target);
    });

    //标记模式
    $("#id-footer-tagit").click(function (e) {
        e.preventDefault();
        handleToolbarTapAction(e.target);
    });
    
    //暗黑模式
    $("#id-footer-darkmode").click(function (e) {
        e.preventDefault();
        handleToolbarTapAction(e.target);
    });
    
    //打开APP
    $("#id-enabled-button").click(function (e) {
        e.preventDefault();

        let schemeUrl = "addons://activeAction";
        // 创建一个隐形的 <a> 标签
        var a = document.createElement('a');
        a.setAttribute('href', schemeUrl);
        a.style.display = 'none';
        document.body.appendChild(a);
        // 模拟点击这个 <a> 标签
        a.click();
        // 移除 <a> 标签
        document.body.removeChild(a);
        
        try {
            window.open(schemeUrl);
        } catch (error) {}
        
        window.close();
    });
    
    //点击进入标记模式
    $("#id-tagit-addbutton").click(function (e) {
        e.preventDefault();
        
        browser.runtime.sendMessage({
            from: "popup",
            operate: "API_ENTER_TAGIT"
        }, function (response) {
            window.close();
        });
    });
}

//处理底部工具栏点击事件
function handleToolbarTapAction(target) {
    if(target == undefined || target == null) return;
    let tab = target.getAttribute("tab");
    let isSelect = target.getAttribute("isSelect");

    if(isSelect != undefined && isSelect != null) return;
    
     //处理标题
     if(tab == "0") {
        let val = localizableKey("matchScripts");
        $("#id-header").text(val);
    } else if(tab == "1") {
        let val = localizableKey("tagit");
        $("#id-header").text(val);
    } else if(tab == "2") {
        let val = localizableKey("darkMode");
        $("#id-header").text(val);
    }

    //先统一隐藏,是否显示由选中的逻辑决定
    $("#id-enabled-container").hide();
    
    /// 处理底部图标
    $("#id-content-footer").children().each(function (index, element) {
        // element == this
        let iTab = $(this).attr("tab");
        if(iTab != tab) {
            //未选
            $(this).removeAttr("isSelect");
            if(iTab == "0") {
                //油猴脚本
                $(this).css("background-image", `url("./images/script_normal_icon.png")`);
                $("#id-script-fix").hide();
            } else if(iTab == "1") {
                //标记模式
                $(this).css("background-image", `url("./images/tagit_normal_icon.png")`);
                $("#id-tagit-fix").hide();
                $("#id-tagit-addbutton").hide();
            } else if(iTab == "2") {
                //暗黑模式
                $(this).css("background-image", `url("./images/dark_normal_icon.png")`);
                $("#id-darkmode-container").hide();
            }
        } else {
            //已选
            $(this).attr("isSelect", "");
            if(iTab == "0") {
                //油猴脚本
                $(this).css("background-image", `url("./images/script_select_icon.png")`);
                
                if(isScriptActive()) {
                    $("#id-enabled-container").hide();
                    $("#id-script-fix").show();
                } else {
                    $("#id-enabled-container").show();
                    $("#id-script-fix").hide();
                    
                    let val = localizableKey("openInApp");
                    $("#id-enabled-button").text(val);
                    
                    val = localizableKey("openWithScript");
                    $("#id-enabled-text").text(val);
                }
                
                saveMoudleIndex("0");
            } else if(iTab == "1") {
                //标记模式
                $(this).css("background-image", `url("./images/tagit_select_icon.png")`);
                
                if(isTagitActive()) {
                    $("#id-enabled-container").hide();
                    $("#id-tagit-fix").show();
                    $("#id-tagit-addbutton").show();
                } else {
                    $("#id-enabled-container").show();
                    $("#id-tagit-fix").hide()
                    $("#id-tagit-addbutton").hide();
                    
                    let val = localizableKey("openInApp");
                    $("#id-enabled-button").text(val);
                    
                    val = localizableKey("openWithTagit");
                    $("#id-enabled-text").text(val);
                }
                
                saveMoudleIndex("1");
            } else if(iTab == "2") {
                //暗黑模式
                $(this).css("background-image", `url("./images/dark_select_icon.png")`);
                $("#id-darkmode-container").show();
                
                if(isDarkModeActive()) {
                    $("#id-enabled-container").hide();
                    $("#id-darkmode-container").show();
                } else {
                    $("#id-enabled-container").show();
                    $("#id-darkmode-container").hide();
                    
                    let val = localizableKey("openInApp");
                    $("#id-enabled-button").text(val);
                    
                    val = localizableKey("openWithDarkMode");
                    $("#id-enabled-text").text(val);
                }
                
                saveMoudleIndex("2");
            }
        }
    });
}

function saveMoudleIndex(index) {
    browser.runtime.sendMessage({
        from: "popup",
        operate: "API_SAVE_MODULE_INDEX",
        index: index,
    }, function(response) {
        
    });
}

function isScriptActive() {
    if(__data__ == null) return false;

    return __data__.config.tampermonkey.isActive;
}

function isTagitActive() {
    if(__data__ == null) return false;

    return __data__.config.tagit.isActive;
}

function isDarkModeActive() {
    if(__data__ == null) return false;

    return __data__.config.darkMode.isActive;
}

function isObjectNull(obj) {
    return obj == null || Object.keys(obj).length === 0;
}

//油猴脚本是否匹配当前网站
function isMatchTheCurrentURL(userScripts, urlString) {
    
    function matchRule(regex, str) {
        var escapeRegex = (str) => str.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
        return new RegExp("^" + regex.split("*").map(escapeRegex).join(".*") + "$").test(str);
    }
    
    let isMatched = false;
    
    if(isObjectNull(userScripts.includes) == false) {
        for(let regex of userScripts.includes) {
            if(regex == '*' || matchRule(regex, urlString)) {
                isMatched = true;
                break;
            }
        }
    }
    
    if(isObjectNull(userScripts.matches) == false) {
        for(let regex of userScripts.matches) {
            if(regex == '*' || matchRule(regex, urlString)) {
                isMatched = true;
                break;
            }
        }
    }
    
    if(isObjectNull(userScripts.excludes) == false) {
        for(let regex of userScripts.excludes) {
            if(regex == '*' || matchRule(regex, urlString)) {
                isMatched = false;
                break;
            }
        }
    }
    
    return isMatched;
}

var __data__ = null;
var __hostname__ = "";
var __url__ = "";
var __darkModel__ = null;

//一次性加载所有popup所需要的数据
function loadingAllData() {
    browser.tabs.query({active: true, contentWindow: true}, (tabs) => {
        //设置hostname
        let tab = tabs[0];
        __url__ = tab.url;
        __hostname__ = new URL(tab.url).hostname;
        
        if(__hostname__ != null) {
            $("#id-scope-span").text(__hostname__);
        }
        
        _loadingAllData(tabs[0]);
    });
}

function _loadingAllData(tab) {
    browser.runtime.sendMessage({
        from: "popup",
        operate: "API_ReloadData",
        status: "1"
    }, (response) => {
        try{
            let data = response.body;
            __data__ = data;
            let isScriptActive = data.config.tampermonkey.isActive;
            let isTagitActive = data.config.tagit.isActive;
            let isDarkModeActive = data.config.darkMode.isActive;
            let lastSelectModuleIndex = data.lastSelectModuleIndex;

            //处理空白数据以及关闭逻辑
            if(isScriptActive) {
                let scriptData = [];
                //筛选当前正在运行的脚本
                for(let item of data.userScripts) {
                    if(isMatchTheCurrentURL(item, tab.url)) {
                        scriptData.push(item);
                    }
                }

                renderScriptData(scriptData);
            } else {
                //显示"打开APP，开启油猴脚本功能"
            }

            if(isTagitActive) {
                renderTagitData(data.tagits);
            } else {
                //显示"打开APP，开启标记去广告"
            }

            __darkModel__ = data.darkModel;
            if(isDarkModeActive) {
                let darkModel = data.darkModel;
                initDarkMode(darkModel);
            }

            let target = null;
            if(lastSelectModuleIndex == 0) {
                target = document.querySelector("#id-footer-js");
            } else if(lastSelectModuleIndex == 1) {
                target = document.querySelector("#id-footer-tagit");
            } else if(lastSelectModuleIndex == 2) {
                target = document.querySelector("#id-footer-darkmode");
            }

            //打开上次关闭的模块
            //因为重复点击不会触发的逻辑,所以去掉isSelect
            $(target).removeAttr("isSelect", "");
            handleToolbarTapAction(target);

        } catch(error) {
            console.error(error);
        }
    });
}

//油猴脚本渲染
function renderScriptData(data) {
    //清空重置
    $("#id-script-scrollView").empty();
    $("#id-script-empty-tips").hide();
    
    if(data == null || data == undefined || data.length == 0) {
        //显示空白样式
        let emptyTips = document.createElement("div");
        emptyTips.id = "id-script-empty-tips";
        $(emptyTips).addClass("empty-tips");
        
        let val = localizableKey("emptyScript");
        $(emptyTips).text(val);
        $("#id-script-scrollView").append(emptyTips);
        return;
    } else {
    }

    for(let item of data) {
        let cell = document.createElement("div");
        cell.id = "id-content-cell";
        $(cell).addClass("content-cell");

        //logo

        if(item.iconUrl != undefined && item.iconUrl != null && item.iconUrl != "") {
            //有logo
            let logo = document.createElement("img");
            logo.src = item.iconUrl;
            $(logo).addClass("content-logo");
            $(cell).append(logo);
        } else {
            //没有logo,用本地图片
            let logo = document.createElement("div");
            let arrowImage = `url("./images/script_default_logo.png")`;
            
            $(logo).css("background-image", arrowImage);
            $(logo).addClass("content-default-logo");
            $(cell).append(logo);
        }

        //标题
        let title = document.createElement("div");
        $(title).addClass("content-title");
        $(title).text(item.name);
        $(cell).append(title);

        //描述
        let desc = document.createElement("div");
        $(desc).addClass("content-desc");
        $(desc).text(item.desc);
        $(cell).append(desc);

        //菜单栏+版本
        let bottomContainer = document.createElement("div");
        $(bottomContainer).addClass("content-bottom-container");
        $(cell).append(bottomContainer);
        
        //版本
        let version = document.createElement("div");
        $(version).addClass("content-version");
        
        var val = localizableKey("version");
        $(version).text(val + ": " + item.version);
        $(bottomContainer).append(version);

        //菜单栏
        let menu = document.createElement("div");
        $(menu).addClass("content-menu");
        val = localizableKey("menu");
        $(menu).text(val);
        $(menu).hide();
        $(bottomContainer).append(menu);
        
        handleScriptMenuWithModel(cell, item);
        
        //箭头
        let arrow = document.createElement("div");
        let arrowImage = "";
        if(isDark()) {
            arrowImage = `url("./images/menu_arrow_white.png")`;
        } else {
            arrowImage = `url("./images/menu_arrow_black.png")`;
        }
        $(arrow).css("background-image", arrowImage);
        $(arrow).addClass("content-menu-logo");
        $(menu).append(arrow);
        
        //开关按钮
        let control = document.createElement("div");
        $(control).addClass("content-control-button");

        let image = "";
        if(isDark()) {
            if(item.isActive) {
                image = `url("./images/icon_pause_dark.png")`;
            } else {
                image = `url("./images/icon_play_dark.png")`;
            }
        } else {
            if(item.isActive) {
                image = `url("./images/icon_pause.png")`;
            } else {
                image = `url("./images/icon_play.png")`;
            }
        }
        
        $(control).css("background-image", image);
        $(cell).append(control);

        $("#id-script-scrollView").append(cell);

        //添加开关按钮点击事件
        $(control).click(function (e) {
            e.preventDefault();
            handleScriptActive(e.target, item);
        });
    }
}

//点击脚本的暂停/激活按钮
function handleScriptActive(target, item) {
    let isActive = !item.isActive;
    item.isActive = isActive;

    if(isDark()) {
        if(isActive) {
            $(target).css("background-image", `url("./images/icon_pause_dark.png")`);
        } else {
            $(target).css("background-image", `url("./images/icon_play_dark.png")`);
        }
    } else {
        if(isActive) {
            $(target).css("background-image", `url("./images/icon_pause.png")`);
        } else {
            $(target).css("background-image", `url("./images/icon_play.png")`);
        }
    }

    browser.runtime.sendMessage({
        from: "popup",
        operate: "API_SCRIPT_ACTIVE_CHANGE",
        scriptId: item.uuid,
        isActive: isActive
    }, function(response){

    });
}

//根据脚本model获取菜单栏信息
function handleScriptMenuWithModel(cell, scriptModel) {
    let scriptId = scriptModel.uuid;
    let menuElement = cell.querySelector('.content-menu');
    
    let injectMode = scriptModel.injectMode;
    browser.tabs.query({active: true, contentWindow: true}, (tabs) => {
        const message = {
               from: "popup",
            operate: "API_GET_Script_Menu",
           scriptId: scriptId,
              tabId: tabs[0].id
        };
        
        browser.tabs.sendMessage(tabs[0].id, message, function(response){
            let type = response.type;
            if(type != "API_GET_Script_Menu") return;
            
            let body = response.body;
            let _scriptId = body.scriptId;
            if(scriptId != _scriptId) return;
            
            let menuArray = body.array;
            for(let item of menuArray) {
                item.injectMode = injectMode;
            }
            
            handleScriptMenuAction(menuElement, menuArray);
        });
    });
}

//点击脚本的菜单栏
function handleScriptMenuAction(menuElement, menuArray) {
    // 根据类名移除旧的select元素
    const oldSelect = menuElement.querySelector('.__dark-row-select');
    if (oldSelect) {
        oldSelect.remove();
    }
    
    //没有数据
    if(menuArray == null
       || menuArray == undefined
       || menuArray.length == 0) {
        //隐藏菜单
        $(menuElement).hide();
        return;
    } else {
        //显示菜单
        $(menuElement).show();
    }
    
    // 创建新的select元素
    const selectElement = document.createElement('select');
    selectElement.className = '__dark-row-select';

    // 获取数据项
    const items = menuArray;

    // 填充select元素的选项
    items.forEach(item => {
        const option = document.createElement('option');
        option.text = item.name;
        
        // 将数据存储在data属性中
        option.dataset.optionData = JSON.stringify(item);
        
        selectElement.add(option);
    });

    // 将新的select元素添加到元素A中
    menuElement.appendChild(selectElement);
    
    // 将selectedIndex设置为-1，确保没有默认选中效果
    selectElement.selectedIndex = -1;
    
    $(selectElement).change(function(e) {
        let selectedOption = $(selectElement).find("option:selected");
        handleOptionClick(selectedOption);
    });
}

// 处理选项点击事件
function handleOptionClick(option) {
    let optionData = option.data('option-data');
    
    browser.tabs.query({active: true, contentWindow: true}, (tabs) => {
        browser.tabs.sendMessage(tabs[0].id, {
                from: "popup",
             operate: "API_Run_Command",
            scriptId: optionData.uuid,
            randomId: optionData.randomId,
          injectMode: optionData.injectMode,
        }, function (response) {
            window.close();
        });
    });
}

//加载标记模式数据并渲染
function renderTagitData(data) {
    //清空重置
    $("#id-tagit-scrollView").empty();

    //按钮国际化适配
    let val = localizableKey("startTagit");
    $("#id-tagit-addbutton").text(val);
    
    if(data == null || data == undefined || data.length == 0) {
        //显示空白样式
        let emptyTips = document.createElement("div");
        emptyTips.id = "id-tagit-empty-tips";
        $(emptyTips).addClass("empty-tips");
        
        let val = localizableKey("emptyData");
        $(emptyTips).text(val);
        $("#id-tagit-scrollView").append(emptyTips);
        return;
    }

    for(let item of data) {
        let cell = document.createElement("div");
        cell.id = "id-tagit-cell";
        $(cell).addClass("tagit-cell");

        //host
        let host = document.createElement("div");
        $(host).addClass("tagit-host");
        $(host).text(item.host);
        $(cell).append(host);

        //xpath
        let xpath = document.createElement("div");
        $(xpath).addClass("tagit-xpath");
        $(xpath).text(item.xpath);
        $(cell).append(xpath);

        //删除按钮
        let deleteButton = document.createElement("div");
        $(deleteButton).addClass("tagit-delete-button");
        if(isDark()) {
            $(deleteButton).css("background-image", `url("./images/icon_delete_dark.png")`);
        } else {
            $(deleteButton).css("background-image", `url("./images/icon_delete.png")`);
        }
        $(cell).append(deleteButton);

        //激活按钮
        let activeButton = document.createElement("div");
        $(activeButton).addClass("tagit-active-button");
        let image = "";
        if(isDark()) {
            if(item.isActive) {
                image = `url("./images/icon_pause_dark.png")`;
            } else {
                image = `url("./images/icon_play_dark.png")`;
            }
        } else {
            if(item.isActive) {
                image = `url("./images/icon_pause.png")`;
            } else {
                image = `url("./images/icon_play.png")`;
            }
        }
        
        $(activeButton).css("background-image", image);
        $(cell).append(activeButton);

        $("#id-tagit-scrollView").append(cell);

        //添加开关按钮点击事件
        $(activeButton).click(function (e) {
            e.preventDefault();
            handleTagitActive(e.target, item);
        });
        
        //点击删除按钮时间
        $(deleteButton).click(function (e) {
            e.preventDefault();
            handleDeleteTagitItem(cell, item);
        });
    }
}

function handleTagitActive(target,item) {
    let isActive = !item.isActive;
    item.isActive = isActive;

    if(isDark()) {
        if(isActive) {
            $(target).css("background-image", `url("./images/icon_pause_dark.png")`);
        } else {
            $(target).css("background-image", `url("./images/icon_play_dark.png")`);
        }
    } else {
        if(isActive) {
            $(target).css("background-image", `url("./images/icon_pause.png")`);
        } else {
            $(target).css("background-image", `url("./images/icon_play.png")`);
        }
    }

    browser.runtime.sendMessage({
        from: "popup",
        operate: "API_TAGIT_ACTIVE_CHANGE",
        tagitId: item.uuid,
        isActive: isActive,
    }, function (response) {
        
    });
}

function handleDeleteTagitItem(cell,item) {
    $(cell).remove();
    
    browser.runtime.sendMessage({
        from: "popup",
        operate: "API_REMOVE_TAGIT",
        tagitId: item.uuid
    }, function (response) {
        
    });
}

// 根据初始化暗黑模式
function handleInitDarkMode(darkModel) {
    if(!darkModel) {
        return;
    }
    
    //是否开启暗黑模式的网站域名
    let siteValue = 0;
    let disableSiteList = darkModel.disableSiteList;
    if(disableSiteList && disableSiteList.length > 0) {
        if(__hostname__ && __hostname__.length>0 && disableSiteList.includes(__hostname__)) {
            //禁止开启暗黑模式
            siteValue = 2;
        }
    }
    $("#id-scope-select").val(siteValue);
    let text = $("#id-scope-select").find("option:selected").text();
    $("#id-scope-text").text(text);
    
    //启用状态
    let enableValue = darkModel.enableValue;
    $("#id-enabled-select").val(enableValue);
    text = $("#id-enabled-select").find("option:selected").text();
    $("#id-enabled-text2").text(text);
    
    //图片明亮度
    let brightnesssValue = darkModel.brightnesssValue;
    $("#id-dimImages-select").val(brightnesssValue);
    text = $("#id-dimImages-select").find("option:selected").text();
    $("#id-dimImages-text").text(text);
}

// 暗黑模式
function handleDarkModeAction() {
    //是否允许当前网站开启暗黑模式
    $("#id-scope-select").change(function(e) {
        let val = $("#id-scope-select").val();
        let text = $("#id-scope-select").find("option:selected").text();
        $("#id-scope-text").text(text);

        handleDarkModeOptionAction(0);
    });
    
    //启用状态
    $("#id-enabled-select").change(function(e) {
        let val = $("#id-enabled-select").val();
        let text = $("#id-enabled-select").find("option:selected").text();
        $("#id-enabled-text2").text(text);
        
        handleDarkModeOptionAction(1);
    });

    //图片明亮度
    $("#id-dimImages-select").change(function(e) {
        let val = $("#id-dimImages-select").val();
        let text = $("#id-dimImages-select").find("option:selected").text();
        $("#id-dimImages-text").text(text);

        handleDarkModeOptionAction(2);
    });
}

//点击了暗黑模式中的各种选项
function handleDarkModeOptionAction(index) {
    //Auto: data={automation: "system"}
    //off: data={enabled: false, automation: ""}
    //On: data={enabled: true, automation: ""}

    let siteValue = $("#id-scope-select").val();
    siteValue = Number(siteValue);
    
    let enableValue = $("#id-enabled-select").val();
    enableValue = Number(enableValue);
    
    let brightnesssValue = $("#id-dimImages-select").val();
    brightnesssValue = Number(brightnesssValue);

    if(index === 0) {
        //是否允许当前网站开启暗黑模式
        let url = __url__;
        if(url == null || __darkModel__ == null) return;
        
        let lastSiteValue = 1;
        let disableSiteList = __darkModel__.disableSiteList;
        if(disableSiteList && disableSiteList.length > 0) {
            if(__hostname__ && __hostname__.length>0 && disableSiteList.includes(__hostname__)) {
                //禁止开启暗黑模式
                lastSiteValue = 2;
            }
        }
        
        //如果是相同的值，那么直接返回
        if(lastSiteValue === siteValue) return;
        
        const index =  __darkModel__.disableSiteList.indexOf(__hostname__);
        if (index < 0) {
            //禁止
            __darkModel__.disableSiteList.push(__hostname__);
        } else {
            //允许
            __darkModel__.disableSiteList.splice(index, 1);
        }
        
        browser.runtime.sendMessage({
            type: "ui-toggle-url",
            data: url
        });
    } else if(index === 1) {
        //启用状态
        if(enableValue === 2) {
            //开启
            browser.runtime.sendMessage({
                type: "ui-change-settings",
                data: {
                  enabled: true,
                  automation: ""
                }
            });
        } else if(enableValue === 3) {
            //关闭
            browser.runtime.sendMessage({
                type: "ui-change-settings",
                data: {
                  enabled: false,
                  automation: ""
                }
            });
        } else if(enableValue === 0 || enableValue === 1) {
            //跟随系统
//            let isColorSchemeDark = matchMedia('(prefers-color-scheme: dark)').matches;
            browser.runtime.sendMessage({
                type: "ui-change-settings",
                data: {
                  automation: "system"
                }
            });
        }
    } else if(index === 2) {
        //图片明亮度
        browser.runtime.sendMessage({
            type: "ui-set-theme",
            data: {
                imageBrightness: brightnesssValue,
            }
        });
    }
    

}

function initDarkMode(darkModel) {
    localizableDarkMode();
    //根据已选值初始化
    handleInitDarkMode(darkModel);
}

function localizableDarkMode() {
    let val = localizableKey("themeDefault");
    $("#id-enabled-default-text").text(val);
    
    val = localizableKey("themeDark");
    $("#id-enabled-on-text").text(val);
    
    val = localizableKey("themeAuto");
    $("#id-enabled-auto-text").text(val);
    $("#id-enabled-text2").text(val);
    
    val = localizableKey("themeLight");
    $("#id-enabled-off-text").text(val);
    
    val = localizableKey("dimImagesDefault");
    $("#id-dimImages-default-text").text(val);
    
    val = localizableKey("scopeDefault");
    $("#id-scope-default-text").text(val);
    
    val = localizableKey("scopeEnable");
    $("#id-scope-enable-text").text(val);
    
    val = localizableKey("scopeDisable");
    $("#id-scope-site-text").text(val);
    
    val = localizableKey("enabled");
    $("#id-enabled-span").text(val);
    
    val = localizableKey("theme");
    $("#id-theme-span").text(val);
    
    val = localizableKey("dimImages");
    $("#id-dimImages-span").text(val);
    
    val = localizableKey("scopeTips")
    $("#id-scope-tips-text").text(val);
}

function isDark() {
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
}

function scaleToPad() {
    if (/iPad/i.test(navigator.userAgent)) {
        $(document.body).css("min-height", "400px");
    }
    
    if(/Macintosh/i.test(navigator.userAgent)) {
        $(document.body).css("min-height", "400px");
    }
}

$(document).ready(function () {
    try {
        //iPad的高度加大一点
        scaleToPad();
        //底部工具栏切换
        setupObservers();
        //暗黑模式切换
        handleDarkModeAction();
        //一次性加载所有数据
        loadingAllData();
    } catch(error) {
        console.error(error)
    }
});



