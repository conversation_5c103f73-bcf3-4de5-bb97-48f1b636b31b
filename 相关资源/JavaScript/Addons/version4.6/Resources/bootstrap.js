! function() {
    ///包一层，否则会报错: SyntaxError: Can't create duplicate variable: 'SchemaDataExtractorJS'
    ///语言本地化
    function localizableKey(key) {
        let objs = {
            zh: {
                tagitNotSupport: "标记模式不支持该网址！"
            },
            en: {
                tagitNotSupport: "Not Support!"
            }
        }

        function _current() {
            let e = navigator.languages;
            if(e.length > 0) {
                e = navigator.languages[0];
            } else if(navigator.language) {
                e = navigator.language;
            } else if(navigator.userLanguage) {
                e = navigator.userLanguage;
            } else {
                e = "en";
            }
            e = (e = e.toLowerCase()).replace(/-/, "_");

            if(e.indexOf("zh") > -1) {
                return objs.zh;
            } else {
                return objs.en;
            }
        }
        
        let values = _current();
        return values[key];
    }

    /// toast
    function _showToast(msg) {
        const toast = $('<div class="addons_toast"></div>');
        $('body').append(toast);
        toast.css({
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          backgroundColor: '#303030',
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          padding: '12px',
          borderRadius: '15px',
          zIndex: '2147483647'
        });

        $(toast).text(msg);

        toast.fadeIn(250).delay(2000).fadeOut(250, function() {
          $(this).remove();
        });
    }
    
    /// 本地缓存帮助类
    class StorageHelper {
        // 保存数据
        static async setItem(key, value) {
            return new Promise((resolve, reject) => {
                let items = {};
                items[key] = value;
                chrome.storage.local.set(items, function() {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve();
                    }
                });
            });
        }

        // 读取数据
        static async getItem(key) {
            return new Promise((resolve, reject) => {
                chrome.storage.local.get([key], function(result) {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(result[key]);
                    }
                });
            });
        }

        //针对两层字典嵌套的情况，获取数据
        static async getSubItem(key, objectId, subKey) {
            try {
                let objectMap = await StorageHelper.getItem(key) || {};
                let objectData = objectMap[objectId] || {};

                return objectData[subKey];
            } catch (error) {
                console.error(error);
            }

            return undefined;
        }
        
        // 删除数据
        static async removeItem(key) {
            return new Promise((resolve, reject) => {
                chrome.storage.local.remove([key], function() {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve();
                    }
                });
            });
        }
        
        //针对两层字典嵌套的情况，删除数据
        static async removeSubItem(key, objectId, subKey) {
            try {
                let objectMap = await this.getItem(key) || {};
                let objectData = objectMap[objectId] || {};
                
                delete objectData[subKey];
                objectMap[objectId] = objectData;
                
                await this.setItem(key, objectMap);
            } catch(error) {
                console.error(error);
            }
        }
        
        // 增量更新数据
        static async updateItem(key, value) {
            try {
                const existingValue = await this.getItem(key) || {};
                const newValue = { ...existingValue, ...value };
                await this.setItem(key, newValue);
            } catch (error) {
                console.error(error);
            }
        }
        
        //针对两层字典嵌套的情况（例如脚本数据），增量更新数据
        static async updateSubItem(key, objectId, subKey, value) {
            try {
                let objectMap = await this.getItem(key) || {};
                let objectData = objectMap[objectId] || {};
        
                objectData[subKey] = value;
                objectMap[objectId] = objectData;

                await this.setItem(key, objectMap);
            } catch (error) {
                console.error(error);
            }
        }
        
        // 获取所有数据
        static async getAllItems() {
            return new Promise((resolve, reject) => {
                chrome.storage.local.get(null, function(items) {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(items);
                    }
                });
            });
        }

        // 清理所有数据
        static async clearAllItems() {
            return new Promise((resolve, reject) => {
                chrome.storage.local.clear(function() {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve();
                    }
                });
            });
        }
    }

    async function _enterTagit(sendResponse) {
        let config = await StorageHelper.getItem('addonsConfig');
        if(config == null || config.tagit == null) {
            sendResponse({
                response: false,
            });
            
            return;
        }
            
        if(config.tagit.isActive == false) {
            sendResponse({
                response: false,
            });
            
            return;
        }
        
        //版权检测
        if(_isCopyrightValid() == false) {
            let val = localizableKey("tagitNotSupport");
            _showToast(val);
            
            sendResponse({
                response: false,
            });
            
            return;
        }
        
        window.__firefox__.tagitHelper.showTagitView();

        sendResponse({
            response: true,
        });
    }
    
    // 监听
    browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
        let operate = request.operate;
        if("API_ENTER_TAGIT" == operate) {
            ///没有激活,直接返回
            _enterTagit(sendResponse);
        } else if("API_GET_Script_Menu" == operate) {
            //获取脚本菜单项
            let result = CommandHelper.getScriptMenuArray(request);
            sendResponse(result);
        } else if("API_Run_Command" == operate) {
            //执行脚本菜单项
            let injectMode = request.injectMode;
            if(injectMode != 1) return;
            
            //这里是page模式
            
            window.postMessage(request);
            
            sendResponse({success: true});
        } else if("API_Register_Command" == operate) {
            //从content传过来的injectMode为0/2, 这里默认选0
            let data = {
                scriptId: request.scriptId,
                name: request.name || "",
                randomId: request.randomId,
                injectMode: 0,
            }
            CommandHelper.registerCommand(data);
            
            sendResponse({success: true});
        } else if("API_UnRegister_Command" == operate) {
            let data = {
                scriptId: request.scriptId,
                randomId: request.randomId
            };
            CommandHelper.unRegisterCommand(data);
            
            sendResponse({success: true});
        } else if("API_GET_Dark_Status" == operate) {
            //获取暗黑模式状态
            let isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            sendResponse({isDark: isDark});
        }

        return true;
    });

    // 有2个地方，版权检测
    function _isCopyrightValid() {
        let hosts = ["youtube.com","youku.com","tudou.com","iqiyi.com","v.qq.com",
                     "mgtv.com","sogou.com","sogoucdn.com","wap.sogou.com"];

        let url = window.location.href;
        let host = new URL(url).host;

        for(let item of hosts) {
            if(host.indexOf(item) >= 0) {
                return false;
            }
        }
        
        return true;
    }

    function isObjectNull(obj) {
        return obj == null || Object.keys(obj).length === 0;
    }

    /// 油猴脚本相关功能 -- Start
    
    //油猴脚本是否匹配当前网站
    function isMatchTheCurrentURL(userScripts, urlString) {
        
        function matchRule(regex, str) {
            var escapeRegex = (str) => str.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
            return new RegExp("^" + regex.split("*").map(escapeRegex).join(".*") + "$").test(str);
        }
        
        let isMatched = false;
        
        if(isObjectNull(userScripts.includes) == false) {
            for(let regex of userScripts.includes) {
                if(regex == '*' || matchRule(regex, urlString)) {
                    isMatched = true;
                    break;
                }
            }
        }
        
        if(isObjectNull(userScripts.matches) == false) {
            for(let regex of userScripts.matches) {
                if(regex == '*' || matchRule(regex, urlString)) {
                    isMatched = true;
                    break;
                }
            }
        }
        
        if(isObjectNull(userScripts.excludes) == false) {
            for(let regex of userScripts.excludes) {
                if(regex == '*' || matchRule(regex, urlString)) {
                    isMatched = false;
                    break;
                }
            }
        }
        
        return isMatched;
    }

    async function listValues(e) {
        try {
            let scriptId = e.data.scriptId;
            
            //获取所有数据
            browser.runtime.sendMessage({
                from: "bootstrap",
                operate: "API_LIST_VALUE",
                scriptId: scriptId,
            }, function (response) {
                window.postMessage({
                    pid: e.data.pid,
                    operate: "Response_API_LIST_VALUE",
                   response: response,
                });
            });
        } catch(error) {
            console.error(error);
        }
    }

    async function setValue(e) {
        let isAsync = e.data.isAsync;
        let responseOperate = "Response_API_SET_VALUE";
        if (isAsync) {
            responseOperate = "Response_API_SET_VALUE_ASYNC";
        }
        
        let data = e.data;
        let uuid = data.uuid;
        let key = data.key;
        let value = data.value;
        
        try {
            //更新数据
            browser.runtime.sendMessage({
                from: "bootstrap",
                operate: "API_SET_VALUE",
                uuid: uuid,
                key: key,
                value: value,
            }, function (response) {
                window.postMessage({
                    pid: e.data.pid,
                    operate: responseOperate,
                    response: {},
                });
                
                //发送值变化通知
                if(response.isValueChanged) {
                    window.postMessage(response);
                }
            });
        } catch(error) {
            console.error(error);
        }
    }

    async function getValue(e) {
        let data = e.data;
        let uuid = data.uuid;
        let key = data.key;
        
        try {
            browser.runtime.sendMessage({
                from: "bootstrap",
                operate: "API_GET_VALUE",
                uuid: uuid,
                key: key,
            }, function (response) {
                window.postMessage({
                    pid: e.data.pid,
                    operate: "Response_API_GET_VALUE",
                    response: response,
                });
            });
        } catch(error) {
            console.error(error);
        }
    }

    async function deleteValue(e) {
        let isAsync = e.data.isAsync;
        let responseOperate = "Response_API_DELETE_VALUE";
        if (isAsync) {
            responseOperate = "Response_API_DELETE_VALUE_ASYNC";
        }
        
        let data = e.data;
        let uuid = data.uuid;
        let key = data.key;
        
        try {
            browser.runtime.sendMessage({
                from: "bootstrap",
                operate: "API_DELETE_VALUE",
                uuid: uuid,
                key: key,
                value: value,
            }, function (response) {
                window.postMessage({
                    pid: e.data.pid,
                    operate: responseOperate,
                    response: {},
                });
                
                //发送值变化通知
                if(response.isValueChanged) {
                    window.postMessage(response);
                }
            });
        } catch(error) {
            console.error(error);
        }
    }

    /// 油猴脚本Menu管理类
    var __menuCommandModel__ = {};
    class CommandHelper {
        static registerCommand(data) {
            let scriptId = data.scriptId;
            let name = data.name;
            let randomId = data.randomId;
            let injectMode = data.injectMode;
            
            chrome.runtime.sendMessage({ operate: "API_GET_TAB_ID" }, function(response) {
                var tabId = response.tabId;

                let tabMenuData = __menuCommandModel__[tabId] || {};
                let scriptMenuData = tabMenuData[scriptId] || {};
                            
                //排重
                for(let key in scriptMenuData) {
                    let menuItem = scriptMenuData[key];
                    
                    if(name == menuItem.name) {
                        //删除旧数据
                        delete scriptMenuData[menuItem.randomId];
                    }
                }
                
                //添加新数据
                let menuData = scriptMenuData[randomId] || {};
                menuData.uuid = scriptId;
                menuData.name = name;
                menuData.randomId = randomId;
                menuData.injectMode = injectMode;
                scriptMenuData[randomId] = menuData;
                
                tabMenuData[scriptId] = scriptMenuData;
                __menuCommandModel__[tabId] = tabMenuData;
            });
        }
        
        static unRegisterCommand(data) {
            let scriptId = data.scriptId;
            let randomId = data.randomId;
            
            chrome.runtime.sendMessage({ operate: "API_GET_TAB_ID" }, function(response) {
                var tabId = response.tabId;

                let tabMenuData = __menuCommandModel__[tabId] || {};
                let scriptMenuData = tabMenuData[scriptId] || {};
                
                delete scriptMenuData[randomId];
                
                tabMenuData[scriptId] = scriptMenuData;
                __menuCommandModel__[tabId] = tabMenuData;
            });
        }
        
        static getScriptMenuArray(data) {
            let scriptId = data.scriptId;
            let tabId = data.tabId;
            
            let tabMenuData = __menuCommandModel__[tabId] || {};
            let scriptMenuData = tabMenuData[scriptId] || {};
            
            let array = [];
            for(let key in scriptMenuData) {
                let menuItem = scriptMenuData[key];
                array.push(menuItem);
            }
            
            let menuCommandOutModel = {};
            menuCommandOutModel["scriptId"] = scriptId;
            menuCommandOutModel["array"] = array;
            
            return {
                type: "API_GET_Script_Menu",
                body: menuCommandOutModel
            }
        }
    };
    
    //串行控制器，让数据可以串行访问
    let promiseChain = Promise.resolve();

    //Page Script
    window.addEventListener("message", function (e) {
        if(!e || !e.data) return;
        
        const pid = e.data.pid;
        const operate = e.data.operate;
        
        if(operate === "API_LIST_VALUE") {
            promiseChain = promiseChain.then(() => listValues(e));
            
        } else if(operate === "API_GET_RESOURCE_URLS") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                        pid: pid,
                    operate: "Response_API_GET_RESOURCE_URLS",
                   response: response,
                });
            });
        } else if(operate === "API_CLOSE_TAB") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_CLOSE_TAB",
                    response: response,
                });
            });
        } else if(operate === "API_OPEN_TAB") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_OPEN_TAB",
                    response: response,
                });
            });
        } else if(operate === "API_SET_VALUE") {
            promiseChain = promiseChain.then(() => setValue(e));
        } else if(operate === "API_GET_VALUE") {
            promiseChain = promiseChain.then(() => getValue(e));
        } else if(operate === "API_DELETE_VALUE") {
            promiseChain = promiseChain.then(() => deleteValue(e));
        } else if(operate === "API_ADD_STYLE") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_ADD_STYLE",
                    response: response,
                });
            });
        } else if(operate === "API_GET_TAB") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_GET_TAB",
                    response: response,
                });
            });
        } else if(operate === "API_SAVE_TAB") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_SAVE_TAB",
                    response: response,
                });
            });
        } else if(operate === "API_SET_CLIPBOARD") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_SET_CLIPBOARD",
                    response: response,
                });
            });
        } else if(operate === "API_SET_CLIPBOARD") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_SET_CLIPBOARD",
                    response: response,
                });
            });
        } else if(operate === "API_Request") {
            browser.runtime.sendMessage(e.data, function (response) {
                window.postMessage({
                    pid: pid,
                    operate: "Response_API_Request",
                    response: response,
                });
            });
        } else if(operate === "API_Request_Abort") {
            browser.runtime.sendMessage(e.data, function (response) {
            });
        } else if(operate === "API_Register_Command") {
            //从page传过来的injectMode为1
            let data = {
                scriptId: e.data.scriptId,
                name: e.data.name || "",
                randomId: e.data.randomId,
                injectMode: 1,
            }

            CommandHelper.registerCommand(data);
        } else if(operate === "API_UnRegister_Command") {
            CommandHelper.unRegisterCommand(e.data);
        }
    });
    
    /// 油猴脚本相关功能 -- End

    /// 标记模式相关功能 -- Start
    let needObserveNode = false;
    async function handleTagitAction(isActive) {
        if(!isActive) return;
        
        window.addEventListener("load", function (event) {
            hideTagitElements();
        });
        
//        window.addEventListener("DOMNodeInserted", function (event) {
//            hideTagitElements();
//        });
        function setupThrottledDOMNodeInsertionObserver(targetElement = document.body, messageInterval = 2000) {
            let lastMessageTime = 0; // 记录上次发送消息的时间
            let observer = null; // MutationObserver 实例

            const elementToObserve = typeof targetElement === 'string' ? document.querySelector(targetElement) : targetElement;

            if (!elementToObserve) {
                return;
            }

            // MutationObserver 回调函数
            const observerCallback = function(mutations) {
                const currentTime = Date.now();

                // 防止频繁调用
                if (!needObserveNode) return;
                // 节流判断：如果距离上次发送消息的时间小于设定的间隔，则不发送
                if (currentTime - lastMessageTime < messageInterval) {
                    return;
                }

                // 检查是否有节点被添加
                let nodeAdded = false;
                for (let i = 0; i < mutations.length; i++) {
                    if (mutations[i].type === 'childList' && mutations[i].addedNodes.length > 0) {
                        nodeAdded = true;
                        break; // 只要发现有节点添加就足够了
                    }
                }

                if (nodeAdded) {
                    // 更新上次发送消息的时间
                    lastMessageTime = currentTime;

                    hideTagitElements();
                }
            };

            // 创建 MutationObserver 实例
            observer = new MutationObserver(observerCallback);

            // 配置观察选项：监听子节点的变化以及子树中的变化
            const config = { childList: true, subtree: true };

            // 开始观察指定元素的变化
            observer.observe(elementToObserve, config);
            
            // 返回 observer 实例，以便外部可以调用 disconnect() 来停止观察
            return observer;
        }
        setupThrottledDOMNodeInsertionObserver();
        
        function onReady(fn) {
            if(document.readyState === "complete" || document.readyState == "interactive") {
                fn();
            } else {
                document.addEventListener("DOMContentLoaded", fn);
            }
        }
        
        onReady(function() {
            hideTagitElements();
        });
            
        async function hideTagitElements() {
            let currentHost = window.location.hostname;
                    
            browser.runtime.sendMessage({
                from: "bootstrap",
                operate: "API_GET_TAGIT_DATA"
            }, (response) => {
                let tagitData = response;
                if(isObjectNull(tagitData)) return;
                //遍历undefined会报错
                for(let item of tagitData) {
                    //查看是否匹配当前网站
                    if(!item.host) continue;
                    if(currentHost.indexOf(item.host) == -1) continue;
                    
                    //需要监听标记模式
                    needObserveNode = true;
                    
                    let xpath = item.xpath;
                    let isActive = item.isActive;
                    if(isActive) {
                        try {
                            window.__firefox__.tagitHelper.hideElementByXpath(xpath);
                        } catch (error) {
                            console.error(error);
                        }
                    }
                }
            });
        }
    }
    /// 标记模式相关功能 -- End

    
    /// 暗黑模式相关功能 -- Start
    
    function isDark() {
        return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    }

    /// 暗黑模式相关功能 -- End
    
    /// 页面更新逻辑
    let lastWindowUrl = window.location.href;
    document.addEventListener('visibilitychange', function () {
        // 用户离开了当前页面
        if (document.visibilityState === 'hidden') {
            //页面不可见，切换到APP后台或者切换到其他页面
        }
    
        // 用户打开或回到页面
        if (document.visibilityState === 'visible') {
            //页面可见，切换到APP前台或者进入页面
    
            let currentWindowUrl = window.location.href;
            if(lastWindowUrl != currentWindowUrl) {
                /// 同一个页面，打开新链接
            } else {
                /// 前后台切换
                loadDataFromNative(true);
                //如果Native数据发生变化，那么则会在用户下一次刷新时生效(已写入local)
            }
    
            lastWindowUrl = currentWindowUrl;
        }
    });

    /// 从扩展APP重新加载数据(只负责更新数据到local)
    async function loadDataFromNative(isFromBackgroundChange) {
        //1、处理数据
        //2、将Native数据和storage.local比较(updateTime)
        //3、storage.local同步Native数据，包括增删改操作
        
        //4、数据的存放问题
        //4.1、各种开关信息对应ExtConfigModel，并保存到addonsConfig字段
        //4.2、安装提示脚本，字符串，保存到js_install字段
        //4.3、标记模式脚本，字符串，保存到js_tagit字段
        //4.4、智能搜索，待处理
        //4.5、用户油猴脚本，耗时操作放在bootstrap处理，因此只保留原始数据
        
        browser.runtime.sendMessage({
            from: "bootstrap",
            operate: "API_ReloadData",
            status: '0',
        }, (response) => {
            if(isFromBackgroundChange) {
                //前后台切换
                browser.runtime.sendMessage({
                    from: "bootstrap",
                    operate: "API_Background_Change",
                }, (response) => {
                });
            }
        });
    }

    /// 从storage.local重新加载数据
    async function loadDataFromLocal() {
        let config, js_installHelper, js_tagit;

        //清除旧数据
        try {
            StorageHelper.removeItem('userScriptData');
            StorageHelper.removeItem('aiSearchData');
            StorageHelper.removeItem('tagitData');
            StorageHelper.removeItem('valueChangedData');
        } catch (error) {
            console.error(error);
        }

        // 获取开关信息
        try {
            config = await StorageHelper.getItem('addonsConfig') || {};
        } catch (error) {
            console.error("Failed to get addonsConfig:", error);
            config = {};
        }
                
        //初始化，等待数据加载
        if(isObjectNull(config)) return;
        
        //需要注入的脚本
        let injectScripts = [];
        
        //安装提示脚本
        try {
            js_installHelper = await StorageHelper.getItem('js_install');
        } catch (error) {
            console.error(error);
        }
        
        if(isObjectNull(js_installHelper) == false) {
            injectScripts.push(js_installHelper);
        }
        
        //标记模式脚本
        let isTagitActive = null;
        if(config.tagit != null && config.tagit.isActive) {
            isTagitActive = true;
        } else {
            isTagitActive = false;
        }
        
        //不管是否开启，都需要添加到脚本中，否则关闭再打开，会没有反应
        try {
            js_tagit = await StorageHelper.getItem('js_tagit');
        } catch(error) {
            console.error(error);
        }

        if(isObjectNull(js_tagit) == false) {
            injectScripts.push(js_tagit);
        }
               
        //添加系统脚本
        for(let item of injectScripts) {
            let isActive = item.isActive;
            let executorJs = item.executorJs;
            let allframe = !item.noframes;
            let runAt = item.runAt;
            let injectMode = item.injectMode;

            if(injectMode == 0 || injectMode == 2) {
                browser.runtime.sendMessage({
                    from: "bootstrap",
                    operate: "API_INJECT_SCRIPT",
                    url: location.href,
                    executorJs: executorJs,
                    allframe: allframe,
                    runAt: runAt,
                });
            } else if(injectMode == 1) {
                let head = document.head || document.body;
                let script = document.createElement("script");
                script.type = "text/javascript";
                script.id = item.uuid;
                script.text = executorJs;

                head.appendChild(script);
            }
        }
        
        if(config.tampermonkey != null && config.tampermonkey.isActive) {
            //添加用户的脚本
            browser.runtime.sendMessage({
                from: "bootstrap",
                operate: "API_GET_USERSCRIPT_DATA"
            }, (response) => {
                let userScriptData = response;
                //遍历undefined会报错
                let urlString = window.location.href;
                let injectScripts = [];
                if(isObjectNull(userScriptData) == false) {
                    for(let item of userScriptData) {
                        if(item.isActive) {
                            //判断是否匹配当前网站
                            if(isMatchTheCurrentURL(item, urlString)) {
                                injectScripts.push(item);
                            }
                        }
                    }
                }
                
                for(let item of injectScripts) {
                    let isActive = item.isActive;
                    let executorJs = item.executorJs;
                    let allframe = !item.noframes;
                    let runAt = item.runAt;
                    let injectMode = item.injectMode;

                    if(injectMode == 0 || injectMode == 2) {
                        browser.runtime.sendMessage({
                            from: "bootstrap",
                            operate: "API_INJECT_SCRIPT",
                            url: location.href,
                            executorJs: executorJs,
                            allframe: allframe,
                            runAt: runAt,
                        });
                    } else if(injectMode == 1) {
                        let head = document.head || document.body;
                        let script = document.createElement("script");
                        script.type = "text/javascript";
                        script.id = item.uuid;
                        script.text = executorJs;

                        head.appendChild(script);
                    }
                }
            });
        }
        
        //暗黑模式,暗黑模式的总开关isActive
        if(config.darkMode && config.darkMode.isActive == false) {
            //关闭了暗黑模式
            //Auto: data={automation: "system"}
            //off: data={enabled: false, automation: ""}
            //On: data={enabled: true, automation: ""}
            browser.runtime.sendMessage({
                type: "ui-change-settings",
                data: {
                  enabled: false,
                  automation: ""
                }
            });
        }
        
        setTimeout(function() {
            //处理标记模式
            handleTagitAction(isTagitActive);
        }, 0);
    }
    
    //bootstrap加载了
    console.log("bootstrap加载了...");
    loadDataFromLocal();
    
    setTimeout(function(){
        loadDataFromNative(false);
    }, 0);
    
} ();


!function () {
    function localizableKey(key) {
        let objs = {
            zh: {
                expand: "扩大",
                narrow: "缩小",
                block: "屏蔽",
                withdraw: "撤回",
                exit: "退出标记模式",
            },
            en: {
                expand: "Expand",
                narrow: "Narrow",
                block: "Block",
                withdraw: "GoBck",
                exit: "Exit",
            }
        }
        
        function _current() {
            let e = navigator.languages;
            if(e.length > 0) {
                e = navigator.languages[0];
            } else if(navigator.language) {
                e = navigator.language;
            } else if(navigator.userLanguage) {
                e = navigator.userLanguage;
            } else {
                e = "en";
            }
            e = (e = e.toLowerCase()).replace(/-/, "_");
            
            if(e.indexOf("zh") > -1) {
                return objs.zh;
            } else {
                return objs.en;
            }
        }
        
        let values = _current();
        return values[key];
    }

    var _key = "__hide_element_style_key__";
    var _mask = null;
    var _status = "0";
    var _container = null;
    var _container_mask = null;
    var _currentNode = null;
    var _lastNode = null;
    var _currentSelectXpath = null;
    
    //创建遮罩mask
    function createMask(e) {
        var n = document.createElement("div");
        n.style.pointerEvents = "none",
        n.style.top = "0px",
        n.style.left = "0px",
        n.style.position = "absolute",
        n.style.opacity = "0.6"
        
        //zIndex最大值是32位二进制最大有符号数，2147483647
        n.style.zIndex = "2147483647";
        n.id = "__mask__",
        
        //标记是addons的内部元素,防止被牛皮癣广告脚本屏蔽
        $(n).attr("__internal__addons__", true);
        
        document.body.appendChild(n);
        for (var i = new Array, a = e.getClientRects(), r = 0; r != a.length; r++) {
            i.push(a[r]);
        }
        
        for (var l = e.children, r = 0; r < l.length; r++) {
            for (var d = l[r].getClientRects(), s = 0; s != d.length; s++) {
                i.push(d[s]);
            }
        }
        
        for (r = 0; r != i.length; r++) {
            var c = i[r],
            u = document.createElement("div"),
            m = document.documentElement.scrollTop || document.body.scrollTop,
            f = document.documentElement.scrollLeft || document.body.scrollLeft,
            m = c.top + m - 2.5,
            f = c.left + f - 2.5;
            u.style.top = m + "px",
            u.style.left = f + "px",
            u.style.width = c.width + 5 + "px",
            u.style.height = c.height + 5 + "px",
            u.style.position = "absolute",
            u.style.backgroundColor = "#2676fe",
            u.style.borderRadius = "2px",
            u.style.padding = "2.5px",
            u.style.pointerEvents = "none",
            n.appendChild(u)
        }
        
        //保证在最上面
        if(_container) {
            document.body.removeChild(_container);
            _container = null;
        }
        
        if(_container_mask) {
            document.body.removeChild(_container_mask);
            _container_mask = null;
        }
        
        showTagitView();
        
        return n
    }
    
    //移除遮罩mask
    function removeMask() {
        if(_mask) {
            document.body.removeChild(_mask);
            _mask = null;
        }
    }
    
    //取消遮罩
    function cancelElementMask() {
        removeMask();
    }
    
    //根据xpath获取elem
    function getElemOfXpath(Xpath) {
        if (0 == Xpath.length) return null;
        var elem = document.evaluate(Xpath, document.documentElement, null, XPathResult.ANY_TYPE, null).iterateNext();
        return elem;
    }
    
    //获取elem元素的xpath路径
    function getXpathOfElement(elem) {
        if (!elem) return "";
        if (elem.nodeName == 'BODY') {
            return '/html/' + elem.tagName.toLowerCase();
        }
        let ix = 1;
        if(elem && elem.parentNode && elem.parentNode.childNodes.length > 0) {
            let siblings = elem.parentNode.childNodes;
            for (let i = 0, l = siblings.length; i < l; i++) {
                let sibling = siblings[i];
                if (sibling == elem) {
                    return getXpathOfElement(elem.parentNode) + '/' + elem.tagName.toLowerCase() + '[' + (ix) + ']';
                } else if (sibling.nodeType == 1 && sibling.tagName == elem.tagName) {
                    ix++;
                }
            }
        }
        
        return "";
    }
    
    //隐藏_currentNode, 并返回Xpath
    function hideTheCurrentElement() {
        if(_currentNode == null) return null;
        
        removeMask();
        _mask = createMask(_currentNode);
        
        return getXpathOfElement(_currentNode);
    }
    
    //根据指定的(x,y)坐标值隐藏节点
    function showElementMaskWithPoint(x,y) {
        let elements = document.elementsFromPoint(x,y);
        for(let elem of elements) {
            if(elem.id != "id-tagit-mask") {
                _currentNode = elem;
                break;
            }
        }
        
        removeMask();
        _mask = createMask(_currentNode);
        
        let xpath = null;
        xpath = getXpathOfElement(_currentNode);

        _currentSelectXpath = xpath;
    }
    
    //显示遮罩父节点，扩大区域，并返回Xpath
    function showParentElementMask() {
        if(_currentNode && _currentNode.parentNode) {
            _lastNode = _currentNode;
            _currentNode = _currentNode.parentNode;
            
            let xpath = hideTheCurrentElement();
    
            _currentSelectXpath = xpath;
        }
    }
    
    //显示遮罩指定子节点，缩小区域
    function showTheChildElementMask() {
        if(_currentNode && _currentNode.children && _currentNode.children.length > 0) {
            var exists = !1;
            if(_lastNode) {
                for(var i=0;i<_currentNode.children.length;i++) {
                    if(_currentNode.children[i] === _lastNode) {
                        _currentNode = _lastNode;
                        exists = !0;
                        break;
                    }
                }
            }
            
            let xpath = null;
            if(exists) {
                xpath = hideTheCurrentElement();
            } else {
                _currentNode = _currentNode.children[0];
                xpath = hideTheCurrentElement();
            }
        
            _currentSelectXpath = xpath;
        }
    }
    
    //document添加hide的css style
    function appendHideStyleIfNeed() {
        if(document.head && !document.getElementById(_key)) {
            var node = document.createElement("style");
            node.id = _key;
            node.innerText = "." + _key + "{display:none !important;height: 0px !important;line-height: 0px !important;}"
            document.head.appendChild(node);
        }
    }
    
    //隐藏元素
    function hideElement(elem) {
        if(elem == null) return;
        
        appendHideStyleIfNeed();
        
        var t = elem.getAttribute("style") || "";
        var index = t.indexOf("display:none !important;height: 0px !important;line-height: 0px !important;");
        if(index == -1) {
            elem.setAttribute("style", t + "display:none !important;height: 0px !important;line-height: 0px !important;");
            if(elem.classList.contains(_key) == false) {
                elem.classList.add(_key)
            }
        }
    }
    
    //根据xpath隐藏元素
    function hideElementByXpath(Xpath) {
        var elem = getElemOfXpath(Xpath);
        
        if(elem) {
            removeMask();
            hideElement(elem);
        }
    }
    
    //恢复元素显示，撤销隐藏
    function showElementByXpath(Xpath) {
        removeMask();
        
        var elem = getElemOfXpath(Xpath);
        if(elem) {
            if(elem.classList.contains(_key)) {
                elem.classList.remove(_key);
                
                var _style = elem.getAttribute("style");
                elem.setAttribute("style", _style.replace("display:none !important;height: 0px !important;line-height: 0px !important;", ""));
                
                _currentNode = elem;
            }
        }
    }
    
    //显示标记模式操作界面
    function showTagitView() {
        let mask = document.createElement("div");
        mask.id = "id-tagit-mask";
        $(mask).addClass("__tagit-mask");
        
        _container_mask = mask;
        
        //标记是addons的内部元素,防止被牛皮癣广告脚本屏蔽
        $(mask).attr("__internal__addons__", true);
        
        document.body.appendChild(mask);
        //获取点击坐标
        $(mask).click(function (e) {
            let touchX = event.pageX - window.scrollX;
            let touchY = event.pageY - window.scrollY;
            
            showElementMaskWithPoint(touchX, touchY);
        });
        

        let container = document.createElement("div");
        $(container).addClass("__tagit-container");
        if(_status == "1") {
            $(container).css("bottom", "40%");
        } else if(_status == "1") {
            $(container).css("bottom", "5%");
        }
        
        //标记是addons的内部元素,防止被牛皮癣广告脚本屏蔽
        $(container).attr("__internal__addons__", true);
        
        document.body.appendChild(container);
        
        _container = container;
        
        let buttons = document.createElement("div");
        $(buttons).addClass("__tagit-buttons");
        container.appendChild(buttons);
        
        let val0 = localizableKey("expand");
        let val1 = localizableKey("narrow");
        let val2 = localizableKey("block");
        let val3 = localizableKey("withdraw");
        let list = [val0, val1, val2, val3];
        for(let index in list) {
            let text = list[index];
            
            let button = document.createElement("div");
            $(button).addClass("__tagit-button");
            $(button).text(text);
            buttons.appendChild(button);
            
            $(button).click(function (e) {
                e.preventDefault();
                
                if(index == 0) {
                    //扩大
                    showParentElementMask();
                } else if(index == 1) {
                    //缩小
                    showTheChildElementMask();
                } else if(index == 2) {
                    //屏蔽
                    hideElementByXpath(_currentSelectXpath);
                    
                    browser.runtime.sendMessage({
                        from: "Tagit",
                        operate: "API_TAGIT_ACTION",
                        xpath: _currentSelectXpath,
                        url: window.location.href,
                        status: "0",
                    });
                } else if(index == 3) {
                    //撤回
                    browser.runtime.sendMessage({
                        from: "Tagit",
                        operate: "API_TAGIT_ACTION",
                        xpath: "",
                        url: window.location.href,
                        status: "1",
                    }, function (response) {
                        let xpath = response.body;
                        if (xpath != null && xpath != undefined) {
                            showElementByXpath(xpath);
                        }
                    });
                }
            });
        }
        
        let bottomContainer = document.createElement("div");
        $(bottomContainer).addClass("__tagit-bottom-container");
        container.appendChild(bottomContainer);
        
        let exitButton = document.createElement("div");
        $(exitButton).addClass("__tagit-exit-button");
        
        let val = localizableKey("exit");
        $(exitButton).text(val);
        bottomContainer.appendChild(exitButton);
        //退出标记模式
        $(exitButton).click(function (e) {
            e.preventDefault();
            
            $(mask).fadeOut(300, function() {
                $(mask).remove();
            });
            
            $(container).fadeOut(300, function() {
                $(container).remove();
            });
            
            window.location.reload();
        });
        
        let updownButton = document.createElement("div");
        $(updownButton).addClass("__tagit-updown_button");
        $(updownButton).attr("status", _status);
        bottomContainer.appendChild(updownButton);
        //初始化
        if(_status == "0") {
            //动画向上
            let logoUrl = browser.runtime.getURL("images/tagit_up.png");
            let logoURL = `url("` + logoUrl + `")`;
            $(updownButton).css("background-image", logoURL);
        } else if(_status == "1") {
            //动画向下
            let logoUrl = browser.runtime.getURL("images/tagit_down.png");
            let logoURL = `url("` + logoUrl + `")`;
            $(updownButton).css("background-image", logoURL);
        }
        
        //点击事件
        $(updownButton).click(function (e) {
            e.preventDefault();
            let status = $(this).attr("status");
            if(status == "0") {
                //动画向上
                _status = "1"
                $(updownButton).attr("status", "1");
                $(container).animate({bottom:"40%"}, 300, function() {
                    let logoUrl = browser.runtime.getURL("images/tagit_down.png");
                    let logoURL = `url("` + logoUrl + `")`;
                    $(updownButton).css("background-image", logoURL);
                });
            } else if(status == "1") {
                //动画向下
                _status = "0"
                $(updownButton).attr("status", "0");
                $(container).animate({bottom:"5%"}, 300, function() {
                    let logoUrl = browser.runtime.getURL("images/tagit_up.png");
                    let logoURL = `url("` + logoUrl + `")`;
                    $(updownButton).css("background-image", logoURL);
                });
            }
        });
    }
    
    Object.defineProperty(window.__firefox__, 'tagitHelper', {
      enumerable: false,
      configurable: false,
      writable: false,
        value: {
            hideElementByXpath: hideElementByXpath,
            showElementByXpath: showElementByXpath,
            showTagitView: showTagitView,
        }
    });
}();
