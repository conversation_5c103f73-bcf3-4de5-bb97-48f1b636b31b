*

INVERT
.jfk-bubble.gtx-bubble
.captcheck_answer_label > input + img
span#closed_text > img[src^="https://www.gstatic.com/images/branding/googlelogo"]
span[data-href^="https://www.hcaptcha.com/"] > #icon
#bit-notification-bar-iframe
::-webkit-calendar-picker-indicator

CSS
.vimvixen-hint {
    background-color: ${#ffd76e} !important;
    border-color: ${#c59d00} !important;
    color: ${#302505} !important;
}
::placeholder {
    opacity: 0.5 !important;
}
a[href="https://coinmarketcap.com/"] > svg[width="94"][height="16"] > path {
    fill: var(--darkreader-neutral-text) !important;
}
#edge-translate-panel-body,
.MuiTypography-body1 {
    color: var(--darkreader-neutral-text) !important;
}
gr-main-header {
    background-color: ${lightblue} !important;
}

IGNORE INLINE STYLE
.sr-wrapper *
.sr-reader *
.diigoHighlight

================================

*.bbb.*

CSS
div[class^='scrollableList'] {
     background: none !important;
     box-shadow: none !important;
}

================================

*.screenconnect.com

CSS
.OuterPanel .MainPanel .MasterPanel .MasterListContainer ul li.HasChildren > div > p {
    filter: brightness(100%) !important;
}

================================

*.stackexchange.com
askubuntu.com
mathoverflow.net
serverfault.com
stackapps.com
stackexchange.com
stackoverflow.com
superuser.com

INVERT
._glyph
.favicon-mathoverflow
.favicon-mathoverflowmeta
.favicon-stackoverflowmeta
.h-auto[alt="Academia"]
.h-auto[alt="Anime & Manga"]
.h-auto[alt="Ask Different"]
.h-auto[alt="Aviation"]
.h-auto[alt="Code Review"]
.h-auto[alt="Electrical Engineering"]
.h-auto[alt="English Language Learners"]
.h-auto[alt="Japanese Language"]
.h-auto[alt="MathOverflow"]
.h-auto[alt="Mathematics"]
.h-auto[alt="Server Fault"]
.h-auto[alt="Skeptics"]
.h-auto[alt="Software Engineering"]
.h-auto[alt="Stack Apps"]
.h-auto[alt="Super User"]
.h-auto[alt="The Workplace"]
.h-auto[alt="Theoretical Computer Science"]
.h-auto[alt="Unix & Linux"]
.h-auto[alt="Web Applications"]
a.js-gps-track::before
img[alt="The Stack Exchange Network"]

CSS
body {
    background-image: none !important;
}
.profile-cards--graph {
    background-image: repeating-linear-gradient(0deg, transparent, transparent 13px, ${#e4e6e8} -13px, ${#e4e6e8} 21px) !important;
}
.c-pointer {
    color: grey;
}
.js-accepted-answer-indicator.fc-green-500 {
    color: var(--green) !important;
}
#newuser-box {
    background-color: ${#FFF8DC} !important;
}

IGNORE INLINE STYLE
.chess-replayer-board td

================================

*.ubereats.com

INVERT
img[alt*='Home']
div[class^='c5'] .gm-style
#main-content > h1 + div
#wrapper > div:nth-of-type(2)
#wrapper > div:nth-of-type(2) > header
#wrapper > div:nth-of-type(2) > footer
#wrapper > div:nth-of-type(2) > #main-content > div:first-child

================================

*.vultr.com

CSS
.svg-banner-shape {
    fill: var(--darkreader-neutral-background) !important;
}

IGNORE INLINE STYLE
.icon-ui *
.icon-user *
.icon-sm *
.svg-logo *
.svg-illustration *
.svg-product *
.svg-shape *

================================

01net.com

CSS
html, body {
    color: ${#090702} !important;
}

================================

10fastfingers.com

CSS
#speedtest-main .hide-time {
    color: transparent !important;
}
#inputfield {
    background: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text) !important;
}
body,
.container-modified > .row,
#practice-main,
#top1000-index-container,
#text-practice,
#content-bg {
    background: var(--darkreader-neutral-background) !important;
}
#main-content-trenner {
    background: var(--darkreader-neutral-background) !important;
    border-bottom: 1px solid rgb(151, 141, 127) !important;
}

================================

123mathe.de

INVERT
.entry-content img

CSS
body,
.post-inner,
ul.sub-menu {
    background-color: var(--darkreader-neutral-background) !important;
}
ul.sub-menu::after {
    border-bottom-color: var(--darkreader-neutral-background) !important;                            
}
.progress-wrap {
    box-shadow: var(--darkreader-neutral-background) 0px 0px 0px 3px inset !important;
}

================================

1337x.gd
1337x.is
1337x.st
1337x.to
x1337x.eu
x1337x.se
x1337x.ws

CSS
.torrent-tabs .tab-nav {
   background-image: none !important;
   background-color: var(--darkreader-neutral-background) !important;
   border-bottom-color: var(--darkreader-neutral-background) !important;
}

================================

163.com

INVERT
.m-playbar .wrap .btns

CSS
body,
.s-bg,
.g-bd1,
.n-bilst,
.m-playbar .bg {
   background: var(--darkreader-neutral-background) !important;
}

================================

1917.com

CSS
body {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

1fichier.com

CSS
body {
    background-image: none !important;
}

================================

1mlsbd.com

CSS
body {
    background-image: none !important;
}

================================

2gis.*

INVERT
#map
path[d^="M34.8"]
._oow7vi
path[d^="M15.759"]

CSS
a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, canvas, caption, center, cite, code, dd, del, details, dfn, div, dl, dt, em, embed, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header, hgroup, html, i, iframe, img, ins, kbd, label, legend, li, main, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section, small, span, strike, strong, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, tt, u, ul, var, video {
    color: var(--darkreader-neutral-text) !important;
}
div[style^="--bg-color"] {
    --bg-color: var(--darkreader-neutral-background) !important;
}
div[style="transform:rotateX(0deg)"] > svg[width="38"] path {
    fill: ${black} !important;
}
button[data-hamburger] span {
    background-color: ${black} !important;
}

IGNORE INLINE STYLE
a[href="/"] > svg *

================================

3.basecamp.com

CSS
body, .nav__main {
    background-color: ${white};
}
@media screen and (min-width: 768px){
    .panel--perma, .panel--project {
        box-shadow: rgba(0, 0, 0, 0.05) 0px -1px 10px, rgba(0, 0, 0, 0.1) 0px 1px 4px, rgb(24, 26, 27) 0px 10px 30px;
    }
}

================================

4pda.ru

CSS
.catend {
    background: ${#e8e8e8} !important;
}
div.description > span {
    display: none;
}

================================

aad.org

INVERT
.header-AAD-logo

================================

abandonia.com

CSS
.alt1G2 {
    background-image: none !important;
}
.alt2G2 {
    background-image: none !important;
}

================================

abiturma.de

INVERT
div.formula
amp-img.latex-inline

================================

ableton.com

INVERT
.main-footer__basics__logo
.main-footer__secondary__signature__logo

================================

academic.microsoft.com

INVERT
.loader

CSS
.legend > rect:nth-child(1) {
    fill: unset !important;
}

================================

academy.abeka.com

INVERT
.logo img
.center-align img

================================

academy.dqlab.id

INVERT
.menu img

================================

access.ing.de

CSS
.tile__info {
    background-color: #fff !important;
}

================================

account.live.com

INVERT
.ms-Grid-col.ms-sm8.ms-lg4
.ms-Grid-col.ms-sm4.ms-hiddenMdDown.pullout-textwrap
.ms-Grid-col.ms-sm2 img

================================

account.orchid.com

INVERT
.walletconnect-qrcode__image

================================

account.protonmail.com

CSS
:root {
    --field-background-color: var(--darkreader-bg--background-norm) !important;
}
.label-stack-item {
    background-color: var(--color) !important;
}
.label-stack-item-button, 
.label-stack-item-delete {
    color: ${white} !important;
}

================================

account.ui.com

CSS
div[class^="Toggle-module_switcher"] {
    background-color: var(--darkreader-neutral-text) !important;
}
input[class^="Toggle-module_input"] {
    background-color: initial;
}
div[class^="Modal-module_content"] form > div > canvas {
    border: 5px solid white !important;
}

================================

account.xiaomi.com

CSS
.mi-profile-layout__profile {
    background-image: none !important;
}

================================

accounts.google.com

INVERT
img[src$="signin_tapyes.gif"]

CSS
#countryList div[role="option"][data-value] > div > div > div[style] {
    background-image: url('//ssl.gstatic.com/i18n/flags/48x32/nobevel/66bdb7a1bbbdbf86a67de382fac49ecc/flags.png') !important;
}

================================

accounts.magister.net

INVERT
.bottom > img

CSS
.splash-container {
    z-index: 0 !important;
}

================================

acer.com

INVERT
.firstHeader .logos

CSS
.contSectionBen img {
    filter: brightness(50%) sepia(40%) !important;
}
.contSectionBen .textOverBen {
    position: inherit !important;
}

================================

ad.nl

CSS
.instanews-page-main-content {
    color: var(--darkreader-neutral-text) !important;
}

================================

ada.org

INVERT
.logo

================================

addons.mozilla.org

IGNORE IMAGE ANALYSIS
.Icon-youtube
span.Permission-description:before

================================

adguard.com

INVERT
a.header__logo
svg.reviews__icon-topic
.article__section--list-ico .md__list .md__img
.article__content--title-ico .article__section .md__title .md__img
.p-header-logo.p-header-logo--image img
.icon--gray-chrome
.icon--gray-edge
.icon--gray-opera
.icon--gray-firefox

IGNORE IMAGE ANALYSIS
.icon--gray-opera
.icon--gray-firefox

================================

admin.migadu.com

CSS
.container {
    background-image: none !important;
}

================================

aftonbladet.se

CSS
a[href$="plus_plusikon"] > :nth-child(1) {
   color: ${white}
}

================================

ai2.appinventor.mit.edu

CSS
.blocklyDraggable text {
    color: var(--darkreader-neutral-text) !important;
}

================================

aiming.pro

INVERT
#left_side_bar > div.logo > a > img

================================

akademy.kde.org
edu.kde.org
dot.kde.org
forum.kde.org

CSS
body,
#footer {
    background: ${rgb(225, 227, 228)} !important;
}

================================

akcemed.pl

INVERT
.td-main-menu-logo
.footer-logo-wrap

================================

akinator.com

INVERT
.bubble
.bubble-body

CSS
.bubble {
    background-color: ${black} !important;
    color: ${white} !important;
}
.bubble-body {
    background-color: ${white} !important;
    color: ${black} !important;
}
.question-number {
    color: ${white} !important;
}

================================

alertus.com

INVERT
.Header-branding
[src^="https://images.squarespace-cdn.com"]

================================

alexpage.de

CSS
#page {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

alfredapp.com

INVERT
[src*="macworld.png"]
[src*="tuaw.png"]
[src*="eddy_awards.png"]
[src*="appstorm.png"]
[src*="tnw.png"]
[src*="search.jpg"]
[src*="clipboard.jpg"]
[src*="control.jpg"]
[src*="konami-code.png"]
[src*="oatmeal404.jpg"]
[src*="query-history.png"]

================================

algorithm-wiki.org

INVERT
.thumbimage

CSS
.thumbimage {
    background-color: ${rgb(1, 4, 5)} !important;
    border-color: ${rgb(48, 54, 57)} !important;
}

================================

alipay.com

INVERT
.alipay-logo
#J_logoHomeUrl
.global-logo

CSS
.qrcode-detail-img {
    background-color: white !important;
}
.authcenter-background {
    z-index: 0 !important;
}
.authcenter-head,
.authcenter-foot {
    z-index: 1 !important;
}

================================

aljazeera.com

INVERT
#site-logo > img
.navbar-brand > img
#navbar-hamburger-mobile

================================

allegro.pl

INVERT
i[title="Smart!"]
div[class="mpof_ki m389_6m"]

CSS
#opbox-listing--base i,
[class*='2UYuR'] {
    background-size: 100% 100% !important;
}
.opbox-sheet-wrapper,
.opbox-sheet {
    background-color: var(--darkreader-neutral-background) !important;
}

IGNORE IMAGE ANALYSIS
._12h65

================================

allmacworld.com

INVERT
.logo
.tie-appear.post-thumbnail
.tie-appear.post-thumbnail > [href="https://allmacworld.com/blocs-free-download/"]
.tie-appear.post-thumbnail > [href="https://allmacworld.com/waves-v11-complete-download-free/"]
.tie-appear.post-thumbnail > [href="https://allmacworld.com/3dequalizer-4-for-mac-free-download/"]
.tie-appear.post-thumbnail > [href="https://allmacworld.com/sylenth1-for-mac-free-download/"]
.tie-appear.post-thumbnail > [href="https://allmacworld.com/synapse-audio-legend-free-download/"]

================================

alphashooters.com

CSS
.site-container {
    --darkreader-bg--site-container-background: var(--darkreader-neutral-background) !important;
}

================================

alt.hololive.tv

INVERT
video[src*="bg.mp4"]
video[src*="first.mp4"]
img[src*="logo_2line_gradient_color.svg"]
svg

================================

amap.com

INVERT
.amap-info
.amap-menu
.imgfeed[style*="place_default.jpg"]
.logo-img
#subway-svg
#themap
.title01-logo > a > img

================================

amazon.*
amazon.*.*

INVERT
#banner-image
#ordersContainer .a-box.order-attributes img
div.a-section.vse-lb-video-metadata
div.vse-video-content
img[src*="smile-logo"]
.a-icon-arrow.a-icon-small.arrow-icon
.a-icon-close
.a-icon-extender-expand
.a-icon-popover
.a-link-nav-icon
.currencyINR

CSS
.banner-border {
    background-image: none !important;
    background-color: ${white} !important;
}
div.milestone.notReached .milestone-marker::before {
    border-color: var(--darkreader-neutral-text);
}
span.milestone-bar {
    z-index: 0;
}
span.milestone-bar_foreground {
    background-color: #4DC2B4;
}
span.milestone-bar_background {
    background-image: linear-gradient(var(--darkreader-neutral-text) 40%, #181a1b00 0px);
}
img {
    border-radius: 5% !important;
}

================================

amazon.cn

INVERT
#nav-logo

CSS
#nav-main,
.nav-search-scope {
    background-image: none !important;
}

================================

ancient.eu

INVERT
img#header_ahe_logo
img[alt="digitalmeetsculture.net"]
img[alt="EAGLE"]
img[alt="Save Ancient Studies in America"]
img[alt="Lithodomos VR"]
img[alt="USI / UNESCO / uniTwin Logo"]

CSS
div#nav_bar {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

androidcentral.com

CSS
.lazy-loaded {
    mix-blend-mode: normal !important;
}

================================

androidpolice.com

INVERT
.header-logo

================================

angrymetalguy.com

INVERT
.plbrand

CSS
.thepage {
    background-color: ${white} !important;
}
.content-pad {
    background-color: ${white} !important;
}

================================

anilibria.tv

CSS
#oframeanilibriaPlayer pjsdiv:nth-child(12) pjsdiv:nth-child(2) pjsdiv,
#oframeanilibriaPlayer pjsdiv:nth-child(14) pjsdiv:nth-child(2) pjsdiv {
     background-color: var(--darkreader-neutral-text) !important;
}

================================

anilist.co

CSS
:root {
    --color-background: 39,44,56 !important;
    --color-foreground: 31,35,45 !important;
    --color-foreground-grey: 25,29,38 !important;
    --color-foreground-grey-dark: 16,20,25 !important;
    --color-foreground-blue: 25,29,38 !important;
    --color-foreground-blue-dark: 19,23,29 !important;
    --color-text: 159,173,189 !important;
    --color-text-light: 129,140,153 !important;
    --color-text-lighter: 133,150,165 !important;
}

================================

animedigitalnetwork.fr

CSS
.spoiler {
    color: transparent !important;
}
.spoiler:hover {
    color: unset !important;
}

================================

ankiweb.net

CSS
.sidebar {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

answers.opencv.org

CSS
#header,
body,
#question-list,
.short-summary,
#ground {
    background-image: none !important;
}

================================

answers.unity.com

IGNORE IMAGE ANALYSIS
header.section-header div.shard:before

================================

antistorm.eu

INVERT
#map-area
#map-controls-area
#controlPlayPauseIcon
#meteogram_temperature
#meteogram_dewPoint
#meteogram_cloudCover
#meteogram_precipitation
#meteogram_precipitationProbability
#meteogram_pressure
#meteogram_windSpeed
img[src*="data:image/png;base64,iVBORw"]

================================

antywirus-nod32.pl

CSS
.module.infolinia {
    background-image: none !important;
}

================================

anytype.io

IGNORE IMAGE ANALYSIS
.img.home

================================

apclassroom.collegeboard.org

INVERT
a.overview-icon svg
.Zoom--expander svg
svg.icon--xmark
.RI_header__button svg.svg-icon

CSS
.standalone_image img {
    background-color: white;
}

================================

api.kde.org

INVERT
.center
.image

CSS
.memdoc, .memproto {
    background-image: initial !important;
}

================================

apnews.com

INVERT
.hamburger-box
.sticky-part > div svg
.LeftRail > div svg

================================

app.codesignal.com

CSS
.monaco-editor .cursor {
   background-color: var(--darkreader-selection-text) !important;
}

================================

app.corellium.com

INVERT
.brand
.logo

================================

app.datadoghq.com

INVERT
.HostMap-canvas

CSS
.log-dt-event.active, .log-dt-event.active:hover, .log-dt-event:hover {
    background-color: rgb(37, 45, 58) !important;
}
svg text.time_cursor {
    fill: ${black} !important;
}

================================

app.getpocket.com

IGNORE IMAGE ANALYSIS
*

================================

app.grammarly.com

CSS
use.valueCircle_f6otssy {
    stroke: url(#pb-gradient-0);
}
[class*="-alerts-markSelectedHigh"], span[class*="markSelectedFocused"] {
    color: rgb(14, 16, 26) !important;
}
[class*="-navigation-counterWrapper"] [class*="-navigation-counterContent"],
[class*="-paidview-counter"] [class*="-paidview-counterContent"] {
    fill: rgb(0, 0, 0, .9);
}

IGNORE INLINE STYLE
use[class*="valueCircle_"]

================================

app.kognity.com

INVERT
img.KogCalculator
.content-image-figure > img[src*="png"]

CSS
body, .KogDashboard-insideLoader {
    background: none var(--darkreader-neutral-background) !important;
}
img:not([src*="png"]):not([src*="svg"]) {
    background-color: white !important;
}

================================

app.mysms.com

CSS
.message a {
     color: grey;
}

================================

app.roll20.net

INVERT
.sheet-hp
.sheet-ac
.sheet-textbox
.sheet-name-container
.sheet-attributes-container
.sheet-attr-container button
.sheet-hlabel-container
.sheet-vitals
.sheet-init button
.sheet-spell-level
.sheet-spell-level input
.sheet-textbox .sheet-options
.sheet-speed input
.sheet-part select
.sheet-resources .sheet-subcontainer
.sheet-resources .sheet-label
.sheet-subcontainer .sheet-top
.sheet-textbox .sheet-label
.sheet-attack .sheet-options

CSS
.sheet-attributes-container,
.sheet-init,
.sheet-speed,
.sheet-trait,
.sheet-part,
.sheet-spell-level,
.sheet-details {
    color: ${white} !important;
}

================================

app.standardnotes.org

CSS
search-options svg {
    fill: var(--darkreader-neutral-text) !important;
}

IGNORE INLINE STYLE
search-options svg

================================

app.timelyapp.com

INVERT
.Clock__clock_css_icon___LfBr6
.Clock__stopwatch___2G-CB

CSS
.Day__container___1Fpnl.Day__showBackground___3CXnw {
     background-image: none;
}

================================

app.traderepublic.com

IGNORE INLINE STYLE
#chartPriceLine

================================

app.youneedabudget.com

CSS
[data-darkreader-inline-fill] {
    fill: ${black} !important;
}

================================

apple.com

INVERT
.ac-slider-ax-track
.controls-progress-indicator

================================

aprs.fi

INVERT
.gm-style > :first-child
.gm-style-iw-a

CSS
#panel {
    background-image: none !important;
}

================================

aras.com

CSS
.alternate_color.banner__title.display-2,
.alternate_color.banner__subtitle.display-5 {
    color: var(--darkreader-neutral-text) !important;;
}
.banner__title.display-2,
.banner__subtitle.display-5 {
    color: ${white} !important;
}

================================

archive.org

INVERT
p + fieldset a svg
.search-field svg

CSS
#search-input {
    color: var(--darkreader-neutral-background) !important;
}

================================

ars.particify.de

CSS
body {
    background-color: var(--darkreader-neutral-background) !important;
}
.mat-mini-fab {
    box-shadow: none !important;
}
.mat-card {
    background-color: #{gray} !important;
    box-shadow: none !important;
}
.mat-chip {
    background-color: var(--darkreader-neutral-background)  !important;
    color: var(--darkreader-neutral-text) !important;
}

================================

arstechnica.com

CSS
.listing, .video-thumbnail {
    background-blend-mode: initial !important;
}
.article-single figure img {
    mix-blend-mode: initial !important;
}

================================

artofproblemsolving.com

INVERT
.latex
.latexcenter

================================

arxiv.org

CSS
.abstract {
     background-color: transparent !important;
}

================================

asahichinese-j.com

INVERT
#HeaderInner img
.SocialNav
.Tag li a

CSS
.Tag li a {
    color: ${grey} !important;
}
a, .LastUpdated, .Lead {
    color: var(--darkreader-neutral-text) !important;
}
#Footer p, #FooterInner {
    background-color: var(--darkreader-neutral-background) !important;
}
h1, .ArticleBody p, .ImagesHeightMod {
    background-color: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text) !important;
}

================================

asana.com

INVERT
.siteHeader__logo
.DatePickerCalendarDate--today .DatePickerCalendarDate-button::after

CSS
.MultiColorIcon--unselected .MultiColorIcon-path--fadedBlack {
    fill: var(--darkreader-bg--color-icon) !important;
}
.MultiColorIcon-path--white {
    fill: var(--darkreader-text--color-icon-foreground) !important;
}
.DailySummaryInboxThread--selected,
.TaskAddedToPotInboxThread--selected,
.InboxExpandableThread.InboxExpandableThread--selected,
.PortfolioItemRow {
    background: var(--darkreader-neutral-background) !important;
}
.PortfolioItemRow:hover {
    background: ${#FFF} !important;
}

================================

asciinema.org

IGNORE INLINE STYLE
polygon
polyline

================================

askjohnmackay.com

INVERT
[src$="ask-john-mackay-logo.png"]
[src$="side-banner-05.gif"]
[src$="reasonwhy.jpg"]

CSS
body,
header,
#site-footer,
#live-search {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

assetstore.unity.com

CSS
div[style^="background-color:var(--color-bg-gray-5)"] {
    --color-bg-gray-5: var(--darkreader-neutral-background) !important;
}
h2[style*="color:var(--color-font-header)"] {
    --color-font-header: var(--darkreader-neutral-text) !important;
}

================================

asus.com

INVERT
span.mobile-menu-toggle.mobile
em#searchopen.icon-search
#af-header .af-inner .logo

IGNORE IMAGE ANALYSIS
#af-header .af-inner .logo
li.nav-Rog a

================================

atcoder.jp

CSS
#fixed-server-timer {
    color: #333;
}

================================

atlas.herzen.spb.ru
guide.herzen.spb.ru
job.herzen.spb.ru

INVERT
img[src="/images/logo.png"]
hr

CSS
body, .body, table, tbody, tr, td, .corner_bottom {
    background: none !important;
}

================================

atlassian.net

INVERT
img[src*="https://github.githubassets.com/favicon.ico"].smart-link-icon
.sc-jTzLTM
img[src^="https://latexmath.bolo-app.com/render/"]

CSS
span.code {
    background-color: rgba(240,246,252,0.15) !important;
}
.ghx-parent-group,
.ghx-issue {
    background-color: rgba(119, 183, 255, 0.05) !important; 
}
span.loader-wrapper a,
.css-s0tfqx,
.css-1vn31bx,
.css-1gx5gpx,
.css-wvfva4 {
    background-color: rgba(240,246,252,0.1) !important; 
}
.css-1yfnrso,
.aui-flag, 
.aui-message {
    box-shadow: 0 0 20px rgba(240,246,252,0.15) !important;
}
.css-1ua1xqz {
    background-color: rgba(65, 65, 83, 0.6) !important;
}
code:first-of-type {
    background-image: linear-gradient(to right, rgba(255,255,255,0.03),rgba(255,255,255,0.03)calc(1ch + 16px),transparent calc(1ch + 16px),transparent) !important;
}
.ak-editor-panel[data-panel-type="note"] {
    background-color: ${rgb(217, 200, 228)} !important;
}
.ak-editor-panel[data-panel-type="info"] {
    background-color: ${rgb(172, 217, 242)} !important;
}
.ak-editor-panel[data-panel-type="error"] {
    background-color: ${rgb(255, 220, 211)} !important;
}
.ak-editor-panel[data-panel-type="warning"] {
    background-color: ${rgb(238, 229, 187)} !important;
}

IGNORE INLINE STYLE
.ak-editor-content-area *

================================

audible.com

INVERT
#adbl-cloud-player-controls

================================

audycje.tokfm.pl/widget

CSS
body {
     background-color: var(--darkreader-neutral-background) !important;
}

================================

avito.ru

INVERT
a[class^="logo-logo"] > svg > g > path
a[class^="logo-logo"] > svg > g > g
div[class^="index-logo"] > a[class^="index-root"]
.item-extended-phone
.item-price-old::before
.contacts-phone-3KtSI
.button-content-phone_size_l-1O5VB
._39EVKDP-9p1BREJQ3fhILl._2sPEvPi-1aWpcq1ggVph1C._4wLX_6jxKYoWRyE1U1WcZ
[class^="suggest-graySuggestCategoryImg"]
ymaps[class$="ground-pane"]

CSS
div[class^="item-preview-image"] {
    z-index: 0 !important;
}

================================

aws.amazon.com

INVERT
a.lb-trigger
.hover .axis-box rect
.img-wrapper
img[alt^="WEB_FreeTier"]
.lb-is-lazyloaded
.lb-none-v-margin.lb-img

CSS
.header-background {
  fill: none !important;
}

================================

azurlane.koumakan.jp

IGNORE IMAGE ANALYSIS
.mw-wiki-logo

================================

bab.la

INVERT
.logo-flash

================================

babyem.co.uk

INVERT
div.elementor-social-icons-wrapper a.elementor-icon
input::placeholder

================================

bahnhof.net

CSS
.contact-form_section {
    background-image: none !important;
}

================================

baike.baidu.com

INVERT
.formula

IGNORE IMAGE ANALYSIS
.wiki-lemma .lemmaWgt-posterBg

================================

bakabt.me

INVERT
span.icon.hd
span.icon.lossless

================================

bandcamp.com
allochiria.com

INVERT
.bclogo.white
.vol-control
.progress
.buffer
.seek-control
#material-close

CSS
.ui-dialog {
    background-image: none !important;
}

================================

banki.ru

INVERT
.header__logo

================================

bankier.pl
bankier.tv

INVERT
.o-home-smart-box__header > .a-anchor > .a-image >  img[class="a-image__img"]
.hamburger
.icon > img
.menu-sidebar__close-menu > img
img[class="menu-sidebar__logo-img"]
img[alt*="Bankier.pl"]
img[src*="logo-smart.svg"]
a[href="https://www.bankier.pl/"]
img[alt="Bankier.TV"]

================================

bankofamerica.com

CSS
.fsdnav-sub-nav-left {
    background-color: var(--darkreader-neutral-background) !important;
}
.ad-acct-layout,.ad-acct-detail-card-layout,
.thrfwl-body,
.ad-summary-container,.olb-summary-widget-container {
    background-image: none !important;
}

================================

basecamp.com

INVERT
.top-nav__logo

================================

bayfiles.com

INVERT
.img-responsive
.kopimi

================================

bbc.co.uk/weather
bbc.com/weather

INVERT
.orb-nav-section .orb-nav-blocks
.orb-icon .orb-icon-arrow

CSS
.wr-icon-weather-type__svg-background,
.wr-icon-rain__svg-background,
.wr-icon-wind-direction__svg-background,
.wr-icon-gel__svg-background {
    opacity: 0% !important;
}
.wr-value--windspeed {
    color: ${#dad7d2} !important;
}
.wr-c-environmental-data__icon-text {
    color: ${#dcd9d4} !important;
}

================================

bbc.com/news
bbc.com/sport
bbc.com/travel
bbc.com/capital
bbc.com/autos
bbc.com/culture
bbc.com/future
bbc.com/sounds
bbc.com/food
bbc.com/bitesize
bbc.com/earth

INVERT
.orb-nav-section .orb-nav-blocks
.orb-icon .orb-icon-arrow
[class*="NavigationLink-LogoLink"] > span > svg

================================

bbs.chinauos.com
bbs.deepin.org

INVERT
.post_tip

CSS
.post_edit p {
    color: var(--darkreader-neutral-text) !important
}

================================

bbs.thinkpad.com
club.lenovo.com.cn

CSS
body[style] {
    box-shadow: inset 0px 0px 0px 9999px var(--darkreader-neutral-background);
}

================================

berlingske.dk

INVERT
.site-header__logo

================================

bestbuy.ca

INVERT
.centerContainer
[class^="facetName"]
[class^="title"]
[class^="sortLabel"]
[class^="headerText"]
[class^="subTitle"]

CSS
div[class*="backgroundContainer"] {
    z-index: 0 !important;
}
body {
    background-image: initial; !important;
}

================================

bestbuy.com

CSS
input[type="radio" i] {
    background-color: initial;
}

================================

bettercap.org

INVERT
.copy-to-clipboard
[src$="mitm.jpg"]
[src$="proxy.png"]
[src$="with-hsts.png"]
[src$="sslstrip2.png"]

CSS
.copy-to-clipboard {
    background-color: var(--darkreader-neutral-text) !important;
}

================================

bfi.org.uk

INVERT
img[src$="bfi_logo_transp.png"]
img[src$="national-lottery-logo-color.png"]
img[src*="sight-and-sound-logo-280x69.png"]

CSS
body {
    background-image: none !important;
}

================================

bgp.he.net

INVERT
img[src="/helogo.gif"]
img[src*="chart.googleapis.com"]
img[src^="/graphs/"]

================================

bigocheatsheet.com

INVERT
.gray

CSS
.green,
.orange,
.yellow,
.yellow-green {
    color: black !important;
}

================================

biletywielkopolskie.pl

INVERT
.kw-logo

================================

binance.com

CSS
div.qr-code > canvas {
    outline: solid 10px white !important;
}
div > input[type="checkbox"] + svg {
    fill: transparent !important;
}
div > input[type="checkbox"]:checked + svg {
    fill: var(--darkreader-neutral-text) !important;
}
label > svg circle {
    fill: rgb(37, 40, 42) !important;
}

================================

bing.com

INVERT
canvas

CSS
.b_searchboxForm,
.b_searchboxForm:hover,
.b_focus .b_searchboxForm,
#sw_as #sa_ul:not(:empty) {
    box-shadow: ${rgba(0, 0, 0, 0.1)} 0px 0px 0px 1px !important;
}
#b_results > li.b_ans.b_topborder, #b_results > li.b_ans.b_topborder.b_tophb.b_topshad {
    box-shadow: ${rgba(13, 13, 13, 0.05)} 0px 0px 0px 1px !important;
}

IGNORE INLINE STYLE
.b_header_bg

================================

biorxiv.org

INVERT
.blood_logo
.logo-img
#czilogo

CSS
body {
    background-image: none !important;
}

================================

bitbay.net

INVERT
.navbar-brand
.logo
.full-page-preloader

================================

bitbucket.org

INVERT
a[href="/product"]
.registration-hero .form-prompt

CSS
span[data-testid="file-tree-file__comments"] > span {
    color: var(--darkreader-neutral-text) !important;
}
span > svg {
    color: var(--icon-primary-color) !important;
}
[type="button"][tabindex="0"],
[data-testid="settingsButton"] {
    background-color: ${rgba(33, 53, 89, 0.04)} !important;
}
code.code,
[type="button"][tabindex="0"]:hover,
[data-testid="settingsButton"]:hover {
    background-color: ${rgba(33, 53, 89, 0.08)} !important;
}
.ak-navigation-resize-button {
    box-shadow: ${rgba(33, 53, 89, 0.08)} 0px 0px 0px 1px,
                ${rgba(33, 53, 89, 0.08)} 0px 2px 4px 1px !important;
}

IGNORE INLINE STYLE
[role="presentation"] svg *
[aria-label="Bitbucket"] *

================================

bitly.com

CSS
.hero-content {
    background-image: none !important;
}

================================

bitwarden.com

INVERT
img[alt="Github Logo"]
img[src$="forbes.png"]

IGNORE IMAGE ANALYSIS
blockquote .blockquote-header

================================

bitwit.tech

CSS
.svg-primary {
    fill: #375D69 !important;
}
.svg-primary-light {
    fill: #4A7F8F !important;
}
.svg-primary-dark {
    fill: #28444D !important;
}
.svg-secondary {
    fill: #B88399 !important;
}
.svg-secondary-light {
    fill: #E8A9C4 !important;
}
.svg-secondary-dark {
    fill: #825F6E !important;
}
.svg-light {
    fill: #ADCED9 !important;
}
.svg-outline {
    fill: none !important;
    stroke: #000000 !important;
}
.svg-primary-outline {
    fill: none !important;
    stroke: #ADCED9 !important;
}
.svg-secondary-outline {
    fill: none !important;
    stroke: #375D69 !important;
}
.theme-light {
    display: none !important;
}
.theme-dark {
    display: none !important;
}
.theme-darkreader {
    display: block !important;
}

================================

blablacar.*
blablacar.*.*

INVERT
.kirk-topBar-left

================================

blahdns.com

INVERT
[src="https://cdn.blahdns.com/logo.png"]
[src="https://cdn.blahdns.com/kofi4.png"]
.liberapay-btn > span
svg

================================

blog.cloudflare.com

CSS
body#main-body {
    color: ${black} !important;
    background-color: ${white} !important;
}
.dn {
    color: ${white} !important;
}
#nav {
    background-color: ${white} !important;
}
main#post {
    background-color: ${white} !important;
}
article.post-full {
    background-color: ${white} !important;
}
article p {
    color: ${gray} !important;
}
code {
    color: ${black} !important;
}
nav.pagination {
    background-color: ${white} !important;
}

================================

blog.doist.com

INVERT
.db-header__logo-img

================================

blog.mozilla.org

INVERT
.content > .logo > [href^="https://www.mozilla.org/"]
.nav-global-donate > [href^="https://donate.mozilla.org/"]

CSS
.nav-global-donate > [href^="https://donate.mozilla.org/"] {
   color: ${white} !important;
}
#page {
    background-image: none !important;
}

================================

blogger.com

INVERT
.gb_ua
.feedflare

================================

blogs.windows.com

CSS
.c-logo .c-image {
    background: ${white} !important;
}

================================

blueberryroasters.pl

INVERT
div#logo

================================

bol.com

CSS
.skeleton-image[loaded] .skeleton-image__img {
    mix-blend-mode: normal !important;
}

================================

book.douban.com

IGNORE IMAGE ANALYSIS
.bigstar50
.bigstar45
.bigstar40
.bigstar35
.bigstar30
.bigstar25
.bigstar20
.bigstar15
.bigstar10
.bigstar05
.bigstar00
.allstar50
.allstar45
.allstar40
.allstar35
.allstar30
.allstar25
.allstar20
.allstar15
.allstar10
.allstar05
.allstar00
.rating
.starb
.collectors

================================

booking.com

INVERT
.bui-calendar__control
.-iconset-close
.-iconset-navarrow_left
.-iconset-navarrow_right
.sort_more_options__button
.mb-ico
.-iconset-review_great
.-iconset-review_poor
.-iconset-chat_bubbles
.location_section_icon
.hp-date-picker-icon
.-streamline-info_sign
.-streamline-person
.-streamline-chat_bubbles
.hp-policies-calendar-icon
.-iconset-moon_crescent

================================

boredpanda.com

INVERT
.logotype

================================

boxberry.ru

INVERT
.slider__image
img[src$="cancel.png"]
.box_img_block
img[src$="Vector.png"]
ymaps[class$="ground-pane"]
ymaps[class$="svg-icon-content"] > ymaps

================================

bpmn.io

IGNORE INLINE STYLE
.djs-visual > *:not(text:not(rect + text))

================================

br.de

CSS
article h4, article h3, article p, article .css-lip0i6, article h5, .asso-gtm, .css-18ckx31 {
    color: ${#323232} !important;
}
article, article section, article footer, .css-1fzo8jw, .css-efdc07, article .css-fhbsai, main h3, section.css-3copat section {
    background-color: ${white} !important;
}

================================

brainly.com

INVERT
.brn-rich-content > p > img

================================

brainly.pl

INVERT
[id^="TexFormula"]
div.brn-qpage-next-answer-box__content > div > div > div img:not(.brn-qpage-next-attachments-viewer-image-preview__image)

================================

brave.com

CSS
section + img {
    display: none !important;
}

================================

brightspace.avans.nl

CSS
:root {
    --darkreader-bg--d2l-dropdown-background-color: #1D1F20;
}

================================

bsi.bund.de

INVERT
.c-branding__logo

CSS
.c-intro {
    z-index: 0 !important;
}

================================

bugreplay.com

INVERT
.br-logo

================================

bugs.chromium.org

INVERT
i.material-icons
.project-logo

CSS
:root {
    --darkreader-fix--border: 1px solid ${#ccc} !important;
}
button[type="submit"],
input#searchq,
select#can,
mr-dropdown,
td, th.group-header {
    border-bottom: var(--darkreader-fix--border) !important;
    border-top: var(--darkreader-fix--border) !important;
}
button[type="submit"] + mr-dropdown {
    border-right: var(--darkreader-fix--border) !important;
}
:host {
    --mr-search-bar-background: var(--darkreader-neutral-background) !important;
    --chops-choice-bg: ${#eceff1} !important;
    --chops-table-header-bg: ${#f1f3f4} !important;
    --chops-card-details-bg: var(--darkreader-neutral-background) !important;
    --chops-card-heading-bg: var(--darkreader-neutral-background) !important;
}
#can {
    border-left: var(--darkreader-fix--border) !important;
    background-color: var(--darkreader-neutral-background) !important;
}
mr-header,
th {
    border-bottom: var(--darkreader-fix--border) !important;
}

================================

build-electronic-circuits.com

INVERT
.tutorial-intro
.tutorial-intro > *

CSS
.page-todo-list {
    background-color: rgb(109, 85, 9) !important;
}

================================

bulldogjob.pl/proxied/job-offers

IGNORE IMAGE ANALYSIS
*

================================

burnaware.com

INVERT
img[alt="Burnaware"]

================================

businessinsider.com
businessinsider.com.au
businessinsider.com.pl
businessinsider.co.za
businessinsider.es
businessinsider.jp
businessinsider.mx
insider.com
it.businessinsider.com

INVERT
.account-text-not-logged-in
.brand
.brands-logo
.drawer-heading.headline-bold
.f-footer-logo
.f-header-logo
.f-header-editionLabel
.ins-drawer-button-area
.ins-drawer-vertical-link
.logo
.logo-primary
.logo-pair-wrapper
.masthead-icon
.menu-search-icon
.menu-lang-icon
.navSocial-item
.navbar-brand
.net-social
.network-toggle
.new-menu-logo
.p-post-contentPrimeEntryPrice
.search-btn
.site-logo
.social-container
.social-icons > .social
.socialmedia-icon-list
.verticals-listitem-label
a[title="Business Insider España"]
button[class*="drawer-dropdown-button"]
button[class="unbutton"] > img
div[class*="footer__LogoWrapper"]
div[class*="footer__SocialWrapper"]
div[class*="header__Wrapper"]
section[class*="brands-social"]

================================

buzzsprout.com

INVERT
.icon_edit
.icon_stats
.icon_share

================================

caddy.community

INVERT
.logo-big

================================

caiyunapp.com

INVERT
#icon_current
#logo-name[src="/imgs/logo/logo-website.png"]
#map_canvas

================================

caldigit.com

CSS
.elementor-widget-heading .elementor-heading-title[class*="elementor-size-"] > a {
    color: var(--darkreader-neutral-text) !important;
}

================================

calendar.google.com

CSS
div[role="checkbox"] > div > div > div {
    border: 1px solid ${black} !important;
}

IGNORE INLINE STYLE
div[role="checkbox"]

================================

calibre-ebook.com

IGNORE IMAGE ANALYSIS
.tooltip
#content-wrapper

================================

calvinklein.us

INVERT
.star:not(.star-active)
.star-half

================================

candidates.ibo.org

INVERT
img#ibLogo

================================

canva.com

INVERT
#__next > div > section > div[style^="background-color"]

================================

canvas.*.edu

INVERT
.file_download_btn

================================

canvas.usask.ca

CSS
#questions .text img {
    background-color: currentColor !important;
}

================================

carmax.com

CSS
.recall__placeholder,
.vehicle-history__placeholder {
    background-color: ${yellow} !important;
}
#recall .recall__header *,
#recall .recall--link span {
    color: #2a343d !important;
}
#recall svg,
.vehicle-history__placeholder .vehicle-history a svg {
    fill: #053361 !important;
}
#recall a,
.vehicle-history__placeholder .vehicle-history a {
    color: #004487 !important;
}

================================

castbox.fm

INVERT
.nav-item.right.msg
.nav-item.right.upload
.nav-item.right.search
.userNav-img[src="https://s3.castbox.fm/webstatic/images/userIcon.06c408dc.png"]
.heart
.playbackRate.timing.icon

================================

cbpp.org

INVERT
.navbar-brand

================================

cdc.gov

INVERT
.cdc-logo path[fill="#000"]

================================

cdimage.ubuntu.com

INVERT
.p-navigation__logo

CSS
#main {
    background-image: none !important;
}

================================

cdp.contentdelivery.nu

CSS
body {
    background-image: none !important;
}

================================

centrum24.pl

INVERT
#mobile-applications .jumbotron
.headerLogo.bankLogo

================================

centrumxp.pl

INVERT
.badge-partner
.img-responsive

================================

cfos.de

CSS
div[style*="keyboard-light.jpg"],
div[style*="organizer-light.jpg"],
div[style*="data-center-light.jpg"],
div[style*="speed-dial-light.jpg"] {
    background-image: none !important;
}

================================

changkun.de

CSS
.content h2, h3 {
    z-index: 0 !important;
}

================================

chase.com

CSS
.menu-button-item {
    color: var(--darkreader-neutral-text) !important;
}

================================

cheapshark.com

CSS
.header {
    border-top: 10px solid #000 !important;
}

================================

check.spamhaus.org

INVERT
.gradient-yellow
header .curve
header .logo

================================

chilkatsoft.com

CSS
body {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

chinadigitaltimes.net

INVERT
.show-menu-button span

CSS
#main-header-wrapper {
    background-image: none !important;
}

================================

chinauos.com

INVERT
.brand

CSS
.choose {
    background-image: none !important;
}

================================

chipotle.com

INVERT
.banner-title
.banner-subtitle
.banner-legal

================================

christinamin9-ancientromancivilisation.weebly.com

CSS
.landing-page #main-wrap,
.tall-header-page #main-wrap,
.short-header-page #main-wrap,
.no-header-page #main-wrap {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

chtoes.li

INVERT
.illustration
.menu-page
.menu-item

================================

cinedrome.ch

CSS
body {
    background-image: none !important;
}

================================

circuit-diagram.org

INVERT
.components-list .item .image

================================

citilink.ru

INVERT
div[class$="RatingDetail__progress_active"]

CSS
.OldPageLayout {
    color: var(--darkreader-neutral-text) !important;
}

IGNORE INLINE STYLE
.Icon

================================

citybuzz.pl

INVERT
img[alt="CityBuzz"]

================================

citymapper.com

INVERT
#map

================================

classroom.google.com

INVERT
img[src$="dark_create_class_arrow.svg"]
img[aria-label="YouTube"]
div[role="dialog"] ~ div[role="menu"] > div[role="menuitem"] > div > div:not([style*="background-image"])

CSS
div[role="toolbar"] div[role="button"] > div[class*='-']:not([onclick]):not(:link):not(:visited):not([style*="background-image"]):first-child,
div[role="toolbar"] div[role="button"] > div[class*='-']:not([onclick]):not(:link):not(:visited) > :nth-child(2) > div,
div[style="bottom: 0px;"] > div[style^="opacity:"] div[role="button"] > div:not([onclick]):not(:link):not(:visited),
li[guidedhelpid="classworkTopicListGh"]:not(hover) > div {
    opacity: 99% !important;
}

================================

cleantechnica.com

CSS
body {
    background-image: none !important;
    background-color: #1d1e1f !important;
}

================================

clever.com

CSS
img[alt="i-Ready icon"], img[alt="Google Meet icon"], img[alt="Google icon"] {
    background-color: white !important;
}
.background-container {
    z-index: 1 !important;
}

================================

clients.servarica.com

INVERT
.logo

================================

cloud.databricks.com
pages.databricks.com
*.azuredatabricks.net

INVERT
.figure
#sparkui-iframe-body #plan-viz-graph svg g.cluster rect
#sparkui-iframe-body #plan-viz-graph svg g.node rect

CSS
text {
    fill: ${black};
}
.cm-string {
    color: rgb(132, 179, 235) !important;
}
.cm-keyword {
    color: rgb(232, 121, 172) !important;
}
.cm-variable-2 {
    color: rgb(97, 215, 255) !important;
}
li.CodeMirror-hint {
    font-family: Source Code Pro, Menlo, monospace;
}
span[role="presentation"] {
    color: ${black} !important;
}
.CodeMirror-cursor {
    border-left-color: ${black} !important;
}
.ansiout {
    color: ${rgb(85, 85, 85)} !important;
}

================================

cloud.google.com

CSS
code,
pre,
th,
td,
.devsite-top-logo-row-middle,
nav.devsite-tabs-wrapper,
devsite-footer-linkboxes,
devsite-footer-utility {
    background-color: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text) !important;
}

================================

cloudhostnews.com

INVERT
.site-title

================================

cnki.net

INVERT
.hello-yx-box
.search-main > .input-box > .search-btn
.Logo

CSS
.foot-top {
    background-image: none !important;
}
.hello-yx-box {
    color: var(--darkreader-neutral-background) !important;
}

================================

cnn.com

INVERT
[data-test="section-link"] > svg
img.metadata-header__logo

IGNORE INLINE STYLE
svg.cnn-badge-icon
svg.cnn-badge-icon > rect
svg.politics-logo-icon

================================

cobaltstrike.com
cobalt-strike.github.io

INVERT
.navbar-brand

================================

code.qt.io

INVERT
div#cgit
td[class="logo"]
select
input

================================

code.visualstudio.com

CSS
.home.linux .jumbotron .screenshot {
 background-image:url("/assets/home/<USER>");
 padding-bottom:82.65802%
}
@media only screen and (min-width: 1200px) {
 .home.linux .jumbotron .screenshot {
  background-image:url("/assets/home/<USER>");
  padding-bottom:68%
 }

================================

codecademy.com

INVERT
.CodeMirror-cursors
.CodeMirror-selected
span[class^="burger"]

================================

codeforces.com

INVERT
#header > div:first-child img
.action-link > div > img:first-child
.roundbox-lt
.roundbox-rt
.roundbox-lb
.roundbox-rb
.delete-resource-link
a.contestParticipantCountLinkMargin > img
.recent-actions > ul > li img[src*="hourglass"]

CSS
.lt,
.rt,
.lb,
.rb,
.ilt,
.irt {
    display: none;
}
input[type="submit"], input[type="button"], select {
    border-style: solid;
}
input[type="submit"]:hover, input[type="button"]:hover, select:hover {
    border-style: groove;
}

================================

codewars.com

INVERT
.logo

================================

codingame.com/ide

CSS
.cg-statement .statement-body span.var,
.cg-statement .statement-body var {
   color: var(--darkreader-neutral-background) !important;
}

================================

coinbase.com

CSS
.hjbuvQ,
.kfQbHv {
    fill: ${black} !important;
}

================================

color-hex.com

INVERT
img[src*="logo"]
.bgwhite
.bgwhite > .previewbox

IGNORE INLINE STYLE
.colordvaline
.colordva
.palettecolordiv

================================

colorhunt.co

IGNORE INLINE STYLE
.palette > div

================================

colorpicker.me

IGNORE INLINE STYLE
html
body
button

================================

colors.dopely.top

INVERT
.footer
.color-selector-icon

CSS
input.number-slider {
    filter: invert(10%) saturate(0%) !important;
}
img[src*="svg"], .title {
    filter: invert(50%) !important;
}
.accountDopely__letter,
.editProfile__changeProfilePicture {
    filter: saturate(0%) brightness(50%) !important;
}
div[class*="text"],
a[class*="header"],
.navbar-dropdown-item__logo,
.navbar__listItem__button {
    filter: saturate(0%) !important;
}

IGNORE INLINE STYLE
.ExpandPalette *
.toner-colors-container *
.color-picker

================================

comicfury.com

CSS
#wrapper {
    background-color: inherit !important;
    padding-bottom: 10px !important;
}
#footer {
    margin-top: 0px !important;
}

================================

comixology.com

INVERT
.comixology-logo
.primary-heading
.heading-content
.try-now
.unlimited-disclaimer

================================

comma.ai

INVERT
.comma-header-logo
.comma-two-benefit-icon
.comma-landing-reviews-logo

================================

commons.wikimedia.org

IGNORE IMAGE ANALYSIS
.mw-wiki-logo

================================

commonvoice.mozilla.org

INVERT
div:not(.logo-container) > .main-logo
.hero-box .fading
.waves
.mars + .screenshot
#help-links img
.button.outline.rounded.hidden-sm-down img
.text-button.contribute-more-button.secondary path

CSS
.language-select .current {
    --white: var(--darkreader-bg--white) !important;
}

================================

community.cloudflare.com

CSS
:root {
    --darkreader-text--primary-medium: ${gray} !important;
}

================================

community.notepad-plus-plus.org

INVERT
.forum-logo

================================

community.ntppool.org

INVERT
#site-logo

================================

compass.pressekompass.net

INVERT
.logo-holder .brand-logo

IGNORE INLINE STYLE
text

================================

computerhope.com

INVERT
#text-tool > p > img

================================

confectioneryproduction.com

INVERT
#mainLogo img

================================

confluence.*

INVERT
.gliffy-text-with-shape-parent-primary
.gliffy-text-with-shape-parent-secondary
.gliffy-text-with-shape-parent-tertiary
.gliffy-text-with-shape-parent-highlight

CSS
.geDiagramContainer rect,
.geDiagramContainer path {
    filter: brightness(60%);
}

================================

console.cloud.google.com

CSS
.cfc-ng2-region .cfc-text-title-3 {
    color: var(--darkreader-neutral-text) !important;
}
.cfc-color-text-secondary {
    color: ${#333} !important; 
}

================================

convertio.co

INVERT
.logo svg > :last-child

================================

cookiepedia.co.uk

INVERT
.main-logo
.footer-logo

================================

coolblue.be
coolblue.nl
hotorangemedia.nl

IGNORE INLINE STYLE
.header__logo-image > path

================================

coolors.co

CSS
.generator_color {
    color: #fff !important;
}
.generator_color.is-light {
    color: #000 !important;
}

IGNORE INLINE STYLE
#generator*

================================

coopgames.eu

INVERT
img[alt="Online Co-Op"]
img[alt="Local Co-Op"]
img[title="PS2"]
img[title="PS3"]
img[title="PS4"]
img[title="PSP"]
img[title="PS Vita"]
img[title="DS"]
img[title="3DS"]
img[title="Switch"]

================================

copitosystem.com

CSS
body {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

corsair.com

INVERT
img.desktop
img[src="/_ui/responsive/common/images/icon_cart_empty.png"]
a.cart-close
input#search-btn

================================

courses.fit.cvut.cz

CSS
body {
    color: #909090;
}
.App.in-search {
    background-color: #212121 !important;
}
.UserMenu .user-initials {
    background-color: #616161 !important;
}
.CoursesItem .column-info > a:hover {
    color: #58c1ffba;
}
header {
    background-color: #212121 !important;
}
pre {
    color: #a8a6ab
}
.header {
    background: none;
    border-bottom: 1px solid #676767;
}
main img {
    opacity: 0.8;
}
a {
    color: #58c1ffba
}

================================

cowkrakowie.pl

INVERT
.logo
.nav-logo

================================

cplusplus.com

CSS
#I_mid, #I_top {
    background-image: none;
}
img[src$=".png"] {
    background-color: white;
}

================================

cppm3144.itdhosting.de

CSS
.ppm_button {
    background: ${white} !important;
}
.tableContent img {
    background-image: url("https://cppm3144.itdhosting.de/niku/ui/uitk/images/odf.png") !important;
}

================================

cqksy.cn

INVERT
td[background="image/Index_QI_CenterBar.gif"]

CSS
table {
    background-color: var(--darkreader-neutral-background) !important;
}
td[background="image/Index_QI_CenterBar.gif"],
td[background="image/Index_QI_CenterBar.gif"] a {
    color: ${white} !important;
}

================================

creative.com

INVERT
div.small-logo img.creative-logo
body:not(body[class^="storepage"]) div#all img.creative-logo
.sb-upgrade > h3
.sb-upgrade > p
.tws-series > h3
.tws-series > p

CSS
.input::placeholder  {
    color: var(--darkreader-neutral-text) !important;
}

================================

crowdin.com

INVERT
.crowdin-navbar__logo

================================

crunchbase.com

INVERT
.identifier-nav
.tab-label

CSS
.mat-tab-label-active {
    color: var(--darkreader-neutral-background) !important;
}

================================

cryptostorm.is

INVERT
img[src*="lock.png"]
img[src*="storm.png"]

CSS
body > div {
    background-image: none !important;
}

================================

cs61a.org

CSS
table#calendar td {
    border-color: var(--darkreader-neutral-background) !important;
}

================================

css-tricks.com

CSS
.article-article {
    background: ${white} !important;
}

================================

cubawiki.com.ar

INVERT
.mwe-math-fallback-image-inline
.mwe-math-fallback-image-display

================================

cynkra.com

INVERT
.btn-outline-dark
.shadow
.navbar-dark .navbar-brand
.cynkra-logo-font

CSS
.btn-outline-secondary {
    color: var(--darkreader-neutral-text) !important;
}

================================

cyprus-mail.com

INVERT
.custom-logo-link

================================

czypada.pl

INVERT
#map

================================

czyztak.pl

INVERT
img[alt*="Logo"]

================================

daily.afisha.ru

INVERT
.sprite-logo-daily
.headline__open
.search-icon
.sprite-search-black-26
mark

================================

dailydot.com

INVERT
h1.u-about-pitch

CSS
svg.main-logo.inline-flex g g[fill="#110133"] {
    --darkreader-inline-fill: ${#110133} !important;
}

================================

dailywritingtips.com

CSS
blockquote {
    z-index: 0 !important;
}

================================

daltonmaag.com

INVERT
#panel-3 img

================================

danyk.cz

CSS
body {
    background-image: none !important;
}

================================

darcs.net

INVERT
#header > a > img

================================

darksky.net

INVERT
.currentLocationButton
.searchButton
span[class^="skycon swip"]
#right-arrow
#left-arrow

================================

dash.cloudflare.com

INVERT
a[data-testid^="link-homepage"]

CSS
.monaco-editor-background, .monaco-editor .margin {
    background-color: ${rgb(226 229 231)} !important;
}

================================

dashboard.thechurchapp.org

INVERT
#ember62

================================

datacamp.com

CSS
video#vjs_video_3_html5_api.vjs-tech {
    transform: translate(0px, 0px) !important;
    background-color: rgb(175, 175, 175, 0.5);
}

================================

daum.net

CSS
.txt_pctop,
.bg_login {
    background-image: none !important;
}

================================

dawn.com

INVERT
img[alt="Dawn Logo"]

================================

debian.org

INVERT
#logo

CSS
body {
    background-image: none !important;
}

================================

debijbel.nl

INVERT
.siteLogo
.filter-item__link > img

================================

decathlon.in

CSS
img {
    mix-blend-mode: normal !important;
}

================================

decathlon.pl

INVERT
.dkt_logo
.benefit-icon

IGNORE IMAGE ANALYSIS
*

================================

deccanchronicle.com

INVERT
img[src$="logo.png"]

================================

deepl.com

INVERT
.dl_ad_pro__features_item::before
.dl_logo_text

================================

deeplearningbook.org

INVERT
.opened > img[src]

CSS
body {
    background-color: transparent !important;
}
#page-container {
    background-image: none !important;
}

================================

deezer.com

CSS
.slider-track .gradient-default {
    background-image: linear-gradient(1deg, var(--color-dark-grey-800) 13%, var(--color-white));
}

================================

delphipraxis.net

CSS
.ipsNavBar_secondary {
    background-color: var(--darkreader-neutral-background) !important;
}
.ipsNavBar_secondary::before {
    border-bottom-color: var(--darkreader-neutral-background) !important;
}

================================

dennisbareis.com

CSS
body {
    background-image: none !important;
}

================================

deno.land

INVERT
img[src="/logo.svg"]

================================

designobserver.com

INVERT
.dologo

================================

desmos.com

INVERT
.dcg-grapher

CSS
.dcg-container.dcg-inverted-colors {
    filter: hue-rotate(180deg) !important;
}

================================

detexify.kirelabs.org

INVERT
.symbol

================================

dev.azure.com

CSS
:root {
    --nav-header-background: var(--darkreader-neutral-background) !important;
}

================================

dev.dota2.com

CSS
.navtabs li a.navtab:hover,
.navtabs li.selected a.navtab {
    color: black !important;
}

================================

dev.to

INVERT
img[src$=".svg"]
path[d^="M19.099 23.508c0"]

CSS
body, h1, h2, h3,
h4, h5, h6, a,
.content-classification-text {
    color: #e8e6e3 !important;
}

================================

developer.android.com
developer.android.google.cn
tensorflow.org
quantumai.google

INVERT
.devsite-site-logo

CSS
:root {
    --devsite-header-color-upper: var(--darkreader-neutral-background);
    --devsite-footer-background: var(--darkreader-neutral-background);
    --devsite-table-heading-background: var(--darkreader-neutral-background);
    --devsite-search-form-background-active: rgba(255, 255, 255, 0.05);
    --devsite-searchbox-inactive: rgba(255, 255, 255, 0.05);
    --devsite-searchbox-hover: rgba(255, 255, 255, 0.1);
    --devsite-searchbox-active: rgba(255, 255, 255, 0.05);
    --devsite-inline-code-background: ${#f1f3f4}
}
devsite-search .devsite-searchbox::before {
    background-color: unset;
}
.devsite-wrapper {
    background-color: inherit;
}
button {
    background-color: inherit;
}
code {
    background-color: rgba(255, 255, 255, 0.05);
}

================================

developer.apple.com

CSS
.disabled {
    color: ${gray} !important;
}
pre,
.code-listing {
    background-color: var(--darkreader-neutral-background) !important;
}

IGNORE IMAGE ANALYSIS
.screen-hero
.screen-fonts
.screen-tabs
.screen-completion
.screen-swiftui

================================

developer.chrome.com

CSS
.featured-card__title-bar > svg,
.card-title-bar > svg,
.navigation-rail__icon > svg {
    fill: ${black} !important;
}
.search-box__btn > svg {
    fill: ${white} !important;
}

================================

developer.mozilla.org

INVERT
.logo
.copy-icon
.bc-browsers > th > span::before
.bc-platforms > th::before
a.external::after
.bc-has-history > .bc-history-link
.ic-footnote
.ic-non-standard
.breadcrumbs-container li .breadcrumb-penultimate::after
.breadcrumbs-container li .breadcrumb::after
ul.main-menu .top-level-entry::before

CSS
.search-results-list mark {
    color: var(--darkreader-neutral-text) !important;
}

IGNORE IMAGE ANALYSIS
.main-page-content .code-example .copy-icon

================================

developer.salesforce.com

CSS
.slds-tabs_card {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

developers.arcgis.com

CSS
a {
    text-shadow: none !important;
}

================================

developers.google.com

INVERT
.devsite-google-wordmark

CSS
:root {
    --devsite-header-color-upper: var(--darkreader-neutral-background) !important;
    --devsite-searchbox-inactive: var(--darkreader-neutral-background) !important;
    --devsite-button-background: var(--darkreader-neutral-background) !important;
    --devsite-footer-background: var(--darkreader-neutral-background) !important;
}
button,
a.button,
.devsite-landing-row-item {
    border-color: var(--darkreader-border--devsite-select-border) !important;
}
.pre-style, 
code, 
pre {
    background: var(--darkreader-neutral-background) !important;
}
.devsite-table-wrapper > table > thead > tr > th {
    color: var(--darkreader-neutral-text) !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

di.com.pl

INVERT
img[alt*="Dziennik Internautów"]

CSS
div#naglowek {
    background-image: none !important;
}

================================

dianping.com

INVERT
.logo

================================

dict.cc

CSS
#langbar {
    background-image: none !important;
}

================================

dictionary.cambridge.org

INVERT
.cb.hao.lpt-2
.cb.hv-2.lmr-10

================================

differencebetween.net

CSS
body,
#page-wrapper {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

digg.com

CSS
.digg-story__title-link {
    color: ${black} !important;
}
.digg-story__description {
    color: ${black} !important;
}

================================

disconnect.me

INVERT
img[src*="vpnCoverageMap"]
img[alt="Toolbar button"]
#press-strip > div > img

CSS
#accordion {
    background-image: none !important;
}

================================

discord.com

INVERT
div[class^="botPermissions"] > ul > li > div > div > span::after

CSS
div[class^="pill"][class*="wrapper"] > span[class^="item"],
div[class*="modeUnread"] > div[class^="unread"] {
    background-color: var(--darkreader-neutral-text) !important;
}
nav[aria-label="Servers sidebar"] foreignObject {
    mask: none !important;
    border-radius: 100% !important;
    transition: all .5s;
}
nav[aria-label="Servers sidebar"] foreignObject:hover {
    border-radius: 30% !important;
}
path[class^="wavePath"] {
    fill: ${rgb(226, 224, 220)} !important;
}
:root {
    --page-max-width: 1260px !important;
    --num-grid-columns: 4 !important;
    --page-gutter: 24px !important;
    --section-spacing: 56px !important;
}
@media screen and (min-width: 768px) {
    :root {
        --num-grid-columns: 8 !important;
        --page-gutter: 40px !important;
        --section-spacing: 80px !important;
    }
}
@media screen and (min-width: 1024px) {
    :root {
        --num-grid-columns: 12 !important;
        --section-spacing: 120px !important;
    }
}

================================

discourse.haskell.org

INVERT
.logo-big

================================

discover.forem.com

INVERT
body > div:nth-child(1) > svg:nth-child(1)

================================

discover.manjaro.org

CSS
html, body {
    background-image: url("/static/images/tux.svg") !important;
    background-repeat: no-repeat !important;
    background-position: bottom 0 left -12px !important;
    background-size: 60px !important;
    background-attachment: fixed !important;
    background-color: var(--darkreader-neutral-background) !important;
}

IGNORE IMAGE ANALYSIS
body

================================

disk.yandex.*
docviewer.yandex.*

INVERT
.logo

================================

ditu.baidu.com
map.baidu.com
maps.baidu.com

INVERT
#activity-banner-panel
.BMap_bubble_pop
.BMap_contextMenu
.BMap_cpyCtrl
.BMap_scaleBarBG
.BMap_scaleTxt
.BMap_simple_bubble_pop
.BMap_stdMpZoom
.BMapLabel
#bt_trafficCtrl
.cbtlinetop
.cbtToolBar
.icon
#iw_tool_icon
.jump_back
#map-operate
#maps
.message-banner
#message-panel
.more-device
#nearby-searchbox-hint
#nearby-searchbox-hint-center
.pass-forceverify-wrapper
.pass-form-item
.route-button
.tang-foreground
.tang-pass-qrcode-title
#userSignPanel

CSS
#newuilogo {
    filter: brightness(80%);
}
#sole-input {
    color: var(--darkreader-neutral-text) !important;
}

================================

dlagentlemana.pl

INVERT
div.f-grid-3.logo-column > a > img
div.opineo-side-slider-widget > button
.logo-bar img

================================

dle.rae.es

CSS
.bloque_publi DE,
.spr_fundacionlacaixa,
.spr_dle90x110,
.spr_logo-enclave-rae,
.spr_google-play-badge-100x39,
.spr_badge-appstore-lrg-96x28,
.spr_edtri,
.spr_ay,
.spr_cita,
.spr_unidrae,
.spr_consultas,
.spr_pnwe2,
img[src="/images/logos/ASALE2.png"],
img[src="/images/logos/rae.png"],
img[src="/images/LibroDeEstilo_300.jpg"],
img[src="/app/doc/es/img/dle.jpg"] {
    filter: invert(93.7%) hue-rotate(180deg) contrast(90.6%) !important
}

================================

dmit.io

INVERT
.logo

================================

dnd5e.wikidot.com

CSS
.feature {
    background-color: var(--darkreader-neutral-background) !important;
}
.feature.offcolor {
    background-color: ${rgb(255, 250, 180)} !important;
}
th {
    background-color: ${rgb(230, 216, 170)} !important;
}

================================

dndbeyond.com

CSS
.mon-stat-block,
.mon-stat-block::before,
.mon-stat-block::after,
body {
     background-image: none !important;
     background-color: var(--darkreader-neutral-background) !important;
}
.read-aloud-text,
.more-info::after,
.details-container::after {
     border-image: none !important;
}
::selection {
    background-color: var(--darkreader-selection-background) !important;
}

================================

dnsleaktest.com

INVERT
img[src$="logo.png"]
img[src$="what-is-a-dns-leak.png"]
img[src$="transparent-dns-proxy.png"]

================================

dnslytics.com

INVERT
img[src*="logo.png"]

================================

doba.pl

INVERT
.social
img[src*="logo.png"]
img[src*="/img/powiaty/"]

================================

dobreprogramy.pl

CSS
header a[href="/"] svg {
    fill: #fff !important;
}

================================

docs.codacy.com

INVERT
div.footer-logo > img[alt="Codacy"]

================================

docs.google.com

INVERT
.docs-icon
.punch-filmstrip-controls-icon
#docs-editor canvas
.docs-homescreen-icon
.kix-equation-toolbar-icon
.kix-equation-toolbar-palette-icon
.cell-input
.formula-content
.docs-instant-button-bubble-icon-container
.docs-gm .docs-dialog .modal-dialog-title-close::after
.docs-preview-palette-item
.goog-menuitem-checkbox
.goog-dimension-picker-unhighlighted
.goog-dimension-picker-highlighted
#docs-star
.rs-role-icon
.toggle-link-icon
.link-management-drop-down-icon
.vs-icon
.vpc-icon
.docs-analytics-img
.share-butter-copy-icon
.doc-previews-indicator-popover .docs-link-bubble-mime-icon
img[src$="googlelogo_dark_clr_74x24px.svg"]
.exportUnderline
.freebirdMaterialIconIconEl
.quantumWizTogglePapercheckboxCheckMark
#docs-titlebar-share-client-button .scb-button-icon:not([class*="white"])
body[itemtype*="PresentationObject"] #docs-titlebar-share-client-button .scb-button-icon
g.punch-filmstrip-indicator > image
.docs-gm .docos-icon-overflow-three-dots-size

CSS
.docs-preview-palette-item {
    border: transparent !important
}
.cell-input {
    background-color: ${black} !important;
    color: ${white} !important;
}
.cell-input > span, .cell-input > font {
    --darkreader-inline-color: ${white} !important;
}
.kix-cursor-caret {
    border-color: ${black} !important;
}
.kix-selection-overlay {
    background-color: var(--darkreader-selection-background) !important;
    border: var(--darkreader-selection-background) !important;
}
.ia-invite-controls-area {
    background-color: transparent !important
}
.docs-gm .docs-revisions-switch .apps-ui-material-slide-toggle-thumb {
    background-color: rgb(43, 46, 48) !important;
}
.docs-gm .docs-revisions-switch.apps-ui-material-slide-toggle-container-checked .apps-ui-material-slide-toggle-thumb {
    background-color: rgb(9, 64, 155) !important;
}
.docs-text-ui-cursor-blink {
    fill: ${black} !important;
}
.docs-title-input-label:not([style*="pointer-events: auto"]) > #docs-title-input-label-inner {
    visibility: hidden !important;
}
input.docs-title-input {
    color: var(--darkreader-neutral-text) !important;
}
.sketchy-text-background + g g rect {
    fill: var(--darkreader-selection-background) !important;
}

================================

docs.google.com/picker

INVERT
#doclist div > div + div div[style="user-select: none;"]:not([role="tab"]):not([role="menuitemradio"]):not([role="menuitem"]) > div:not([aria-pressed]):not([role="tab"]):not([class$="highlightsgrid"]):first-of-type
#doclist div > div + div label + input + div[style]
img[src$="search-black.png"]

================================

docs.sentry.io

CSS
.document-wrapper {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

dominos.*

CSS
svg.trackerbarimage:not(.complete svg.trackerbarimage) {
    fill: var(--darkreader-neutral-background) !important;
}
.stage:not(.complete) > .title > span {
    color: var(--darkreader-neutral-background) !important;
}
.tracker-page-container div.tracker-module.pizza-tracker .tracker-container > div.stage.complete [class^="chevron-"] {
    fill: #689d30 !important;
}
.tracker-page-container div.tracker-module.pizza-tracker .tracker-container > div.stage [class^="chevron"] {
    stroke: var(--darkreader-neutral-text) !important;
}

================================

doodle.com

INVERT
svg.d-checkmark .d-background

================================

doordash.com

INVERT
input[kind="RADIO"][id^="Toggle"]::after

================================

dotaunderlords.gamepedia.com
dota2.gamepedia.com
dota2.fandom.com

CSS
#right-navigation {
    background-image: none !important;
}

IGNORE IMAGE ANALYSIS
#left-navigation

================================

dou.ua

CSS
.g-right-shadowed,
.img::before, .img::after {
    background-image: none !important;
}

================================

downloads.khinsider.com

CSS
body {
    background: none !important;
}
#faux {
    background-image: none !important;
}

================================

drive.google.com

INVERT
img[src$="google_gsuite"]
div[role="menuitem"] svg
div[role="dialog"][style^="width: 350px;"] div[role="button"][tabindex="0"]
div[role="dialog"] ~ div[role="menu"] > div[role="menuitem"] > div > div:not([style*="background-image"])
img[src$="type/audio/mp3"]
div[role="listitem"] > div > div > div > svg[fill="#000000"] > path
div[role="menu"] > div > div[role="menuitem"] > div > div > div
div[role="menu"] > div > div[role="menuitem"][class*=" "] > div > :last-child
div[data-label="nd"] > div > div > svg > path[fill="#000000"]
div[role="document"] > div[role="button"] .a-b-c

CSS
span[data-type="spelling"] {
    mix-blend-mode: normal !important;
}
div[role="menu"] div[role="menuitem"][class*=" "] > div > div > div,
div[role="button"][aria-disabled="true"] > div {
    filter: invert(50%) !important;
}

IGNORE INLINE STYLE
div[role="presentation"] svg

================================

drive.google.com/file

INVERT
div[role="menu"] > div[role="menuitem"] > div > div > div
div[role="menu"] > div[role="menuitem"] > div > div

================================

drive.google.com/picker

INVERT
div[role="menu"] div[role="menuitem"]:not([class*=" "]) > div > div > div
div[role="button"][tabindex="0"]

CSS
div[role="menu"] div[role="menuitem"][class*=" "] > div > div > div,
div[role="button"][aria-disabled="true"] > div {
    filter: invert(50%) !important;
}

IGNORE INLINE STYLE
svg[class=""]
svg[class=""] *

================================

droid-life.com

CSS
.collection-posts__list .preview__picture {
    mix-blend-mode: normal !important;
}

================================

dropbox.com

INVERT
.dl-dropbox
.drops-empty-page-header-image
.restorations-education__df-help__image
.search__view--empty img
.plan-status-section__icon img

IGNORE INLINE STYLE
.home__suggest_image path
.selection-preview-pane__icon path

================================

drupal.org

CSS
input[type="image"] {
  background: none;
}

================================

dsausa.org

INVERT
.navbar-toggler-icon

================================

dtf.ru

INVERT
mark

================================

duckduckgo.com

INVERT
.is-active.js-tw-card.bg-clr--white.tw-card > .tw-card__footer > a.js-tweet-action.tw-card__action.tw-card__link > [src="/assets/common/slider/twitter-like.svg"]
[src="https://duckduckgo.com/assets/icons/thirdparty/wikipedia.svg"]

CSS
.justify-content-center.align-items-center.flex-lg-column.flex-md-row.card-1.card > .card-body > h3,
.justify-content-center.align-items-center.flex-lg-column.flex-md-row.card-2.card > .card-body > h3,
.justify-content-center.align-items-center.flex-lg-column.flex-md-row.card-3.card > .card-body > h3,
.justify-content-center.align-items-center.flex-lg-column.flex-md-row.card-1.card > .card-body > p,
.justify-content-center.align-items-center.flex-lg-column.flex-md-row.card-2.card > .card-body > p,
.justify-content-center.align-items-center.flex-lg-column.flex-md-row.card-3.card > .card-body > p,
.js-hiring-open-pos.join__btn.mt-5.btn.col-sm-auto.col{
    color: var(--darkreader-neutral-background) !important;
}
.logo_homepage {
    background-image: url("https://duckduckgo.com/assets/logo_homepage.alt.v108.svg") !important;
}
.header__logo {
    background: no-repeat center url("https://duckduckgo.com/assets/logo_header.alt.v108.svg") !important;
}

IGNORE INLINE STYLE
#color_picker_container .sample
.zci--color_codes .circle

================================

duo.google.com

INVERT
.gb_se .gb_ye:not(.gb_Ae)

================================

duolingo.com

INVERT
.Z392z
._24NNT
[data-test="level-crown"]

CSS
button[aria-disabled="true"] > span {
    opacity: 0.3 !important;
}

IGNORE INLINE STYLE
div[data-test="challenge-translate-prompt"] svg > g[clip-path] *

================================

dvizhcom.ru

INVERT
.mobile-header-icon:nth-last-of-type(3)
.mobile-header-icon:nth-last-of-type(2)
.mobile-header-icon:nth-last-of-type(1)

================================

dw.com

CSS
#bodyMover {
    background-image: none !important;
}

================================

dynadot.com

INVERT
img[data-src="/tc/navbar-logo.png"]
#navigation-shopping-cart

================================

dziennik.pl

INVERT
img[alt="Dziennik"]

================================

dzienniknaukowy.pl

INVERT
.top-logo

================================

ea.com

INVERT
.left_context .context dl dd.select
.modifyMobilePhoneBtn

CSS
img[src*="white-bg-ea-bg-global-white"] {
    display: none !important;
}
body.origin-com,
.left_context .context {
    background-image: none !important;
}
.body_foot .language .select_language .languageText,
.buttonstyle_3 span,
.origin-ux-drop-down-selection > span,
.origin-ux-button-primary > span,
.origin-ux-button-secondary > span {
    color: var(--darkreader-neutral-background) !important;
}

================================

easypost.com

INVERT
.logo
.progress-bar

================================

ebok.pgnig.pl

INVERT
img[src*="logo"]

================================

ebok.vectra.pl

IGNORE IMAGE ANALYSIS
.side-floater

================================

ebooks.cpm.org

INVERT
img[alt="Review and Preview problems below"]

================================

economist.com

INVERT
#ds-wordmark-1843 path:nth-child(5)

IGNORE INLINE STYLE
#ds-economist-logo > path
.ds-share-link > svg > g > circle

================================

edstem.org

CSS
.tab-bar-dark .active .tbb-tab-side .fill {
    fill: rgb(34, 37, 38) !important;
}
.tab-bar-dark .tbb-tab-side .fill {
    fill: rgb(26, 28, 29) !important;
}

================================

education.github.com

INVERT
img[alt="GitHub Education"]
.octicon-logo-github

================================

eduke32.com

CSS
.main {
    background-image: none !important;
}

================================

edulastic.com

INVERT
img.keyboardButton

================================

eduserver.ru

CSS
section.content {
    background: var(--darkreader-neutral-background) !important;
}

================================

edziecko.pl

INVERT
.pageHeadBrand .col1 img

================================

eff.org

INVERT
#masthead #main-menu-nav form.search-site button[type="submit"]

CSS
select > optgroup,
select > optgroup > option {
    background-color: var(--darkreader-neutral-background) !important;
}
#masthead #main-menu-nav .pane-main-nav-menu > ul > li a::before {
    background-color: var(--darkreader-neutral-text) !important;
}

================================

elearning.utdallas.edu

INVERT
.contentBox.contentBox-edit
.content.clearfix

================================

electrical-symbols.com

INVERT
.table-striped img

================================

electrical4u.com

INVERT
img[src*="equation"]

================================

electricitymap.org

INVERT
.name
.circular-gauge
img[alt="logo"]
.capacity
.mapboxgl-canvas

================================

element.io

INVERT
.header--logo--img
.ems-nav__logo

================================

elementalmatter.info

INVERT
[src$="atom.gif"]
[src$="periodic-atomic.gif"]

CSS
td {
    background: var(--darkreader-neutral-background) !important;
}

================================

elp.northumbria.ac.uk

INVERT
.selected-answer

================================

emacswiki.org

CSS
body {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

endomondo.com

INVERT
.header-shop-logo
.footer-shop-logo
.eoFeedWorkout-map-image
.workoutMap img[src*="maps"]

CSS
.highcharts-background{
    fill: rgb(24, 26, 27) !important;
}
.highcharts-data-labels text{
    text-shadow: none !important;
}

================================

enduhub.com

CSS
.navbar .nav > li > a {
    text-shadow: rgb(40, 43, 54) 0px 1px 0px !important;
}
.blurme, .blurme a {
    filter: unset;
    color: inherit;
}

================================

enea.pl

INVERT
.menu__logo
.navbar-brand
.slider-content > .slider-title

CSS
.col-lg-12.col-md-12 > div > div > p {
    color: var(--darkreader-neutral-background) !important;
}

================================

enjen.net

CSS
body {
    background-image: none !important;
}
input.input-medium {
    background-image: none !important;
}
select.input-small {
    background-image: none !important;
}

================================

eshop-switch.com

INVERT
.logo

================================

eshot.gov.tr

CSS
a.navbar-brand img {
    filter: hue-rotate(90deg) invert(1) hue-rotate(90deg) saturate(15);
}
#heading .left, #heading .sub-menu .left-xs, #heading .sub-menu .right-xs {
    background-color: transparent !important;
    background-image: none !important;
}
section#announcements-area .container {
    background-image: none;
}

================================

esphome.io

INVERT
img.component-image

================================

esquire.com

INVERT
.nav-logo

CSS
.marquee-logo-link > svg {
    background-color: ${black} !important;
}

================================

etsy.com

INVERT
.banner-container
.secondary-banner-title
.wt-radio label:before
.review-star.rating:not(.lit)
div.rating.nolit:not(:hover) > .ss-star

================================

evernote.com

INVERT
.global-logo svg

================================

ewybory.eu

INVERT
.navbar-brand

================================

exmo.me

INVERT
canvas

================================

expedia.com

INVERT
.uitk-header-brand-logo

================================

experiencia.xpi.com.br

INVERT
#yield-portal-header a.xpi__header__logo svg path
#yield-portal-header a.xpi__error__logo svg path
#chart-profitability > div:nth-child(2) > svg > g > g:nth-child(2) > g:nth-child(1) > g > g > g > g:nth-child(2) > g > g:nth-child(1) > g:nth-child(2) > g:nth-child(2) > g > text > tspan

CSS
#chart-patrimony g[role='menu'] ~ g tspan {
    color: var(--darkreader-neutral-text) !important;
}

================================

explainxkcd.com

INVERT
.mwe-math-fallback-image-inline
.mwe-math-fallback-image-display
.mw-ext-score
.main-footer-menuToggle
img[src*="Loudspeaker.svg"]
img[alt="The Signpost"]

CSS
.diff-addedline .diffchange {
    background-color: ${lightblue} !important;
}
.diff-deletedline .diffchange {
    background-color: ${#feeec8} !important;
}

IGNORE INLINE STYLE
.legend-color
.infobox > tbody > tr > td[style*="background-color"]

================================

expressjs.com

CSS
body {
    background-image: none !important;
}

================================

ezgif.com

INVERT
#logo

================================

f-droid.org

INVERT
.anti-feature-icon

================================

facebook.com

INVERT
._2o89
._2q08
.sx_af7fe0
.sx_7ed17e
.sx_a4a936
.sx_4d607f
.sx_aca067
.sx_77228a
.sx_51302f
._2yu5
._3iiv
._3pao
.kv0qyzoi
.sx_426ea6
.sx_b77acf
._2gb3
._7sjb
.sx_b9f33b
.sx_2e7846
.sx_6d18f4
.monochrome
.repliedLast
.sx_73ef60
._81u_ .img
._3ffs li
#profile_intro_card_bio i
.uiScrollableAreaWrap .uiList button[type="submit"] i._3-8_
.editPhoto i._3-8_
.sx_08856a
.sx_ac12f7
.sp_hk4DJV_EEeW
.sp_V53xxlprDHX_1_5x
.sp_V53xxlprDHX_2x
#pagelet_ego_pane button .img._3-8_
#homepage_panel_promote_footer_pagelet button .img._3-8_
#event_tabs #reaction_units span img
.fbPlaceFlyoutWrap img
._83aj
._7xv1
._83ak
._8g4q

CSS
.fbNubButton {
    background-image: none !important;
}
.jewelItemNew ._33e {
    background-color: ${#d0d1d3} !important;
}
._5qxm {
    background-color: rgba(0, 0, 0, 0.25);
    background-blend-mode: color;
}
.sx_cf4e6b {
    filter: brightness(250%) !important;
}
._34k2 {
    filter: brightness(1000%) !important;
}
._3hx- ._4tdt,
._3hx- ._5wd4,
._3hx- ._6vu5 ._5w-5,
._3hx- ._1aa6::after,
._3hx- ._6vu5 ._31o4,
._3hx- ._2cnu:only-of-type ._5wdf,
._3hx- ._5w0o,
._3hx- ._5z-5,
._3hx- ._16ys._3e7u,
._3hx- ._49or .__6j,
._3hx- ._324d .__6j,
._llj {
    border-color: ${#eee} !important;
}
._3hx- ._ua1,
._3hx- ._40qi,
._3hx- ._5ye6,
._3hx- ._llj,
._3hx- ._1a6y,
._31o4,
._3_bp,
._4gd0,
._49or,
._40fu {
    background-color: ${#eee} !important;
}
.fbNub._50mz .fbNubFlyoutFooter::after {
    background-color: ${rgba(0, 0, 0, .15)} !important;
}
.fbNub._50mz .fbNubFlyoutInner,
._4cd8 ._69pt,
._4mq3 .fbNubFlyout,
._4mq3 .fbNubButton {
    box-shadow: 0 0 0 1px ${rgba(0, 0, 0, .15)} !important;
}
:root, .__fb-light-mode {
    --filter-disabled-icon: invert(100%) opacity(30%) !important;
    --filter-placeholder-icon: invert(59%) sepia(11%) saturate(200%) saturate(135%) hue-rotate(176deg) brightness(96%) contrast(94%) !important;
    --filter-primary-icon: invert(100%) !important;
    --filter-secondary-icon: invert(62%) sepia(98%) saturate(12%) hue-rotate(175deg) brightness(90%) contrast(96%) !important;
    --filter-warning-icon: invert(77%) sepia(29%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(128%) hue-rotate(359deg) brightness(102%) contrast(107%) !important;
    --filter-blue-link-icon: invert(73%) sepia(29%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(103.25%) hue-rotate(189deg) brightness(101%) contrast(101%) !important;
    --filter-positive: invert(37%) sepia(61%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(115%) hue-rotate(91deg) brightness(97%) contrast(105%) !important;
    --filter-negative: invert(25%) sepia(33%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(110%) hue-rotate(345deg) brightness(132%) contrast(96%) !important;
}
div[role=article] div.k4urcfbm[aria-hidden="true"] {
    background-color: rgba(0, 0, 0, 0.25);
    background-blend-mode: color;
    --darkreader-inline-bgcolor: none !important;
}
div[data-pagelet="Stories"] .ha302278 {
    background-color: rgba(0, 0, 0, 0.4) !important;
}
div[aria-label="Change volume"] .ha302278,
div[aria-label="Change Position"] .ha302278,
._3paq {
    background-color: rgba(255, 255, 255, 0.4) !important;
}
.r4vyqqch {
    background-color: var(--fds-white-alpha-50) !important;
}
.lyi53s4r {
    background-color: rgba(88,144,255,.9) !important;
}
.tdjehn4e, .oo1teu6h {
    background-color: rgba(255, 255, 255, 0.1) !important;
}
.tdjehn4e:hover, .ovq5dppa:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}
.k19f6yf2 {
    background-color: var(--press-overlay) !important;
}
.cxbav39q {
    background-color: rbga(0, 0, 0, 0.8) !important;
}
.rnr61an3 {
    background-color: var(--hover-overlay) !important;
}
.qbubdy2e {
    fill: none !important;
}
.s1i5eluu {
    background-color: var(--primary-button-background) !important;
}
.q66pz984 {
    color: var(--accent);
}
.esnais5j, ._8bb_ img, ._8bb_ i,
._8bb_ video, ._8bb_ ._w80, ._8bb_ ._7umt ._47yj,
._8bb_ [style*='background-image: url'] {
    filter: none !important;
}
div[role="button"] > i.hu5pjgll:not(.sp_Jc8OKpJq5NW, .sp_T89CCTT7d9Z),
a[role="link"] > i.hu5pjgll, ._3w97 {
    filter: var(--filter-secondary-icon) !important;
}
._2yua, ._3pas {
    background-color: #888 !important;
}
._2yu8 {
    background-color: rgba(255, 255, 255, .5) !important;
}
img[src*="/map"], [style*="map"],
._8bb_ img[src*="/map"], ._8bb_ [style*="map"],
img[src*="mapy.cz"], ._8bb_ img[src*="mapy.cz"],
img[src*="%2Fmap"], ._8bb_ img[src*="%2Fmap"],
img[src*="map.php"], ._8bb_ img[src*="map.php"],
img[src*="www.traseo.pl%2Froute"], ._8bb_ img[src*="www.traseo.pl%2Froute"] {
    filter: invert(100%) hue-rotate(180deg) !important;
}
.j7vl6m33 {
    fill: var(--always-white) !important;
}
::after {
    border-left-color: #1c1e20 !important;
    border-bottom-color: #1c1e20 !important;
    border-top-color: #1c1e20 !important;
    border-right-color: #1c1e20 !important;
}

IGNORE INLINE STYLE
[role="button"] svg
[role="button"] svg line
div svg[viewBox="0 0 36 36"] mask path
mask > rect
mask > circle
g > rect

IGNORE IMAGE ANALYSIS
*

================================

fakespot.com

INVERT
.logo
.fs-logo
img[alt="Header menu"]
.app-button > img

================================

fakt.pl

INVERT
.header-links

================================

fanatical.com

INVERT
.trustpilot > .logo-container > a > img

IGNORE INLINE STYLE
div.drm-container svg g

================================

fandom.com

INVERT
.wds-global-navigation__logo-fandom

CSS
.fandom-community-header__background.cover {
    z-index: 0 !important;
}

IGNORE INLINE STYLE
.wds-global-navigation__logo-image g path
.wds-global-footer__header-logo g path
.wds-global-footer__link svg g path

================================

fantasy.premierleague.com

INVERT
.ism-table

================================

farside.ph.utexas.edu
mathpages.com
mathprofi.ru
mathprofi.net
mathworld.wolfram.com
reference.wolfram.com
terrytao.wordpress.com
wolframalpha.com

INVERT
img:not([src^="/_next/static/images/"])

================================

fast.com

INVERT
div.logo
div.powered-by

IGNORE IMAGE ANALYSIS
div.logo
div.powered-by

================================

fastmail.com

INVERT
.logo svg

================================

fastmail.com/mail

CSS
.app-listItem.is-focused,
.app-source.is-selected {
    background-color: ${lightgray} !important;
}
.app-source.is-focused {
    background-color: ${lightblue} !important;
    color: ${black} !important;
}
.v-MailboxItem-unreadbadge {
    background-color: ${darkblue} !important;
}
.v-Message-body {
    border-color: transparent !important;
}

================================

fedex.com

CSS
.fx-global-prelog-link.fx-showlogin span {
    background-image: none !important;
}

================================

feedly.com

CSS
.entry.u0:hover,
.entry--selected.u0,
.entry--selected.u0:hover,
.entry--selected.u4,
.entry--selected.u5,
button.secondary:hover {
    background-color: ${#ccc} !important;
}

================================

feynmanlectures.caltech.edu

INVERT
.figure > img[src$=".svgz"]

================================

ffmpeg.zeranoe.com

INVERT
.active

================================

fibermap.it

INVERT
img[src$="assets/images/bg-cloud.png"]

================================

figma.com

IGNORE INLINE STYLE
div[class*="paint_panels--chit"]
div[class*="modal--modalShadow"] div

================================

fileformat.info

INVERT
[src$="logo_wide.png"]
.thumbnail

CSS
body {
    background-color: var(--darkreader-neutral-background) !important;
    background-image: none !important;
}

================================

filetransfer.io

INVERT
a.logo

================================

filmweb.pl

INVERT
.ribbonLbl
.isInit.ribbon[data-state="1"]::after
.isInit.ribbon[data-state="2"]::after
.isInit.ribbon[data-state="3"]::after
.isInit.ribbon[data-state="4"]::after
.isInit.ribbon[data-state="5"]::after
.isInit.ribbon[data-state="6"]::after
.isInit.ribbon[data-state="7"]::after
.isInit.ribbon[data-state="8"]::after
.isInit.ribbon[data-state="9"]::after
.isInit.ribbon[data-state="10"]::after

CSS
.filmInfo__info {
    color: var(--darkreader-neutral-text) !important;
}

================================

filterlists.com

IGNORE INLINE STYLE
header.ant-layout-header img

================================

fio.fnar.net

INVERT
.chartjs-render-monitor

================================

firebase.google.com

INVERT
.devsite-site-logo

================================

firefox.net.cn

CSS
.btn, 
.core_follow, 
.pop_deep .ct dt.reward, 
.pages a, .pages strong, 
.design_mode_edit, 
.pop_showmsg {
    background-image: none;
}

================================

firstcontributions.github.io

CSS
.App,
.topnav {
    background-color: ${CornflowerBlue};
}

================================

fiverr.com

IGNORE INLINE STYLE
.site-logo *

================================

fivethirtyeight.com

INVERT
.logo
.site-logo
#searchform
.header-espn-link

================================

flightfinder.fi

INVERT
.logo

================================

flow.polar.com

INVERT
.brand
.detail-data-panel__icon
.sleep-chart-yaxis.end
.supergraph-canvas

CSS
.highcharts-container svg {
    fill: ${#3f3f3f} !important;
}
.card__item-icon--rounded img {
    background-color: rgba(255, 255, 255, 0.15) !important;
    background-blend-mode: color;
}
.altitudetitle, .altdescmax, .altdescmin, 
.zonedesctitle, .zonedescmax, .zonedesclight, 
.maxhighlightval, .maxhighlightname {
    color: rgb(0, 0, 0) !important;
}

IGNORE INLINE STYLE
.zonebox
.zonestartbox

================================

flowkey.com

INVERT
body.front #zone-branding-wrapper

CSS
body.html #page.page #subslogan {
    color: ${white} !important;
}

================================

flyzipline.com

INVERT
#logo

IGNORE IMAGE ANALYSIS
#logo

================================

follow.it

INVERT
.header-logo
.logo

================================

fontsinuse.com

INVERT
.fiu-header__branding
.fiu-sample-list__item img
.fiu-gallery-head__text img

================================

fontspring.com

INVERT
.grid6 .fullwidth

================================

fontsquirrel.com

INVERT
.fontlistitem

================================

foobar2000.org

INVERT
img[src="/foobarlogo.png"]

================================

foolcontrol.org

CSS
html {
    background-color: transparent !important;
}

================================

forem.com

INVERT
nav a > img[alt="Home"]
#cta > section > div > div > img

================================

forms.yandex.ru

INVERT
.header2__service-logo
.header2__logo-wrap

================================

forsal.pl

INVERT
.serviceLogo
.homePageUrl

CSS
.mini-tabs {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

forum.donanimhaber.com

CSS
body {
    background-color: var(--darkreader-neutral-background)
}

================================

forum.eset.com
forums.laptopvideo2go.com
nieidealny.pl
forum.ithardware.pl

INVERT
.ipsReact_button
img[alt="Forum komputerowe ITHardware"]
.ipsItemStatus.ipsItemStatus_custom.ipsItemStatus_read

CSS
.ipsBadge {
    --darkreader-bg--badge--background: var(--darkreader-neutral-background) !important;
}
.ipsReact_reactCount > a,
.ipsReact_reactCount > span,
.elFullInbox_menu,
.ipsHovercard,
.ipsList_reset,
.ipsMenu_auto,
.ipsMenu_footerBar,
.ipsMenu_headerBar,
.ipsMenu_innerContent,
.ipsMenu_item,
.ipsMenu_sep,
.ipsMenu_title,
.ipsPadding,
.cUserHovercard,
a[data-mentionid] {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

forum.ivao.aero

INVERT
.buttonlist

CSS
.cat_bar, .cat_bar > *, .catbg > * {
    background-color: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text) !important;
    background-image: none !important;
}
:not(a) > span, :not(a) > span > *, #footer {
    background: none !important;
}
.buttonlist a:not(.active) {
   color: var(--darkreader-neutral-background) !important;
}
ul.dropmenu li {
    border: none !important;
    background: var(--darkreader-neutral-background) !important;                                                                                                         
}
ul.dropmenu ul {
    border: 1px solid var(--darkreader-neutral-text) !important;
    background: var(--darkreader-neutral-background) !important;
}

IGNORE INLINE STYLE
#header

================================

forum.miranda-ng.org

CSS
.firstlevel {
    color: ${#666666} !important;
}

================================

forum.p300.it

CSS
.ipsReact_reactCount > a {
    background-color: var(--darkreader-neutral-background);
    border-color: var(--darkreader-neutral-text); !important;
}
.ipsType_normal {
    border-color: rgb(48,52,54);
}
.ipsMenu_headerBar,
.ipsMenu_footerBar,
.ipsEmoticons_category,
.ipsMenu_innerContent,
ul.ipsMenu, .ipsMenu > ul,
a[data-mentionid],
.cUserHovercard {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

forums.mydigitallife.net

IGNORE INLINE STYLE
#TotpQrCode

================================

forums.opera.com

INVERT
.forum-logo-wrapper

================================

forums.tomshardware.com

INVERT
img[src="/styles/tomshardware/tomshardware/toms-hardware-logo.png"]
div.trophyShowcase.trophyShowcase--postbit

================================

forvo.com

INVERT
img[src$="layout/logo.svg"]

================================

frame.work

INVERT
a[title="Framework Home"] > img[alt="Framework"]

================================

freecommander.com

CSS
.am-body,
#subsilver-nav-topic {
    background-image: none !important;
}
.header > .row-item,
.body-blok-h3 h3 {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

freelancer.com

INVERT
.LogoImg[src*="freelancer-logo.svg"]

================================

fritz.box

INVERT
div.formular input[type="radio"]
div.formular input[type="checkbox"]

CSS
div.formular input[type="radio"], 
div.formular input[type="checkbox"] {
    background-color: transparent !important;
}

================================

ftp.nluug.nl

INVERT
img[alt="[NLUUG]"]

================================

funpay.ru

INVERT
.logo-color
.logo

================================

furrychina.com

INVERT
.main_logo

================================

fusoya.eludevisibility.org

CSS
body {
    background-image: none !important;
}
td {
    color: var(--darkreader-neutral-text) !important;
    background-image: none !important;
}

================================

garmin.com

INVERT
.gh__logo

================================

gat.no

INVERT
figure.logo > img

CSS
main.container .every_board > .row:not(.row--takeover) {
    --front-background-color: var(--darkreader-neutral-background) !important;
}

================================

gazeta.pl
plotek.pl

INVERT
.column
.navigation__logo
.main-navigation__logo
.hp_redirector-circle-progress__svg

CSS
.top_section_bg, .bottom_section_bg {
    background-color: ${#e5e5e5} !important;
}

================================

gazetaprawna.pl

INVERT
.logoHeading
.homePageUrl

CSS
#menuTrigger {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

gazetaswietokrzyska.pl

INVERT
.logo

CSS
.elementor-column-wrap.elementor-element-populated {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

geeksforgeeks.org

CSS
.gsc-input-box {
    box-shadow: ${rgba(0, 0, 0, 0.3)} 0px 0px 2px !important;
}

================================

geizhals.*
skinflint.co.uk
cenowarka.pl

CSS
img,
.mocat-item__image,
.primary-nav .nav-item-type-cat-image {
    mix-blend-mode: normal !important;
}

================================

genius.com

INVERT
.texmath

================================

genshin-impact-map.appsample.com

INVERT
.bg-secondary.px-1.py-0.input-group-text > .icon
.border-top2.text-center.w-100.sidebar-footer
.gm-ui-hover-effect

================================

geogebra.org

INVERT
.icon-m
.elemText canvas
.gwt-Image
.gwt-StackPanelItem img
.GeoGebraMenuImage.menuImg
.menuImg
.buttonContent.stylebarButton
.EuclidianPanel > canvas

================================

get.google.*
get.google.*.*

INVERT
a[href*="about/products"]
span[aria-label="Settings"][role="menuitem"] path
span[aria-label="About Album Archive"][role="menuitem"] path
span[aria-label="Download photo"][role="menuitem"] path
span[aria-label="Manage in Google Photos"][role="menuitem"] path

================================

getmimo.com

INVERT
a[title="Home"][href] > svg
div[class^="FifthSection___StyledDiv8"] > svg
div[class^="Footer__Row"] > :nth-child(1) svg

CSS
login-container {
    background: var(--darkreader-neutral-background) !important;
}

================================

getpocket.com

CSS
a .title span {
    text-shadow: none !important;
}

================================

gg.pl

INVERT
.chat-btns
.profile-close i
.settings-close i

CSS
.sr-contact-name span {
    background-image: none !important;
}

================================

ggmania.com

CSS
body {
    background-image: none !important;
}

================================

git-scm.com

INVERT
img[alt="Git"]

CSS
body, #masthead {
    background-image: none !important;
}

================================

github.blog

INVERT
a[href="https://github.com"]
.site-branding > svg
.icon-logo-github
.aligncenter
.wp-image-56594
.wp-image-56590
[src^="https://github.blog/wp-content/uploads/2019/03/BlogHeaders_Aligned_CHANGELOG"]
[src^="https://i0.wp.com/user-images.githubusercontent.com/"]
[src^="https://i1.wp.com/user-images.githubusercontent.com/"]
[src^="https://i2.wp.com/user-images.githubusercontent.com/"]

================================

github.com
github.*.*

INVERT
[src="https://github.githubassets.com/images/modules/site/icons/footer/github-logo.svg"]
[src^="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src^="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src^="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/home/<USER>"]
[src="https://github.githubassets.com/images/modules/site/codespaces/codespaces-icon.png"]
[src="https://github.githubassets.com/images/modules/site/codespaces/dependency-rust.png"]
[src="https://github.githubassets.com/images/modules/site/codespaces/dependency-3.png"]
[src^="https://github.githubassets.com/images/modules/site/codespaces/dependencies-"]
[src="https://github.githubassets.com/images/modules/site/codespaces/commit-3.png"]
[src="https://github.githubassets.com/images/modules/site/codespaces/extensions-1.png"]
[src="https://github.githubassets.com/images/modules/site/codespaces/extensions-2.png"]
[src="https://github.githubassets.com/images/modules/site/codespaces/commit-workflow.png"]
[src="https://github.githubassets.com/images/modules/site/codespaces/workflow-view.png"]
[src="https://github.githubassets.com/images/modules/site/codespaces/code.png"]
[src="https://github.githubassets.com/images/email/explore/explore-gradient-icon.png"]
[src="https://raw.githubusercontent.com/github/explore/80688e429a7d4ef2fca1e82350fe8e3517d3494d/collections/learn-to-code/learn-to-code.png"]
.js-viewport-aware-video.color-bg-primary.width-full.d-block.codespaces-hero-video
.build-in-animate.position-relative.mb-6-fluid.box-shadow-active-mktg.mx-auto.home-mobile-iphone.build-in-slideY.js-build-in > .js-viewport-aware-video.width-full
.overflow-hidden.position-relative.box-shadow-active-border-mktg.rounded-2-fluid.color-bg-primary.build-in-scale-fade.js-build-in-item
.build-in-animate.overflow-hidden.box-shadow-active-border-mktg.rounded-2-fluid.position-relative.home-workflow-comp.js-build-in-item
.mx-lg-auto.col-lg-7.col-12

CSS
.markdown-body code,
.markdown-body pre,
.markdown-title code {
    background-color: ${rgba(27, 31, 35, 0.1)} !important;
}
.markdown-body pre code {
    background-color: transparent !important;
}
.refined-github .dashboard .js-all-activity-header + div {
    background-color: ${#e4e5e9} !important;
    border-color: ${#bbc1c9} !important;
}
.refined-github .dashboard-rollup-items .body {
    border-top-color: ${#bbc1c9} !important;
}
.refined-github .reaction-summary-item a {
    box-shadow: 0 0 0 2px ${white} !important;
}
.refined-github button.reaction-summary-item {
    border-top-color: rgb(52, 59, 68) !important;
    border-bottom: rgb(77, 172, 253) !important;
}
.js-site-search-form {
    background-color: #ffffff1a !important;
    border-radius: 2pt !important;
}
.blob-num:not(.cc-coverage-covered-border):not(.cc-coverage-missed-border) {
    border-right: 0 !important;
}
.cc-issue-description {
    color: #24292e !important;
}
.cc-readup-background {
    background-color: rgb(28, 30, 31) !important
}
.cc-readup-content {
    border-left: 1px solid grey !important;
    color: rgb(216, 214, 208) !important;
}
.cc-readup-content blockquote {
    border-left: 3px solid dimgrey !important;
}
.cc-pr__link-text {
    color: darkgrey !important;
}
.cc-pr__tooltip {
    background-color: rgb(28, 30, 31) !important;
    color: darkgrey !important;
}
.jfk-bubble, .octotree-sidebar, .cc-pr__logo, .cc-octicon, #network canvas, img.network-tree {
    filter: invert(94.4%) hue-rotate(180deg) contrast(90%) !important;
}
.blob-code-inner, .blob-code-inner > *, .CodeMirror pre > span, .CodeMirror-linenumber {
    font-family: SFMono-Regular,Consolas,Liberation Mono,Menlo,monospace !important;
}
#commit-activity-detail > svg {
    fill: ${black} !important;
}
.ContributionCalendar-day,
.ContributionCalendar-day[data-level="0"] {
    fill: var(--color-calendar-graph-day-bg) !important;
}
.ContributionCalendar-day[data-level="1"] {
    fill: var(--color-calendar-graph-day-L1-bg) !important;
}
.ContributionCalendar-day[data-level="2"] {
    fill: var(--color-calendar-graph-day-L2-bg) !important;
}
.ContributionCalendar-day[data-level="3"] {
    fill: var(--color-calendar-graph-day-L3-bg) !important;
}
.ContributionCalendar-day[data-level="4"] {
    fill: var(--color-calendar-graph-day-L4-bg) !important;
}
.day,
.day[data-Count="0"] {
    fill: var(--color-calendar-graph-day-bg) !important;
}
.day[data-Count="1"] {
    fill: var(--color-calendar-graph-day-L1-bg) !important;
}
.day[data-Count="2"] {
    fill: var(--color-calendar-graph-day-L2-bg) !important;
}
.day[data-Count="3"] {
    fill: var(--color-calendar-graph-day-L3-bg) !important;
}
.day[data-Count="4"] {
    fill: var(--color-calendar-graph-day-L4-bg) !important;
}
:root {
    --color-previewable-comment-form-bg: var(--darkreader-neutral-background) !important;
    --color-calendar-graph-day-bg: ${#ebedf0} !important;
    --color-calendar-graph-day-L1-bg: ${#9be9a8} !important;
    --color-calendar-graph-day-L2-bg: ${#40c463} !important;
    --color-calendar-graph-day-L3-bg: ${#30a14e} !important;
    --color-calendar-graph-day-L4-bg: ${#216e39} !important;
}
.Box-row--yellow {
    background-color: ${#fffbdd} !important;
}
.merge-status-list {
    border-color: ${#c0c5c7} !important;
}
.user-has-reacted {
    background-color: rgba(17, 88, 199, 0.2) !important;
}
.hx_IssueLabel {
    background: rgb(var(--label-r),var(--label-g),var(--label-b)) !important;
    color: hsl(0,0%,calc(var(--lightness-switch)*100%)) !important;
    border-color: hsla(var(--label-h),calc(var(--label-s)*1%),calc((var(--label-l) - 25)*1%),var(--border-alpha)) !important;
}
.header-search-input {
    border: 0 !important;
}
.timeline-comment--caret.current-user::after {
    --color-current-user-tip-bg: var(--darkreader-bg--color-box-bg-info) !important;
}
.timeline-comment--caret.current-user::before {
    background-color: var(--darkreader-border--color-box-border-info) !important;
}
@media screen and (min-width: 1012px) {
    markdown-toolbar.border-lg-top-0 {
        border-top: 1px solid var(--darkreader-border--color-border-primary) !important;
    }
}
@media screen and (max-width: 767px) {
    div.tabnav--responsive button.tabnav-tab {
        --color-border-primary: var(--darkreader-border--color-border-primary) !important;
    }
}
.TimelineItem-badge[style^="background-color: var(--color-timeline-merged-bg)"] {
    background-color: var(--color-timeline-merged-bg) !important;
}
.ContributionCalendar-label {
    --color-text-primary: var(--darkreader-neutral-text) !important;
}
article div[style^="background: linear-gradient"] {
    background: linear-gradient(to top, var(--darkreader-bg--color-bg-primary), transparent) !important;
}

IGNORE INLINE STYLE
a[href^="https://apps.apple.com/app/"] g
a[href^="https://apps.apple.com/app/"] path
tracked-issues-progress svg *

================================

github.myshopify.com

INVERT
.site-header__logo-image
.column-gh.column-full>svg

================================

githubstatus.com

CSS
.illo-desktop-header {
    z-index: 0 !important;
}

================================

gitlab.com
gitlab.*.*
gitlab.*.*.*
code.videolan.org
framagit.org

INVERT
.js-contrib-calendar

CSS
:root {
    --svg-status-bg: #181a1b;
}
.avatar, .avatar-container {
    border: none !important;
}
table.code .line_content *:not(pre),
.job-log *:not(pre) {
  font-family: "Menlo", "DejaVu Sans Mono", "Liberation Mono", "Consolas", "Ubuntu Mono", "Courier New", "andale mono", "lucida console", monospace !important;
}
.gl-drawer-close-button,
.js-reply-button,
.dropdown.more-actions > button,
.js-note-edit,
.btn-default-tertiary {
    mix-blend-mode: unset !important;
}
.board-inner {
    --gray-10: ${#e1e3e4} !important;
    --darkreader-border--gray-100: ${#d1cdc7} !important;
}

================================

giveawayoftheday.com

INVERT
.header_lang .curr_lang::before
.header_logo
.header_nav_trig
.header_search
.header_search .button

IGNORE IMAGE ANALYSIS
.countdown-amount .diggit

================================

giveaways.cavebot.xyz

CSS
.respon1.p-r-50.p-l-50.p-b-22.p-t-42.bor1.cd100.flex-sa-m.flex-w {
    border: none !important;
}
.overlay1::after {
    background: unset !important;                                                                                                                                                                                                                                                                               
}
.overlay1::before { 
    opacity: 0.8 !important;  
    background-color: unset !impotant;   
    background-image: unset !impotant;                                                                                                                                                                                                             
}
.overlay1::before {
    background-color: unset !important;
    background-image: unset !important;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
}

================================

gizmodo.com

INVERT
[aria-label="Gizmodo logo"]

================================

glasswire.com

INVERT
.download-n-marks div img
.menu

CSS
.head-menu .menu a {
  color: var(--darkreader-neutral-background) !important;
}

================================

global.gotomeeting.com/join/*

IGNORE IMAGE ANALYSIS
body

================================

globo.com

CSS
.bar-scrubber .bar-scrubber-icon,
.bar-background .bar-fill-2 {    
    background-color: var(--darkreader-neutral-text) !important;
}
.bar-background .bar-fill-1 {
    background-color: rgba(255, 255, 255, .3) !important;
}

IGNORE INLINE STYLE
.poster__play-wrapper *

================================

gloswielkopolski.pl

INVERT
.componentsNavigationNavbar__brand

================================

gls-pakete.de

CSS
.tracking--status .status-box::before, .tracking--status .status-box::after {
    z-index: 0 !important;
}
.tracking--status .status-box.status--complete.status--lastcomplete .status-box--tooltip, .tracking--status .status-box.status--current .status-box--tooltip {
    transform: none !important;
    position: absolute !important;
    margin-bottom: 0px !important;
    margin-top: -10px !important;
    bottom: 0 !important;
}

================================

gnu.org

INVERT
#gnu-banner img
#search-icon

================================

godoc.org

CSS
.banner {
    background-color: ${rgb(225, 190, 130)} !important;
}

================================

godzinyotwarcia24.pl

INVERT
.mbdtr
.mpktr
form[action="/szukaj"]
div[id="suchfeldlabels"]

================================

gog-games.com/game

CSS
.container.game > .bg {
    background-image: none !important;
}

================================

gog.com

INVERT
i.icn.icn--close

CSS
.product-tile__info:hover {
    background-color: var(--darkreader-neutral-background) !important;
}

IGNORE IMAGE ANALYSIS
.menu-anonymous__shelf

================================

gokulv.netlify.app

IGNORE IMAGE ANALYSIS
body

================================

golang.org

INVERT
.Footer-gopher
.gopher

CSS
#file-editor .CodeMirror,
#file-editor .CodeMirror-lines, 
#file-editor .CodeMirror-gutters,
#wrap {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

google.*/chrome

INVERT
[src$="slate.png"]
[src$="articles.png"]
[src$="phone_desktop.png"]
[src$="homepage_tools.png"]
[src$="chrome-ui-sync.png"]
[src$="password-check.png"]
[src$="big_pixel_phone.png"]
[src=$"homepage_privacy.png"]
[src$="big_phone_desktop.png"]
[src$="chrome-ui_desktop.png"]
[src$="pixel_slate_port_desktop.png"]
[src^="/chrome/static/images/productivity/"]
[src^="/chrome/static/images/be-more-productive/"]
[src^="/chrome/static/images/download-browser/pixel"]
[src^="/chrome/static/images/google-translate/screen-"]

================================

google.*/maps
google.*.*/maps

INVERT
#app-container.vasquette:not(.app-imagery-mode):not(.app-globe-mode) .widget-scene-canvas
#app-container.vasquette:not(.app-imagery-mode):not(.app-globe-mode) .canvas-container > canvas
#app-container.vasquette:not(.app-imagery-mode):not(.app-globe-mode) .full-screen > img
.widget-settings-button-icon
.searchbox-button
.searchbox-searchbutton
.searchbox-hamburger::before
.maps-sprite-settings-chevron-left
a.ita-kd-icon-button > span
li.ita-kd-menuitem > span.ita-kd-menuitem-inputtool-icon
li.ita-kd-menuitem > span.ita-kd-checkbox
div.maps-sprite-common-chevron-left
span.maps-sprite-common-chevron-right
span.section-destination-via-line-icon
div.section-directions-trip-travel-mode-icon
button.searchbox-hamburger.white-foreground
label.kd-radio-label:before
label.kd-checkbox-label:before
label.kd-checkbox-label:after
button.section-directions-details-action-button
div.section-loading-spinner
a.gb_b > div
a.gb_xc
.gm-style img[role="presentation"]:not([src*="v="])
.i4ewOd-xl07Ob
.i4ewOd-LQLjdd li::before
.un1lmc-j4gsHd
.maps-sprite-settings-languages
a[href*="about/products"]
.google-logo
.watermark
.section-review-action-menu
.section-review-interaction-button
.section-directions-trip-travel-mode-icon
.renderable-component-icon
.cards-rating-star
.maps-sprite-common-chevron-right
[role="region"] button[jsaction^="pane.list-item.add"] [class*="icon-background"] [class*="icon"][src*="black"]
.top-activity-icon
.activity-icon
img[src$="menu_black_24dp.png"]

IGNORE IMAGE ANALYSIS
.widget-settings-map
.widget-settings-satellite
.widget-settings-terrain
.widget-settings-globe
.widget-settings-traffic
.widget-settings-transit
.widget-settings-bike
.widget-settings-street-view
.widget-settings-covid-info-icon
.widget-settings-location-sharing
.widget-settings-your-places
.widget-settings-rate-review
.widget-settings-timeline
.widget-settings-link
.widget-settings-print

================================

goplay.anontpp.com

INVERT
img[src*="download.svg"]
img[src*="cast.png"]
img[src*="bookmark.png"]
.jw-slider-container
.jw-time-tip::after

================================

gorod.gov.spb.ru

INVERT
.header__logo
.reason__icon
.map-with-address
.problem-details__map
.problem-map-page__map

================================

gorod.mos.ru

INVERT
.scene__logo
div[id^="map"] > div > div > div
div[style^="text-align: center; margin"] > div > div:first-of-type

CSS
#footer {
    background: ${#CCECE8} !important;
}

================================

gosuslugi.ru

INVERT
ymaps[class$="ground-pane"]
ymaps[class$="places-pane"] span

================================

gramota.ru

INVERT
img[src*="logo-gramota"]
.caret
td > strong

================================

greatergood.com

INVERT
.ctg-logo
.logo-ggc-color
#site-logo

================================

grubhub.com

CSS
.s-checkbox-filler {
    color: rgb(24, 26, 27) !important;
}
label, h5, h6, header, .h5 {
    color: ${black};
}

================================

gsuite.google.com

INVERT
.header--logo img

================================

gu.spb.ru

INVERT
ymaps[class$="ground-pane"]

CSS
.bg {
    background-image: var(--darkreader-neutral-background) !important;
}

================================

guiott.com

CSS
.Section1 > div {
    background-image: none !important;
}

================================

guitarcenter.pl

INVERT
tr > td > img
p.standard_price
p.promo_price

================================

gurushots.com

CSS
.c-challenges-speed-item__countdown > .round-progress-wrapper,
.challengesItemSuggested__timer .round-progress-wrapper {
    background-color: transparent !important;
}
.modal-vote__exposure-meter__arrow,
.modal-vote__exposure-meter__arrow:after {
    background-color: var(--darkreader-neutral-text) !important;
}

================================

habitica.com

INVERT
.logo path:nth-child(2)

================================

habr.com

INVERT
img[src*="//tex.s2cms.ru/"]

CSS
html {
    text-shadow: none !important;
}
.main-navbar .icon-svg {
  fill: ${#929ca5} !important;
}

================================

hackerone.com

INVERT
.app__logo

================================

hackerrank.com

INVERT
.badge-title
.badge-star

CSS
.monaco-editor .cursor {
    background-color: ${black} !important;
}

================================

handshake.org

INVERT
[src*="logo-dark.svg"]
[src*="blocks.svg"]

CSS
ul.notes li::before {
    background-color: var(--darkreader-neutral-text) !important;
}

================================

haokan.baidu.com

INVERT
.header-logo-icon

================================

hbr.org

INVERT
.hamburger-icon
.top-header--logo
.search-icon
.footer-logo

================================

hdgo.cc
vio.to

INVERT
.hdplayer .big_play_button div
.hdplayer .hdgo_controls div.hdgo_pause_control div
.hdplayer .hdgo_controls div.hdgo_play_control div

================================

heise.de

INVERT
.heise-online-logo

================================

helix.ru

INVERT
.Site-Header-Logo

================================

heritage.org/index

INVERT
.bar

CSS
.content-container {
    background-image: none !important;
}

================================

hex-rays.com

INVERT
#logo
.footer-logo

================================

hh.ru

CSS
html {
    text-shadow: none !important;
}

================================

hindustantimes.com

INVERT
.menu.noti-dot
.searchHolder .search
img[src$="logo-big-cm.png"]
img[src$="logo-ht.png"]
img[src$="htlogo.png"]

================================

history.state.gov

CSS
body {
    background-image: none !important;
}

IGNORE IMAGE ANALYSIS
body

================================

homebrewery.naturalcrit.com

CSS
.CodeMirror-scroll {
  background-color: #444;
}
span[role="presentation"] {
  color: white;
}
span[role="presentation"] > .cm-header {
  color: rgb(50, 150, 250);
}
span[role="presentation"] > .cm-variable-2 {
  color: rgb(50, 150, 250);
}
.phb blockquote {
  background-color: #e0e5c1 !important;
}
.phb h3, .phb h2, .phb h1 {
  color: #58180D;
}
.phb p {
  color: black;
}
.cm-link,.cm-attribute {
  color: rgb(90, 140, 255) !important;
}
.cm-url,.cm-string {
  color: rgb(200, 50, 50) !important;
}
.cm-quote,.cm-tag {
  color: rgb(50, 200, 50) !important;
}

================================

hootsuite.com

INVERT
img[src$="hootsuite_white_form3.png"]

================================

howbuy.com

INVERT
.bottomStar
.cpBottomWord
.logo
.navList dt
.phCon
.title
#valuationChar

================================

hs.fi

CSS
article, 
article > article {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

huba.news

INVERT
img[alt="Huba"]
img[alt="INTERIA.PL"]
.most-interesting-topics-header:before

================================

hvdic.thivien.net

INVERT
img.hvres-variant-img.lazy

CSS
body {
    background-image: none !important;
}

================================

hyperphysics.phy-astr.gsu.edu

INVERT
tbody td > img
tbody center > img
tbody center > a > img

================================

hyperskill.org

INVERT
.html-preview

CSS
.monaco-editor .margin,
.monaco-editor-background {
    background-color: #181A1B !important;
}
.monaco-editor .cursor {
    background-color: ${#000000} !important;
}

================================

hypixel.net

INVERT
.p-nav-inner
.message-avatar::after

CSS
.p-navEl-link, .p-header-playNow .p-header-playNow-button, .p-navgroup-link--search {
    color: ${rgb(214, 210, 205)} !important;
}

================================

icloud.com

INVERT
.sc-iujRgT.jxLrRl
.icloud-text.image
.img.app-icon-icloud

================================

icofont.com

INVERT
.logo

================================

iconify.design

IGNORE INLINE STYLE
svg.iconify *
.si-svg-wrapper svg *
.block-container .icons svg *

================================

iett.istanbul

CSS
.LineTimeDuyuru > li:nth-child(1) {
    display:none;
}
.LineTimeDuyuru::before {
    content: "Kırmızı renkli seferler ÖHO ve OAŞ'a, beyaz renkli seferler İETT'ye aittir."
}
.DetailTable img {
    filter: invert(100%) hue-rotate(180deg) !important;
}

================================

iflscience.com

INVERT
a.logo

================================

ifood.com.br

CSS
.responsive-header__logo svg {
    --darkreader-inline-fill: rgb(212, 45, 58) !important;
}

================================

ikea.*

CSS
.range-revamp-ratings-bar__star--empty path,
.range-revamp-ratings-bar__star--half path:first-child  {
    fill: ${rgb(246, 245, 244)} !important;
}
.hnf-svg-icon {
    fill: var(--darkreader-neutral-text) !important;
}

IGNORE INLINE STYLE
.gpr__color-dot

================================

iliad.it

IGNORE IMAGE ANALYSIS
.background

================================

ilovepdf.com

INVERT
.brand__logo
.ico--down
.ico--desk
.ico--hamburger

================================

ilyabirman.*

INVERT
img[src*="layout-win"]

CSS
body {
    background: none !important;
}

================================

imdb.com

INVERT
.a-icon
.jw-slider-volume

CSS
#wrapper {
    background: ${#e3e2dd} !important;
}
.ipc-page-section--base {
    --ipc-pageSection-base-bg: var(--darkreader-neutral-background) !important;
}
:root: {
    --darkreader-bg--ipt-baseAlt-shade1-bg: var(--darkreader-neutral-background) !important;
}
.imdb-header__nav-drawer div,
.ipc-menu--on-baseAlt,
.imdb-header__search-menu,
.ipc-promptable-base__panel {
    background-color: var(--darkreader-neutral-background) !important;
}
.ipc-promptable-base__content {
    background-color: #171a1b !important;
}
div[class^="SubNav__SubNavContainer"],
div[class^="Media__ButtonContainer"] > a,
div[class*="WatchlistButton__ButtonParent"] > button,
a[class*="EmptyState__FYWStateButton"],
a[class*="EmptyState__FromYourWatchlistRibbon"] > svg,
.ipc-poster-card,
.ipc-poster-card__actions > .ipc-button {
    background: rgba( var(--ipt-on-baseAlt-rgb,255,255,255),var(--ipt-baseAlt-hover-opacity,0.08) ) !important;
}
div[class^="SubNav__ShareButtonWrapper"]::before,
div[class^="Root__Separator"] {
    background: rgba(var(--ipt-base-rgb,255,255,255),0.2); !important;
}
.ipc-title__text::before,
.ipc-tabs__indicator {
    background: #f5c518 !important;
}
.ipc-signpost--accent1 {
    background: var(--darkreader-bg--mdc-theme-ipt-accent1-color, #bb9508) !important;
}
hgroup[class*="ipc-title--category-title"] > .ipc-title__text,
.ipc-title-prompt__title {
    color: #f5c518 !important;
}
.boxOfficeTitle:nth-of-type(2n) {
    background: linear-gradient( to right,rgba(var(--ipt-on-baseAlt-rgb),0),rgba(var(--ipt-on-baseAlt-rgb),0.06) ) !important;
}
div[class^="VideoInfostyles__VideoDescriptionOverlay"] {
    background: linear-gradient(transparent 50%,var(--ipt-baseAlt-bg)) !important;
}
main {
    background: var(--ipc-pageSection-baseAlt-bg) !important;
}
.ipc-watchlist-ribbon__bg-ribbon {
    fill: rgba(var(--ipt-baseAlt-shade1-rgb), 0.75) !important;
}
.ipc-watchlist-ribbon--inWatchlist .ipc-watchlist-ribbon__bg-ribbon {
    fill: #BB9508 !important;
}
.ipc-switch__slider::before,
.ipc-switch__slider::after {
    background: #5799ef !important;
}

================================

immobilienscout24.de

INVERT
img[alt^="ImmobilienScout24"]
.topnavigation__sso-login__plus-logo
.button-primary
.main-search__content--rent--from-0
.result-list-entry__new-flag
.product-teaser__image
.no-of-results-highlighter

CSS
.result-list__listing {
    background-color: transparent !important;
}

================================

infinitysearch.co

INVERT
img[src$="github.ico"]
img[src$="unsplash.png"]
img[src$="boardreader.ico"]
#logo_img_home[src$="logo_text_black.png"]

================================

inforlex.pl

INVERT
div[class="content -dark"]
div[class="loginLogo"]
img[src*="infor-lex_2020.jpg"]
a[class="client-logo-entry"]

================================

inoreader.com

CSS
.article_expanded {
    background-color: rgb(31, 35, 38) !important;
}

================================

instagram.com

INVERT
.s4Iyt
.coreSpriteActivityHeart
.coreSpriteAppStoreButton
.coreSpriteCall
.coreSpriteCheck
.coreSpriteCi
.coreSpriteClose
.coreSpriteDesktopNavDirect
.coreSpriteDesktopProfileSaveActive
.coreSpriteDesktopProfileTaggedActive
.coreSpriteDirectHeart
.coreSpriteDownload
.coreSpriteDropdownArrowGrey9
.coreSpriteGallery
.coreSpriteGooglePlayButton
.coreSpriteKeyhole
.coreSpriteLockSmall
.coreSpriteLoggedOutWordmark
.coreSpriteMobileNavDirect
.coreSpriteMobileNavTypeLogo
.coreSpriteNavBack
.coreSpriteNotificationLeftChevron
.coreSpriteNullProfile
.coreSpriteOptionsEllipsis
.coreSpritePagingChevron
.coreSpriteProfileCamera
.coreSpriteReload
.coreSpriteSaveNull
.coreSpriteSpinstaStory
.coreSpriteStoryCreation
.coreSpriteTaggedNull
.coreSpriteVideoNux
.coreSpriteWindowsStoreButton
.coreSpriteWordmark
.glyphsSpriteAdd__outline__24__grey_9
.glyphsSpriteAdd_friend__outline__96
.glyphsSpriteApp_instagram__outline__24__grey_9
.glyphsSpriteApp_messenger__outline__24__grey_9
.glyphsSpriteApp_twitter__outline__24__grey_9
.glyphsSpriteApp_whatsapp__outline__24__grey_9
.glyphsSpriteCall__outline__24__grey_9
.glyphsSpriteCamera__outline__24__grey_9
.glyphsSpriteChevron_down__outline__24__grey_9
.glyphsSpriteChevron_left__outline__24__grey_9
.glyphsSpriteChevron_up__outline__24__grey_9
.glyphsSpriteCircle_add__outline__24__grey_9
.glyphsSpriteComment__outline__24__grey_9
.glyphsSpriteContact_import
.glyphsSpriteContact_import_sm
.glyphsSpriteDirect__outline__24__grey_9
.glyphsSpriteDirect__outline__96
.glyphsSpriteDownload_2FAC
.glyphsSpriteError__outline__24__grey_9
.glyphsSpriteError__outline__96
.glyphsSpriteFacebook__outline__24__grey_9
.glyphsSpriteFb_brand_center_grey
.glyphsSpriteForward__outline__24__grey_9
.glyphsSpriteFriend_Follow
.glyphsSpriteGlyph_chevron_right
.glyphsSpriteHashtag__outline__24__grey_9
.glyphsSpriteHeart__filled__16__grey_9
.glyphsSpriteHeart__filled__24__grey_9
.glyphsSpriteHeart__outline__24__grey_9
.glyphsSpriteHome__filled__24__grey_9
.glyphsSpriteHome__outline__24__grey_9
.glyphsSpriteInfo__filled__16__grey_9
.glyphsSpriteLink__outline__24__grey_9
.glyphsSpriteLocation__outline__24__grey_9
.glyphsSpriteLock__outline__24__grey_9
.glyphsSpriteLock__outline__96
.glyphsSpriteMail__outline__24__grey_9
.glyphsSpriteMenu__outline__24__grey_9
.glyphsSpriteMore_horizontal__outline__24__grey_9
.glyphsSpriteNew_post__outline__24__grey_9
.glyphsSpritePaging_chevron
.glyphsSpritePlay__filled__16__grey_9
.glyphsSpriteSave__filled__24__grey_9
.glyphsSpriteSave__outline__24__grey_9
.glyphsSpriteSearch__filled__24__grey_9
.glyphsSpriteSearch__outline__24__grey_9
.glyphsSpriteSettings__outline__24__grey_9
.glyphsSpriteShare__outline__24__grey_9
.glyphsSpriteShopping__outline__16__grey_9
.glyphsSpriteStar_filled_24
.glyphsSpriteStar_half_filled_24
.glyphsSpriteStory__outline__24__grey_9
.glyphsSpriteUser__filled__24__grey_9
.glyphsSpriteUser__outline__24__grey_9
.glyphsSpriteUser_follow__filled__24__grey_9
.glyphsSpriteUser_follow__outline__24__grey_9
.glyphsSpriteUsers__outline__24__grey_9
.glyphsSpriteVideo_chat__outline__24__grey_9
.glyphsSpriteWhatsapp__outline__24__grey_9
.glyphsSpriteX__outline__24__grey_9
span.LikeSprite.embedSpriteHeartOpen
span.hideText.embedSpriteComment
span.hideText.embedSpriteShare
span.hideText.embedSpriteSaveOpen
span.Sprite.embedSpriteGlyph.hideText
.-Nmqg
svg[aria-label*="Facebook"]

CSS
div > a[tabindex],
div > span > a[tabindex] {
    border-color: transparent !important;
}

================================

instructure.com

INVERT
.equation_image

================================

interaktywnie.com

INVERT
img[alt="Interaktywnie.com"]
img[src*="companym/"]

================================

interia.pl

INVERT
.interia-logo
.search-submit

CSS
.calendar-date, 
.calendar-body {
    background-color: var(--darkreader-neutral-background) !important;
    background-image: none !important;
}

================================

internetowa.tv

INVERT
img[src*="/img/"]

CSS
.star-on-png::before {
    color: #FCAD03 !important; 
}

================================

interpride.org

INVERT
.logo .img-responsive

================================

invisioncommunity.com

INVERT
img[src*="customer_logos.png"]
img[src*="logo_dark.png"]
img[src*="footer"]
.cDownloadsCategoryCount

CSS
.ipsBadge {
    --darkreader-bg--badge--background: var(--darkreader-neutral-background) !important;
}
.ipsReact_reactCount > a, 
.ipsReact_reactCount > span, 
.elFullInbox_menu, 
.ipsMenu_auto, 
.ipsMenu_footerBar, 
.ipsMenu_headerBar, 
.ipsMenu_innerContent, 
.ipsMenu_item, 
.ipsMenu_sep, 
.ipsMenu_title, 
.ipsPadding {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

inwestomat.eu

CSS
main {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

iopscience.iop.org

INVERT
.article-text img[alt^="$"]

================================

ipinfo.io

INVERT
.navbar-brand

================================

ipko.pl

INVERT
.ZLc3L
._1IGN3
a.x-logo

CSS
._1IGN3 {
    background-size: 75px 56px !important;
}
._3cnGx::before {
    background-size: 112px 80px !important;
}

================================

ipla.tv

INVERT
img[alt="ipla.tv"]

================================

iqiyi.com

INVERT
.qy-micro-bg

================================

isbgpsafeyet.com

CSS
h1, h2, h3,
h4, h5, h6 {
    color: rgb(255, 255, 255) !important;
}
.Hero, .Footer {
    background: None !important;
}
.Button-is-primary {
    background: var(--primary-background-color);
}

================================

ising.pl

CSS
body.grey-gui-bg #content, 
div.grey-gui-bg,
.small-navy-heading, 
.small-blue-heading, 
.small-purple-heading, 
.small-navy2-heading {
    background-image: none !important;
}

================================

itbiznes.pl

INVERT
.custom-logo

================================

itch.io

INVERT
.header_widget .mobile_nav_btn

================================

item.jd.com

IGNORE IMAGE ANALYSIS
*

================================

itemfix.com

INVERT
.logo-outer
.footer-logo
a[href="ll"]

================================

ithardware.pl

INVERT
.navbar-brand
a[href*="uzywki.expert"]
a[href*="rankingi"]
a[href*="feed"]

================================

iu-fernstudium.de

INVERT
.cp-logo
.cp-contact-bubble__svg-open
svg[class$="transform"]

================================

ixbt.com

CSS
body {
    background-color: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text);
}

================================

jacobinmag.com

INVERT
.si-hr-nv__icon--menu

================================

jailbreak.fce365.info

CSS
body {
    background-image: none !important;
}

================================

jamboard.google.com

INVERT
.docs-icon-img-container
.jam-button-content
.jam-icon-palette-black
.jam-icon-sticky-note-black

================================

jamendo.com

INVERT
.player-volume_range_track
.player-volume_range_fill

================================

java.com

INVERT
html #jvc0v2.bg1 .jvc0w1
html #jvc0v2.bg3 .jvc0w1
html #jvc0v2.bg5 .jvc0w1

================================

jbl.com

CSS
.product-tile .product-image .thumb-link img {
    mix-blend-mode: normal !important;
}
.search-refinement,
i[class^="icon"].hidden-xs {
    color: var(--darkreader-neutral-text) !important;
}
.product-tile .product-image {
    background-color: white !important;
}
.product-tile .product-swatches .swatch-list li.selected img, 
.product-tile .product-swatches .swatch-list li.selected:hover img {
    border-color: white !important;
}

================================

jewishcurrents.org

INVERT
img[alt="Jewish Currents"]

================================

jira.*.com

INVERT
.aui-dropdown2-trigger::after

================================

jisho.org

INVERT
h1.logo
.stage

================================

jobs.github.com

CSS
#page {
    background-image: none !important;
}

================================

joemonster.org

CSS
body {
    background-image: none !important;
}

================================

joplinapp.org

CSS
#in-the-press-section {
    background-image: none !important;
}

================================

journal.tinkoff.ru

INVERT
._17-LK > span
._3aEco
._1Dhxk
.uVy35.V6dif::before
._38Vkx.FrrKu::after
._3LO60._4GqdT::after
.PwECA
.best-authors__arrow.best-authors__arrow--active
.best-authors__arrow

================================

jpl.nasa.gov

CSS
.brand_area {
   background-image: url("https://www.jpl.nasa.gov/assets/images/<EMAIL>") !important;
}

================================

jpost.com

INVERT
img[alt*="The Jerusalem Post"]
img[class*="header"]
.btn-digital-library-header > img

================================

jsdelivr.com

INVERT
.navbar-brand > img

================================

juejin.cn

INVERT
.equation

================================

jumia.*
jumia.*.*
zando.co.za

INVERT
svg.ic[role="img"]:not([aria-label="Zando logo"])
.logo > a > img
.inbox > a:not(:nth-of-type(1)) > svg
img[src*="jumia-group-logo.png"]
img[src*="jumia_logo_small_checkout.png"]
img[src*="/Jumia-Pay"]
li.logo:nth-child(1) > .-i-jumia-logo
.-ecosystem > a.-category.-inlineblock.-vatop:not([href*="mall"]) > img
img[src*="empty-cart.png"]
.-header > img:nth-child(1)
.-gy5 > .-fs0 > .vent-link[title*="Rewards"]
.-gy5 > .-fs0 > .vent-link[title*="Pay"]
.-gy5 > .-fs0 > .vent-link[title*="Primo"]
.-gy5 > .-fs0 > .vent-link[title*="Food"]
.-gy5 > .-fs0 > .vent-link[title*="Party"]
.-gy5 > .-fs0 > .vent-link[title*="Now"]
.col4 > .s-menu > a:not(:nth-child(1)):not(:nth-child(3)):not(:nth-child(7)) > svg
div.-fw-w:nth-child(1) > a.-fs0 > img
article.-df > svg > use
svg.ic.xprss
div.-df.-d-co.-c-bet > h3.-fw > svg.ic
a.fk-cb.-me-start.-fsh0 > svg.ic.-h-24

================================

justhost.ru

INVERT
.header__logo

================================

k-report.net

CSS
.dfzlu1 div,
#diskuse_zahlavi a,
.dfprofil,
#diskuse_zahlavi td {
    color: var(--darkreader-neutral-background) !important;
}

================================

kaggle.com
kaggleusercontent.com

CSS
div.output_png img {
    background-color: ${black} !important;
}

IGNORE INLINE STYLE
div.output_png img

================================

kahoot.it

INVERT
path[d^="M39.99 12.621v14.736l14.736.001V39.99H39.99v14.736H27.359V39.99H12.62V27.359h14.736l.001-14.737H39.99z"]
path[d^="M46.244 15.355l8.127 7.393-25.623 28.184-15.526-14.483 7.743-7.747 7.333 6.396 17.946-19.743z"]

IGNORE INLINE STYLE
[data-functional-selector^="answer"]
[data-functional-selector="intro-animation-animation_pre-question-animation"] *

IGNORE IMAGE ANALYSIS
*

================================

kali.org

IGNORE IMAGE ANALYSIS
#banner-logo

================================

kaosx.us

INVERT
.Header-brand img

================================

kaytrip.com

INVERT
.date
.jd_ss
.liuchengt
.nApp
.nTel
.n_app
.tt_t
.wmap

CSS
#imageField {
    background-color: transparent !important;
}
.jd_xl {
    filter: none !important;
}

================================

kcsoftwares.com

CSS
.status-10-fg {
    color: #FF14FF !important;
}
.status-20-fg {
    color: #FF2190 !important;
}
.status-30-fg { 
    color: #FFF719 !important;
}
.status-40-fg {
    color: #FF1919 !important;
}
.status-50-fg {
    color: #0062FF !important;
}
.status-80-fg { 
    color: #47FF19 !important;
}
.status-90-fg { 
    color: #727985 !important;
}

================================

keep.google.com

INVERT
.gb_hc

================================

keepa.com

INVERT
div#loadingIcon
div.pLText

================================

keepass.info

INVERT
img[src="help/images/trans.png"]

CSS
body {
    background-image: none !important;
}

================================

keepassxc.org

CSS
.btn-primary {
    color: var(--darkreader-neutral-background) !important;
}

================================

kenh14.vn

CSS
.kbwcb-left, .kbwcb-left::before {
    background-image: none !important;
}

================================

kfccoupons.co.nz

CSS
.img-container {
    z-index: 0 !important;
}
.img-container > img {
    filter: brightness(50%) sepia(40%) !important;
}

================================

kiedyprzyjedzie.pl

INVERT
.cluster-marker div
.leaflet-pane > .leaflet-layer

CSS
.cluster-marker div span {
    color: var(--darkreader-neutral-background) !important;
}

================================

killedbygoogle.com

INVERT
img[src*="e.svg"]
img[src*="-logo"]

================================

kinoart.ru

INVERT
main > div > svg
header > a > svg
footer > div > svg
header > div > a > svg

================================

kinopoisk.ru

CSS
input[name="kp_query"]::placeholder {
    background-image: linear-gradient(45deg, var(--darkreader-neutral-text) 70%, transparent 100%) !important;
}
input[name="kp_query"] {
    background-color: ${lightgray};
}
input[name="kp_query"]:focus {
    background-color: ${rgb(195, 195, 195)};
}

================================

kinzhal.media

INVERT
img[src$="logo.svg"]
img[src$="search.svg"]
img[src$="close.svg"]
.footer-soc
.footer__18
.kinzhal-icon

================================

knife.media

INVERT
svg[class$="logo-image"]
.explorer__header-close

================================

ko-fi.com

INVERT
img[alt="Ko-fi Logo"]
.ui-mobile-nav-toggle

================================

komorkomat.pl

INVERT
.navbar-brand > img
.owl-wrapper
.result-item-logo

================================

komputerswiat.pl

INVERT
.serviceLogoImg
.serviceIcon

================================

kraken.com

CSS
header > nav > ul > li > a::after,
span[data-testid^="asset-pair"][data-testid$="-low"] + div > :last-child {
    background-color: var(--darkreader-neutral-text) !important;
}

================================

krew.info

INVERT
a[class*="kropla"]

================================

krita.org

CSS
.container {
    background: none !important;
}

================================

krytykapolityczna.pl

INVERT
img[src*="logo"]

================================

lajtmobile.pl

CSS
body > * {
    --background: var(--darkreader-neutral-background) !important;
}
.ls-bg-wrap > img,
.generatorTemplate > .columnsBlockGeneratorBlock.mobileHide {
    filter: brightness(50%) sepia(40%) !important;
}
.columnsBlockGeneratorBlock .offertBlock:not(.highlightOffert),
.generatorTemplate > .columnsBlockGeneratorBlock.mobileHide,
.summaryModule,
.formModule,
input[type="text"] {
    background: var(--darkreader-neutral-background) !important;
}
.generatorTemplate > .columnsBlockGeneratorBlock.mobileHide > *{
    background: ${#FEF} !important;
}

================================

lambda-the-ultimate.org

CSS
a {
    color: #3391ff !important;
}

================================

languagetool.org

INVERT
#checktext_ifr

================================

laptopmag.com

INVERT
div#publisherDetails.logo
.qc-cmp-publisher-logo

================================

laravel.com

CSS
.bg-gray-100,
aside > div {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

last.fm

INVERT
.resource-external-link--homepage::before

CSS
.user-dashboard-loved-tracks .user-dashboard-big-datapoint-value a {
    color: ${#666666} !important;
}
.user-dashboard-catalogue-item-total a {
    color: ${#666666} !important;
}
.highcharts-text-outline {
    stroke: none !important;
}
.user-dashboard-scalable-content span {
    color: ${#666666} !important;
}

IGNORE IMAGE ANALYSIS
.masthead-logo-loading
.masthead-logo

================================

lastpass.com

INVERT
.lp-header__logo--link

================================

latex.wikia.org

INVERT
.mwe-math-fallback-image-inline
.mwe-math-fallback-image-display

================================

latimes.com

INVERT
.page-logo
.page-header-logo

================================

launchpad.net

INVERT
.edit-controls
#launchpad-logo-and-name

CSS
.footer .lp-arcana {
    background-image: none !important;
}

================================

learnopengl.com

CSS
#hover {
    background-image: none !important;
    opacity: 1 !important;
}

================================

leetcode.com
leetcode-cn.com

INVERT
img[alt="logo"]
.cursor
.CodeMirror-cursor
.user-story-chapter-base .companies-showcase-base .logo

CSS
div[class^="data-structure-viewer"] g[class^="node"] > circle {
    fill: var(--darkreader-neutral-background) !important;
}
[class^=question-picker-detail] {
    background: none !important;
}
.monaco-editor, .monaco-editor-background, .monaco-editor .margin {
    background-color: var(--darkreader-neutral-background) !important;
}
div#solution img {
    background-color: ${black};
}

================================

legacy.com

CSS
.Directory {
    background-image: none !important;
}

================================

lenovo.com

INVERT
.m-megaMenu

CSS
.m-mastheadUtilityLinks {
    background: none !important;
}

================================

lesbonscomptes.com

CSS
.important {
    background-color: rgb(92, 92, 61) !important;
}

================================

letters.gov.spb.ru

INVERT
.leaflet-map-pane

================================

letyshops.com

INVERT
.header-logo-image

================================

lg.com

INVERT
img[src*="/features/"]

================================

liberte.pl
sklep.liberte.pl

INVERT
img[src*="liberte_logo.svg"]
img[src*="libertesklep.svg"]
.hamburger

================================

libretexts.org

CSS
.mt-content-container img:hover {
    background-color: rgba(255, 255, 255, 0.75) !important;
    background-blend-mode: normal;
}

================================

librewolf-community.gitlab.io

INVERT
[src="/images/search.svg"]
[src="/images/no-looking.png"]

================================

lichess.org

CSS
.time {
    z-index: 0 !important;
}

IGNORE INLINE STYLE
cg-container > svg > line

IGNORE IMAGE ANALYSIS
.is2d .bishop.black
.is2d .king.black
.is2d .knight.black
.is2d .pawn.black
.is2d .queen.black
.is2d .rook.black
.brown .is2d cg-board

================================

lightning.force.com

CSS
.slds-brand-band,
.slds-brand-band:after,
.slds-brand-band_cover,
.slds-brand-band_medium,
.slds-page-header,
.slds-utility-bar,
.slds-tabs_card,
.slds-card-wrapper,
.slds-grid,
.slds-clearfix {
    background-color: var(--darkreader-neutral-background) !important;
}
.slds-card {
    background-color: #333333 !important;
}
.slds-button_neutral,
.slds-button--neutral {
    color: rgb(20, 150, 20) !important;
}

================================

lightningmaps.org

INVERT
.bo_map_realtime
.live_ctrl > img

================================

lingvoforum.net

INVERT
div#header
div#header img.avatar
span.topslice
span.botslice

CSS
body {
    background-image: none !important;
}
div#header {
    color: black !important;
}
div#header a:link, 
div#header a:visited {
    color: black !important;
}

================================

linkedin.com

CSS
:root {
    --color-text-low-emphasis: ${rgba(0,0,0,0.6)};
}
.js-job-card-company-logo {
    background-color: rgba(255, 255, 255, 0.20);
    background-blend-mode: color;
}
.global-footer-compact__linkedin-logo,
li-icon[type="linkedin-logo"],
.bug-text-color {
    fill: ${black} !important;
}
.pds-ge-entry-card__card {
  background: linear-gradient(to bottom, var(--color-action, var(--blue-70, #0073b1)), var(--color-action, var(--blue-70, #0073b1)) 4px, var(--darkreader-neutral-background, #fff) 4px, var(--darkreader-neutral-background, #fff)) !important;
}
.vjs-load-progress {
    background-color: hsla(0,0%,100%,.3) !important;
}
.vjs-play-progress, .vjs-play-progress::before,
.vjs-volume-level::before, .vjs-volume-level {
    background-color: #fff !important;
}
.vjs-volume-bar {
    background-color: hsla(0,0%,100%,.4) !important;
}
img[class*="ghost-"], div[class*="ghost-"] {
    background-size: 100% 100% !important;
}
.artdeco-pill {
    background-color: ${#ccc} !important;
}
img {
    background-color: transparent !important;
}
img[src*="logo"] {
    filter: brightness(75%);
}

================================

linode.com

INVERT
.c-identity__image

CSS
.o-form__input {
    background-color: rgb(41, 44, 46) !important;
    border-color: rgb(232, 230, 227) !important;
    color: rgb(232, 230, 227) !important;
}
.o-form__input:hover {
    background: transparent !important;
}
.o-form__icon::before {
    color: rgb(232, 230, 227) !important;
}
.o-form__input::placeholder {
   color: rgb(232, 230, 227) !important;
}
.o-button, o-button--secondary {
    background-color: rgb(41, 44, 46) !important;
    border-color: rgb(232, 230, 227) !important;
    color: rgb(232, 230, 227) !important;
}
.o-button:hover {
    background: transparent !important;
}

================================

linotype.com

INVERT
#logo

================================

linustechtips.com

CSS
.ipsBadge,
a.ipsBadge {
    --darkreader-bg--badge--background: ${white} linear-gradient(rgba(0,0,0,0.9), rgba(0,0,0,0.9))
}

================================

linux-hardware.org

CSS
#ezcGraphChart > path:first-of-type {
    fill: var(--darkreader-neutral-background) !important;
}

================================

linuxgrandma.blogspot.com

CSS
body {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

lirc.org

CSS
body {
    background: none !important;
}

================================

literia.pl

INVERT
.icon-svg-pinterest
.icons-icon-facebook
.sec-logo
.slick-arrow
a::after
a#logo
span.as-label::before
img[alt="ringier axel springer"]

================================

live.com

INVERT
.ewr-sheettable

================================

live.myvrspot.com

INVERT
.img-logo

CSS
.wrap {
    background: var(--darkreader-neutral-background);
}

================================

livemint.com

INVERT
div.icoMenu
a.icoSearch.iconSprite
a.icoBell.iconSprite
a.icoBookmark2.iconSprite
a.icoBookmark3.iconSprite
a.icoTwit.iconSprite
div.icoShare.iconSprite

================================

lkml.org

INVERT
img[src*="corner"]

CSS
body {
    background-image: none !important;
}
td.c {
    color: ${white} !important;
}

================================

login.live.com

INVERT
div[id$="Proofs"] .tile-img

================================

login.yahoo.com

INVERT
.social-login
label[for=persistent]::before

IGNORE IMAGE ANALYSIS
.social-login

================================

lol.fandom.com

CSS
.bracket-line:not(.l-down)::after,
.bracket-line::before,
.bracket-spacer.horizontal::before {
    border-width: 0 0 2px 0 !important;
}
.bracket-line.z-down::after {
    border-width: 0 0 2px 2px !important;
}
.bracket-line.z-down::before {
    border-width: 2px 2px 0 0 !important;
}
.bracket-line.z-up::before {
    border-width: 0 2px 2px 0 !important;
}
.bracket-line.z-up::after {
    border-width: 2px 0 0 2px !important;
}

================================

lovekrakow.pl

INVERT
.brand

================================

lowendtalk.com

INVERT
.MeMenu > :last-child
.SpBookmarks

================================

lowes.com

CSS
img {
    mix-blend-mode: normal !important;
}

================================

lunapic.com

INVERT
.toolbar-button
.square-toolbar-button

IGNORE INLINE STYLE
.color-well-color

================================

lux.camera

CSS
.u-content-background {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

m.dianping.com

INVERT
.flash-screen
.header-download-img

================================

m.genk.vn

CSS
.news .li {
    background-image: none !important;
}

================================

m.motonet.fi

CSS
form#haku input[type="text"], #select_my_motonet {
    color: var(--darkreader-neutral-text);
}

================================

m.slashdot.org

CSS
.slashbar,
.story-prop,
#discussion,
#filler {
    background-image: none !important;
}

================================

madshi.net

CSS
body,
tr,
td {
    background-image: none !important;
}

================================

magazine.skyeng.ru

INVERT
.header__logo__img

================================

magazynbieganie.pl

INVERT
.logo-box

================================

magic.freizeitspieler.de

INVERT
img[src="pics/MtG-Freizeitspieler.gif"]

CSS
body {
    background-image: none !important
}

================================

mail.google.com

INVERT
img.Hl
img.Hk
img.Ha
.asor_t0
.asor_i4
.d-Na-Jo.d-Na-N-ax3
.RK-QJ-Jk
.RK-Mo.RK-Qq-LF
#ita-st-id-cs
.d-Na-N-M7-JX.d-Na-J3
.gb_df
img[src$="google_gsuite"]
img[src$="profile_mask2.png"]
.rY>.sa
.buh
.n3 .qr
.aii + .qj
.aih + .qj
.asor
.aiC
.mixmax-flyout__wrapper
div[aria-label="Hangouts"] > div[role="tablist"] > div[tabid="chat"] > div
form[method="POST"] ~ table div[style] > div > :first-child button:not([string]):not([id])

CSS
@media (min-resolution: 144dpi), (-webkit-min-device-pixel-ratio: 1.5) {
    .buk {
        background-image: url(//ssl.gstatic.com/ui/v1/icons/mail/rfr/density_default_v1_2x.png) !important;
    }
    .bui {
        background-image: url(//ssl.gstatic.com/ui/v1/icons/mail/rfr/density_comfortable_v1_2x.png) !important;
    }
    .buj {
        background-image: url(//ssl.gstatic.com/ui/v1/icons/mail/rfr/density_compact_v1_2x.png) !important;
    }
}
.buk {
    background-image: url(//ssl.gstatic.com/ui/v1/icons/mail/rfr/density_default_v1_1x.png) !important;
}
.bui {
    background-image: url(//ssl.gstatic.com/ui/v1/icons/mail/rfr/density_comfortable_v1_1x.png) !important;
}
.buj {
    background-image: url(//ssl.gstatic.com/ui/v1/icons/mail/rfr/density_compact_v1_1x.png) !important;
}
div[class*="bym"][role="navigation"] {
    background-color: var(--darkreader-neutral-background) !important;
}
.ain .TO,
.TO.ol {
    background-color: rgba(255,255,255,0.05) !important;
}
::-webkit-scrollbar-thumb {
    background-color: #424242 !important;
}
::-webkit-scrollbar {
    background-color: transparent !important
}

IGNORE INLINE STYLE
.at
.au
.av
.qj
.hU.hM
.hV.hM
.ajZ-Jt
.aH5
.JA-Kn-Jr-Kw-Jt

================================

mail.protonmail.com

CSS
.customCheckbox-input:not(:checked) + .customCheckbox-mask {
    background: none !important;
}
select.field {
    background: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text) !important;
}
.label-stack-item {
    background-color: var(--color,currentColor) !important;
}
.items-column-list-inner {
    border: solid var(--bordercolor-input) !important;
}

================================

mail.qq.com

INVERT
.listbg
.navbar
.topline
.topbg
.todaybar_ad_bg
.navbottom
.search_subject .smartsearch
#searchIcon

CSS
.fdbody, .tipstitle {
    background-color: ${#9bbb59} !important;
}
.fdbody {
    border-left-color: var(--darkreader-neutral-background) !important;
}
.readmailinfo, .settingtable, .tabtitle td.selected, .biginfo, .tipstitle, .colorsure {
    background-color: ${#ebf4d8} !important;
}
.toolbgline, .tabtitle td, .readmailinfo, .settingtd, .addr_line, .filtertd1, .bartools {
    border-bottom-color: ${#87a34d} !important;
}
.toolbg, .toolbg1, .tabtitle, .addrsort {
    background-color: ${#bed393};
}
.attbg {
    background-color: ${#dceac0} !important;
}
body[module="qmReadMail"] {
    background-color: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-color) !important;
}
.selbar_bt {
    background-color: var(--darkreader-neutral-background) !important;
    border-top-color: var(--darkreader-neutral-background) !important;
    border-bottom-color: var(--darkreader-neutral-background) !important;
}
table.O2 td {
    border-bottom-color: ${#c1c8d2} !important;
}
body[accesskey],
.cpright {
    background-color: var(--darkreader-neutral-background) !important;
}
.B, .nomail, .colortitle {
    background-color: ${#a9b3b6} !important;
}
.settingTitle .selected {
    color: ${#ebf4d8} !important;
}

================================

mail.tutanota.com

CSS
div.mb > div > svg > path {
    fill: black !important;
}

================================

makeuseof.com

IGNORE IMAGE ANALYSIS
.header-logo

================================

manage.buyvm.net

INVERT
.legend-container

================================

manjaro.org

CSS
.page-header-image {
    z-index: 0 !important;
}

================================

manualslib.com

IGNORE IMAGE ANALYSIS
div.pdf

================================

map.qq.com

INVERT
#FullScreen
#Logo
#Map
.MenuPanelCheckbox
.MenuPanelContent
.nextStep
.prevStep
.textInput
.titleText
.traffic_next
.traffic_rightbg_b2
.traffic_rightbg_m
.traffic_rightbg_m3
.traffic_rightbg_t2

================================

mapa-turystyczna.pl

INVERT
.ts-map svg

================================

maps.metager.de

INVERT
.map canvas

================================

marginalrevolution.com

INVERT
.logo-mobile
.logo-desktop
img[src$="mru-logo-450.png"]

CSS
nav {
  background-color: #288d73 !important;
}

================================

marinij.com

INVERT
.custom-logo.logo > a > img

================================

market.yandex.*

INVERT
ymaps[class$="ground-pane"]
ymaps[class$="svg-icon-content"] > ymaps

================================

marketplace.visualstudio.com

CSS
.ux-updated-date {
    color: ${rgb(55, 255, 0)} !important;
}
.ms-Grid-row {
    color: ${rgba(0, 0, 0, .8)} !important;
}

================================

markmcgranaghan.com

CSS
body {
    background-color: transparent !important;
}

================================

marktplaats.nl

INVERT
.mp-Header-logo
.mp-svg-messages
.mp-svg-notification
.mp-svg-profile
.svg-icon-block

================================

mastarti.com
streamguard.cc

INVERT
.fp-play-icon div
.fp-pause-icon .fp-pause-block div
.fp-volumebar em.fp-color
.fp-fullscreen-line
.fp-fullscreen-dot

================================

math.semestr.ru

INVERT
.img-online

================================

mathsisfun.com

CSS
#searchFld {
    background-color: var(--darkreader-neutral-background) !important;
    border-color: black !important;
}
#searchBtn {
    border-color: black !important;
    background-color: var(--darkreader-neutral-background) !important;                                                                                                       
}
#content h1 {
    color: var(--darkreader-neutral-text) !important;
}
#hdr {
    opacity: 0.2;
}
#menuWide li{
    background-color: rgb(64, 99, 255) !important;
    color: white !important;
}
#menuWide li:hover {
    background-color: rgb(18, 35, 112) !important;
}

================================

matomo.org

INVERT
img[alt*="WordPress"]
img[src*="Mailfence"]
img[src*="sampling"]
img[src*="logo"]
img.mega-menu-logo

CSS
select, .elementor-text-editor, .elementor-text-editor *,
.elementor-blockquote *, .elementor-testimonial *,
.elementor-price-table * {
    color: var(--darkreader-neutral-text) !important;
}
.elementor-card-shadow-yes .elementor-post__card:hover {
    box-shadow: rgb(76 75 75 / 42%) 0px 0px 30px 0px;
}
.elementor-card-shadow-yes .elementor-post__card {
    box-shadow: rgb(0 0 0 / 23%) 0px 0px 10px 0px;
}

================================

matrix.org

INVERT
.mxnavbar__logo
.mxnavsection__icon
.mxgrid__item__bg__img
.mxblock__explore__item__img
a[aria-label="matrix live  permalink"] > svg
a > svg

================================

matsci.org

INVERT
.d-header #site-logo

CSS
.category-logo.aspect-image img {
  background-color: white !important;
}

================================

matters.news

INVERT
.splash-screen .icon
.logo .icon

CSS
section[class$="container"][style^="background-image"] {
    background-image: none !important;
}

================================

mediawiki.org

INVERT
#p-logo-text

IGNORE IMAGE ANALYSIS
.mw.wiki-logo

================================

medium.com

INVERT
.svgIcon
.svgIcon-use

================================

medium.freecodecamp.org

CSS
span.markup--quote.markup--p-quote.is-other {
    background-image: linear-gradient(rgba(14, 255, 167, 0.2), rgba(14, 255, 167, 0.2)) !important;
}

================================

meet.google.com

IGNORE INLINE STYLE
svg[preserveAspectRatio*="meet"] *

================================

meet.jit.si

IGNORE INLINE STYLE
.jitsi-icon *

================================

mega.nz

INVERT
a.fm-files-view-icon
a.top-icon.menu
div.checkdiv.megaapp-download.checkboxOff.switches
div.fm-dialog-close.medium
div.nw-tree-panel-arrows.icons-sprite
i.small-icon.context
i.small-icon.dialog-sprite.arrows-to-bottom
i.small-icon.dialog-sprite.arrows-to-top
i.small-icon.download-as-zip
i.small-icon.icons-sprite.tiny-grey-tick
i.small-icon.import-to-cloud
i.top-menu-icon.menus-sprite
i.transfer-progress-icon
span.top-icon.language

CSS
.grid-wrapper {
    outline-color: var(--darkreader-bg--surface-main);
}
.topbar-searcher {
    background: var(--darkreader-bg--surface-grey-2) !important;
}

================================

meituan.com

INVERT
.dpLogo
.header-title
.site-logo

================================

mendeley.com

CSS
.highlightLayer * {
   background-color: var(--darkreader-selection-text) !important; 
}

================================

mercury.postlight.com

INVERT
.SVGInline-svg path
#merc-logo
[src^="/_/src/components/images/reader_kindle_image"]
[href="https://postlight.com/"] > svg > g > path
[src^="/_/src/components/images/reader_tout_image"]
[src^="/_/src/components/images/phone"]
.w-0-m.db-l.dn.cover.w-50

================================

merriam-webster.com

INVERT
circle.outline.Oval

================================

messages.android.com

INVERT
.x4Tquc
.QrWqSe
.XCHXxd
.pXeIKc

================================

messages.google.com

INVERT
.x4Tquc
.QrWqSe
.XCHXxd
.pXeIKc

CSS
.input-background {
  --input-bg-fade-color: #202124 !important;
}

================================

messenger.com

INVERT
._4rv6
a._4ce_
div[aria-label="Change Position"]
div[role="slider"]

CSS
._8rsr {
  fill: #0098ff !important;
}
._576q mask path {
  fill: white !important;
}
._576q mask rect {
  fill: black !important;
}
._576q div {
  background-color: transparent !important;
}
.j7vl6m33, .a8c37x1j, .bp9cbjyn, mask[id*="jsc_c"] > circle {
    fill: var(--always-white) !important;
}
:root, .__fb-light-mode {
    --filter-disabled-icon: invert(100%) opacity(30%) !important;
    --filter-placeholder-icon: invert(59%) sepia(11%) saturate(200%) saturate(135%) hue-rotate(176deg) brightness(96%) contrast(94%) !important;
    --filter-primary-icon: invert(100%) !important;
    --filter-secondary-icon: invert(62%) sepia(98%) saturate(12%) hue-rotate(175deg) brightness(90%) contrast(96%) !important;
    --filter-warning-icon: invert(77%) sepia(29%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(128%) hue-rotate(359deg) brightness(102%) contrast(107%) !important;
    --filter-blue-link-icon: invert(73%) sepia(29%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(103.25%) hue-rotate(189deg) brightness(101%) contrast(101%) !important;
    --filter-positive: invert(37%) sepia(61%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(115%) hue-rotate(91deg) brightness(97%) contrast(105%) !important;
    --filter-negative: invert(25%) sepia(33%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(200%) saturate(110%) hue-rotate(345deg) brightness(132%) contrast(96%) !important;
}
.qbubdy2e {
    fill: none !important;
}
.tdjehn4e, .oo1teu6h {
    background-color: rgba(255, 255, 255, 0.1) !important;
}
.tdjehn4e:hover, .ovq5dppa:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
}
::after {
    border-left-color: #1c1e20 !important;
    border-bottom-color: #1c1e20 !important;
    border-top-color: #1c1e20 !important;
    border-right-color: #1c1e20 !important;
}
i.eb18blue {
    filter: none;
}

IGNORE INLINE STYLE
[role="button"] svg
[role="button"] svg line
div svg[viewBox="0 0 36 36"] mask path
mask > rect
g > rect

================================

meteo.imgw.pl

INVERT
.meteo-chart > div > canvas

CSS
.menu-close img {
    filter: none !important;
}

IGNORE IMAGE ANALYSIS
.gory-imgw a

================================

mewe.com

CSS
body > .ember-view {
    background-color: ${#ebf0f3} !important;
}
body > .ember-view.dialog_wrapper {
    background-color: transparent !important;
}

================================

microsoft.com

CSS
#announce picture + div h3,
#announce picture + div p {
    color: var(--darkreader-neutral-background) !important;
}

================================

microsoftedge.microsoft.com

INVERT
rect[x="7.5"]
text[text-anchor="middle"]

================================

midkar.com

CSS
body {
    background-image: unset !important;
}

================================

miktex.org

INVERT
.icon-bar

CSS
body,
.site-footer {
    background-image: none !important;
}

================================

minecraft.net

CSS
div[style$="/bg-wool-white.png);"] {
    background-image: none !important;
}

================================

minecraftskins.com

CSS
.farbtastic .overlay {
    background-image: url("https://www.minecraftskins.com/bundles/app/images/mask.png") !important;
}

IGNORE INLINE STYLE
.color
#color

IGNORE IMAGE ANALYSIS
.farbtastic .overlay

================================

miro.com

INVERT
#pixiCanvasContainer > :nth-child(1)
.b-icon

================================

mixcloud.com

INVERT
.cQABPj

IGNORE INLINE STYLE
path[fill="none"]
path[fill-rule="evenodd"]

================================

mjtnet.com

INVERT
.navbar-brand

================================

mnt.ee

CSS
#zone-content-wrapper {
    color: var(--darkreader-neutral-text) !important;
}
#zone-content {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

mobiel.nl

INVERT
.credit-warning__image
.dt-carousel-home__dots
.spec__network
img[alt="Tele2"]
img[alt="hollandsnieuwe"]
.header-main__icon
.spec__network-ready

IGNORE INLINE STYLE
.version-without-subscription-row__color
.gEJLdA

================================

moegirl.org.cn

CSS
span.heimu a.external,
span.heimu a.external:visited,
span.heimu a.extiw,
span.heimu a.extiw:visited {
    color: #dadada !important;
}
.heimu,
.heimu a,
a .heimu,
.heimu a.new {
    background-color: #dadada !important;
    color: #dadada !important;
}
body:not(.heimu_toggle_on) .heimu:hover,
body:not(.heimu_toggle_on) .heimu:active,
body:not(.heimu_toggle_on) .heimu.off {
    color: black !important;
}
body:not(.heimu_toggle_on) .heimu:hover a,
body:not(.heimu_toggle_on) a:hover .heimu,
body:not(.heimu_toggle_on) .heimu.off a,
body:not(.heimu_toggle_on) a:hover .heimu.off {
    color: darkblue !important;
}
body:not(.heimu_toggle_on) .heimu.off .new,
body:not(.heimu_toggle_on) .heimu.off .new:hover,
body:not(.heimu_toggle_on) .new:hover .heimu.off,
body:not(.heimu_toggle_on) .heimu.off .new,
body:not(.heimu_toggle_on) .heimu.off .new:hover,
body:not(.heimu_toggle_on) .new:hover .heimu.off {
    color: #BA0000 !important;
}

================================

mojosoft.com.pl

INVERT
img[src*="logo16"]
img[src*="img/iko"]
img[src*="img/ideabank"]
img[src*="img/logo"]
img[src*="img_logo"]
#searchbox

CSS
#viewsh,
#view,
#szukajka,
header,
header.smaller #szukajka .inputsearch,
#search_query,
#mainMenusmall {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}
#searchbox, 
select {
    color: var(--darkreader-neutral-background) !important;
}

IGNORE INLINE STYLE
header.smaller

================================

moluch.ru

INVERT
.logo

CSS
body {
    background-image: var(--darkreader-neutral-background) !important;
}

================================

money.pl

CSS
.dynks {
    color: ${#666666} !important;
}
a[href$="twitter.com/money_pl"] path:first-of-type {
    --darkreader-inline-fill: transparent !important;
}

================================

monokai.pro

INVERT
.c-icon-process

================================

monstercat.com

CSS
.volume-slider-outer {
  z-index: -***********;
  background-color: ${#4d4d4d} !important;
}
.volume-slider-inner{
  background-color: ${#1873cc} !important;
}
.volume-slider-handle {
  background-color: ${#1d1d1d} !important;
}
.title, h3, .line-bottom, .line-top, a, .one-line-ellipsis {
  color: ${#4b4e50} !important;
}

================================

monta.ir

INVERT
.floatLeft
.formula-pack > img
.m--content-panel > div > div > canvas
.highcharts-background
.customized-field
.source
.choice > .layoutImageInline
.choice > img
.choice > div > img
.navbar-item > img
.empty_state_student
progress
td > img
.notice > div > img
td > div > img
.extrapractice > div > img
.extrapractice > div > div > img
.empty_teacher
.GO5O4PB-gc-b
.GO5O4PB-gc-i
.GO5O4PB-gc-e
.GO5O4PB-gc-i > div
.GO5O4PB-gc-Eb > div > a
img.is-rounded
img.is-128x128

CSS
.GO5O4PB-gc-n {
    background-image: none !important;
}

================================

moodle.herzen.spb.ru

INVERT
.img-responsive

================================

moovitapp.com

INVERT
.map
.logo

================================

morele.net

INVERT
.h-logo.col
.h-control-btn > [src="/static/img/shop/icon-bezpieczna-dostawa.svg"]
.swiper-slide-visible.mx-auto.swiper-slide > .d-flex.img-container
div.cn-different-frontend.cn-menu-item-hover.cn-shop > .cn-main-name.cn-name > .cn-name-value > span:nth-of-type(1)

================================

mos.ru

INVERT
div[class^="src-components-Map-___styles-module__map_container"]
div[class^="src-components-Map-___styles-module__control"]::before
div[class^="src-components-Map-___styles-module__control"]::after
#map_div_container
#points_layer > text

================================

moto.pl

INVERT
img[src="https://bi.im-g.pl/im/0/24525/m24525630.png"]

CSS
.main_wrapper,
.top_section_bg, .bottom_section_bg {
    background-color: ${#e5e5e5} !important;
}

================================

mozilla.net

INVERT
.css-editor-container header h4::before

================================

mp.weixin.qq.com

INVERT
.audio_card_progress
.audio_card_progress_handle

================================

msmgtoolkit.in

CSS
.cid-r89gzwx5mR .mbr-section-title {
    color: ${white};
}
.cid-r89gzwx5mR .mbr-section-subtitle {
    color: ${white};
}

================================

msn.com

INVERT
.logowrapper

================================

msys2.org

CSS
body {
    background-image: none !important;
}

================================

mt.lv

CSS
.graph > text {
    fill: var(--darkreader-neutral-text) !important;
}

================================

mturk.com

INVERT
[src="/assets/images/how-it-works.png"]
[src="/assets/images/requester_signup.svg"]
[src="/assets/images/worker_signup.svg"]
.lb-mid-8.lb-tiny-24.lb-col > .lb-img > div

================================

mullvad.net

CSS
#bg {
    fill: #192E45;
}

================================

mumble.info

INVERT
.os
.suggested-download-button-caption

================================

musclewiki.com

INVERT
img[src="/static/images/logo.png"]

================================

music.163.com

INVERT
.btns
.cor
#g_backtop
#g_player
.hot
.logo
.m-playbar
.m-table thead
.u-btn2
.u-lv

CSS
.n-myinfo {
    background-image: none !important;
}

================================

music.amazon.*

CSS
.listViewStatusButtonInLibrary .add,
.listViewStatusButtonInLibrary .added {
    background-color: ${black} !important;
}
.slider .scrubber,
.slider .scrubberHandle,
.slider .sliderTrackRemainder {
    background-color: ${black} !important;
}

================================

music.apple.com

INVERT
.web-navigation__logo-link.button-reset.ember-view
.native-upsell__logo
.native-upsell__action
.dt-search-box__icon
.web-chrome-playback-controls
.dt-media-contextual-menu
.web-chrome-playback-lcd__contextual-badge--non-marquee.web-chrome-playback-lcd__contextual-badge
.love-or-popular__popular.love-or-popular__glyph
.web-chrome-playback-lcd__progress-bar-container
.web-chrome-playback-lcd__volume

================================

musictheory.net

INVERT
[data-musictheory-id="lesson"] canvas

================================

my.bible.com

CSS
.nav-title {
    color: var(--darkreader-neutral-text) !important;
}

================================

my.cofc.edu

CSS
.container {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

my.contabo.com

CSS
#mainmenu > div, 
#submenu > div,
.button,
.ui-button {
    background-image: none !important;
}
.ui-button {
    color: var(--darkreader-neutral-text) !important;
}

================================

my.frantech.ca

INVERT
.logo

================================

my.nextdns.io

INVERT
.mt-2.mb-4.text-center
img[src*="/static/media/logo-large"]
img[src*="/static/media/samsung"]
img[src*="/static/media/sonos"]
.d-md-inline > img
.settings-button
.stream-button
.tooltipParent > img
.list-group-item > div > img
.d-flex > .text-break > img
.d-flex > .flex-grow-1 > div > img
img[src*="handshake-logo"]

CSS
.text-right[style*="opacity: 0.3"] {
    opacity: 0.6 !important;
}
g.rsm-geographies {
    filter: invert(1) hue-rotate(180deg) brightness(130%) !important;
}

================================

my.nintendo.com

CSS
.Layout-app,
.signUpButton {
    background-image: none !important;
}

================================

myaccount.google.com

INVERT
c-wiz ul img

================================

myaccount.suse.com

INVERT
.auth-org-logo
.logo

================================

myanimelist.net

CSS
body:not(.ownlist) {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

mybrandnewlogo.com

IGNORE INLINE STYLE
.b-logo-wrap *

================================

mymenu.be

INVERT
.close-icon

CSS
.uix-steps .item.active .number {
    color: ${darkred} !important;
}

IGNORE INLINE STYLE
.uix-tracking .information .step .number

================================

n.maps.yandex.ru
mapeditor.yandex.com

INVERT
span[class*="nk-icon_id_commit-"] > svg > g > path
span[class*="nk-icon_id_business-feedback-task"] > svg > g > path

================================

nalog.ru

INVERT
.header__logo

================================

nasa.gov

IGNORE IMAGE ANALYSIS
.vjs-has-started .vjs-control-bar .vjs-control.vjs-logo-image

================================

natemat.pl
aszdziennik.pl
innpoland.pl
dadhero.pl
mamadu.pl

INVERT
img[src*="/static/media/"]
img[alt="close icon"]
a[href="#comments"]

CSS
img[class*="Footer--Logo"] {
    background-color: #fff !important;                                                          
}

================================

nature.com

INVERT
.c-header__logo
[href="#search-menu"] > svg
.c-header__link--chevron > svg
img[alt="Nature Careers"]

================================

nauka.rocks

INVERT
.navigation-branding

================================

nba.com

INVERT
.nlProgressBar

================================

nbc12.com

INVERT
div.logo.logo-slim
div.logo.logo-large.logo-footer

IGNORE IMAGE ANALYSIS
.logo

================================

neonet.pl

INVERT
div[class^="newsSingleBar"] > div[class^="newsSingleBarHeaderWidescreen-category"]

CSS
div[class^="newsSingleBar"] > div[class^="newsSingleBarHeaderWidescreen-category"] {
    color: ${white} !important;
}

================================

neowin.net

CSS
.select option {
    background-color: #1c1e1f !important;
    color: white !important;
}

================================

nerdschalk.com

INVERT
.logo-default

================================

netlify.com

CSS
[data-darkreader-inline-fill] {
    fill: var(--scrim-icon-color) !important;
}

================================

netzpolitik.org

INVERT
img[src*="/wp-content/themes/liebefeld/images/netzpolitik_logo.svg"]
img[src*="/wp-content/themes/liebefeld/images/palasthotel.svg"]
a.menu__canvas--toggle

================================

news.mit.edu

INVERT
.logo > .mit-news

================================

news.mynavi.jp

INVERT
.itsearch-head .logo
.site-header__bnr
.site-header__logo

================================

news.yahoo.com

INVERT
#uh-logo
.vp-handle

================================

news.ycombinator.com

INVERT
.votearrow

================================

newyorker.com

INVERT
[data-testid="Logo"]
[class*="Logo__logo"]

================================

nexojornal.com.br

INVERT
.g-aiImg

================================

nextdns.io

CSS
.index .key-features .feature.blocklists {
    background-image: none !important;
}

IGNORE INLINE STYLE
path.rsm-geography

IGNORE IMAGE ANALYSIS
.index .hero .content .logo

================================

ngrok.com

INVERT
.customer-logos

CSS
.browser path[fill="#BBB"], .browser g[fill="#BBB"] {
    fill: #BBB !important;
}
.terminal tspan[fill="#FFF"] {
    fill: #FFF !important;
}
.terminal tspan[fill="#BBB"] {
    fill: #BBB !important;
}

================================

nicehash.com

INVERT
.chart
img[src*="hashpower@2x]

================================

nirsoft.net

CSS
.menub1 a:link, .menub1 a:visited{
    color: #0040ff;
}
.menub1 a:hover {
    color: #ffffd0;
}

================================

nnmclub.to
nnm-club.me

INVERT
img[src$="images/icon_arrow.gif"]
img[src$="images/freeleech.gif"]

================================

nokia.com

CSS
.pds-background-cover > .pds-background-image-set {
    background-color: var(--darkreader-neutral-background) !important;
}
.pds-background-cover video {
    filter: brightness(50%) sepia(40%) !important;
}

================================

nos.nl

INVERT
.npo-button

IGNORE IMAGE ANALYSIS
.npo-button

================================

notion.so

INVERT
img[alt="People using Notion"]
.global-margin-s .logos
.persona-grid > .persona-grid-item > .persona-grid-image
.desktop-illustration
.illustration .next-image
.api-section-image .next-image

CSS
.notion-divider-block div div {
    border-bottom: 1px solid ${rgba(55, 53, 47, 0.4)} !important;
}
.header .logo svg > path:nth-child(1),
nav[aria-label="Main footer navigation"] .logo svg > path:nth-child(1) {
    fill: var(--darkreader-neutral-background) !important;
}
.header .logo svg > path:nth-child(3),
nav[aria-label="Main footer navigation"] .logo svg > path:nth-child(3) {
    fill: var(--darkreader-neutral-text) !important;
}

================================

novartis.com

INVERT
#logo

================================

npmjs.com

INVERT
header a[href="/"] svg
#orgs_panel img.h2
#enterprise_detail_panel img.h2
#customers_panel img[src*="adobe.full.png"]
#customers_panel img[src*="bbc.full.png"]
#customers_panel img[src*="conde-nast.full.png"]
#customers_panel img[src*="netflix.full.png"]
#customers_panel img[src*="visa.full.png"]
._5532dff2
._93bbf0b4

CSS
a title + g[data-darkreader-inline-fill] {
    fill: var(--darkreader-neutral-text) !important;
}

================================

ns.nl

CSS
.section--nsYellow {
   background-color: var(--darkreader-neutral-background) !important; 
}

================================

ntlite.com

CSS
.fr-wrapper *::selection {
    background-color: dodgerblue !important;
}

================================

nvidia.com
nvidia.in

INVERT
svg.global-footer__logo
a.brandLink
img[src*="Experience"]
a[href*="facebook"]
a[href*="twitter"]
a[href*="youtube"]
a[href*="flickr"]

CSS
div.nvidia a svg {
    fill: ${black} !important;
}
div.brand-container a svg {
    fill: ${black} !important;
}
.taboff {
    background-image: none !important;
}

================================

nvidia.pl

INVERT
.brand-container
img[src*="Experience"]
a[href*="facebook"]
a[href*="twitter"]

CSS
body,
.taboff {
    background-image: none !important;
}

================================

nymag.com

INVERT
.logo-svg
.article-nav-top-center
#latest-feed-title
#site-feed-curbed-title > .feed-title-wrapper
.tab-trigger > svg
.tab-trigger > svg > circle
.tab-trigger > svg > g > circle
#vulture-trigger > svg > g > path:nth-child(1)
#wwwthecut-trigger > svg > .active > path

================================

nytimes.com

INVERT
.svelte-1v1dl99
#xwd-board

CSS
.css-oylsik,
.css-nhjhh0 svg,
.css-18z7m18 svg,
.css-1q2j1fr svg,
a[data-testid] > svg {
    fill: ${black} !important;
}
.headline-link div {
    color: ${black} !important;
}
.headline-link div:hover {
    color: ${#555} !important;
}
.svelte-15nnlbj {
    font-weight: bold !important;
}
#xwd-board rect[class^="Cell-block--"] {
    fill: ${black} !important;
}

================================

nzbget.*

INVERT
.icon-top
.icon-bottom
.icon-up
.icon-down

================================

nzz.ch

INVERT
.logo

CSS
.logo {
    background-color: unset !important;
    color: ${white} !important;
}

================================

oalevelsolutions.com

INVERT
p > span > img

================================

obserwatorgospodarczy.pl

INVERT
.logo-img

================================

oclc.org

IGNORE INLINE STYLE
.chart-key

================================

odysee.com

INVERT
.header__navigation-logo
[data-vjs-player] .vjs-control-bar > [title="Autoplay Next Off"]
[data-vjs-player] .vjs-control-bar > [title="Autoplay Next On"]::after

CSS
.content__viewer {
    border: none !important;
}

================================

oferteo.pl

INVERT
img[src*="oferteo-color"]
ul.logo-box.text-center
span.icon-gray.quotation-mark

================================

oisd.nl

INVERT
img[alt="logo"]

================================

okonto.pl

INVERT
.imageWidget

================================

okta.com

INVERT
.chiclet--container
.chiclet--article
.chiclet--footer

================================

old.reddit.com

INVERT
.volume-slider-bar
.volume-slider-thumb
.reddit-video-seek-bar-root

CSS
.seek-bar-progress {
    background-color: ${#004daa} !important;
}

================================

oleole.pl

INVERT
#logo::after
div[id="brands-carousel"]
div[id="footer-logo-brands"]
img[src*="main_menu"]
img[src*="SG/icon"]

================================

olx.pl

CSS
.searchmain {
    background-color: var(--darkreader-inline-bgcolor) !important;
}
.wrapper, .footer-bottom {
    background-color: var(--darkreader-neutral-background) !important;
}

IGNORE IMAGE ANALYSIS
.cat-icon-628
.cat-icon-87
.maincategories .maincategories-list .li .item a[data-id="promo"] .category-1.cat-icon-promo

================================

omni.se

CSS
.article,
.resource,
.component--storyLink,
.component--storyHeading,
.btn--secondary,
.starbox-star {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

omnicalculator.com

INVERT
.GenericText

================================

omnivox.ca

INVERT
td[style*="/cvir/UI"]
img[src^="/cvir/UI/Theme/Lea_Defaut/Images/Accueil_LEA"]
img[src="/cvir/UI/Theme/Lea_Defaut/Images/mesclasses_cg_subtop.jpg"]
img[src^="/cvir/UI/Theme/Lea_Defaut/Images/accueil_"]
img[src$="/bas_du_menu.jpg"]
img[src$="tile.jpg"]
.cgSelect
.mioLinks
.calendrier
.servLinks
.descSection

CSS
.cgSelect > table > tbody > tr > td:nth-child(1) > a > font {
    color: green !important;
}
.calendrier > table > tbody > tr,
.calendrier > table > tbody > tr > td > img,
.calendrier > table > tbody > tr > td > table > tbody > tr > td > img
{
    filter: invert(100%) hue-rotate(180deg) contrast(100%) !important;
}
table[style*="/cvir/UI"],
.cgBg
{
    background-image: none !important
}

================================

onet.pl
plejada.pl

INVERT
.websiteLogo
.serviceIcon
a.serviceLogo > img
.sheet
.logoImage
.locationName svg
.logoImageRight
.weatherBox .iconNow
.forecast img
.weatherIcon
ul.contentList img.icon
._3ZySwSLi_pur0unnAQO2No
.WeatherDay_tempIcon__3Uzdm
img[alt="Plejada.pl"]
img[alt="O!Konto"]

CSS
.mainBoxBgHolder {
    background-color: rgba(0, 0, 0, 0.25);
    background-blend-mode: color;
}
.weatherMap .mapLayer .fil0, #weatherChartsHolder .chartValue {
    fill: rgb(128, 128, 128) !important;
}

================================

online.noordhoff.nl

INVERT
#EAGABA img

================================

online.rbb.bg

INVERT
.has-context:hover
.active
.btn

================================

onlineuniversities.com

CSS
body {
    background-image: none !important;
}

================================

op.gg

INVERT
.Level
.ranking-highest__icon .ranking-highest__level

================================

openai.com

INVERT
figure.release-cover>div:first-of-type>img
.math

CSS
body {
    background: initial !important;
}
header[style*="background-image"],
.bg-cover[style*="background-image"] {
    background: none !important;
}
.is-below-fold .header--cover .nav,
.is-below-fold .post-header--cover .nav {
    background: ${white} !important;
    color: ${black} !important;
}
span.token.comment {
    color: ${#aaa} !important;
}
span.token.punctuation {
    color: ${#777} !important;
}
a.btn:not(.fade), button.btn {
    background: ${rgba(5, 5, 38, 0.05)} !important;
    color: ${#050526} !important;
}
button.btn:hover {
    color: ${#b0b0b0} !important;
}
figcaption, .caption {
    color: ${rgba(5,5,38,0.5)} !important;
}
.content .timeline > li::before, .timeline > li::before {
    color: ${black} !important;
}
.content .timeline > li::after, .timeline > li::after {
    background-color: ${black} !important;
}
.switch-input:checked + .switch-label {
    color: ${rgba(0, 0, 0, 0.7)} !important;
}

================================

openanolis.org

INVERT
.logo

================================

opencollective.com

INVERT
img[alt="Open collective"]

CSS
#section-contributors > div {
    background-image: none !important;
}

================================

openebooks.net

INVERT
img[src="images/home_banner.png"]

================================

openenglishbible.org

INVERT
#logo
img[src="html-logo.png"]

================================

opengeofiction.net
openstreetmap.org

INVERT
.map-layout #map
.ideditor g.vertex .icon
.ideditor g.point .icon
.ideditor .icon.areaicon-halo

CSS
div[dir="ltr", id="map"] {
    filter: none !important;
    background: #000 !important;
}
.ideditor .labels-group.halo text {
    stroke: var(--darkreader-neutral-background) !important;
}
.map-layout #map {
    background-color: ${#1b1b1b} !important;
}

IGNORE INLINE STYLE
.ideditor .main-content *

================================

openreview.net

CSS
#__next {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

openvpn.net

IGNORE INLINE STYLE
.navbar-brand > svg *

================================

openwall.com

INVERT
.logo

================================

openwrt.org

CSS
#dw__pagetools .menuitem svg {
    fill: var(--darkreader-neutral-text) !important;
}

================================

orf.at/corona/*

INVERT
.bg
.annotation
.sparkline--fill

================================

otomoto.pl

INVERT
.seller-card__links__link__icon

================================

overleaf.com

INVERT
.pdf-page-container

================================

owncube.com

INVERT
[src="/assets/img/logo.png"]

CSS
h1, h2, h3, h4, h5 {
    color: var(--darkreader-neutral-color);
}

================================

ozbargain.com.au

CSS
div.comment-op {
    background-image: linear-gradient(#000000, #212121) !important;
}

================================

ozon.ru

INVERT
ymaps[class$="ground-pane"]

================================

p30download.com

CSS
.article-wrapper {
    color: ${#090702} !important;
}

================================

pacjent.gov.pl

CSS
.hero-content {
    color: var(--darkreader-neutral-background) !important;
}

================================

packages.ubuntu.com

CSS
body {
    background-image: none !important;
}

================================

palshovon.wixsite.com

INVERT
#BACKGROUND_GROUP
[alt="Marble Surface"]

CSS
[data-mesh-id="SITE_FOOTERinlineContent-gridContainer"] {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

papaya.rocks

INVERT
.favorite.js-open-login
.header__title
.js-logo
.js-logo-big
.js-menu-toggle
.js-open-search
.nav__item--profile
.search__button
.search__close

================================

park-in.gr

CSS
.div-background {
    z-index: 0 !important;
}
.container {
    z-index: 1 !important;
    position: relative !important;
}

================================

passport.baidu.com

INVERT
.pass-header-logo
.right-icon

================================

paulgraham.com

CSS
body {
    background-image: none !important;
}

================================

payu.com

CSS
img[src$="PayU-Map_Homepage_World.svg"] { 
    filter: invert(90%) hue-rotate(180deg) contrast(90%) !important;
}

================================

pbs.org

CSS
BODY.normal td.bodyarea {
    background-image: none !important;
}

================================

pcdiga.com

CSS
.data {
    background-color: var(--darkreader-neutral-background) !important;
}
input, textarea {
    background-color: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text) !important;
}
li.level0 > a {
    color: var(--darkreader-neutral-text) !important;                                                                                                                                                                                                                                                                                                                                                                                                                                            
}
li.level0 > a:hover {
    color: #ff4d23 !important;                                                                                                                                                                                                                                                                                                                                                                                                                                            
}

================================

pcpartpicker.com

IGNORE INLINE STYLE
.price-history-legend-color

================================

peardeck.com

INVERT
.boxy-svg

================================

pennylane.ai

INVERT
div[style*="xanadu-background.png"]
div[style*="xanadu-background.png"] > div
div[style*="banner.png"]
div[style*="banner.png"] > div

================================

peonaviveu.blogspot.com

CSS
body {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

perfekcyjnawdomu.pl

INVERT
img[src*="logo"]

================================

pgatour.com

CSS
.score-card tr td.birdie {
    background-image: initial;
    background-color: rgb(1, 76, 181);
}

================================

phonescoop.com

INVERT
#sitelogo

CSS
div#outer.wide,
#socpage,
div#top.top-lg,
div#outer {
    background-image: none !important;
}

================================

phys.nagoya-u.ac.jp

INVERT
img[src="img/top_94.gif"]

CSS
#wrap table {
    background-image: none !important;
}

================================

physics.gmu.edu

INVERT
img.math-display

================================

picrew.me

IGNORE INLINE STYLE
.imagemaker_colorpalette li

================================

pilot.wp.pl

INVERT
.logo
div > button > svg

================================

ping.sx

INVERT
.ring-0

CSS
#headlessui-switch-1 {
    filter: brightness(70%) !important;
}

================================

pixiv.net

CSS
.jAENWx,
.iYRMgo {
    color: ${aliceblue} !important;
}

================================

pl.glosbe.com

INVERT
footer > div > div > a > img

================================

planetagracza.pl

INVERT
p.site-title
#footer .site-title a

================================

plantuml.com

INVERT
.scale

================================

play.google.com

INVERT
.bUWb7c
.WF1WQd
img[src="https://www.gstatic.com/android/market_images/web/play_prism_hlock_2x.png"]
a[href*="about/products"]

================================

play.google.com/apps/publish

INVERT
.IXNAUGB-u-e
.IXNAUGB-U-g img
.IXNAUGB-U-g
img[src^="data:image/png;"]
.LTMPNY-u-e

================================

play.google.com/books/listen

INVERT
a[href*="books/audiobooks"]

CSS
.chapter-item:not(.iron-selected) .chapter-title {
    color: var(--darkreader-neutral-text) !important;
}

================================

play.google.com/music

INVERT
.music-logo
a[title="Google apps"]
.song-row .rating-container

================================

plumbingforums.com

INVERT
.node-icon::before

CSS
div,
.node-icon {
    background-color: var(--darkreader-neutral-background) !important;
}
.input:focus,
.input.is-focused::placeholder {
    background: var(--darkreader-neutral-background) !important;
}

================================

plus.google.com

INVERT
a.gb_b > div
a[href*="about/products"]

================================

pochta.ru

INVERT
ymaps[class$="ground-pane"]

================================

poeditor.com

INVERT
.chart img

================================

polar.com

INVERT
.logo

================================

polarion*

INVERT
.polarion-dle-toolbar-Button img
.polarion-dle-toolbar-ButtonWithMenu img
.polarion-MenuButton img
.polarion-rp-column-configure-layout img

================================

polsatnews.pl

CSS
.news--over .news__category, .news--over .news__time, .news--over .news__title, .sent__title {
    filter: none !important;
}

================================

polskabiega.sport.pl

CSS
.main_wrapper {
    background-color: #181a1b !important;
}

================================

polskatimes.pl

INVERT
.componentsNavigationNavbar__logo

================================

poradnikzdrowie.pl

INVERT
.logo

================================

portal.qiniu.com

INVERT
.global-loading-content img

================================

portswigger.net

INVERT
img[alt="Web Security Academy"]
img[alt="The Daily Swig"]

================================

postnauka.ru

INVERT
.m-header__logo

================================

praca.pl

INVERT
img[alt="Praca.pl"]

CSS
.app-offer__content {
    background-color: rgb(25, 26, 27) !important;
}
.szcont .f1top,
.szcont .f1template_content {
    background-color: rgba(0, 0, 0, 0.25) !important;
    background-blend-mode: color;
}
.company__img {
    padding: 0 0 !important;
}
.listing__logo,
.app-offer__logo-img,
.company__img,
.employer-profile-header .logo img,
.company-job-list .logo img,
.epc-other-employers .logo img {
    filter: brightness(0.75)
}

IGNORE IMAGE ANALYSIS
.szcont .f1top
.szcont .f1template_content

================================

prajwalkoirala.com

INVERT
svg

================================

pressgazette.co.uk

INVERT
.site-logo

================================

pro-run.pl

INVERT
.td-main-logo

================================

procyclingstats.com

IGNORE IMAGE ANALYSIS
.flag
.flag.w32
.flag.c16

================================

producthunt.com

INVERT
[class="icon_f5f81"]

================================

projectstream.it

CSS
body {
    background: none !important;
}

================================

pronto.io

CSS
input.btn-new-group.pointer {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

prospectmagazine.co.uk

INVERT
.header__logo

================================

prostovpn.org

CSS
body {
    background: none !important;
}

================================

proxmox.com

INVERT
#logo
.uix_logo

================================

psemu.pl

CSS
.menu-item {
    text-shadow: rgb(40, 43, 54) 0px 1px 0px !important;
}

================================

psprices.com

INVERT
.store-logo
.highcharts-label text
.game--description::after

================================

psychonautwiki.org

INVERT
#mw-panel > a > svg
.mw-headline > a > svg
a[href="http://www.emcdda.europa.eu/about"] > svg
img[alt="Tasks.svg"]
img[alt="Star-o.svg"]
img[alt="Pencil.svg"]
#InfoTable > tbody > tr > .Image > div > div > .image
.dosechart

CSS
.dosechart,
.thumbimage,
.mwe-popups-discreet > svg {
    background-color: ${black} !important;
}

================================

publicwww.com

CSS
body {
    background-image: none !important; 
}

================================

pushsquare.com

CSS
.page {
    background-image: none !important;
}

================================

pyszne.pl
lieferando.*
justeat.*
just-eat.*
takeaway.com
thuisbezorgd.nl

INVERT
.gm-style

CSS
.cover img, .logowrapper,
.orderoverview__restaurant-image-container-inner,
div[data-qa="avatar"] {
    background-color: rgba(255, 255, 255, 0.5) !important;
    background-blend-mode: color;
}

IGNORE INLINE STYLE
svg > circle
svg > text > tspan

================================

pythonanywhere.com

INVERT
#id_logo
img[src^="/static/glyphicons/"]

================================

pytorch.org

INVERT
#site-logo
.header-holder:not(.homepage-header) > div.container > div.header-container > .header-logo

================================

pz.gov.pl

CSS
.banner-text {
    color: var(--darkreader-neutral-background) !important; 
}

================================

quantrimang.com

CSS
.taxonomyList .navigation a {
    color: var(--darkreader-neutral-text) !important;
}
.taxonomyList .navigation a:hover {
     color: ${orange} !important;
}

================================

qubes-os.org

INVERT
[src$="xen-logo.svg"]
[src$="whonix-tor.svg"]

================================

quickbase.com

CSS
td.cell, td.label {
   border-color: rgb(24, 26, 27) !important;
}

================================

quizlet.com

CSS
.UIKeyboardHint {
    background-color: transparent !important;
}

IGNORE INLINE STYLE
.FeedbackHeading-emoji > svg *

================================

quora.com

CSS
.logo_fill {
    fill: rgb(219, 87, 83) !important;
}
body {
    background-color: var(--darkreader-neutral-background) !important; 
}

IGNORE INLINE STYLE
#upvote
#downvote

================================

qwant.com

INVERT
.background-home__logo
.home__logo__container .home__logo

================================

rachel53461.wordpress.com

CSS
#grad {
    background-image: none !important;
}

================================

racketboy.com

CSS
#rb-split,
#rb-split > div {
    background-image: none !important;
}

================================

radar-opadow.pl

INVERT
#chase-map
.ro_tooltip

================================

radareu.cz

INVERT
#radarMap .leaflet-map-pane

================================

radio17.pl

INVERT
.main-title

================================

railwaygazette.com

INVERT
.mastheadLogo

================================

rapidtables.com

IGNORE INLINE STYLE
td[style^="background:"]

================================

raspberrypi.org

IGNORE IMAGE ANALYSIS
.c-at-home__container

================================

rateyourmusic.com

IGNORE IMAGE ANALYSIS
*

================================

rationalwiki.org

INVERT
.mw-wiki-logo

================================

read.amazon.com

INVERT
.header_bar_icon:not(#kindleReader_button_close)
.header_bar_button

================================

realmicentral.com

INVERT
img[itemprop="logo"]
div.fly-but-wrap.left.relative > span

================================

reddit.com

INVERT
[role="slider"]
video ~ div [style^="height"]

CSS
[role="slider"] > div:nth-child(4) {
    background-color: ${#0079d3} !important;
}
[style^="--background"] {
    --background: ${#FFFFFF} !important;
}
[style^="--canvas"] {
    --canvas: ${#DAE0E6} !important;
}
[style^="--pseudo-before-background"] {
    --pseudo-before-background: ${#DAE0E6} !important;
}
[style^="--comments-overlay-background"] {
    --comments-overlay-background: ${#DAE0E6} !important;
}
[style^="--commentswrapper-gradient-color"] {
    --comments-overlay-background: ${#DAE0E6} !important;
}
[style^="--fakelightbox-overlay-background"] {
    --fakelightbox-overlay-background: ${#DAE0E6} !important;
}
.md p>a[href="#s"]::after, a[href="#s"]::after {
    color: #000;
}
header a[aria-label="Home"] svg:last-child g,
header > div > div + div a[href] *,
header > div > div + div button[aria-label] * {
    fill: var(--darkreader-neutral-text) !important;
}
#COIN_PURCHASE_DROPDOWN_ID > div {
    background: linear-gradient(180deg,hsla(0,0%,100%,.1) 45.96%,hsla(0,0%,100%,.57) 46%,hsla(0,0%,100%,0) 130%),${gold} !important;
}
#COIN_PURCHASE_DROPDOWN_ID > div > span {
    color: ${white} !important;
}
.md-spoiler-text:not([data-revealed])::selection {
    color: transparent !important;
    background-color: var(--darkreader-bg--newCommunityTheme-metaText) !important;
}

================================

redditstatus.com

INVERT
.logo-container

================================

redgamingtech.com

CSS
body {
    background-image: none !important;
}

================================

redhat.com

CSS
p, li {
   color: ${black} !important;
}
.PFElement {
    background-color: ${white} !important;
}

================================

redpenreviews.org

CSS
header a {
    color: var(--darkreader-neutral-text) !important;
}

================================

refactoring.guru

CSS
.recipe {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

referentiemateriaalvo.noordhoff.nl

CSS
body {
    background-image: none !important;
}

================================

regex101.com

INVERT
canvas

================================

reheader.glitch.me

INVERT
[src=$"GitHub-Mark.png"]
[src=*"header-image-readme-gen.gif]
[src=*"Screen%20Shot%202020-07-17%20at%205.19.18%20PM.png"]

CSS
#add-to-github {
    background-color: #96943f !important;
}
#upload-github {
    border: 1px solid #dddddd;
}

================================

relive.cc

INVERT
img[src*="logo-relive"]

IGNORE IMAGE ANALYSIS
.email-button i

================================

render.githubusercontent.com/view/ipynb

INVERT
img.math

================================

replit.com

INVERT
.toggle-bars-wrapper

CSS
.monaco-editor .cursor {
    background-color: ${#000};
}

================================

reproducible.archlinux.org

INVERT
img[src*="icons/"]

================================

resmigazete.gov.tr

CSS
img[src="/assets/img/arma.png"] {
    filter: hue-rotate(180deg) invert(1) brightness(2);
}

================================

respekt.cz

INVERT
.sitelogo-link

================================

restoreprivacy.com

CSS
body {
    background-color: var(--darkreader-bg--color-body) !important;
}

================================

reuters.com

INVERT
path[d^="M121.865 50.29c0"]

================================

rfi.fr

INVERT
.o-header__site-nav-link

================================

richie-bendall.ml

CSS
.content--card {
    background-color: #303030;
}
::-webkit-scrollbar {
    width: 0;
    color: transparent;
}
body {
    background-color: #5c6bc0;
}
.app--bar, .drawer--content :not(.mdc-list--non-interactive) > :not(.mdc-list-item--disabled) .mdc-list-item--activated:after, :not(.mdc-list--non-interactive) > :not(.mdc-list-item--disabled) .mdc-list-item--activated:before {
    background-color: #3f51b5;
}
.btc-dialog .mdc-button {
    color: #3f51b5;
}
.btc-dialog svg {
    fill: white;
}

================================

richiebendallstatus.ml

CSS
#page-container {
    background-image: none;
}
.success-bg {
    background-color: #80BA27;
}
.warning-bg {
    background-color: #f7921e;
}
.success {
    color: #80BA27;
}
.warning {
    color: #f7921e;
}
.danger-bg {
    background-color: #ff0000;
}
.paused-bg, .info-bg, .black-bg {
    background-color: #17252e;
}

================================

riptutorial.com

CSS
.whole-container {
    background-image: none !important;
}

================================

roblox.com

CSS
.checkbox input[type=checkbox]:checked + label::before {
    background-color: var(--darkreader-neutral-text) !important;
}

================================

rog.asus.com

IGNORE IMAGE ANALYSIS
.rog-header .nav-menu .nav-bar

================================

ros.org

CSS
.bg-ros-dots-grid {
    background-image: none !important;
}

================================

roskomsvoboda.org

INVERT
img[src="/static/core/images/logo.svg"]

================================

rottentomatoes.com

IGNORE IMAGE ANALYSIS
.icon__fresh
.fresh

================================

rp.pl

INVERT
img[alt="Logo RP"]
.slider-track
img[src$="stare-logo.png"]
img[src$="nowe-logo.png"]

================================

rpcs3.net

CSS
p,
.compat-types,
.compat-hdr-left,
.compat-status-container {
    color: var(--darkreader-neutral-text) !important;                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
}

================================

rpm.org

IGNORE IMAGE ANALYSIS
body

================================

rpo.gov.pl

INVERT
.view-content .field-content > img
#etykieta_szukaj a

IGNORE IMAGE ANALYSIS
#bip_kontener

================================

rtlnieuws.nl

CSS
section {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

rubenfixit.com

CSS
.shape-top-left::after,
.nav-btn::after {
    border-color: transparent transparent transparent var(--darkreader-neutral-background) !important;
}
.shape-top-right::after,
.nav-btn::before {
    border-color: transparent transparent var(--darkreader-neutral-background) transparent !important;
}
.shape-bottom-left::after {
    border-color: var(--darkreader-neutral-background) transparent transparent transparent !important;
}
.shape-bottom-right::after {
    border-color: transparent var(--darkreader-neutral-background) transparent transparent !important;
}
#blog-isotope-masonry article,
.shape-top-left,
.shape-top-right,
.shape-bottom-left,
.shape-bottom-right {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

rubjo.github.io

CSS
select {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

runkit.com

INVERT
.CodeMirror div.CodeMirror-cursor

================================

rynekzdrowia.pl

INVERT
img[src*="rynekzdrowia.svg"]
img[alt*="partner"]
.search
.copyright > img

================================

rytmy.pl

INVERT
.logo-rytmy-a

================================

saladelcembalo.org

INVERT
#PageDiv td

CSS
html,
body,
input,
textarea,
select,
button {
    background-color: transparent !important;
    color: var(--darkreader-neutral-text) !important;
}

================================

salsa.debian.org

INVERT
a#logo
img.brand-header-logo

================================

samcodes.co.uk

INVERT
.logo

================================

samsung.*

INVERT
.icon

CSS
.feature-full-bleed-text img {
    filter: brightness(50%) sepia(40%) !important;
}
.feature-full-bleed-text__content {
    z-index: 1 !important;
}

================================

savannah.gnu.org

CSS
.boxtitle {
    background-image: none !important;
}

================================

sbermegamarket.ru

INVERT
ymaps[class$="ground-pane"]

================================

scholar.google.*
scholar.google.*.*

INVERT
a[role="checkbox"] > :nth-child(2)
div[role="banner"] > a > span
a[aria-label="Homepage"]
.gs_ico
img[src*="scholar_logo"]

================================

sci-hub.*

IGNORE IMAGE ANALYSIS
#raven
#logo

================================

science.org

INVERT
.navbar-brand > .hidden-on-dark
.main-header__secondary__logo > img

CSS
h1.news-article__hero__title {
    color: var(--darkreader-neutral-text) !important;
}

================================

scipy-lectures.org

INVERT
img.math

================================

scmp.com

INVERT
.header-menu-container__menu-top-left-wrapper
.global-menu-features__menu-icon
.global-menu-features__search-icon
.global-menu-features__close-icon
.social-button__twitter--active
.social-button__email--active
.footer-wrapper__logo

================================

scpclassic.wikidot.com
scpexplained.wikidot.com
scpfoundation.net
scp-wiki.net
scp-wiki.wikidot.com
scp-wiki-cn.wikidot.com
scpwiki.com

CSS
div#container-wrap, .panel-body, .content-panel {
    background-image: none !important;
}
div#container-wrap::before {
    content: "";
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 162px;
    background-image: url(http://www.scp-wiki.net/local--files/component:theme/body_bg.png) !important;
}
.yui-navset .yui-nav a {
    background-image: none !important;
}

================================

scratch-wiki.info

INVERT
.sb3-comment-label
.sb3-literal-string
.sb3-literal-number

================================

scratch.mit.edu/projects/editor

CSS
path.blocklyFlyoutBackground {
    fill: rgb(32, 32, 32);
}

================================

scribd.com

INVERT
.logo

================================

script.google.com

INVERT
.docs-icon
.icon
.monaco-editor .cursors-layer > .cursor

================================

scroll.com

INVERT
#content > div > div > header > div > div > div > a.Titlebar__wordMark___2U8-r.display__desktop___1Cfo7 > div > div > svg
#content > div > div > footer > div > div > div > div.layout__column___diAE2.layout__span-12___2a4Fw.layout__span___31pR0.layout__portrait-span-3___A34nc.layout__span___31pR0.layout__landscape-span-2___30F_p.layout__order-3___3WFlS.layout__portrait-order-0___108js.space__stack-xl___1oKsQ.flex__column___JnBPu.flex__flex___1N4XU.flex__align-stretch___2HAV3.flex__direction-column___fSYwh > div > div > div > svg

CSS
.Careers__logo___1kLq6 {
    z-index: 1 !important;
}

================================

se.pl

INVERT
.mobile-bars

================================

sec.sangfor.com
sec.sangfor.com.cn

INVERT
.en-logo

================================

secure.ally.com

INVERT
.nobd-aob-day
#lp_invite
#manageNonAllyAccountsFrame .third-party-iframe
#billPayFrame

================================

secureage.com

INVERT
.logo-block

================================

segmentfault.com

INVERT
.sf-header-logo
.sf-header__logo
.sf-logo
.navbar-brand

================================

sejm.gov.pl

CSS
.main {
    background-image: none !important;
}

================================

sembr.org

CSS
mark {
    color: var(--darkreader-neutral-text) !important;
}

================================

seminka-chilli.cz
chilli-shop.sk

CSS
#pannelWrapper {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

semmle.com

CSS
#Header-logo * {
    fill: #ffffff !important;
}

================================

senscritique.com

INVERT
.d-media-videos::before
.eins-wish.black
.eins-logo-small
.header-navigation-main-item a img
.eins-search-header
.eins-poll
.eins-compass
.eins-compass-xl
.eins-notification
.eins-tv
.eins-ticket
.eins-current.black
.eins-done.green
.eins-done.white
.eins-newspaper
.juyLRn

CSS
.ecap-products-next, .ecap-products-prev {
    background-color: hsla(100, 20%, 50%, .8) !important;
    color: ${black} !important;
}
.d-chevron3-b, .d-chevron3-l, .d-chevron3-r, .d-chevron3-t {
    background-image: -webkit-image-set(url(https://static.senscritique.com/img/layout/icons/chevrons/chevron-size3.png?201710121789416) 1x,url(https://static.senscritique.com/img/layout/icons/chevrons/<EMAIL>?201710121789416) 2x);
}

================================

sephora.com

INVERT
img[alt="Sephora"]
img[src="/img/ufe/icons/stores.svg"]
img[src="/img/ufe/icons/community.svg"]
img[src="/img/ufe/icons/me32.svg"]
svg[data-at="basket_icon_large"]
div[aria-label$="stars"]

================================

server.pro

INVERT
svg.server-pro-logo

================================

servercat.net

INVERT
.theme-header-logo

================================

share.dmhy.org

CSS
.jmd_base td a {
    color: ${#3391ff};
}
.jmd .today a {
    color: ${#fff};
}

================================

sharepoint.com

INVERT
img.WACPageImg

CSS
.ms-FocusZone,
.ms-DetailsRow-cell,
.ms-Button {
    background-color: var(--darkreader-neutral-background) !important;
}
.ms-DetailsRow-cell,
.ms-FocusZone,
.ms-Button,
.od-ItemContent-title,
.ms-DetailsHeader-cellName {
    color: var(--darkreader-neutral-text) !important;
}
.ms-Button {
    border-color: ${#F0F0F0} !important;
}

================================

shop.dr-rath.com

CSS
.colored-header-desktop {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

shopify.com
shopify.dev

INVERT
.marketing-nav--skin-light > .marketing-nav__logo
.shopify-logo
.header-country-select__trigger
.lia-message-count::before
.DateTime::before

================================

shorthistory.org

INVERT
.td-logo > .td-main-logo > .td-retina-data

================================

signal.org

INVERT
.navbar-item .icon
.signal-logo

================================

signin.nianticlabs.com

INVERT
img[alt="Niantic"]

================================

simplemachines.org

CSS
#header div.frame,
#header,
span.botslice span,
span.botslice,
span.topslice,
span.topslice span,
#content_section,
#content_section div.frame,
span.lowerframe,
span.lowerframe span,
div.title_bar,
h4.titlebg,
h3.titlebg,
h4.catbg,
h4.catbg2,
h3.catbg,
h3.catbg2,
.table_list tbody.header td.catbg {
    background-image: none !important;
}

================================

singularlabs.com

INVERT
img[alt="SingularLabs"]

================================

sio2.staszic.waw.pl

INVERT
.texmath

================================

sklepbiegacza.pl

INVERT
.header__logo
.button__icon
.main-slider__nav-item
.homepage__brand-logo
.footer__list-image
img.footer__image[src*=cash]
.product__brand-logo
.paypo-info__button

================================

skycash.com

CSS
.c-hero__newsletter {
    background-image: none !important;
}

================================

skyscanner.*
skyscanner.*.*
backpack.github.io
tianxun.cn
whoflies.com

CSS
body {
    background: ${white} !important;
}
[class*=bpk-flare-bar__curve] {
    fill: ${white} !important;
}

================================

slack.com

INVERT
.slack_logo > img

================================

slackware.com

CSS
body {
    background-image: none !important;
}
td[bgcolor="#000000"] {
    border: 1px solid !important;
}

================================

slader.com

INVERT
.navigation__logo
.explanation
.solution-cell img
.solution-content img
.answer img

================================

slashnet.wordpress.com

CSS
#container,
.entry,
body {
    background-image: none !important;
}

================================

smap.uthm.edu.my

CSS
.slider-background {
    background: none !important;
}
.backstretch {
    opacity: 0.5 !important;
}

================================

smcdsb.elearningontario.ca

CSS
:host([no-padding-footer]) .d2l-dropdown-content-footer, :host([no-padding-header]) .d2l-dropdown-content-header, :host([no-padding]) .d2l-dropdown-content-container {
    background: var(--darkreader-neutral-background);
}

================================

smithsonianmag.com

INVERT
.site-logo
.footerLogo > a > img
#mobile-icon > span

================================

smzdm.com

INVERT
#logo
.logo-left

================================

snack.expo.io

CSS
#root > div > div > div:nth-child(2) > div:nth-child(2) > div:nth-child(2) > div > div:nth-child(2) {
    background-color: white !important;
}

================================

snapeda.com

INVERT
img[title="SnapEDA"]
img.part-organization
canvas:not(#firstfootprint)

================================

softorage.com

CSS
.tg {
    background-image: linear-gradient(36deg,#fc466b,#3f5efb) !important;
}

================================

softpedia.com

IGNORE IMAGE ANALYSIS
.menubar-hp span.logov
h1.logov

================================

sony.*

IGNORE INLINE STYLE
.brand-logo-svg > g

================================

soundcloud.com

INVERT
.notificationIcon.messages::before

CSS
.listenEngagement, .commentForm__wrapper {
    border: none !important;
}
body,
.commentForm__wrapper,
.searchTitle {
    background: none !important;
}

================================

souq.com

INVERT
img[src*="/souqAmazon-logo-v2"]
li.fashion-menu-link[aria-expanded="false"]
.userNameField::after
.filter-icon.deals
.cart-icon

================================

source.dot.net

CSS
.r {
    border-style: none !important;
}

================================

sourceforge.net

CSS
.intro,
.audience {
    background-image: none !important;
}
.all-facets, .m-project-search-results {
    background-color: ${white} !important;
}

================================

sourcegraph.com

INVERT
.header__logo

CSS
body {
    background-image: none !important;
}

================================

southpark.cc.com

CSS
body {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

soylent.com

INVERT
.header-logo__image
.d-header #site-logo

================================

space.bilibili.com

INVERT
.count::before

================================

speed.cloudflare.com

INVERT
img[src*="speedrabbit-animate.gif"]
img[src*="speedrabbit-static.png"]

================================

speeddial2.com

INVERT
img[src*="images/"]

================================

spidersweb.pl

INVERT
img[alt="logo bizBlog"]
img[alt="bizblog to ludzie"]
a[href="/rozrywka"]
a[href="/plus"]
.amp-site-title

================================

sport.pl

INVERT
#pageHead img

CSS
.top_section_bg, .bottom_section_bg {
    background: ${#e5e5e5} !important;
}

================================

sports.ru

INVERT
.nav-top-line__logo

================================

sporza.be

CSS
.sc-score, 
.sc-score__away,
.bouton:hover {
    color: ${#222} !important;
}
.sc-score__wrapper {
    background-color: ${#BBB} !important;
}
.bouton {
    color: ${#fff} !important;
}
.sc-epg--live .sc-epg__program {
    background-color: ${#CCC} !important;
}
.vrt-newsletter {
    background-color: ${#EEE} !important;
}
.vrt-site-footer .vrt-newsletter .vrt-link--newsletter {
    background-color: ${#AAA} !important;
}
.logo__letters {
    fill: var(--darkreader-neutral-background) !important;                                                                                                                                                                                                                                                                                                                                                              
}
.vrt-link {
    background-color: ${rgba(255, 165, 0, 0)} !important;
    color: ${rgba(50, 50, 50)} !important;
}
.vrt-link:hover {
    background-color: ${rgb(100, 100, 100)} !important;
}
.vrt-site-footer__navigation--green {
    background-color: ${#AAA} !important;
    color: var(--darkreader-neutral-background) !important;                                                                                                                                                                                                                                                                                                                                                                                                                     
}

================================

spreadprivacy.com

INVERT
[src^="https://spreadprivacy.com/content/images/2017/07/linux"]
[src^="https://spreadprivacy.com/content/images/2017/11/Significant-Actions"]
[src^="https://spreadprivacy.com/content/images/2018/09/ddg-traffic"]
[src^="https://openclipart.org/image/24px/svg_to_png/28768/qubodup-Cubikopp-smilies"]
[src="https://spreadprivacy.com/content/images/2017/10/smiley.png"]
[src="https://spreadprivacy.com/content/images/2018/09/private-browsing4.png"]
[src="https://spreadprivacy.com/content/images/2017/10/https-in-address-bar26.png"]
[src="https://spreadprivacy.com/content/images/2018/01/private-browsing-reasons-1.png"]
[src="https://spreadprivacy.com/content/images/2020/05/search-preference-menu_heatmap.jpg"]
[src="https://spreadprivacy.com/content/images/2020/10/trilateral-invitation_header-1.png"]
[src="https://spreadprivacy.com/content/images/2020/05/search-preference-menu_comparison-1.jpg"]
[src="https://spreadprivacy.com/content/images/2020/05/search-preference-menu_screen-sizes-1.jpg"]
[src="https://spreadprivacy.com/content/images/2020/02/Awareness-of-Privacy-Risk-of-Public-USB-Charging-.jpg"]
[src="https://spreadprivacy.com/content/images/2018/02/DuckDuckGo-Extension_Desktop-OnDevice.jpg"]

================================

ssllabs.com

INVERT
#logo

================================

sso.qiniu.com

INVERT
.navbar-brand

================================

stackage.org

INVERT
.logo

================================

stardewvalleywiki.com

CSS
html {
    background: none !important;
}

================================

start64.com

INVERT
img[alt="logo"]

================================

startpage.com

INVERT
.hamburger-menu .hamburger-button

IGNORE IMAGE ANALYSIS
.home__section__search-logo
.header__logo
.header-settings__logo
.hamburger-menu .hamburger-button

================================

station-drivers.com

INVERT
img[src*="/topic_icons/"]
img[src*="/folder_icons/"]
img[src*="/images/bios_firmware"]
img[src*="/images/driver.jpg"]
img[src*="/images/utile"]
img[src*="/images/rating"]
img[src*="/images/star"]
img[src*="/images/downl"]
img[src*="/components/com_remository/images/file_icons/"]
img[src*="/images/gohome.gif"]

================================

stats.stackexchange.com

CSS
.site-header {
    background-image: none !important;                                                                                                           
}

================================

status.aws.amazon.com

INVERT
.logo
.tabStandard
td > img
td > a > img
th > a > img
th > div > a > img

CSS
.tabStandard a {
    color: ${white} !important;
}
.tabStandard,
.selected a {
    color: ${white} !important;
    background-color: ${black} !important;
}
tbody > tr > th {
    background: transparent !important;
}
table > tbody > tr > td {
    background: transparent !important;
}
.gradient {
    background: transparent !important;
}

================================

status.npmjs.org

INVERT
.logo-container > a > img

================================

statusinvest.com.br

INVERT
div.indice-list div.ml-1
div.indice-list div.mt-2
i.ml-1

================================

stevendoesstuffs.dev

INVERT
.post-img-wrapper img

================================

stine.uni-hamburg.de

CSS
.appointment {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

stm.info

INVERT
.map-OSM

================================

stolichki.ru

INVERT
.logoBig__img
ymaps[class$="ground-pane"]
ymaps[class$="svg-icon-content"] > ymaps
ymaps[class$="placemark__content-inner"] > ymaps > ymaps

================================

stooq.pl

CSS
a[href="//stooq.pl"] path:not([fill]) {
    fill: var(--darkreader-neutral-text) !important;
}

================================

store.google.*

CSS
[style*="background-image"] {
    filter: brightness(50%) sepia(40%) !important;
}

================================

store.playstation.com

INVERT
.psw-brand-text--playstation-store

================================

store.ubi.com

INVERT
div.primary-logo

CSS
.pt_storefront-homepage #main .homepage-slider-wrapper .homepage-custom-slide > a:not(a[href$="/deals"]) .homepage-custom-slide__content .custom-slide-inner h2,
.pt_storefront-homepage #main .homepage-slider-wrapper .homepage-custom-slide > a:not(a[href$="/deals"]) .homepage-custom-slide__content .custom-slide-inner h3,
.pt_storefront-homepage #main .homepage-slider-wrapper .homepage-custom-slide a {
    color: var(--darkreader-neutral-background) !important;
}
.pt_storefront-homepage #main .homepage-slider-wrapper .homepage-custom-slide > a[href$="/deals"] .homepage-custom-slide__content .custom-slide-inner h3 {
    color: var(--darkreader-neutral-text) !important;
}

================================

strava.com

INVERT
.icon-dark
.labelGroup
#effort-box
.gear::after
.icon-lg
.icon-edit
.week.clearfix svg
#compare-graph svg
#compare-graph canvas
div[data-cy="challenge_results_container"] svg
nav svg
.nav-item a::after
.weekly-goal svg .sport-type
footer img[alt="Strava"]
.activity-indicator

CSS
.base-chart .grid-line,
#athlete-history-chart .vgrid {
  stroke: #555555;
}
#athlete-history-chart #effort-box {
  fill: #3e3e3e;
  stroke: black;
}
#basic-analysis .xaxis-container .background,
#basic-analysis rect.static-info-box,
#basic-analysis rect.static-label-box {
  fill: #2c2c2c;
}
.base-chart rect.simple-bar.segmentbar {
  opacity: 1;
}
.base-chart rect.simple-bar {
  fill: #444444;
  stroke: #252627;
}
.current-week-label { fill: black; }
.sum.no-rest { fill: black; }
.options img {
  filter: invert(40%);
}
#infoBox text {
  color: black;
}
.weekly-goal svg .sport-type {
    opacity: 0.5 !important;
}

IGNORE IMAGE ANALYSIS
.app-icon.icon-nav-training
.app-icon.icon-fb
.app-icon.icon-rowing
.app-icon.icon-nordicski

================================

streamable.com

INVERT
.landing .features-pane .feature > span > svg
.landing .logo
.landing .start-pane .start-form .try-arrow > img
#player.container .actions-section .logo
#player-play-pause-button
#player-progress-track
#player-progress-value

================================

studio.youtube.com

INVERT
paper-radio-button
.ytcp-home-button img

================================

subdivx.com

INVERT
#cabecera img
#contenedor_foro .cita img
#contenedor_foro .datos img[src*="/img/"]
#perfil_izq img[src*="img/"]

CSS
BODY * {
   color: ${#333};
}
A {
    color: ${#0366d6} !important;
}
input[type="text"],
input[type="password"],
input[type="button"],
input[type="submit"] {
    background: ${#bbb};
    border: 1px solid #999;
}
input[type="submit"]:hover {
    background: ${#999};
    border: 1px solid #999;
}
mark {
    background: ${#f00} !important;
    color: ${#fff} !important;
}
#barra a,
#menu_largo a,
#menu_largo_chat a,
#foro_tema_menu a,
#ultimos_foros_renglon .link_foro_tema,
.titulo_menu_izq {
    color: #ddd !important;
}
#wrapper {
    background-image: none !important;
}
#contenedor_gral {
    display: table;
    background: ${#e0e0e0} !important;
    box-shadow: rgba(0, 0, 0, 0.7) 0px 0px 8px;
    height: -webkit-fill-available !important;
    width: fit-content !important;
}
#cabecera {
    background: ${#e4e4e4};
}
#barra,
#menu_top,
#menu_detalle_buscador,
#menu_detalle,
#menu_largo,
#menu_largo_chat,
#prog_menu_detalle,
#primer_msg_voto,
#reg_menu_detalle,
#drdivx_menu_detalle,
#contenedor_foro .fecha,
#perfil_menu,
#foro_tema_menu {
    background: ${rgb(153,173,206)} !important;
    background: linear-gradient(to bottom, ${rgb(153,173,206)} 0%, ${rgb(75,110,171)} 100%) !important;
}
#foro_home,
#foro_home_renglon,
#cuadrados_izq,
#cuadrados_izq_reng,
#chat_reng,
#contenedor_foro,
#contenedor_foro .datos,
#contenedor_foro .cita,
#contenedor_foro .mensaje {
    background: ${#fff} !important;
}
#foro_home_datos,
#cuadrados_izq {
   color: ${#999} !important;
}
#foro_home_datos a {
   color: ${#669} !important;
}

================================

subscene.com

CSS
body {
  background-image: none !important;
}

================================

suckless.org

INVERT
[src="dwm.svg"]
[src="st.svg"]
[src="core.svg"]
[src="surf.svg"]
[src="blind.svg"]
[src="farbfeld.svg"]
[src="quark.svg"]
[src="sent-bullets-s.png"]
[src="slstatus.svg"]

================================

suite.smarttech-prod.com

INVERT
.smart-wbp-canvas-layer

================================

suno.com.br

INVERT
#logoSuno > a > svg

================================

support.discord.com

INVERT
.logo img
ol.breadcrumbs li:first-child:before

================================

support.eset.com

INVERT
i.table-icon.product

================================

support.mozilla.org

INVERT
.card--topic .card--icon
.card--icon-sm
div.sumo-nav--logo

================================

surveymonkey.com

CSS
.is-high-contrast .radio-button-display,
.is-high-contrast .checkbox-button-display  {
    opacity: 1 !important;
}
.modern-browser .radio-button-display::after, 
.modern-browser .checkbox-button-display::after {
    border-color: var(--darkreader-neutral-text) !important;
}

================================

sverigesradio.se

INVERT
#sprite-check
#sprite-facebook
#sprite-instagram
#sprite-reddit
#sprite-twitter
#sprite-whatsapp
.circle
.cross
.default
.episode-list-item__controls svg
.external-link-with-icon__icon
.gallery-button .icon
.gallery-button svg g
.info-teaser-container__title h2
.link-icon .icon
.local-weather-item__wind-icon
.logo
.menu-icon.icon
.play-icon__pause-symbol
.play-icon__play-symbol
.progress.queue-progress .bar
.sr-link__svg svg
.sr-logo-wrapper
.sound-bars .bar
.support-info__icon svg
[data-require="modules/custom-click-tracking"] svg
[data-require="modules/listen-later"] svg
[data-require="modules/share-button"] svg
button.reset
input[type="range"]::-moz-range-thumb
input[type="range"]::-moz-range-track
input[type="range"]::-webkit-slider-runnable-track
input[type="range"]::-webkit-slider-thumb

CSS
.weather-icon {
    filter: invert(1) hue-rotate(180deg) !important;
}
.live-marker .dot, 
.live-label::before,
.active::before {
    background-color: var(--darkreader-neutral-text) !important;
}
.search-page em, .search-result em, .info-teaser-container__title {
    color: var(--darkreader-neutral-background) !important;
}
#sprite-home path, #sprite-news path, #sprite-podcast path, #sprite-direct path, #sprite-profile path {
    fill: var(--darkreader-neutral-text);
}

================================

svt.se

INVERT
header img[src*=svt]
.nyh_navigation__menu-toggle
[class^="VideoPlayerTheme__play-pause-button-simple"]::before
[class^="_play-pause-button-simple"]::before

CSS
.nyh_breaking__top-prefix,
.nyh_teaser__live-text {
    color: var(--darkreader-neutral-background) !important;
}
button[class^="PaginationButton"],
a[class^="PaginationButton"],
.nyh_screamer,
[class^="_RightNowTeaser__title"],
[class^="_RightNowTeaser__root"] {
    background: var(--darkreader-bg--nyh-color-white) !important;
    border-left-color: var(--darkreader-bg--nyh-color-news);
}
button[class^="PaginationButton"]:hover,
a[class^="PaginationButton"]:hover {
    background: var(--darkreader-border--color-play-white) !important;
}
.flexbox .nyh_teaser.nyh_teaser--group-secondary {
    border-right: none !important;
}
[class*="GroupSecondaryTeasers__container___"],
.nyh_feedbox-item+.nyh_feedbox-item,
.nyh_teaser.nyh_teaser--no-border-top {
    border-top: none !important;
}
.nyh_navigation .nyh_submenu::before {
    border-bottom-color: var(--darkreader-bg--nyh-color-white) !important;
}
.nyh_teaser.nyh_teaser--story-grid,
.nyh_icons_caret--factbox {
    border: none;
}
.nyh_submenu-menucard,
[class*="_Post__contentVisitor___"]::after {
    border-right-color: var(--darkreader-bg--nyh-color-grey-lighter) !important;
}
[class*="CurrentTopics__item___"],
[class*="GroupSecondaryTeasers__showMoreButton___"],
.nyh_mobile-menu__list-item,
.nyh_menu-card__submenu-item,
.nyh_submenu-menucard,
.nyh_submenu {
    border-color: var(--darkreader-border--nyh-color-grey-lighter) !important;
}
.nyh_fact-box {
    border-bottom: 1px solid var(--darkreader-border--nyh-color-grey-lighter);
    border-top: 1px solid var(--darkreader-border--nyh-color-grey-lighter);
}
.nyh_fact-box__body--closed::after {
    background: linear-gradient(to bottom, #1e202180 0%, #1e2021 100%);
}
.nyh_regional-widget-regions {
    background: var(--darkreader-bg--nyh-color-grey-lightest);
}
.nyh_submenu-menucard {
    background-color: var(--darkreader-bg--nyh-color-white);
}

================================

swiatrolnika.info

INVERT
img[alt*="logo"]
img[src*="/images/images2/icons/"]

================================

t.bilibili.com

CSS
#app {
    background-image: none !important;
}

================================

t.me
telegram.me

INVERT
.tgme_logo

================================

tableau.com

INVERT
.feature-list__icon
.mobile-nav-button__icon
.site-logo

CSS
section {
    background-image: none !important; 
}

================================

tablesgenerator.com

INVERT
.icon-all-borders.toolbar-icon
.icon-no-borders.toolbar-icon
.icon-edit-borders.toolbar-icon
.icon-merge-cells.toolbar-icon
.icon-split-cells.toolbar-icon
.icon-no-colors.toolbar-icon

IGNORE INLINE STYLE
.sp-thumb-inner
.sp-preview-inner

================================

tails.boum.org

CSS
body {
    background-image: none !important; 
}

================================

tailwindcss.com

CSS
.bg-white:not(#docsearch), .bg-gray-100 {
    --bg-opacity: none !important;
}
.text-gray-900, .hover\:text-gray-900:hover,
h1, h2, h3,
h4, h5, h6 {
    --text-opacity: none !important;
}
.bg-center {
    background: none !important;
}

================================

take-a-screenshot.org

INVERT
.switch-window span::before

================================

taobao.com

INVERT
#J_SearchIcon
.J_MyShopCoupon
.J_BtnEditSKU
.cart-checkbox

CSS
.tbh-member.J_Module {
    background-image: none !important;
}
#q.search-combobox-input {
    background-color: var(--darkreader-neutral-background);
}

================================

tarnogorski.info

INVERT
.td-main-logo

================================

tcrf.net

CSS
body {
    background-image: none !important;
}

================================

teamtrees.org

CSS
.hypeTemplate_tano {
    background-color: ${white} !important;
}

================================

techmaniak.pl
activemaniak.pl
agdmaniak.pl
appmaniak.pl
blogomaniak.pl
fotomaniak.pl
gizmaniak.pl
gsmmaniak.pl
luxmaniak.pl
mobimaniak.pl
rtvmaniak.pl
tabletmaniak.pl

CSS
body,
#headerNavBlogomaniak,
#header,
#top,
#bottom,
#footer,
#footer_menu,
.wrapper,
#search input.search {
    background-color: var(--darkreader-neutral-background) !important;
    background-image: none !important;
}

================================

teleman.pl

INVERT
.movieRank

================================

telerik.com

INVERT
a.TK-Aside-Menu-Link
a.TK-Aside-Menu-Button
#ContentPlaceholder1_C377_Col00 > img

CSS
a.TK-TLRK-Logo svg path[fill="#7c878e"] {
    --darkreader-inline-fill: ${black} !important;
}
a.TK-TLRK-Logo svg path[fill="#4b4e52"] {
    --darkreader-inline-fill: ${black} !important;
}
a.TK-PRGS-Logo-Footer svg path[fill="#4b4e52"] {
    --darkreader-inline-fill: ${black} !important;
}
#ContentPlaceholder1_C418_Col00 > footer {
    background-image: none !important;
}

================================

teltarif.de

CSS
body,
.ttColBack,
#tthdrbox {
    background: var(--darkreader-neutral-background);
    color: var(--darkreader-neutral-text);
}

================================

tenforums.com

CSS
.garhead {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
    border-left-color: var(--darkreader-neutral-background) !important;
    border-right-color: var(--darkreader-neutral-background) !important;
}

================================

tenor.com

INVERT
.FlagIcon
.ShareIcon

================================

terraform.io

IGNORE INLINE STYLE
.text

================================

tesco.com

CSS
.ui-components-library .nav-dropdown .dropdown a.nav-toggle h1,
.ui-components-library .nav-dropdown .dropdown a.nav-toggle h2,
.ui-components-library .nav-dropdown .dropdown a.nav-toggle h3,
.ui-components-library .nav-dropdown .dropdown a.nav-toggle h4,
.ui-components-library .nav-dropdown .dropdown a.nav-toggle h5,
.ui-components-library .nav-dropdown .dropdown a.nav-toggle h6 {
    z-index: 0 !important;
}

================================

tesla.com

INVERT
.tds-modal-close-icon
.tds-menu-header-legacy [id*="tds-menu-header-main--trigger"]:checked ~ [for*="tds-menu-header-main--trigger"] .tds-menu-header-main--cross_hatch

CSS
.tcl-hero-parallax__heading,
.tcl-hero-parallax__subheading a,
.tcl-hero-parallax__additional-link {
    color: ${gray} !important;
}
#block-mainheadernavigation .tds-menu-header-nav--list_link {
    color: var(--darkreader-neutral-background) !important;
}

================================

testudo.umd.edu

CSS
#secondary-side,
#widgetbox_widget_parent_0 {
    background-color: var(--darkreader-neutral-background) !important;       
}

================================

the-race.com

INVERT
.img-fluid

================================

theatlantic.com

INVERT
.c-nav__icon--lacroix
.hamburger-inner

================================

theatlas.com

CSS
.d4 .axis .tick text {
    fill: var(--darkreader-neutral-text) !important;
}
.d4 .axis .tick rect {
    fill: var(--darkreader-neutral-background) !important;
}

================================

thecamels.org

INVERT
.block-tags
.block-client-group-list
.slider-content-list-item-opinion-logo
.slider-testimonials-list-item

CSS
.slider-testimonials-content {
    background-color: var(--darkreader-neutral-background) !important;
    background-image: none !important;
}

================================

thecanadianencyclopedia.ca

INVERT
.edit
.citation
.share
.print
.dot-1.dot
.dot-2.dot
.dot-3.dot
.accordion-toggler.dropdown-toggler.nav-submenu-toggler

================================

thecode.media

INVERT
img[src$="/logo.svg"]
.post-item__arrow
li::before

================================

thedailybeast.com

INVERT
.Logo
.NavSubDesktop__hamburger-bars

================================

theguardian.com

INVERT
.inline-the-guardian-logo__svg
a[data-link-name$="logo"] > svg

================================

theinformation.com

INVERT
.logo

IGNORE IMAGE ANALYSIS
header.locked .logo

================================

theins.ru

INVERT
header > div > a > svg

================================

thejakartapost.com

INVERT
.logo-jakartapost

================================

thelancet.com

INVERT
.journal-logos
.footer__logo

================================

themoviedb.org

INVERT
.glyphicons_v2.link
.glyphicons_v2.keyboard
.glyphicons_v2.speech-bubble-alert
.glyphicons_v2.arrow-thin-right
.glyphicons_v2.arrow-thin-left

CSS
.card {
    background-color: ${#dfe1e2} !important;
}

================================

theoatmeal.com

INVERT
img.d-inline-block.align-bottom

================================

theonion.com

INVERT
a[href="//www.theonion.com"] .theonion

================================

thepaper.cn

INVERT
.head_logo

================================

thereader.mitpress.mit.edu

INVERT
#logo

================================

theregister.*
theregister.*.*

INVERT
.row_label.title_rhs_line

================================

thesaurus.com

INVERT
#__next > div header svg

================================

theverge.com

INVERT
.c-global-header__logo
.c-tab-bar__logo
.c-footer__logo-link

================================

thewindowsclub.com

INVERT
.custom-logo

================================

thompsonstein.com

INVERT
a.logo

================================

thronemaster.net

INVERT
.head-info-wild

================================

thunderbird.net

INVERT
.w-48
a[title="Thunderbird"] > svg

CSS
body {
    background-image: none !important;
}

================================

tianocore.org

INVERT
.tcLogoArea

================================

tieba.baidu.com

INVERT
.add-more-forum
.all-wraper
.core_title_btns
.day_rcmd > .class_title
.fengchao-wrap-box span
.focus_btn
img[src*="sign_err.png"]
.save_face_bg
.search_bright .nav_wrap_add_border
.share_btn_wrapper
.sub_nav_wrap
.threadlist_rep_num
.u-f-item

CSS
.card_banner {
    filter: brightness(50%);
}
.class_title,
.content-sec,
.f-d-item,
.left-sec,
.page-container,
.pb_footer,
.sub_nav_wrap,
.tb_footer {
    background-image: none !important;
}
.u-f-item {
    color: var(--darkreader-neutral-background) !important;
}

================================

time.com

INVERT
nav.main .menu-btn .menu-btn-box

================================

tinder.com

CSS
svg > path[style*="--rewind"] {
  fill: ${black} !important;
}
svg > path[style*="--nope"] {
  fill: ${red} !important;
}
svg > path[style*="--super-like"] {
  fill: ${cyan} !important;
}
svg > path[style*="--like"] {
  fill: ${#22ebc0} !important;
}
svg > path[style*="--boost"] {
  fill: ${purple} !important;
}

================================

tjournal.ru

INVERT
mark

CSS
mark a {
  color: ${#346eb8} !important;
}
.main.layout {
    background: var(--darkreader-neutral-background) !important;
}

================================

tns-e.ru

INVERT
img[title*="ТНС энерго"]
img[src="/img/refresh.png"]
img[src="/img/logout-black.png"]

================================

todoist.com

INVERT
.ist_button_apple > img
#td-help-logo_svg__a ~ g:last-of-type

CSS
main > section:first-of-type > div {
    z-index: 1 !important;
}
img[src^="/_next/static/images/"],
[style*="/_next/static/images/"]  {
    filter: brightness(50%) sepia(40%) !important;
}
option,
.oT9NU,
footer {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

tokfm.pl

INVERT
.play

CSS
div.top_section_bg, 
div.bottom_section_bg {
    background-color: ${#e7e5e4} !important;
}
body.desk #content {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

tonsky.me

CSS
body {
    background-image: none !important;
    background-color: var(--darkreader-neutral-background) !important;
}

================================

tosdr.org

INVERT
img[src*="tosdr-logo-128.svg"]
img[src*="/img/news/"]
img[src*="guidelines.svg"]

================================

track.toggl.com

CSS
#root,
.content-wrapper > * {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

trailhead.salesforce.com

INVERT
lwc-tds-header

CSS
.search-input__input {
    background-color: var(--darkreader-neutral-background) !important;
}
.user-info__name {
    color: inherit !important;                                                                              
}
.progress-ring__content {
    background-color: white !important;
}
[data-layout="desktop"] .nav-list-item__link:hover,
[data-layout="desktop"] .nav-list-item__button[aria-expanded="true"] {
    background: inherit !important;
}

================================

translate.google.*
translate.google.*.*

INVERT
.ttsbutton
.tlid-copy-translation-button
.starbutton
.speech-button
.clear
.swap > .jfk-button-img
.morebutton
.close-button
.ita-kd-icon-button
.ita-kd-menuitem-inputtool-icon
.ita-kd-checkbox
.vk-t-btn
.vk-sf-b
.ita-hwt-backspace-img
.ita-hwt-enter-img
.ita-hwt-grip
.gt-ex-quote

CSS
.trans-verified-button {
    background-size: cover;
}

IGNORE IMAGE ANALYSIS
.ita-kd-img

================================

translate.yandex.*

INVERT
.button.button_tab.state-selected::before
#optionsButton::after
#clearButton::after
#textNativeSpeaker
#textSpeaker
#microphone::after
#keyboardButton::after
#spellerButton::after
.button.button_icon.button_icon_swap::after
#favButton::after
#translatorSpeaker
#translatorNativeSpeaker
#copyButton::after
#shareButton::after
#goodVoteButton::after
#badVoteButton::after
#editorButton::after
.toggler::after
.button.button_icon.button_icon_speaker
.dictionary-example_toggler::after
.dictionary-example_meta__vote-buttons
.icon.button.button_icon.button_icon_sync::after
.message.message_yellow.message_replace
.placeholder
.button.header-button.button_icon.button_icon_clear2::after
#shareCopyLink::after

================================

translate.yandex.*/collections

INVERT
.button.header-button.button_icon.button_icon_clear2::after
.record-line_speaker.button.button_icon.button_icon_speaker::after
.button.button_icon.button_icon_lines::after
.button.button_icon.button_icon_trash::after
#shareCollectionCopy::after

================================

translate.yandex.*/doc

INVERT
.button.button_tab.state-selected::before
.listbox-option::after

================================

translate.yandex.*/ocr

INVERT
.button.button_tab.state-selected::before
.button.button_icon.button_icon_swap::after
.button.button_icon.button_icon_plus::after
.button.button_icon.button_icon_minus::after
.button.button_icon.button_icon_lines::after
.button.button_icon.button_icon_words::after
.button button_icon.button_icon_blocks::after
.button.button_icon.button_icon_blocks::after
.button.button_icon.button_icon_reset::after

================================

translate.yandex.*/translate

INVERT
.button.button_tab.state-selected::before
#shareButton::after
.listbox-option::after
#shareCopyLink::after

================================

trezor.io

INVERT
.hero-image

================================

tribunemag.co.uk

INVERT
.si-hr-nv__toggle--menu
.si-hr-sm__item

================================

trip101.com

INVERT
.logo
.name
.partner-logo
img.partner-img

================================

trojmiasto.pl

INVERT
img[alt*="Trojmiasto.pl"]

================================

truity.com

INVERT
.navbar-btn.logo
.footer-logo.logo

================================

tug.org/FontCatalogue

INVERT
img

================================

tutorialspoint.com

INVERT
i.fa
i.fab
i.fal
img.tp-logo
button.slick-prev.slick-arrow::before
button.slick-next.slick-arrow::before

================================

tv.yandex.*

INVERT
.icon_location::after
.icon_location::before
.button_arrow_down::after

IGNORE IMAGE ANALYSIS
.icon_location::after
.icon_location::before
.button_arrow_down::after

================================

tvrain.ru

INVERT
.menu3__logo

================================

tweakers.net

INVERT
.infoBox > .title:before
#userbar li.icon.selected > a

CSS
#logo a {
    background: url("https://tweakers.net/g/if/v3/framework/tweakers_logo_full.svg") no-repeat center !important;;
}
@media screen and (max-width: 1000px) {
    #logo a {                                                     
        background: url("https://tweakers.net/g/if/v3/framework/menu_icons_responsive_v6.png") no-repeat -8px -220px !important;
    }
}
.ankeiler > a.commentCount {
    background-image: url("https://tweakers.net/g/icons/commentcounter_small_bg.svg") !important;
    text-decoration: none !important;
}
#userbar li.icon a {
    background-image: url("https://tweakers.net/g/if/v3/framework/menu_icons_v2.png") !important;
}
#userbar li.icon.flag.nl::after {
    border-top-color: #e7184c !important;
    background-color: #fff !important;
    border-bottom-color: #014a93 !important;
}
#userbar li.icon.flag.be::after {
    border-left-color: #000 !important;
    background-color: #ffff1a !important;
    border-right-color: #e7184c !important;
}
.thumb.category {
    border: none !important;
}
.ctaButton.play::after {
    background-color: #FFF !important;
}
.relatedContentContainer .relatedContentItems .itemContainer {
    border-top-color: ${#d9d9d9} !important;
    border-bottom-color: ${#d9d9d9} !important;
}
#categoryBrowser li a {
    background-color: initial !important;
    background-image: none !important;
}
#categoryBrowser li.more {
    background-image: url("https://tweakers.net/g/if/categories/arrows.png") !important;
}
#categoryBrowser li.more:not(li.active.more) {
    background-position-y: -17px !important;
}
#categoryBrowser li.more.active {
    background-image: url("https://tweakers.net/g/if/categories/arrows.png"), linear-gradient(rgb(131, 24, 46), rgb(148, 15, 49)) !important;
}
#tracker .fakeTop .toggleVisibility .corner::before,
#tracker .fakeTop .toggleVisibility .corner::after {
    border-color: ${#ccc} !important;
}
@media screen and (max-width: 767px) {
    #userbar li.icon a {                                                     
        background-image: url("https://tweakers.net/g/if/v3/framework/menu_icons_responsive_v6.png") !important;
    }
    #categoryBrowser li.more.active {
        background-image: none !important;
        box-shadow: none !important;
    }
}

IGNORE IMAGE ANALYSIS
#categoryBrowser li a
table.highlights .title a.showMoreItems

================================

twitch.tv

INVERT
.volume-slider__slider-container
.seekbar-bar

CSS
.seekbar-segment {
    background-color: rgb(169, 112, 255) !important;
}

================================

twitter.com

INVERT
[role="slider"]
[style^="flex-grow"]

CSS
main,
header {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

typescriptlang.org

CSS
.dark-theme .markdown pre {
    filter: none !important;
}
:root {
    --darkreader-bg--raised-background-color: #1c1f21 !important;
}

================================

ubuntu.com

INVERT
.global-nav__header-logo-anchor

CSS
header[style$="image-background-paper.png);"] {
    background-image: none !important;
}

IGNORE INLINE STYLE
g#ubuntu-logo > path

================================

udemy.com

CSS
.radio:not(.radio-button) input[type="radio"]:checked + .radio-label::before,
.radio-inline:not(.radio-button) input[type="radio"]:checked + .radio-label::before {
    background-color: var(--darkreader-neutral-text) !important;
}

IGNORE INLINE STYLE
[data-purpose="star-rating-mask"] > rect

================================

ultimate-guitar.com

INVERT
form[action$="/search.php"] > button
form[action$="/search.php"] > button:not(:hover) > span
canvas[style^="height: 72px; width: 84px;"]
canvas[style^="height: 42px; width: 141px;"]

================================

un.org

INVERT
a.logo[title="United Nations"]

================================

uol.com.br

CSS
.text a {
    text-decoration: underline;
}

================================

ups.com

CSS
#ups-alertsWrap,
#ups-skipNav {
    background: ${#FE9} !important;
}

================================

uptimerobot.com

INVERT
.normal-logo
.socialWrapper > ul > li
.feature-icon
[src$="godaddy-logo.svg"]
[src$="moodys-logo.svg"]
[src$="ibm-logo.svg"]
[src$="expedia-logo.svg"]
[src$="creditcards.svg"]

CSS
.page-head,
.sidebar {
    background-image: none !important;
}

================================

urbandecay.com

IGNORE INLINE STYLE
.c-select__icon
.c-swatch

================================

urbandictionary.com

INVERT
.top-bar-section .has-dropdown > a::after

CSS
.ribbon {
    color: ${#333} !important;
}

================================

userstyles.world

INVERT
nav .menu-icon .i

================================

uspassporthelpguide.com

CSS
body {
    background: none !important;
}

================================

usps.com

INVERT
img[src$="hamburger.svg"]
img[src$="search.svg"]
ul.nav-list > li.menuheader::after
ul.nav-list > li.qt-nav > div > ul > li > a > img

================================

uteka.ru

INVERT
img[src="/static/img/logo.svg"]
ymaps[class$="ground-pane"]

================================

v.qq.com

INVERT
.page_user_center .link_logo

================================

vaccines.gov
vacunas.gov

INVERT
.accIcon
.Button
img[src$="/images/bch-logo.svg"]
img[src$="/images/en-US/wcdt.svg"]
img[src$="/images/es-US/wcdt.svg"]
img[src$="/images/hhs-logo.svg"]

================================

vaccines.procon.org

INVERT
#right-sidebar > aside.widget_media_image > a > img

================================

valgrind.org

CSS
p {
   color: var(--darkreader-neutral-text) !important;
}

================================

vandale.nl

INVERT
#content-top.form

================================

vanguard.com

CSS
.hidePageIfJSdisabled {
    display: block !important;
}

================================

vbulletin.com

CSS
.std {
    background-color: var(--darkreader-neutral-background) !important;
}
.hero-text,
#hometext li,
#hometext h4,
#hometext_buttons div p {
    color: var(--darkreader-neutral-background) !important;
}

================================

vc.ru

INVERT
mark

CSS
.main.layout,
#page_wrapper {
    background: var(--darkreader-neutral-background) !important;
}

================================

vechevoikolokol.ru

INVERT
.map
.main-page-about .img-responsive
.header-menu__mobile .img-responsive

CSS
.main-page-about, .main-suggestions, .i-about, .issues-page__content{
    background: none !important;
}

================================

ventusky.com

CSS
.qo {
    color: ${white} !important;
}

================================

vercel.com

INVERT
[alt="optimized-frameworks"]
[class^="features_logo"]

CSS
:root {
    --geist-foreground: var(--darkreader-neutral-text) !important;
}
div[style="background:var(--accents-1);"] {
    background-color: var(--darkreader-neutral-background) !important;
}
.geist-text-center {
    color: ${#333} !important;
}

================================

vfsglobal.cn

INVERT
.covid-icon
.logo

================================

vg24.pl

INVERT
img[alt="VG24.PL logo"]

================================

vice.com

INVERT
img[src*="article-logo"]
div.nav-bar__hamburger-button__hamburger
a.page-footer__logo-vice-link

IGNORE INLINE STYLE
.logo-vice *

================================

videolan.org

CSS
ul {
    color: var(--darkreader-neutral-text) !important;
}

================================

videoman.gr

INVERT
#logo-custom span
.logo-single

================================

virtualbox.org

CSS
body {
    background-image: none !important;
}

================================

virustotal.com

CSS
:root {
    --darkreader-bg--vt-grey-50: ${#eee} !important;
    --darkreader-bg--vt-grey-700: ${#666} !important;
    --vt-white: var(--darkreader-neutral-background) !important;
}
:host(.filled) .chip {
    --vt-ui-chips-color: var(--darkreader-bg--vt-grey-200) !important;
}
.range-wrapper svg {
    --vt-grey-200: var(--darkreader-bg--vt-grey-200) !important;
}
vt-ui-main-footer {
    --vt-grey-25: var(--darkreader-bg--vt-grey-200) !important;
    --darkreader-bg--vt-grey-700: ${#a0a6b4} !important;
}
.logo {
    z-index: 1 !important;
}
:host > a:not(.item),
:host > span {
    border: var(--vt-ui-button-border, 1px solid black) !important;
    color: var(--vt-ui-button-color-text, var(--darkreader-text--vt-grey-800, #e8e6e3)) !important;
}
:host(vt-ui-text-input) {
    border: var(--vt-ui-text-input-border, 1px solid var(--darkreader-border--vt-grey-200)) !important;
}
.detections[clean],
.detections[clean] svg {
    fill: var(--vt-green-500) !important;
    color: var(--vt-green-500) !important;
}
.detections {
    color: var(--vt-red-500) !important;
}
#detections .detection,
#detections {
    --darkreader-border--vt-grey-100: var(--darkreader-border--vt-grey-200) !important;
}
.avatar-section {
    background: var(--darkreader-border--vt-grey-200) !important;
}

IGNORE INLINE STYLE
.circle-progressbar circle

================================

vitaexpress.ru

INVERT
ymaps[class$="ground-pane"]
ymaps[class$="default-cluster"] > ymaps

================================

vivaldi.com

INVERT
.hero-cover
.hero-cover p

================================

vk.com

INVERT
.HeaderNav__item--logo

================================

vod.tvp.pl

INVERT
img[alt="serwisy tvp"]

================================

vox.com

INVERT
.c-app-nav__logo
.c-footer__logo-link

================================

vrt.be/vrtnws

INVERT
.vrt-header__logo
.vrt-nav-toggle-btn

================================

vshojo.com

CSS
.elementor-section,
.elementor-heading-title {
    background: var(--darkreader-neutral-background) !important;
    color: var(--darkreader-neutral-text) !important;
}

================================

vtimes.io

INVERT
.site-header__logo
.sticky-nav__logo
.article__block__disclaimer__close::after
.article__block__disclaimer__close::before

================================

w.atwiki.jp

CSS
.atwiki-contents-shadow, 
.main_wrapper {
    background-color: var(--darkreader-neutral-background) !important;
}
.atwiki-jp-bg2 {
    color: var(--darkreader-neutral-background) !important;
}

================================

w3.org

INVERT
img[alt*="equation" i]

================================

wakamaifondue.com

CSS
.upload-button > strong, .upload-button > span {
    color: ${white} !important;
}

================================

wallet.myalgo.com

IGNORE INLINE STYLE
.export-qr-code > *

================================

warframe.com

CSS
.wrapper {
    background-image: none !important;
}

================================

washingtonpost.com

INVERT
.masthead_svg__wplogo

IGNORE INLINE STYLE
a[data-sc-c="headerlogo"] path

================================

waze.com

INVERT
.wm-map__leaflet
.leaflet-bottom
.leaflet-popup-content-wrapper

================================

weather.com

INVERT
div[class*="DynamicMap"]
svg[name="arrow-up"]
svg[name="arrow-down"]
text[class*="DonutChart"]

IGNORE INLINE STYLE
[id^="svg-symbol"] *

================================

weather.com.cn

INVERT
.w_logo
#hourHolder

CSS
.lv1,
.lv2,
.weatherBg01,
.weatherBgAll,
.weatherBgAll01,
.weatherBgAll02,
.weatherBgAll03 {
    background-image: none !important;
}

================================

web.archive.org

INVERT
.sparkline-canvas
.sparkline-mouse-highlight
.search-toolbar-logo

CSS
.measure {
    z-index: 0 !important;
}

IGNORE INLINE STYLE
.ia-wordmark

IGNORE IMAGE ANALYSIS
.search-toolbar-logo

================================

web.dev

CSS
.w-aside--gotchas,
.w-aside--key-term {
    color: #b98fff;
}
.w-aside--gotchas::after,
.w-aside--key-term::after {
    background-color: #b98fff;
}

================================

web.microsoftstream.com

INVERT
.vjs-progress-holder
.vjs-volume-bar

================================

web.telegram.org

INVERT
.composer_emoji_tooltip_category
.nano-slider
.divider

CSS
.progress-bar {
    background-color: rgba(255,255,255,.9) !important;
}

================================

webaim.org

CSS
body {
    background-image: none !important
}

================================

webbrowsertools.com

CSS
.tile {
    background-color: var(--darkreader-neutral-background);
}

================================

wego.here.com

INVERT
#map
.route_card_left
.route_tooltip_icon
.btn_directions

================================

wenxuecity.com

INVERT
.logo

================================

wepe.com.cn

INVERT
#u0_img

================================

wesbos.com

CSS
footer > div > div:not(.bottom) {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

what-if.xkcd.com

INVERT
.illustration
.archive-image
.logo

================================

whatsapp.com

INVERT
span[data-icon="audio-download"]
.landing-main .invisible-space > span
.landing-main div[data-ref] span > svg > path
div.landing-header > span > svg
#wafaq_search_input

CSS
[data-asset-intro-image], [data-asset-intro-image-light] {
    background-image: url(/img/intro-connection_c98cc75f2aa905314d74375a975d2cf2.jpg) !important;
}
html[dir] .landing-main > :first-child > :nth-child(2) > :first-child {
    border: 5px solid white !important;
}
span[data-icon="tail-in"] {
    color: rgb(30, 36, 39) !important;
}
span[data-icon="tail-out"] {
    color: rgb(4, 57, 51) !important;
}
div#subheader {
    background-image: url(https://static.whatsapp.net/rsrc.php/v1/yQ/r/dPFl9fRFF9u.jpg) !important;
    background-size: 100% !important;
}

IGNORE INLINE STYLE
path[fill="currentColor"]

================================

who.int

INVERT
.background-url-holder > picture > img

================================

whois.com

INVERT
img[src^="logo.gif"]

================================

wielkopolskiebilety.pl

INVERT
.KW__headerContainer
.logo
.map-container
img[src*="carriers/"]

================================

wiemy.to

INVERT
img.site-logo
.content-font__icon
.font-dropdown__item

================================

wiesci24.pl

INVERT
.tdb-logo-img

================================

wiki.facepunch.com

CSS
body {
    background-color: transparent !important;
}
.body {
    background-image: none !important;
}

================================

wiki.mozilla.org

CSS
body {
    background-image: none !important;
}

================================

wiki.teamfortress.com

INVERT
.mw-wiki-logo

CSS
body,
#mw-page-base,
#p-logo,
#content,
#footer {
    background-image: none !important;
}
div.vectorTabs ul li.selected a,
#searchInput::placeholder {
    color: var(--darkreader-neutral-text) !important;
}

================================

wiki.unity3d.com

CSS
div#content,
#pt-login,
div.vectorTabs,
div.vectorTabs *,
.portal *,
div.vectorMenu,
div.vectorMenu h5 a,
div#footer,
div#footer * {
    background-image: none !important;
}

================================

wikibooks.org

INVERT
.bookend
.mwe-math-fallback-image-inline
.mwe-math-fallback-image-display

IGNORE INLINE STYLE
.infobox td

================================

wikidata.org

INVERT
.wd-mp-headerimage

IGNORE IMAGE ANALYSIS
.mw-wiki-logo

================================

wikiless.org

INVERT
.mw-wiki-logo

================================

wikimapia.org

INVERT
#map

================================

wikimedia.de

INVERT
.logo

================================

wikimedia.org

INVERT
img.graphite-graph
img[src="images/black.png"]

CSS
div.mw-warning-with-logexcerpt,
div.mw-lag-warn-high,
div.mw-cascadeprotectedwarning,
div#mw-protect-cascadeon,
div.titleblacklist-warning,
div.locked-warning {
    background-color: ${#ffa9a9} !important;
    border-color: ${#ffa1a1} !important;
}

================================

wikinews.org
wikiquote.org

INVERT
.bookend

================================

wikipedia.org

INVERT
.mwe-math-fallback-image-inline
.mwe-math-fallback-image-display
.MathJax:not(span#MathJax_Zoom > .MathJax)
span#MathJax_Zoom
.mw-ext-score
.mw-wiki-logo
.central-textlogo__image
.svg-Wikimedia-logo_black
header .branding-box > a > span > img
.main-footer-menuToggle
div.post-content.footer-content > h2 > img
.mw-hiero-outer.mw-hiero-table
#p-logo-text
body > .oo-ui-windowManager .vega .marks

CSS
.mwe-popups-discreet > svg,
div .thumbimage[src$=".png"],
div .thumbimage img[src$=".png"] {
    background-color: white;
}
.mw-mmv-image .svg,
.fullImageLink [src*=".svg"],
a[href$=".svg"]:hover > img,
a[href*=".gif"]:hover > img {
    background-color: rgba(255, 255, 255, 0.75) !important;
    background-blend-mode: color;
}
.diff-addedline .diffchange {
    background-color: ${lightblue} !important;
}
.diff-deletedline .diffchange {
    background-color: ${#feeec8} !important;
}
@keyframes unseen-fadeout-to-unread {
    from {
        background-color: ${#dce8ff} !important;
    }
    to {
        background-color: ${#ffffff} !important;
    }
}
@keyframes unseen-fadeout-to-read {
    from {
        background-color: ${#dce8ff} !important;
    }
    to {
        background-color: ${#eaecf0} !important;
    }
}
.main-top {
    background: none !important;
}
ol.references li:target,
sup.reference:target {
    background-color: ${lightblue} !important;
}
.control-bar {
    background-color: var(--darkreader-neutral-background) !important;
    background-image: none !important;
}
.k-player,
body.mediawiki,
#dialogEngineContainer #dialogEngineDialog {
    background-color: var(--darkreader-neutral-background) !important;
}

IGNORE INLINE STYLE
.legend-color
.infobox > tbody > tr > td[style*="background-color"]
#on_image_elements span

IGNORE IMAGE ANALYSIS
.k-player .k-menu-bar li a

================================

wikisource.org
wikiversity.org

INVERT
.mwe-math-fallback-image-inline
.mwe-math-fallback-image-display

================================

wikitech.wikimedia.org

IGNORE IMAGE ANALYSIS
.mw.wiki-logo

================================

wikiwand.com

INVERT
img.mwe-math-fallback-image-inline
img.mwe-math-fallback-image-display
img.immediate:not(.ntmb)
.title_icon
img.logo_img
td.icon
li.menu-tooltip:not(.lang_btn)
#main_menu > li:not(.lang_btn) > img.logo_img ~ i
.article_btn .drop_down li a > i
.feedback_btn .drop_down li a > i

================================

wiktionary.org

INVERT
.bookend
.central-featured-logo-text

CSS
div.NavFrame div.NavHead {
    background-image: none !important;
}

================================

wildberries.ru

INVERT
ymaps[class$="ground-pane"]
ymaps[class$="svg-icon-content"] > ymaps

================================

windows.php.net

CSS
#content-columns .block,
#page-area .content {
    background-image: none !important;
}
#content-columns .block .corners-top, 
#content-columns .block .corners-bottom, 
#page-area .content .corners-top,
#page-area .content .corners-bottom {
   filter: invert(91%) !important;
}

IGNORE IMAGE ANALYSIS
#content-columns .block .corners-top
#content-columns .block .corners-bottom
#page-area .content .corners-top
#page-area .content .corners-bottom
#content-columns .block .corners-top span
#content-columns .block .corners-bottom span
#page-area .content .corners-top span
#page-area .content .corners-bottom span
#main-column .innerbox .corners-top
#main-column .innerbox .corners-bottom
#mid-column .innerbox .corners-top
#mid-column .innerbox .corners-bottom
#main-column .innerbox .corners-top span
#main-column .innerbox .corners-bottom span
#mid-column .innerbox .corners-top span
#mid-column .innerbox .corners-bottom span

================================

windscribe.com

CSS
#body_wrap > div.blue-bg.blue-top > div.twinkling-blue {
    background-image: none;
}

================================

wired.co.uk
wired.com

INVERT
body a svg
.c-nav__open-icon
.c-nav__close-icon
.standard-navigation__logo-image > img

================================

wireshark.org

INVERT
.navbar-brand

================================

wmar2news.com

INVERT
.PageLogo-image
.wx-nav > img

================================

word-view.officeapps.live.com

INVERT
img.WACPageImg

================================

wordnik.com

INVERT
img[alt="wordnik logo"]
.footer-logo

================================

wordpress.com

CSS
.p2020-sidebar {
    background-image: none !important;
    background: var(--darkreader-neutral-background);
}
.block-editor-rich-text__editable {
    color: var(--darkreader-neutral-text);
}
.block-editor-block-list__layout .block-editor-block-list__block::after {
    box-shadow: none !important;
}

================================

worldcubeassociation.org

CSS
.event-checkbox input[type="checkbox"] + i.cubing-icon, 
.event-checkbox input[type="radio"] + i.cubing-icon, 
.event-radio input[type="checkbox"] + i.cubing-icon, 
.event-radio input[type="radio"] + i.cubing-icon {
    color: rgba(0, 0, 0, 1) !important;
}

IGNORE IMAGE ANALYSIS
.flag-icon-pl
.flag-icon-kr
.flag-icon-fi
.flag-icon-jp
.flag-icon-ru
.flag-icon-si
.flag-icon-co
.flag-icon-lt
.flag-icon-sk
.flag-icon-bg
.flag-icon-ve
.flag-icon-ge
.flag-icon-ec
.flag-icon-pk
.flag-icon-uy
.flag-icon-mg
.flag-icon-pa
.flag-icon-cy
.flag-icon-bh
.flag-icon-mt
.flag-icon-bt
.flag-icon-jm

================================

worldometers.info

INVERT
#coronavirus-cases-log.active .highcharts-background
#coronavirus-deaths-log.active .highcharts-background
#coronavirus-cases-linear.active .highcharts-background
#coronavirus-deaths-linear.active .highcharts-background

================================

worldtimebuddy.com

CSS
body,
.clientarea > :first-child {
    background-image: none !important;
}

================================

wowturkey.com

CSS
body {
    background: none !important;
}
.cevapButton {
    background-image: none !important;
}
span.postdetails span.name a {
    background: transparent !important;
}

IGNORE INLINE STYLE
.cevapButton
span.name a

================================

wpshout.com

INVERT
a[rel="home"] > img

CSS
.site-title {
    background-color: var(--darkreader-neutral-background) !important;
}

================================

writefreely.org

INVERT
img[src*="writefreely.svg"]
img[src*="icon.svg"]
img[src*="i.snap.as/"]
span

================================

wuffs.org

CSS
.heavyShower {
    background-color: ${#e4b849} !important;
}
.lightShower {
    background-color: ${#ff9} !important;
}
.wday-0, .wday-6 {
    background: ${#ccc} !important;
}
.wday-0 .mWeekday,
.wday-6 .mWeekday {
    color: ${#000059} !important;
}
select:focus {
    color: var(--darkreader-selection-text) !important;
}

================================

wunderground.com

CSS
.bar-on {
   fill: rgba(0, 0, 0, 0.4) !important;
}

================================

wvpublic.org

INVERT
.Page-header-logo
.chevron

================================

www.123cha.com

INVERT
.logo

================================

www.adjust.com

INVERT
.homepage .a-section:first-child::before

CSS
.a-section.bg-gradient {
    --bg-gradient-end: ${white} !important;
    --bg-gradient-start: var(--darkreader-neutral-background) !important;
}

================================

www.androidauthority.com

INVERT
header > a > svg
header > div > button > svg
footer > div > div > a > svg

CSS
footer {
    background-image: none !important;
}

================================

www.bromite.org

CSS
body,
#container {
    background-image: none !important;
}

================================

www.ebay.*
www.ebay.*.*

CSS
html,
body {
    background-image: none !important;
}

================================

www.encyclopedia-titanica.org

CSS
body {
    background-color: var(--darkreader-neutral-background) !important;
    background-image: none !important;
}

================================

www.flickr.com

IGNORE INLINE STYLE
#icon-flickr_logo_dots path

================================

www.freepascal.org/docs-html

INVERT
img

================================

www.gamer.com.tw

CSS
body {
    background-image: none !important;
}

================================

www.google.*
www.google.*.*

INVERT
.gb_hc
.gb_ec
.gb_x.gb_Vb
.gb_x.gb_Ub
.gb_0b
#dictionary-modules img[src*="png"]
a.gb_b > div
a[href*="about/products"][title]
.JOmIqc
.hLcKi
#EcMbV
#wrap a[href="/mobile/"]
.vk-sf-b
.vk-t-btn
.ChZgtd div::before
.ChZgtd div::after
.yPHXsc div
.mn-dwn-arw
img.act-icon-dark-gray
.YsGUOb
img[src^="https://www.google.com/maps"]
[data-attrid="formula-image"]
[data-attrid^="variable"] img
[src^="/chrome/static/images/thank-you/"]
[src^="/chrome/static/images/reversible/"]
[src="/chrome/static/images/gpay/slate.png"]
[src^="/chrome/static/images/productivity/"]
[src^="/chrome/static/images/features/phone"]
[src^="/chrome/static/images/features/big_phone"]
[src^="/chrome/static/images/be-more-productive/"]
[src^="/chrome/static/images/search-bar/chrome-ui"]
[src="/chrome/static/images/go-mobile/qr-code.png"]
[src^="/chrome/static/images/download-browser/pixel"]
[src^="/chrome/static/images/browser-by-google/pixel"]
[src^="/chrome/static/images/sync-incognito/chrome-ui"]
[src^="/chrome/static/images/download-browser/big_pixel"]
[src="/chrome/static/images/homepage/homepage_tools.png"]
[src="/chrome/static/images/homepage/homepage_privacy.png"]
[src="/chrome/static/images/google-translate/screen-english.png"]

CSS
.RNNXgb,
.aajZCb {
    box-shadow: 0 0 2px 0 ${rgba(0,0,0,0.16)}, 0 0 0 1px ${rgba(0,0,0,0.08)} !important;
}
.Gor6zc {
    background-color: white !important;
}
.chr-full-bleed-hero {
    background-image: none !important;
}

IGNORE INLINE STYLE
.kdPwrb
.rnt3Ze
.NYcQFd

================================

www.hapo.org

INVERT
.section__overlap[style*="/new-site/background-swoosh.png"]

================================

www.jiqizhixin.com

INVERT
.header__logo

================================

www.khirevich.com

INVERT
.fig > img

================================

www.mayoclinic.org

INVERT
.mc-logo
.logo > a > img[alt="Mayo Clinic"]

================================

www.mozilla.org

INVERT
img.sidebar-icon
h1.c-logo

CSS
div.mzp-c-navigation-logo {
    background: ${black} !important;
}

IGNORE IMAGE ANALYSIS
.c-logo.t-browser-word-hor-white-xs
.c-logo.t-browser-word-hor-xs
.noodle-browser

================================

www.msn.cn

CSS
.heading span,
msft-attribution span {
    color: var(--darkreader-neutral-background) !important;
}
.heading + .abstract {
    color: ${rgb(247, 244, 241)} !important;
}

IGNORE INLINE STYLE
.article
msft-ad-label

================================

www.oschina.net

INVERT
.logo > svg > g > path

================================

www.pixivision.net

INVERT
.hdc__logo
.ghdsp__logo
.sidebar-visible-button
._search-field-visible-button

================================

www.realtek.com

CSS
#t3-header,
body > div.t3-wrapper > div > div.section-wrap.bg-wrap1 > div > div > div.custom > div > a  {
    background: var(--darkreader-neutral-background) !important;
}
#t3-header > div > div.col-xs-3.visible-xs-block > button.search-toggle,
#t3-header > div > div.col-xs-3.visible-xs-block > button.globe-toggle  {
    background-color: inherit !important;
}

================================

www.soepub.com/discuzx33

CSS
.c_l,
.c_r,
.c_yb,
.c_yc,
.c_ycr,
.c_yt {
    background-image: none !important;
}

================================

www.songsterr.com

CSS
#tablature svg text {
    fill: var(--darkreader-neutral-text) !important;
}
#tablature svg path {
    stroke: var(--darkreader-neutral-text) !important;
}

================================

www.sport5.co.il

INVERT
.news-room-placeholder .container-message-in .newsroom-row .newsroom-spitz

================================

www.statshunters.com

INVERT
.logo

CSS
.highcharts-background {
    fill: none !important;
}
.highcharts-text-outline {
    stroke: none !important;
}

================================

www.storm.mg

INVERT
.footer_logo_img
#logo_img

================================

www.tandfonline.com

INVERT
img.no-mml-formula

================================

www.thisoldhouse.com

INVERT
.c-masthead__main[style^="background-image"]

IGNORE INLINE STYLE
.c-footer__logo-link *

================================

www.thivien.net

CSS
body {
    background-image: none !important;
}

================================

www.tiktok.com

INVERT
.like-container .icon
.logo-link
div[class*="DivSeekBarContainer"]
div[class*="DivVolumeControlContainer"]
.video-control-container-browser
.volume-control-container-browser
.seek-bar-container
.volume-control-container

================================

www.tinkoff.ru

INVERT
a[title="Tinkoff"]

================================

www.tumblr.com

CSS
:root {
    --darkreader-bg--white: 23, 23, 23 !important;
    --darkreader-text--black: 228, 224, 218 !important;
    --darkreader-bg--secondary-accent: 31, 32, 34 !important;
}

================================

www.windy.com

INVERT
.data-table > .forecast-table.progress-bar > canvas

================================

wx.qq.com
wx2.qq.com

CSS
body {
    background-image: none !important;
}

================================

wyborcza.pl
wyborcza.biz

INVERT
#logo-wyborcza
.header-cap-holder > a > img
.header-headline
.header > .header-content > .header-items
.paywall-widget-svg-wrapper
.vin-img
.tools
img[src*="exchange/staticCharts"]
img[alt="Giełda"]
img[alt="Waluty"]

CSS
.contianer-hp-grid .hp-grid-picture-inner {
    z-index: 0 !important;
}
.plot,
.prev,
.sbt {
    background-image: none !important;
    color: var(--darkreader-neutral-text) !important;
}

================================

wysokieobcasy.pl

INVERT
.vin-img-link-pic.vin-local-img-link-pic

================================

x-kom.pl

INVERT
a[href="/"] img
a[href="https://x-kom.pl"] img
a[href="https://www.x-kom.pl"] img
img[alt="Menu"]
img[alt*="Logo"]
img[src*="Logo_strefy_marek"]

CSS
.ePVVIv {
    filter: brightness(0.8);
}

================================

xcite.com

INVERT
li.xc-product-slider-item__actions-bar__express.xcf.xcf--Rocket3-012222
li.xc-product-slider-item__actions-bar__express.xcf.xcf--express-delivery
li.xc-product-slider-item__actions-bar__pickup.xcf.xcf--bag
li.xc-product-slider-item__actions-bar__secret-deal.xcf.xcf--secret-deal
div > a.xc-product-slider-item__name
div.titleFyler > span.weeklyTitle

CSS
div.algolia-instant-results-wrapper {
    background-color: var(--darkreader-neutral-background);
}

================================

xda-developers.com

INVERT
.hb-trigger

CSS
body.tag {
    color: var(--darkreader-neutral-text) !important;
}

================================

xfree86.org

INVERT
img[src*=".png"]

================================

y.qq.com

INVERT
.popup_guide__logo
.qqmusic_logo

================================

yadi.sk

INVERT
span.logo.burger-sidebar__logo
span.logo.burger-sidebar__sidebar-logo

================================

yamicsoft.com

CSS
.slider-wrapper .slide-secound-detail, 
.slider-wrapper .slide-secound-title {
    color: var(--darkreader-neutral-background) !important;
}

================================

yandex.*/internet

INVERT
a[href="/internet"]

================================

yandex.*/maps
yandex.*/web-maps

INVERT
.content-panel-header__logo path[d^="M39.64 22.32"]
.whats-here-preview__control-search-icon
.close-button._color_black._circle
.business-social-links-view__icon
.social-share-view_discovery-small
.social-share-view_discovery-large
.orgpage-social-links-view__icon
.promo-button__promo-image-container
.orgpage-photos-view__empty
.map-container > ymaps > ymaps > canvas

CSS
span[class$="business-reactions-view__icon"],
div[class$="business-reactions-view__counter"],
span[class$="business-reactions-view__icon _dislike"] {
    color: ${#aba8a8} !important;
}

================================

yandex.*/pogoda

INVERT
.maps-widget-nowcast__map-link
.weather-maps__map > ymaps > ymaps > ymaps > ymaps
.color-scale__line
.weather-table__wind-direction > .icon_wind
.icon_color_black

================================

yandex.*/support

INVERT
.header2__logo

================================

yandex.ru/blog/narod-karta

INVERT
.y-header_club__logo
.y-header_club__service

================================

yandex.ru/q

INVERT
header a[href="/q/"] svg

================================

yelp.com

INVERT
#logo:not(.homepage-hero_logo)
img[src$="40x40_food_v2.svg"]
.gm-style img[role="presentation"]:not([src*="v="])

================================

yle.fi

INVERT
[class*="AreenaHeader"]
[class*="TheEndIsDelight__Svg"]

IGNORE IMAGE ANALYSIS
.yle-header figure.yle-header-logo

================================

yougetsignal.com

INVERT
img[src^="img/"]

================================

youla.ru

INVERT
section[data-test-component="HeaderActionMenu"] a > svg > path:nth-child(2n+2)
#map-1 canvas

================================

youmath.it
*.youmath.it

INVERT
.ltximg

================================

youtube.com

INVERT
#tube-mount .b img

CSS
html[hide-scrollbar] ::-webkit-scrollbar {
    display: none !important;
}
#search-icon-legacy.ytd-searchbox {
    border: 1px solid var(--ytd-searchbox-legacy-border-color) !important;
}
html:not(.style-scope) {
    --primary-text-color: ${#212121} !important;
    --primary-background-color: ${#ffffff} !important;
    --disabled-text-color: ${#9b9b9b} !important;
    --divider-color: ${#dbdbdb} !important;
    --error-color: ${#dd2c00} !important;
    --primary-color: ${#3f51b5} !important;
    --accent-color: ${#ff4081} !important;
    --yt-live-chat-action-panel-background-color: ${hsla(0, 0%, 93.3%, .4)} !important;
    --yt-live-chat-action-panel-background-color-transparent: ${hsla(0, 0%, 97%, .8)} !important;
    --yt-live-chat-background-color: ${hsl(0, 0%, 100%)} !important;
    --yt-live-chat-primary-text-color: ${hsl(0, 0%, 6.7%)} !important;
    --yt-live-chat-secondary-background-color: ${hsl(0, 0%, 93.3%)} !important;
    --yt-live-chat-secondary-text-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-live-chat-tertiary-text-color: ${hsla(0, 0%, 6.7%, .4)} !important;
    --yt-live-chat-disabled-icon-button-color: ${hsla(0, 0%, 6.7%, .2)} !important;
    --yt-live-chat-picker-button-color: ${hsla(0, 0%, 6.7%, .4)} !important;
    --yt-live-chat-text-input-field-suggestion-background-color: ${hsl(0, 0%, 100%)} !important;
    --yt-live-chat-text-input-field-suggestion-background-color-hover: ${#eee} !important;
    --yt-live-chat-text-input-field-suggestion-text-color: ${#666} !important;
    --yt-live-chat-text-input-field-suggestion-text-color-hover: ${#333} !important;
    --yt-live-chat-vem-background-color: ${hsl(0, 0, 93.3%)} !important;
    --yt-emoji-picker-search-background-color: ${hsla(0, 0%, 100%, .6)} !important;
    --yt-emoji-picker-search-color: ${hsla(0, 0%, 6.7%, .8)} !important;
    --yt-emoji-picker-search-placeholder-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --ytd-searchbox-border-color: ${hsla(0, 0%, 53.3%, .2)} !important;
    --ytd-searchbox-legacy-border-color: ${#ccc} !important;
    --ytd-searchbox-legacy-border-shadow-color: ${#eee} !important;
    --ytd-searchbox-legacy-button-color: ${#f8f8f8} !important;
    --ytd-searchbox-legacy-button-border-color: ${#d3d3d3} !important;
    --ytd-searchbox-legacy-button-focus-color: ${#e9e9e9} !important;
    --ytd-searchbox-legacy-button-hover-color: ${#f0f0f0} !important;
    --ytd-searchbox-legacy-button-hover-border-color: ${#c6c6c6} !important;
    --ytd-searchbox-legacy-button-icon-color: ${#333} !important;
    --ytd-moderation-panel-background: ${hsla(0, 0%, 93.3%, .6)} !important;
    --ytd-moderation-panel-hover: ${hsla(0, 0%, 93.3%, .8)} !important;
    --ytd-moderation-panel-comment-text: ${hsl(0, 0%, 6.7%)} !important;
    --ytd-moderation-panel-comment-metadata-text: ${hsla(0, 0%, 6.7%, .6)} !important;
    --ytd-moderation-icon-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --ytd-moderation-icon-hover-color: ${hsl(0, 0%, 6.7%)} !important;
    --ytd-comment-text-color: ${hsl(0, 0%, 6.7%)} !important;
    --ytd-watch-card-secondary-text-color: ${hsl(0, 0%, 53.3%)} !important;
    --ytd-watch-card-album-header-background: ${hsl(0, 0%, 100%)} !important;
    --ytd-backstage-metadata-text-color: ${hsl(0, 0%, 53.3%)} !important;
    --ytd-backstage-video-link-background-color: ${hsla(0, 0%, 93.3%, .4)} !important;
    --ytd-backstage-image-alert-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --ytd-backstage-cancel-background-color: ${hsl(0, 0%, 100%)} !important;
    --ytd-backstage-cancel-color: ${hsla(0, 0%, 6.7%, .4)} !important;
    --ytd-backstage-attachment-background-color: ${hsl(0, 0%, 100%)} !important;
    --ytd-backstage-creationbox-background-color: ${hsl(0, 0%, 98%)} !important;
    --ytd-backstage-creationbox-background-color-focus: ${hsl(0, 0%, 96%)} !important;
    --ytd-backstage-creationbox-inactive-color: ${hsla(0, 0%, 0%, .26)} !important;
    --ytd-backstage-creationbox-text-color: ${hsla(0, 0%, 0%, .54)} !important;
    --ytd-backstage-creationbox-input-text-color: ${hsla(0, 0%, 0%, .87)} !important;
    --ytd-backstage-creationbox-disabled-button-color: ${hsla(0, 0%, 0%, .04)} !important;
    --ytd-backstage-creationbox-disabled-button-text-color: ${hsl(0, 0%, 100%)} !important;
    --ytd-backstage-attachment-icon-hover-color: ${hsla(0, 0%, 0%, .74)} !important;
    --ytd-sponsorships-background-color-focus: ${hsla(0, 0%, 93.3%, .4)} !important;
    --ytd-badge-disabled-color: ${hsla(0, 0%, 53.3%, .4)} !important;
    --ytd-collection-badge-color: ${hsla(0, 0%, 6.7%, .8)} !important;
    --ytd-owner-badge-color: ${hsla(0, 0%, 6.7%, .4)} !important;
    --ytd-simple-badge-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --ytd-shopping-product-info: ${hsla(0, 0%, 6.7%, .8)} !important;
    --ytd-transcript-cue-hover-background-color: ${hsl(0, 0%, 93.3%)} !important;
    --ytd-transcript-toolbar-background-color: ${hsl(0, 0%, 93.3%)} !important;
    --ytd-transcript-toolbar-text: ${hsl(0, 0%, 6.7%)} !important;
    --ytd-video-publish-date-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --ytd-vat-notice-text: ${hsla(0, 0%, 6.7%, .6)} !important;
    --ytd-offer-background-color: ${hsla(0, 0%, 93.3%, .4)} !important;
    --ytd-video-game-watch-card-logo-color: ${hsl(0, 0%, 6.7%)} !important;
    --ytd-watch-split-pane-sidebar-background-color: ${hsl(0, 0%, 98%)} !important;
    --yt-swatch-icon-color: ${hsla(0, 0%, 6.7%, .4)} !important;
    --yt-swatch-primary: ${hsl(0, 0%, 100%)} !important;
    --yt-swatch-primary-darker: ${rgb(230, 230, 230)} !important;
    --yt-swatch-text: ${hsla(0, 0%, 6.7%, .4)} !important;
    --yt-swatch-input-text: ${hsl(0, 0%, 6.7%)} !important;
    --yt-swatch-textbox-bg: ${rgb(255, 255, 255)} !important;
    --yt-spec-brand-background-solid: ${#FFFFFF} !important;
    --yt-spec-brand-background-primary: ${rgba(255, 255, 255, 0.98)} !important;
    --yt-spec-brand-background-secondary: ${rgba(255, 255, 255, 0.95)} !important;
    --yt-spec-feed-background-a: ${#F9F9F9} !important;
    --yt-spec-feed-background-b: ${#F3F3F3} !important;
    --yt-spec-feed-background-c: ${#EDEDED} !important;
    --yt-spec-error-background: ${#1F1F1F} !important;
    --yt-spec-text-primary: ${#0A0A0A} !important;
    --yt-spec-text-primary-inverse: ${#FFFFFF} !important;
    --yt-spec-text-secondary: ${#606060} !important;
    --yt-spec-text-disabled: ${#909090} !important;
    --yt-spec-call-to-action: ${#065FD4} !important;
    --yt-spec-icon-active-other: ${#606060} !important;
    --yt-spec-icon-inactive: ${#909090} !important;
    --yt-spec-icon-disabled: ${#CCCCCC} !important;
    --yt-spec-badge-chip-background: ${rgba(0, 0, 0, 0.05)} !important;
    --yt-spec-suggested-action: ${#F2F8FF} !important;
    --yt-spec-button-chip-background-hover: ${rgba(0, 0, 0, 0.10)} !important;
    --yt-spec-touch-response: ${#000000} !important;
    --yt-spec-filled-button-text: ${#FFFFFF} !important;
    --yt-spec-call-to-action-inverse: ${#3EA6FF} !important;
    --yt-spec-brand-icon-inactive: ${#606060} !important;
    --yt-spec-filled-button-focus-outline: ${rgba(0, 0, 0, 0.60)} !important;
    --yt-spec-call-to-action-button-focus-outline: ${rgba(6, 95, 212, 0.30)} !important;
    --yt-spec-brand-text-button-focus-outline: ${rgba(204, 0, 0, 0.30)} !important;
    --yt-spec-inactive-text-button-focus-outline: ${#CCCCCC} !important;
    --yt-spec-brand-subscribe-button-background: ${#FF0000} !important;
    --yt-spec-wordmark-text: ${#282828} !important;
    --yt-spec-10-percent-layer: ${rgba(0, 0, 0, 0.10)} !important;
    --yt-spec-selected-nav-text: ${#CC0000} !important;
    --yt-spec-themed-blue: ${#065FD4} !important;
    --yt-spec-themed-green: ${#107516} !important;
    --yt-std-body-300: ${hsla(0, 0%, 0%, .54)} !important;
    --yt-std-surface-200: ${hsl(0, 0%, 98%)} !important;
    --yt-std-surface-300: ${hsl(0, 0%, 96%)} !important;
    --yt-std-surface-400: ${hsl(0, 0%, 93%)} !important;
    --yt-primary-color: ${hsl(0, 0%, 6.7%)} !important;
    --yt-primary-text-color: ${hsl(0, 0%, 6.7%)} !important;
    --yt-hovered-text-color: ${hsla(0, 0%, 6.7%, .8)} !important;
    --yt-secondary-text-color: ${hsla(0, 0%, 6.7%, .8)} !important;
    --yt-tertiary-text-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-placeholder-text-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-border-color: ${hsl(0, 0%, 93.3%)} !important;
    --yt-commentbox-border-inactive: ${hsl(0, 0%, 93.3%)} !important;
    --yt-commentbox-border-active: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-primary-disabled-button-text-color: ${hsl(0, 0%, 100%)} !important;
    --yt-paper-button-ink-color: ${hsl(0, 0%, 53.3%)} !important;
    --yt-icon-color: ${hsla(0, 0%, 6.7%, .4)} !important;
    --yt-icon-hover-color: ${hsla(0, 0%, 6.7%, .8)} !important;
    --yt-icon-disabled-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-icon-active-color: ${hsl(0, 0%, 6.7%)} !important;
    --yt-expand-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-placeholder-text: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-playlist-background-header: ${hsl(0, 0%, 93.3%)} !important;
    --yt-playlist-background-item: ${hsla(0, 0%, 93.3%, .6)} !important;
    --yt-playlist-title-text: ${hsl(0, 0%, 6.7%)} !important;
    --yt-playlist-message-text: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-playlist-message-text-hover: ${hsl(0, 0%, 6.7%)} !important;
    --yt-button-active-color: ${hsl(0, 0%, 6.7%)} !important;
    --yt-copyright-text: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-guide-entry-text-color: ${hsla(0, 0%, 6.7%, .8)} !important;
    --yt-thumbnail-placeholder-color: ${hsl(0, 0%, 89%)} !important;
    --yt-featured-channel-title-text-color: ${hsla(0, 0%, 0%, .54)} !important;
    --yt-formatted-string-deemphasize-color: ${hsl(0, 0%, 53.3%)} !important;
    --yt-alert-background: ${hsla(0, 0%, 93.3%, .4)} !important;
    --yt-video-secondary-info-description-background: ${hsla(0, 0%, 93.3%, .6)} !important;
    --yt-material-searchbox-inactive: ${hsla(0, 0%, 93.3%, .6)} !important;
    --yt-material-searchbox-active: ${hsl(0, 0%, 100%)} !important;
    --yt-material-searchbox-inactive-shadow: ${hsla(0, 0%, 53.3%, .2)} !important;
    --yt-material-searchbox-active-shadow: ${hsla(0, 0%, 0%, .26)} !important;
    --yt-material-searchbox-inset: ${hsla(0, 0%, 0%, .04)} !important;
    --yt-simple-menu-header-background: ${hsl(0, 0%, 93.3%)} !important;
    --yt-item-section-header-color: ${hsla(0, 0%, 6.7%, .6)} !important;
    --yt-menu-hover-backgound-color: ${hsl(0, 0%, 93.3%)} !important;
    --yt-menu-focus-background-color: ${hsla(0, 0%, 6.7%, .2)} !important;
    --yt-chat-bubble-other-border-color: ${#CCCCCC} !important;
    --yt-chat-bubble-other-background-color: ${#F9F9F9} !important;
    --yt-chat-bubble-self-border-color: ${#CCCCCC} !important;
    --yt-chat-bubble-self-background-color: ${#EDEDED} !important;
    --yt-app-background: ${hsl(0, 0%, 100%)} !important;
    --yt-main-app-background: ${hsl(0, 0%, 98%)} !important;
    --yt-main-app-background-tmp: ${hsl(0, 0%, 100%)} !important;
    --yt-guide-background: ${hsl(0, 0%, 96%)} !important;
    --yt-dialog-background: ${hsl(0, 0%, 100%)} !important;
    --yt-searchbox-background: ${hsl(0, 0%, 100%)} !important;
    --yt-channel-header-background: ${hsl(0, 0%, 98%)} !important;
    --yt-sidebar-background: ${hsl(0, 0%, 98%)} !important;
    --yt-transcript-background: ${hsl(0, 0%, 100%)} !important;
    --yt-spec-general-background-a: ${white} !important;
    --yt-spec-general-background-b: ${#f1f1f1} !important;
    --yt-spec-general-background-c: ${#e9e9e9} !important;
    --yt-spec-brand-icon-active: #{rgb(255, 0, 0)} !important;
}
.ytp-hover-progress-light {
    background-color: rgba(255,255,255,.5) !important;
}
.ytp-progress-list {
    background-color: rgba(255,255,255,.2) !important;
}
.ytp-volume-slider-handle {
    background-color: white !important;
}
.ytp-volume-slider-handle::before {
    background-color: white !important;
}
.ytp-volume-slider-handle::after {
    background-color: rgba(255,255,255,.2) !important;
}
button[aria-pressed="true"] > yt-icon:not(#guide-icon.ytd-app),
[id$="-replies"].ytd-comment-replies-renderer > .ytd-button-renderer > paper-button,
[id$="-replies"].ytd-comment-replies-renderer > .ytd-button-renderer > paper-button  > yt-icon {
    fill: var(--yt-spec-call-to-action) !important;
    color: var(--yt-spec-call-to-action) !important;
}
.ytp-menuitem-toggle-checkbox {
    background: rgba(255, 255, 255, 0.3) !important;
}
.ytp-menuitem[aria-checked="true"] .ytp-menuitem-toggle-checkbox {
    background: #f00 !important;
}
.ytp-menuitem-toggle-checkbox::after {
    background-color: #bdbdbd !important;
}
.ytp-menuitem[aria-checked="true"] .ytp-menuitem-toggle-checkbox::after {
    background-color: white !important;
}
#date yt-formatted-string.ytd-video-primary-info-renderer, .yt-view-count-renderer {
    color: var(--yt-spec-text-secondary) !important;
}
.ytp-contextmenu .ytp-menuitem .ytp-menuitem-toggle-checkbox {
    background: none !important;
}
.ytp-contextmenu .ytp-menuitem[aria-checked="true"] .ytp-menuitem-toggle-checkbox {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" fill="white"/></svg>') !important;
}
#backgroundFrontLayer.tp-yt-app-header {
    background-image: var(--app-header-background-front-layer_-_background-image) !important;
}
#metadata-line.ytd-video-meta-block span.ytd-video-meta-block {
    color: var(--ytd-metadata-line-color, var(--yt-spec-text-secondary)) !important;
}
yt-formatted-string[has-link-only_]:not([force-default-style]) a.yt-simple-endpoint.yt-formatted-string {
    color: var(--yt-endpoint-color, var(--yt-spec-text-primary)) !important;
}
paper-button.ytd-subscribe-button-renderer[subscribed] {
    background-color: var(--yt-spec-10-percent-layer) !important
}
.badge-style-type-live-now.ytd-badge-supported-renderer {
    border: 1px solid rgb(255, 78, 69) !important;
}
.ytp-load-progress {
    background: rgba(255,255,255,0.3) !important;
}
yt-chip-cloud-chip-renderer[chip-style="STYLE_DEFAULT"] {
    background-color: rgba(255,255,255,0.05);
}
yt-chip-cloud-chip-renderer[chip-style="STYLE_DEFAULT"].iron-selected {
    background-color: rgba(255,255,255,0.2);
}
paper-item[aria-selected="true"] {
    background-color: rgba(255,255,255,0.2) !important;
}
iron-input.paper-input > input.paper-input,
.input-content.paper-input-container > label, 
.input-content.paper-input-container > .paper-input-label {
    color: var(--paper-input-container-shared-input-style_-_color) !important;
}
ytd-compact-autoplay-renderer,
ytd-video-primary-info-renderer,
#sections.ytd-guide-renderer > .ytd-guide-renderer:not(first-child),
ytd-video-secondary-info-renderer,
#placeholder-area.ytd-comment-simplebox-renderer {
    border-bottom-color: var(--yt-spec-10-percent-layer) !important;
}
ytd-guide-collapsible-section-entry-renderer.ytd-guide-section-renderer:not(:first-child),
ytd-metadata-row-header-renderer[has-divider-line] {
    border-top-color: var(--yt-spec-10-percent-layer) !important;
}
ytd-guide-entry-renderer[active] .guide-icon.ytd-guide-entry-renderer {
    fill: ${black} !important;
}
#hearted.ytd-creator-heart-renderer {
    fill: var(--yt-spec-static-brand-red) !important;
}
#hearted-border.ytd-creator-heart-renderer {
    fill: ${white} !important;
}
.guide-entry-badge.ytd-guide-entry-renderer {
    fill: var(--yt-spec-static-brand-red) !important;
}
#endpoint.yt-simple-endpoint.ytd-guide-entry-renderer:hover,
#endpoint.yt-simple-endpoint.ytd-guide-entry-renderer:focus {
    background-color: var(--yt-spec-badge-chip-background) !important;
}
a.yt-simple-endpoint.yt-formatted-string {
    color: var(--yt-endpoint-color, var(--yt-spec-call-to-action)) !important;
}
a.yt-simple-endpoint.yt-formatted-string:hover {
    color: var(--yt-endpoint-hover-color, var(--yt-spec-call-to-action)) !important;
}
.ytp-cards-button-icon .ytp-svg-shadow {
    fill: var(--darkreader-neutral-background) !important;
}
.ytp-doubletap-ui-legacy[data-side="back"] .ytp-doubletap-base-arrow {
    border-right-color: var(--darkreader-neutral-color) !important;
}
.ytp-doubletap-ui-legacy[data-side="forward"] .ytp-doubletap-base-arrow {
    border-left-color: var(--darkreader-neutral-color) !important;
}

IGNORE INLINE STYLE
yt-live-chat-ticker-paid-message-item-renderer *

================================

yscec.yonsei.ac.kr

INVERT
iframe
#page-sidebar

================================

yuque.com

INVERT
.lake-math-content-preview-img > img
.lake-math-container > img
div[data-lake-card="mindmap"] > div > div > div

================================

zacznijzyc.net

INVERT
.logo_container

================================

zdic.net

INVERT
li a img
.kxtimg
.zipic img
.zx img

CSS
.nr-box-header {
    background-image: none !important;
}

================================

zdw.krakow.pl

CSS
#kw-alert p,
#kw-alert a {
    color: initial !important;
}

================================

zenn.dev

INVERT
a[class^="AppHeader_homeLink"]

================================

zeptovm.com

INVERT
.logo

================================

zerossl.com

INVERT
.logo

================================

zhihu.com

INVERT
img[eeimg="1"]

================================

znanium.com

INVERT
.bookreadimgs

================================

zybooks.com

CSS
.image-content-resource img,
.zyimage {
    background-color: var(--darkreader-neutral-text) !important;
}
