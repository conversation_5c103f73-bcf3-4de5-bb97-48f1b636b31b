*

NEUTRAL BG
html
body
:not([style*="background-color:"])

NEUTRAL TEXT
html
body
:not([style*="color:"])
.sr-reader *:not([class*='sr-pivot'])

RED TEXT
h1:not([style*="color:"])
h2:not([style*="color:"])
h3:not([style*="color:"])
h4:not([style*="color:"])
h5:not([style*="color:"])
h6:not([style*="color:"])

GREEN TEXT
cite:not([style*="color:"])

BLUE BG ACTIVE
input:not([style*="background-color:"])
textarea:not([style*="background-color:"])
button:not([style*="background-color:"])
[role="button"]

BLUE TEXT ACTIVE
a:not([style*="color:"])

BLUE BORDER
:not([style*="border-color:"])
::before
::after

FADE BG
div:empty
.sr-reader *
.sr-backdrop

FADE TEXT
input::placeholder
textarea::placeholder

NO IMAGE
input:not([style*="background-image:"])
textarea:not([style*="background-image:"])

================================

github.com

RED TEXT
.pl-k

GREEN BG ACTIVE
.btn-primary

GREEN TEXT
.pl-c

BLUE TEXT
.pl-s
.pl-pds
.pl-c1

NO IMAGE
.btn
.btn-primary

================================

imdb.com

RED BG
.jw-progress:empty
.jw-knob:empty

FADE BG
.MediaSheetstyles__MediaContainer-sc-1warcg6-0
.MediaSheetstyles__MediaContentContainer-sc-1warcg6-1

TRANSPARENT BG
.ipc-lockup-overlay
.ipc-lockup-overlay__content *
.jw-captions
.jw-controls
.jw-controls *
.jw-controls-backdrop:empty
.jw-overlays
.MediaSheetstyles__MediaContentContainer-sc-1warcg6-1 div
.Slate__VideoPreviewContainer-ss6ccs-3
.styles__MediaViewerTouchHandler-sc-6t1jw8-5

================================

mail.google.com

RED BG ACTIVE
.T-I-KE

NO IMAGE
.T-I-KE

================================

nationstates.net

TRANSPARENT BG
.paperclip

================================

opencollective.com

BLUE BG ACTIVE
.TierCard .action

TRANSPARENT BG
.CollectiveCover .content

================================

reddit.com

RED BG
.reddit-video-player-root .seek-bar-progress
.reddit-video-player-root .volume-slider-progress
.reddit-video-player-root .volume-slider-thumb

BLUE BG
.reddit-video-player-root .seek-bar-buffered
.reddit-video-player-root .volume-slider-track

FADE BG
.reddit-video-player-root .ended-controls
.reddit-video-player-root .playback-controls

TRANSPARENT BG
.reddit-video-player-root video + div
.reddit-video-player-root .ended-controls :not(button)
.reddit-video-seek-bar-root
.reddit-video-player-root .playback-controls .control-button

================================

youtube.com

RED BG
.ytp-swatch-background-color.ytp-swatch-background-color

BLUE BG
.ytp-load-progress:empty

FADE BG
.ytp-chrome-top
.ytp-chrome-bottom
.ytp-pause-overlay

TRANSPARENT BG
#previewbar
.ytp-button.ytp-button
.ytp-chrome-bottom *
.ytp-chrome-top *
.ytp-gradient-bottom:empty
.ytp-gradient-top:empty
.ytp-pause-overlay *
.ytp-progress-bar-padding:empty
.ytp-scrubber-container
.ytp-timed-markers-container:empty
.ytp-tooltip-text-wrapper
