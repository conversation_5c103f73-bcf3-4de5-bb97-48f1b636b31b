*

INVERT
img
video
:not(object):not(body)>embed
object
svg image
[style*="background:url"]
[style*="background-image:url"]
[style*="background: url"]
[style*="background-image: url"]
[background]
twitterwidget
.sr-reader
.sr-backdrop

NO INVERT
[style*="background:url"] *
[style*="background-image:url"] *
[style*="background: url"] *
[style*="background-image: url"] *
input
[background] *
img[src^="https://s0.wp.com/latex.php"]
twitterwidget .NaturalImage-image

REMOVE BG
.compatibility-with-darkreader-below-4-3-3

CSS
.compatibility-with-darkreader-below-4-3-3 {
    background: white !important;
}

================================

1fichier.com

INVERT
#oby_na
#mkdir
#rename
#rmdir
#share
.file_link
.file_info
.file_at
.file_export
.file_desc
.file_rename
.file_password
.file_acl
.file_ddl
.file_inline
.file_remove

================================

airbnb.com

INVERT
.background-cover
#footer
#photo-modal

NO INVERT
#footer *
#photo-modal *

================================

algassert.com

NO INVERT
img

================================

aliexpress.com

CSS
#j-qrcode-img, #J_QRCodeImg {
    background-color: white !important;
}

================================

amazon.com

INVERT
#navbar
#nav-subnav
#navFooter
.dv-storefront-page
.av-hero-background-size
.av-detail-bottom-bar
.webPlayerContainer
.avu-content
.avu-section-alt
.av-badges
.controlsOverlayTopRight
.pausedOverlay
.nav-flyout

NO INVERT
.DigitalVideoWebNodeLists_Item__itemImage
.av-hero-background
.avu-content img
.dv-tile img
video

================================

anankeiot.com

INVERT
#sidebar-left
.page-header
.add-button
.panel-footer

================================

androidpolice.com

INVERT
.ap-nav-wrapper
.footer
.logo

NO INVERT
.ap-nav-wrapper *
.footer *

================================

answers.unity.com

INVERT
.header-wrapper
.navbar

================================

askubuntu.com

REMOVE BG
body

================================

atlassian.net

INVERT
.EditPage_withPanels_3Hr
.aui-toolbar
.editor
.toolbar-item

================================

audible.com

NO INVERT
img.adbl-cp-cursor

================================

avisendanmark.dk

INVERT
.logo

================================

aws.amazon.com

INVERT
.m-nav-header
.m-nav-panel-wrapper

================================

baike.baidu.com

INVERT
.formula

================================

bart.gov/schedules

NO INVERT
img

================================

basho.com

INVERT
#site-navigation
footer
header
.featured-news-wrapper
:not(.lang):not(#foot-logo)>img

================================

bbc.com

INVERT
div.orb-nav-section.orb-nav-blocks > a > img

================================

berlingske.dk

INVERT
.site-header__logo

================================

bestbuy.com

INVERT
#None > div > section > div.comparison-container.comparison-container-l.legacy-variant
.product-container

================================

betterexplained.com

NO INVERT
img.tex

================================

bit-tech.net
forums.bit-tech.net

INVERT
div#subNav
div#anotherSubNav
.footerContainer
div.footer
#bitFooter
.nodeIcon
li.navTab.forums.selected
li.navTab.xengallery.selected
li.navTab.members.selected

REMOVE BG
#blurBackground
.background-image
#headerMover
#loginBar

================================

buxfer.com

INVERT
.UIDesktopAppHeader
.UISidebarDrawer
.UISidebar

================================

bytesloader.com

INVERT
.footer-distributed
.navbar-inverse

================================

central.bitdefender.com

INVERT
#left_nav
.top_nav
#user_avatar

NO INVERT
.logo
#left_nav *
.top_nav *

================================

circleci.com

INVERT
.css-1ibtrcs
.css-1lflngw

================================

cloud-catcher.squiddev.cc

INVERT
button[class*="styles_action-button"]

CSS
button[title*="computer off"] svg path {
  fill: green !important;
}

================================

cloud.digitalocean.com

INVERT
.aurora-container-left

================================

codebasehq.com

INVERT
a.Project__link

================================

codecombat.com

INVERT
.modal-content
.map-background
.game-controls
.level-content
.modal-backdrop

================================

dailymail.co.uk

NO INVERT
.masthead #logo

================================

dash.cloudflare.com

NO INVERT
.c_bt > div:nth-child(1) > svg:nth-child(1)
.c_k

================================

dedication.pl

INVERT
.wwzing

================================

deezer.com

INVERT
.page-sidebar

NO INVERT
.player-cover *

================================

detexify.kirelabs.org

INVERT
.symbol img

================================

devdocs.io

NO INVERT
._theme-dark

================================

developer.chrome.com

CSS
mark, pre b {
    background-color: ${#fe7} !important;
}

================================

developer.mozilla.org

INVERT
#nav-footer
.highlight-span
.readOnlyInline
#nav-sec
#toc
.socialaccount-providers
.toggle-container .current

================================

developer.playfab.com

INVERT
header

================================

deviantart.com

INVERT
#navbar-menu
#overhead-collect
.text-wrap
.thumb > .info
.mobileMenuWrapper
.tv150-tag
.hero
.hero-thumb
.hero-thumb > .info
.loggedOut
.minibrowse-mobile-paddles-container

NO INVERT
.extra-info .avatar
.tt-crop > .tinythumb

================================

disqus.com/embed/

INVERT
body.dark

NO INVERT
body.dark *

================================

docs.gamesparks.com

INVERT
.side-menu
.home-menu
.hljs

================================

docs.google.com

INVERT
.jfk-palette-colorswatch
[aria-label^="Google Account"]
div[title="Profile"]
.kix-embeddedobject-image

================================

docs.microsoft.com

NO INVERT
.theme-dark
.theme_night

================================

docs.unity3d.com

INVERT
.header
.navbar-inverse

================================

doodle.com

INVERT
.d-footerContentContainer

================================

drive.google.com

INVERT
[aria-label^="Showing viewer"] > [aria-label^="Displaying"]
[aria-label^="Showing viewer"] > [role="toolbar"]
[aria-label^="Showing viewer"] [role="button"][data-tooltip="Previous"]
[aria-label^="Showing viewer"] [role="button"][data-tooltip="Next"]
[aria-label^="Showing viewer"] [aria-label^="Page"]
[aria-label^="Showing viewer"] [aria-label^="Page"] + div
[aria-label^="No preview available"]
[aria-label^="No preview available"] ~ div
[aria-label^="There was a problem previewing"]
[aria-label^="There was a problem previewing"] ~ div
[aria-label^="Google Account"]
div[title="Profile"]

================================

dsl.sk

NO INVERT
#header img
#title_bar img
#bg > table > tbody > tr:nth-of-type(1) > td:nth-of-type(1)
img[src="images/article_perex.gif"]
img[src="/images/design/feedback_pos.png"]
img[src="/images/design/feedback_neg.png"]

REMOVE BG
#menu
#bg > table > tbody > tr:nth-of-type(1) > td:nth-of-type(1)

================================

duckduckgo.com

INVERT
html:not(.dark-bg) #logo_homepage_link
html:not(.dark-bg) span.header__logo.js-logo-ddg

NO INVERT
html.dark-bg
html.dark-bg *

================================

ebay.*

INVERT
body[style*="background-image: url"] img
body[style*="background-image: url"] video
body[style*="background-image: url"] [style*="background-image: url"]

NO INVERT
body[style*="background-image: url"]

================================

electronics-tutorials.ws

REMOVE BG
img

================================

electronjs.org

INVERT
header
.jumbotron-home
.highlight-dark

================================

exhentai.org
e-hentai.org

INVERT
#gd1
.gdtm > div

================================

facebook.com

INVERT
._3ixn
._2teu
.userContentWrapper canvas
._4lqu
._4lqt
._24ws
._1z0-
._19eb
._5rpb
._5rpu
._3t54
._1-h1
._3pit
._5asn
._1e8c
._2b-0
._2w_u
._4who
._4g9v
._3s6x
._5b-_
._2r86
._2r84 ._62jm
._3ekx
._3uzm
._4-lv
._6m3
._2t_q
._4-lv [role="button"]
._2ph-
._39n
._3u15
.snowliftOverlay
.coverNoImage
.fbTimelineProfilePicSelector
.uiTooltipX
body.UIPage_LoggedOut #blueBarDOMInspector
body.UIPage_LoggedOut #blueBarDOMInspector .inputtext
canvas

NO INVERT
.uiStreamStory video
.inputtext

REMOVE BG
._4d3w .stageWrapper
._4lpe
.stage
.videoStage

CSS
._2s1x ._2s1y {
    background: #9BB5E8 !important;
    border-bottom-color: #92A6CA !important;
    color: black !important;
}
.uiContextualLayerParent {
    box-shadow: none !important;
}

================================

farside.ph.utexas.edu

NO INVERT
img

================================

feedback.unity3d.com

INVERT
.header-wrapper
.navbar
.bg-ud

================================

feedly.com

INVERT
.people .photo
#feedlyUndoSign
#feedlySignPart
.pinContainer
.visual-overlay

CSS
.entry-overlay {
    background-color: rgba(220,220,220,0.5);
}

================================

feynmanlectures.caltech.edu

NO INVERT
img

================================

fontspring.com

NO INVERT
.grid6 .fullwidth

================================

forum.unity.com

INVERT
.main-navigation
.sub-nav
.unity-logo
.icon
.sub-nav

================================

gadgety.co.il

INVERT
iframe[src^="https://www.youtube.com/embed"]

================================

gamesparks.com

INVERT
.main_header
.block_compatibility
.grid_overlay
.block_insights
.main_footer
.docs-hero

================================

genk.vn

INVERT
.ghw-bottom-header
.ghw-top-header

================================

gigaom.com

INVERT
.bg

================================

github.com

INVERT
.jumbotron-codelines
.bg-gray-dark
.bg-gray-dark .form-group
.js-project-header
.header-logo-invertocat
.notification-indicator
.label
.label-color
.tooltipped:not(.notification-indicator)::before
.tooltipped:not(.notification-indicator)::after
.HeaderNavlink [data-ga-click*="(Logged out)"]

CSS
header {
    background-color: #dae1e7 !important;
}
.markdown-body img {
    background-color: transparent !important;
}
.Header .header-search-wrapper {
    background-color: rgba(0, 0, 0, 0.125) !important;
}
.js-selected-navigation-item {
    color: rgba(0, 0, 0, 0.75) !important;
}
header,
.HeaderNavlink,
.Header .header-search-input,
.Header .header-search-input::placeholder {
    color: rgba(0, 0, 0, 0.75) !important;
}
.Header .header-search-scope {
    border-right-color: #ccd7e1 !important;
    color: rgba(0, 0, 0, 0.75) !important;
}
.select-menu-item.navigation-focus,
.select-menu-item.navigation-focus.selected,
.select-menu-item.navigation-focus.select-menu-action,
.select-menu-item.navigation-focus .description-inline {
    background: #93c6ff !important;
    color: black !important;
}
.HeaderMenu-link,
.select-menu-item.navigation-focus > .octicon,
.select-menu-item.navigation-focus.selected > .octicon,
.select-menu-item.navigation-focus.select-menu-action > .octicon,
.select-menu-item.navigation-focus .description-inline > .octicon {
    color: black !important;
}

================================

gitlab.*

INVERT
gl-emoji
.navbar
.code.dark
.code.dark .notes_holder
.code.solarized-dark
.code.solarized-dark .notes_holder
.code.monokai
.code.monokai .notes_holder
.blame-commit

NO INVERT
.ui_light .navbar

CSS
#build-trace {
    background: rgba(255, 255, 255, 0.7) !important;
}
pre code {
    color: #777777 !important;
}

================================

gls-pakete.de

CSS
.tracking--status .status-box::before, .tracking--status .status-box::after {
    z-index: 0 !important;
}
.tracking--status .status-box.status--complete.status--lastcomplete .status-box--tooltip, .tracking--status .status-box.status--current .status-box--tooltip {
    transform: none !important;
    position: absolute !important;
    margin-bottom: 0px !important;
    margin-top: -10px !important;
    bottom: 0 !important;
}

================================

google.*
google.*.*

INVERT
.gb_2
.gb_M
.gsri_a
.gbii
.gbip
.azp
.aLF-aPX-KP
.irc_bg
.RY3tic
canvas.circle
#app-container.vasquette.app-imagery-mode .widget-scene-canvas
#app-container.vasquette.app-globe-mode .widget-scene-canvas

NO INVERT
.irc_mi
.irc_rii
.irc_mut
.act-icon-dark-gray
.amI
.amJ
.adk
.adj
[src*="ic_"]
[src*="black"]
.mL
.ajT
.hB
.hA
.mK
.mI
input[style*="data:image"]
img[src*="/books/content?"]

CSS
.pI {
    background: rgba(255, 255, 255, 0.7) !important;
}
.eB {
    background: rgba(255, 255, 255, 0.4) !important;
}

================================

granttree.co.uk

INVERT
img
video
embed
.photo

================================

habitica.com

INVERT
nav.navbar
#app-header
.header
.quest-boss
.character-sprites
.item-content
.drawer-container .item
.npc_matt
.npc
.seasonal .background
.image
.habitica-emoji
.daniel_front
.world-boss
.dropdown-menu
.dropdown-menu .notification
.fade.show
.modal-content
.drawer-container

NO INVERT
#app-header .character-sprites
.seasonal .background .npc

================================

habr.com

NO INVERT
img[src*="//tex.s2cms.ru/"]

================================

hangouts.google.com

INVERT
.g-Ue-ad
.g-Ue-v0h5Oe
.bdXzDb .pTh3n
.gb_Hc
.Ik
.gbii
.vm
.Ce1Y1c
.mUbCce

NO INVERT
#gbq1

REMOVE BG
.g-Qx-eb

CSS
.kFx1Ae-xdwExf-eb-m,
.g-Qx-r4m2rf-wZVHld {
    display: none;
}

================================

homestuck.com

INVERT
.read-link
body.scratch
body.sweet
body:not(.scratch):not(.sweet) #animation_container
body:not(.scratch):not(.sweet) .nav-btn--center
body:not(.scratch):not(.sweet) #nav-container
body:not(.scratch):not(.sweet) #game_overlay
body.scratch #overlay
body.sweet #overlay
body.sweet #gamenav-container
body.scratch #site-search
body.sweet #site-search
body.scratch .o_chat-log-btn

NO INVERT
body.scratch img
body.sweet img

REMOVE BG
body:not(.scratch):not(.sweet)
body:not(.scratch):not(.sweet) .o_site-footer

================================

html5rocks.com

INVERT
.prettyprint

================================

hysterical-amusement.surge.sh

INVERT
body
.instructionsContainer
#liveOutput

================================

ign.com

INVERT
.video_embed_content-poster
.video_embed_content-poster-play-button

REMOVE BG
#review-promo

================================

imdb.com

INVERT
#nb20 > div
#navbar-suggestionsearch
.caption_overlay
.vital
.slate_wrapper > .poster
.slate
.slate_fade_top
.slate_fade_bottom
.ab_hero
.ab_hero .ninja_image
.sub_nav
.app-links
#navMenuPro img
#proAd
.rec_default_image
#photo-container
#video-container

NO INVERT
#navMenuPro
#photo-container img
#video-container img
#video-container video

REMOVE BG
#nb20

================================

immutables.github.io

INVERT
.illustration
.documentation .highlight

================================

instagram.com

INVERT
._mli86
._cqw45._2pnef

CSS
[role="dialog"] {
    background-color: rgba(255, 255, 255, 0.5) !important;
}
._sxolz {
    background-color: #fff !important;
}

================================

io9.com

CSS
html {
    height: auto !important;
}

================================

iopscience.iop.org

NO INVERT
img[alt*="Equation"]

================================

java.com

INVERT
html #jvc0v2.bg1 .jvc0w1
html #jvc0v2.bg2 .jvc0w1
html #jvc0v2.bg3 .jvc0w1
html #jvc0v2.bg4 .jvc0w1
html #jvc0v2.bg5 .jvc0w1

================================

javarepl.com

INVERT
.terminal-content

REMOVE BG
body

================================

jcrew.com

CSS
.c-filters__refinement--label .refinement--label__checkbox svg path {
    fill: transparent !important
}
.c-filters__refinement--label.is-selected .refinement--label__checkbox svg path {
    fill: #fff !important
}

================================

jeremykun.com

NO INVERT
img[src*=".gif"]
img[src*=".png"]

================================

jira.*
jira.*.*

INVERT
.chart img

================================

journalisten.dk

INVERT
.top-logo

================================

juejin.im

NO INVERT
.equation

================================

jyllands-posten.dk

INVERT
.c-icon:not(.c-icon--fill-white)

================================

khanacademy.org

INVERT
.task-container .modal-backdrop

================================

kinopoisk.ru

INVERT
#GOWrapper
#external_header_wrapper
.header-fresh-search-partial-component_theme_default
.header-fresh-partial-component_theme_light
.footer-v2-partial-component__navigation-wrapper
.footer-v2-partial-component__bottom-navigation
.header-fresh-partial-component_theme_default .header-fresh-partial-component__dropdown
.info_title .gradient
.info_title .gradient_director
.movie-ticket-button
.movie-trailer-button
.randomMovie .name
.picAndNums
.promo-special
.flag
.main_info__quote
.feedback_img
.main_slider_arrow
#top_3banners
.yearsBox
.bottom_bg
.insert
.trailer_descr
.arrow
.play
.kp2-authapi-overlay
.kp2-authapi-paranja
.app__sticky-header
.search-suggest__content
.video-snippet__inner:after
.video-snippet__content
.tabs__tab_selected

NO INVERT
.right-slider p
#top_3banners img
.zod img
.peopleInfo table img
.date img
.rating img

REMOVE BG
#content_block
.box_block table table *

CSS
.discovery-trailers-overlay {
    background: rgba(255, 255, 255, 0.8) !important;
}
#popup_info_wrapper * {
    color: #bbb !important;
}

================================

kubernetes.io

INVERT
section.header-hero
footer

================================

last.fm

INVERT
div.masthead-logo
ul.navlist-items
div.navlist-more-wrap
div.header-title-label-wrap
div.grid-items-cover-image-image::after
div.grid-items-item-details
div.featured-item-details
div.top-bar
div.features-footer-cover-image::after
p.features-footer-content
button.btn-primary
a.btn-primary
footer

================================

lazada.com.my

INVERT
.lzd-logo-content

================================

learningsuite.byu.edu

INVERT
.sidebarCalendar
.calendarEmpty
.white

================================

libretexts.org

NO INVERT
img

================================

lifehacker.com

INVERT
.videoCube a .thumbBlock

CSS
html {
    height: auto !important;
}

================================

linerad.io

INVERT
.header h1 img

================================

linkedin.com

INVERT
#top-header
#responsive-nav-scrollable

NO INVERT
#top-header *
#responsive-nav-scrollable *

================================

linternaute.fr

INVERT
.dico_liste.grid_line > li

CSS
.dico_liste_greyandwhite .dico_liste li a {
    color: var(--darkreader-neutral-background);
}

================================

mail.live.com

INVERT
img:not([src="https://a.gfx.ms/rte_metro2.png"])
video
object
.ComposeContent

REMOVE BG
.ComposeContent

================================

mail.protonmail.com

CSS
.angular-squire-iframe body,
.angular-squire-iframe body div {
    background: #0D0E12 !important;
    color: #fff !important;
}

================================

mailchimp.com

INVERT
svg

NO INVERT
.cke_button_icon

================================

mathpages.com

NO INVERT
img

================================

mathprofi.ru
mathprofi.net

NO INVERT
p img

================================

mathworld.wolfram.com

NO INVERT
img

================================

matsci.org

NO INVERT
.d-header #site-logo

CSS
.category-logo.aspect-image img {
  background-color: white !important;
}

================================

medium.com

NO INVERT
.canvas-renderer

================================

meduza.io

INVERT
.Header-root
.Footer-root

================================

messenger.com

INVERT
._4tsk

================================

minhaclaro.claro.com.br

INVERT
.header-black
.header-red-desktop-logada
#txt-busca-header-desktop

================================

mixtape.moe/$

INVERT
body
.top > .menu
#upload-filelist
.file-url

================================

mspaintadventures.ru

INVERT
.row
.row img
.row object
.mspa_content_inner > .mspa_page_pictures
.adv_content_inner > .adv_page_pictures
.sidebar
.content
.footer
.copyright
.row:nth-of-type(1) > .header
.row:nth-of-type(1) > .header > .banner
.row:nth-of-type(1)[style*='width:940px'] div:not(.nav)

NO INVERT
.nav img
#page > .content
.row > .row
.content > .content
.mspa_content_inner > .mspa_page_text img
.adv_content_inner > .adv_page_text img

CSS
body {
    background-color: #fff !important;
}
.sidebar {
    border-color: #fff !important;
}
html,
.banner,
.row:nth-of-type(2),
.row:nth-of-type(4) {
    background-color: #000 !important;
}

================================

mullvad.net

INVERT
#Logo

================================

my.mail.ru/(music|mail|vk|ok|bk|list|gmail.com|inbox)

INVERT
.b-head-layer_music
.b-head__menu__logo
.b-head__portal-navigation-wrapper
.b-history-event__videoevent-name
.b-music__genre__header__info
.b-music__playlists--tile__item__controls
.b-music__playlists--tile__item__stats
.b-music__section__content
.b-music__section__footer
.b-music__section__row
.b-music__top-artists__item__cover
.b-music__top-artists__item__info
.b-music__user-header__content
.b-music__user-header__controls
.b-music__user-header__title
.b-popup__fade
.btn-import
.filed-images
.l-music__menu-main
.l-music__portal-navigation
.l-music__search-form
.l-music__sidebar__playlist-playing > .cover
.playlists-block
.smiles
.songs-table__row__col__cover
.ui-button-main

NO INVERT
html
.b-music__artist-header__content
.b-music__artist-header__cover
.b-music__section__cell
.fixed-menu > .b-music__section__footer
.l-mm__avatar
.l-music__player__song__cover
.playlists-block

REMOVE BG
.b-music__user-header__bg--no-profile-cover

CSS
.l-music {
    background: #111;
}

================================

myaccounts.capitalone.com

INVERT
c1-ease-core-features-hero-bar

================================

mydealz.de

INVERT
.nav
.search
.threadTempBadge-icon
.emoji
.comment-image
.avatar
#footer
#thread-comments
.cept-action-user-profile
.popover--mainNav
.supportImage
.profileHeader
.cept-thread-image-link
.cept-thread-image-clickout

NO INVERT
.cept-userProfile-avatar
.imgFrame-img

CSS
.cept-dealBtn, .btn--facebook{
    letter-spacing: 0.5px;
}
.twitter-share-button{
    letter-spacing: 1.5px;
}
.text--color-red, .vote-temp--hot, .vote-temp--burn, .icon--plus{
    color: #ff3d00;
}

================================

myfitnesspal.com

INVERT
#fancybox-overlay

================================

neopets.com

INVERT
#ban
#header

================================

nodejs.org

INVERT
#column2
footer
header

CSS
#column2.interior {
    background: #212121;
}
#column2 ul {
    background-color: #212121;
}

================================

nvidia.com

INVERT
.sub-brand-nav
.brandLink

================================

onliner.by

INVERT
.b-tile-main
.b-teasers-2__teaser
.news-tiles__subtitle
.b-opinions-main-2__tile
.news-header__top > .news-header__title
.b-tile-grad

NO INVERT
.b-opinions-main-2__tile *
.b-teasers-2__teaser-i

================================

op.gg

INVERT
.__sprite

================================

openclassrooms.com

INVERT
code.ace

NO INVERT
[class*=ace] *

================================

opencollective.com

INVERT
.CollectiveCover
.Footer
.MailChimpInputSection
.PublicFooter

NO INVERT
.CollectiveCover *
.Footer *
.MailChimpInputSection *
.PublicFooter *

================================

otakumode.com

INVERT
.c-slick-image-viewer__main-image img

================================

outlook.live.com

INVERT
#O365_NavHeader
span.ms-Pivot-text
span.ms-Pivot-icon
.ms-FocusZone.ms-CommandBar

================================

pagerduty.com

REMOVE BG
body

================================

pandora.com

INVERT
#body
.contentbox
#detailContainer
#mainContent
.slidesBackground
.slidesForeground

CSS
.skinContainer,
.slidesBackground,
.slidesForeground,
#trackDetail,
.track_detail_close {
    background-color: black !important;
}
.fxCol-cont-1,
.jspContainer,
#trackInfoButtons {
    background-color: white !important;
}
#brandingBar,
.contentnav,
.top {
    background-color: #3d4043 !important;
}
#playerBar {
    background-color: #0d0d0d !important;
}

================================

paper.dropbox.com

INVERT
.attrcomment.attrcomment

================================

perforce.com

INVERT
.title-bar
.sidenav-wrapper

================================

photos.google.com

INVERT
.DwJIde .QtDoYb svg

CSS
.ZSTBVb, .DwJIde .NRbSyd {
    background: white !important;
}

================================

play.afreecatv.com

INVERT
#afreecatv_player

================================

playfab.com

INVERT
.hero

================================

poeaffix.net

INVERT
#header

NO INVERT
img

================================

politiken.dk

INVERT
.pol-logo

================================

practice.geeksforgeeks.org

INVERT
.ace_dark

================================

prntscr.com
prnt.sc

INVERT
.header

================================

producthunt.com

INVERT
.backgroundImage_1hK9M
.v-image

NO INVERT
.backgroundImage_1hK9M *
.v-image *

CSS
.post-detail--body--gallery-fullscreen--image {
    background-color: black;
}

================================

python.org

INVERT
nav ul li > a
nav .tier-2
#site-navigation
header
.featured-news-wrapper
:not(.lang):not(#foot-logo) > img

NO INVERT
:not(.lang):not(#foot-logo) > img
#content > div > section > div

================================

quora.com

INVERT
.section_photo

================================

reactjs.org

INVERT
.gatsby-highlight
.css-mlkfzr
.css-17t02fm

================================

readthedocs.io

INVERT
.wy-nav-side
.rst-versions
li.current

================================

reddit.com/r/(homestuck|Undertale)

INVERT
#header
#header-bottom-right
#sr-header-area
.md h4

NO INVERT
#header-img

REMOVE BG
body

================================

reddit.com/r/europe

INVERT
.flair

================================

reddit.com/r/firefox

INVERT
#header
#header-bottom-right

================================

reddit.com/r/GlobalOffensive

INVERT
#header
.side

NO INVERT
#header *
.side *

CSS
#mail::before {
    display: none !important;
}

================================

reddit.com/r/Paranormal

INVERT
#header
#sr-header-area
#header-bottom-right

================================

reddit.com/r/skyrimmods

INVERT
#header
.infobar

================================

reference.wolfram.com

NO INVERT
img

================================

regexr.com

INVERT
canvas.highlights

================================

rijnijssel.elo.education-online.nl

NO INVERT
.app-left-sidebar

================================

roblox.com

INVERT
.thumbnail-span
.color-dot
.avatar-back

NO INVERT
.thumbnail-span *
.avatar-back *

CSS
.avatar-thumbnail .enable-three-dee {
     background-color: #000000;
     color: #FFFFFF;
}

================================

ruby.sketchup.com
rubydoc.info

INVERT
#navbar

NO INVERT
#navbar a > img
#nav

================================

scotch.io

INVERT
.language-js
.language-javascript

================================

script.google.com

INVERT
.docs-icon-img-container

================================

semlar.com

INVERT
.thumbnail
.col-sm-2
.img-rounded
#alert-list
.background-texture

NO INVERT
img

================================

shiyanlou.com

INVERT
#display

================================

shop.ubi.com

INVERT
#topNavbar
#UplayHeader

================================

slack.com

INVERT
.client_channels_list_container
figure
.member_preview_link.member_image

NO INVERT
.client_channels_list_container *
figure *

CSS
.emoji {
    background-color: white;
}

================================

slushpool.com

INVERT
.siteHeader
.contentHeader
.contentSlider

================================

spotify.com

INVERT
img
video
embed
.hero
.header-main
.search-bar
#bg-wrap
#main

================================

stackexchange.com
*.stackexchange.com
askubuntu.com
mathoverflow.net
serverfault.com
stackapps.com
superuser.com

INVERT
.top-bar

================================

stackexchange.com
askubuntu.com
mathoverflow.com
serverfault.com
stackapps.com
stackoverflow.com
superuser.com

CSS
#hlogo a {
    text-indent: -256em !important;
}

================================

statlect.com

NO INVERT
span.displayed > img
span.inm > img
#lensDIV img
.gs-image-box img.gs-image
img[alt="Table of Contents"]

================================

stiften.dk

INVERT
.logo

================================

studio.restlet.com
cloud.rest-let.com

INVERT
.left-panel
.header
.editor-ace

NO INVERT
.avatar

================================

subscene.com

INVERT
#logo

================================

subscription.packtpub.com

INVERT
.sidebar
.navbar
.cover-info-overlay

================================

symfony.com

INVERT
#sln
.header__top :not(img)
.highlighttable

NO INVERT
[class^="sln-visible-"] > img:first-child

================================

systemuicons.com

INVERT
img

================================

techpowerup.com

CSS
.page-is-loading {
    display: none !important;
}

================================

teddit.net
teddit.ggc-project.de
teddit.kavin.rocks

INVERT
.preview

================================

terraform.io

INVERT
.mega-nav-banner

================================

terrytao.wordpress.com

NO INVERT
img

================================

tianchi.aliyun.com

NO INVERT
img

================================

toggl.com

INVERT
.left-nav

================================

tpondemand.com

INVERT
.tau-cover-view__overlay
.tau-app-secondary-pane
.app-header
.uv-popover-content

NO INVERT
.tau-app-secondary-pane *
.app-header *

================================

tproger.ru

INVERT
.top-duft-punk
#colophon

NO INVERT
.text_logo img
.tmenu_icon

================================

translate.yandex.ru

CSS
.application.state-new .container_main,
.application.state-new .container_main[style],
.application.state-new .header {
    border-bottom-color: transparent;
}

================================

travis-ci.org

INVERT
.job-log

================================

trip101.com

INVERT
#homeaway-searchbox-searchtext
.footer
.hero-full-screen
.hero-section
.name
.tabs
.tabs-content

NO INVERT
.banner-img *
.logo *
.partners *
.partner-logo *
.tab-carrental *
.tab-hotel *
.tab-pa *
.tab-skyscanner *

================================

tvtime.com

NO INVERT
.page-sidebar *
.page-left *

================================

tweakers.net

INVERT
#menubar
#bottom
#tracker
#imageOverlay
#thumbContainer
.button
.hr

NO INVERT
#thumbContainer *
#bottom .button

================================

twitch.tv

INVERT
#site_footer
#carousel_background::after
#left_col
.player
.badge
.social-column
.top-nav
.anon-front__featured-section
.side-nav
.footer

NO INVERT
.player *
.social-column *
.top-nav *
.anon-front__featured-section *
.side-nav *
.footer *

REMOVE BG
#carousel
.chat-container
.conversations-list-header
.dark_wrapper ul
html
.nav
.js-conversations-list
.js-conversations-list-bottom-bar
.rightcol-content > .top

CSS
.chat_text_input,
.colon,
.dark_wrapper *,
.message,
.room-title,
.selected > a {
    color:black !important;
}
.chat_text_input {
    border: 1px solid black !important;
}

================================

twitter.com

INVERT
.tcu-textEllipse--multiline
.GalleryTweet .tweet
.Gallery-media
.DashboardProfileCard-bg
.SummaryCard-content

NO INVERT
.Gallery-media *

CSS
.gallery-overlay,
#permalink-overlay {
    background: rgba(255, 255, 255, 0.7) !important;
}

================================

ubi.com

REMOVE BG
html
body

================================

unicode.org

INVERT
.chars

================================

unicodelookup.com

CSS
body {
    background-color: #FFF !important;
}

================================

unity3d.com

INVERT
.main-navigation
.sub-nav
.wrapper
.unity-logo
.icon
.main-footer

================================

unity3d.com/learn

INVERT
.main-navigation
.sub-nav
.wrapper
.unity-logo
.bg-instructors
.hero
.bg-workflow

================================

unityhacks.com

INVERT
#unityhacks_sidebar
#exposeMask
#logoBlock
#navigation
#moderatorBar
.xenOverlay .sectionMain .heading overlayOnly
.banner_owner
.banner_staff
.banner_supportleader
.banner_supporter
.banner_partner
.banner_designer
.banner_promoter
.banner_honorable
.banner_premium
.messageHeading
.subHeading
.ToggleTrigger
.prefix
.PopupOpen
.categoryStrip
.secondaryContent h3
.LTR
.callToAction
.xenOverlay .section .heading
.xenTooltip
.redactor_dropdown
.errorOverlay
.sectionHeaders
.button
[title="Unread messages"]
footer

NO INVERT
#logo *
#taigachat_toolbar *
.listItemText h3
.pollBlock .pollResult div
.pollBlock .pollResult h3

================================

vergil.chemistry.gatech.edu/notes

NO INVERT
img

================================

virtua.speedtestcustom.com

NO INVERT
.branding

================================

vk.com

INVERT
#video_player
.mv_playlist
.page_header_cont
.page_album_title
.article_snippet__fade
.article_snippet__info
.article_snippet__read_btn
#layer_bg
#box_layer_bg
#pv_more_acts_tt
.login_app_devices
.friends_import_icon
.articleSnippet__inner
.articleSnippet_button
#z_photoview
.vv_summary
.thumb_map:after
.input_back_wrap[style*=none] ~ #ts_input
#ts_cont_wrap
#ts_cont_wrap img
#top_notify_wrap
#top_notify_wrap .feedback_sticky_icon
#audio_layer_tt
#audio_layer_tt [style*=background-image]
.audio_page_player_play .icon
.page_video_play_icon
.apps_featured_thumb_content
[class^=apps_featured]:before
.apps_frtt_photo
.apps_frtt_level
.settings_separated_row_iconed:before
.owner_photo_bubble_wrap
#top_profile_menu
.tt_black
.tt_w.tt_black
.box_title
.box_layer_wrap
#box_loader
.chat_tab_close
#notifiers_wrap
.notifier_image_wrap
.doc_ext
.doc_title
.like_tt::after
.audio_row__cover_back
.poster__text

NO INVERT
#video_player *
.mv_playlist *
.page_header_cont *
#z_photoview img
.box_grey .box_title

REMOVE BG
.mv_layer_bg
.pv_bottom_info
.pv_img_area_wrap
.vv_body
.like_tt

CSS
.pv_bottom_info *,
.like_tt {
    color: #000 !important;
}
#pv_more_acts_tt * {
    color: #eee !important;
}
#top_notify_wrap,
#audio_layer_tt {
    box-shadow: none;
}
.chat_onl_inner {
    background-color: #dae1e8 !important;
}
.chat_tab_wrap:hover {
    background-color: #ccd5de !important;
}
.photos_row {
    background-color: #000 !important;
    border-color: #000 !important;
}

================================

vocabolario.sns.it

INVERT
img[src]

NO INVERT
body[background]

================================

vuejs.org

INVERT
.content
pre
h3
p

NO INVERT
img

================================

w3schools.com

INVERT
#topnav

================================

wallet.trezor.io

INVERT
.page-header
.qr-code

REMOVE BG
.qr-code

================================

web.telegram.org

INVERT
.im_dialog_unread
.icon-message-status
.dropdown-toggle
.icon-tg-title

================================

web.whatsapp.com

INVERT
.qrcode .icon-logo

CSS
.qrcode {
    border: 8px solid black;
}

================================

wikipedia.org

INVERT
.mwe-popups-discreet > svg
.mw-ext-score
.mw-mmv-overlay
#Vorlage_Infobox_Chemikalie > tbody > tr:nth-child(2) > td > a

NO INVERT
.mwe-math-fallback-image-inline
.mwe-math-fallback-image-display
.mwe-popups image
img[src*="svg.png"]
img[alt^="Skeletal" i]
img[alt^="Structural" i]

================================

wikiwand.com

REMOVE BG
img

================================

wolfram.com

NO INVERT
.numberedequation
.displayformula
.inlineformula

================================

wolframalpha.com

NO INVERT
img

================================

xfree86.org

NO INVERT
img[src*=".png"]

================================

yandex.*

INVERT
body.b-page_theme_pure_night
[class*="icon_moon_"]
.favicon
.weather__icon
.traffic__icon
.dist-popup__image
.footer_distro_yes
.serp-footer
.distro__icon
.services-big__item_icon
.services-all__icon
.popup2:before
html.i-ua_swipe_yes .informers7__icon
html.i-ua_swipe_yes .zen__item-domain
html.i-ua_swipe_yes .zen__item-title
html.i-ua_swipe_yes .teaser__service-bg
html.i-ua_swipe_yes .stream-intro
html.i-ua_swipe_yes .edadeal
html.i-ua_swipe_yes .afisha
html.i-ua_swipe_yes .services__icon
html.i-ua_swipe_yes .menu2__list

NO INVERT
body.b-page_theme_pure_night *
html.i-ua_swipe_yes .stream-intro__vod-preview
html.i-ua_swipe_yes .edadeal__item-img
html.i-ua_swipe_yes .afisha__film-image

REMOVE BG
html.i-ua_swipe_yes .mini-suggest__popup-spacer
html.i-ua_swipe_yes .menu2__container

================================

youtube.com

INVERT
html[dark=true] #scrim
.html5-video-player
.has-custom-banner
#theater-background
#watch-appbar-playlist
html:not([dark=true]) #header > #background
html:not([dark=true]) #links-holder yt-formatted-string
html:not([dark=true]) #subscribe-button
ytd-thumbnail-overlay-time-status-renderer
ytd-thumbnail-overlay-toggle-button-renderer
paper-toast
ytd-masthead[dark]
html:not([dark=true]) ytd-masthead[dark] #avatar-btn

NO INVERT
html[dark=true]
html[dark=true] *
.html5-video-player *
.has-custom-banner *
#watch-appbar-playlist *

CSS
#scrim.app-drawer {
    background: rgba(255, 255, 255, 0.5);
}
#player-theater-container {
    background: none !important;
}

================================

yscec.yonsei.ac.kr

INVERT
iframe
#page-sidebar
