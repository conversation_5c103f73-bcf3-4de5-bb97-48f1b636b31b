! async function() {
    //当前网址是否符合脚本指定运行的网址
    const urlString = window.location.href;
    
    var browser = window.browser;

    var GM = {};
    const valueListeners = new Map();
    
    GM.info = JSON.parse(JSON.stringify({{GMScriptInfo}}));

    let isActive = GM.info.script.isActive;
    if(!isActive) {
        //没有激活
        return;
    }
    
    let uuid = GM.info.script.uuid;
    
    let _includes = GM.info.script.includes ? GM.info.script.includes : [];
    let _matches  = GM.info.script.matches ? GM.info.script.matches : [];
    let _excludes = GM.info.script.excludes ? GM.info.script.excludes : [];
    
    function isObjectNull(obj) {
        return obj == null || Object.keys(obj).length === 0;
    }
    
    function isMatchUrl() {
        let isMatched = false;
        
        function matchRule(regex, str) {
            var escapeRegex = (str) => str.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
            return new RegExp("^" + regex.split("*").map(escapeRegex).join(".*") + "$").test(str);
        }
        
        if(isObjectNull(_includes) == false) {
            for(let regex of _includes) {
                if(regex == '*' || matchRule(regex, urlString)) {
                    isMatched = true;
                    break;
                }
            }
        }
        
        if(isObjectNull(_matches) == false) {
            for(let regex of _matches) {
                if(regex == '*' || matchRule(regex, urlString)) {
                    isMatched = true;
                    break;
                }
            }
        }
        
        if(isObjectNull(_excludes) == false) {
            for(let regex of _excludes) {
                if(regex == '*' || matchRule(regex, urlString)) {
                    isMatched = false;
                    break;
                }
            }
        }
        
        return isMatched;
    }
    
    let isMatched = isMatchUrl();
        
    //脚本不适合当前网址,直接返回
    if(!isMatched) {
        return;
    } else {
    }
    
    const unsafeWindow = window;
    
    if(window && window.__GMHandlers__ == null) {
        window.__GMHandlers__ = {};
    }
    
    window.__GMHandlers__[uuid] = {};
    
    //菜单
    var __GMUserScriptMenu__ = {};
    
    //加载Resource
    GM.__ResourceData__ = await _getResourceData() || {};
    GM.__Data__ = await _getDataValue() || {};
    
    //加载所有的key-value存储
    function _getDataValue() {
        return new Promise(resolve => {
            const message = {
                 operate: "API_LIST_VALUE",
                scriptId: uuid
            };
            browser.runtime.sendMessage(message, function(response) {
                resolve(response.body);
            });
        });
    }
    
    //@resource
    function _getResourceData() {
        return new Promise(resolve => {
            const message = {
                 operate: "API_GET_RESOURCE_URLS",
                scriptId: uuid
            };
            browser.runtime.sendMessage(message, response => resolve(response.body));
        });
    };
    
    const _userScriptFunc = function() {
            
        try {
            
            {{GMRequireContent}}
            
            {{GMScriptContent}}
                
        } catch (e) {
            console.error(e);
        };
    };
    
    //根据runAt运行用户脚本
    const _runUserScript = function(runAt) {
        if (runAt === "document_start" || runAt === "document_body") {
            if (document.readyState === "loading") {
                document.addEventListener("readystatechange", function() {
                    if (document.readyState === "interactive") {
                        _userScriptFunc();
                    }
                });
            } else {
                _userScriptFunc();
            }
        } else if (runAt === "document_end") {
            if (document.readyState !== "loading") {
                 _userScriptFunc();
            } else {
                document.addEventListener("DOMContentLoaded", function() {
                    _userScriptFunc();
                });
            }
        } else {
            //document_idle
            if (document.readyState === "complete") {
                _userScriptFunc();
            } else {
                document.addEventListener("readystatechange", function(e) {
                    if (document.readyState === "complete") {
                        _userScriptFunc();
                    }
                });
            }
        }
    };
    
    GM.closeTab = function(tabId) {
        return new Promise(resolve => {
            const message = {
                operate: "API_CLOSE_TAB",
                tabId: tabId
            };
            browser.runtime.sendMessage(message, response => resolve(response));
        });
    };

    GM.openInTab = function(url, optionsOrLoadInBackground) {
        if (!url) return console.error("openInTab missing url arg");
        return new Promise(resolve => {
            let message = {
                operate: "API_OPEN_TAB",
                url: url,
            };
            
            if(typeof optionsOrLoadInBackground === 'boolean') {
                message.active = !optionsOrLoadInBackground;
            } else if(typeof optionsOrLoadInBackground === 'object' && optionsOrLoadInBackground !== null) {
                let options = optionsOrLoadInBackground
                if(options.active !== undefined) {
                    message.active = options.active;
                } else if(options.loadInBackground !== undefined) {
                    message.active = !options.loadInBackground
                }
            }
            
            if(message.active === undefined) {
                message.active = false;
            }
            
            browser.runtime.sendMessage(message, response => resolve(response));
        });
    };

    GM._setValue = function(name, value) {
        try {
            if(name == null) return;
            
            const uuid = scriptUUID();
            
            let data = GM.__Data__ || {};
            let oldValue = data[name];
            data[name] = value;
            
            const message = {
                operate: "API_SET_VALUE",
                uuid: uuid,
                key: name,
                value: value
            };
            
            browser.runtime.sendMessage(message, function (response) {
            });
            
            handleValueChangeIfNeed(name, oldValue, value);
        } catch(error) {
            console.log("error = " + error);
        }
    }

    GM.setValue = function(name, value) {
        return new Promise(resolve => {
            try {
                if(name == null) {
                    resolve();
                    return;
                }
                
                const uuid = scriptUUID();
                
                let data = GM.__Data__ || {};
                let oldValue = data[name];
                data[name] = value;
                
                const message = {
                    operate: "API_SET_VALUE",
                    uuid: uuid,
                    key: name,
                    value: value
                };
                
                browser.runtime.sendMessage(message, function (response) {
                });
                
                handleValueChangeIfNeed(name, oldValue, value);
                
                resolve();
            } catch(error) {
                console.log("error = " + error);
                
                resolve();
            }
        });
    };

    GM._getValue = function(name, defaultValue) {
        let data = GM.__Data__ || {};
        let result = data[name];
        //区分null和undefined
        if(result === null && defaultValue === undefined) {
            return result;
        } else if(result == null) {
            return defaultValue;
        }
        
        return result;
    }

    GM.getValue = function(name, defaultValue) {
        return new Promise(function(resolve, reject) {
            let data = GM.__Data__ || {};
            let result = data[name];
            //区分null和undefined
            if(result === null && defaultValue === undefined) {
                resolve(result);
            } else if(result == null) {
                resolve(defaultValue);
            } else {
                resolve(result);
            }
        });
    };

    GM._deleteValue = function(name) {
        if(name == null) return;
        
        let data = GM.__Data__ || {};
        let oldValue = data[name];
        delete data[name];
        
        const uuid = scriptUUID();
        
        const message = {
            operate: "API_DELETE_VALUE",
            uuid: uuid,
            key: name
        };
        browser.runtime.sendMessage(message, function (response) {
        });
        
        handleValueChangeIfNeed(name, oldValue, undefined);
    }

    GM.deleteValue = function(name) {
        return new Promise(function(resolve, reject) {
            if(name == null) {
                resolve({success: false});
                return;
            }
            
            let data = GM.__Data__ || {};
            let oldValue = data[name];
            delete data[name];
            
            const uuid = scriptUUID();
            
            const message = {
                operate: "API_DELETE_VALUE",
                uuid: uuid,
                key: name
            };
            browser.runtime.sendMessage(message, function (response) {
            });
            
            handleValueChangeIfNeed(name, oldValue, undefined);
            resolve({success: true});
        });
    };

    GM._listValues = function() {
        let result = [];
        let data = GM.__Data__ || {};
        
        for(let key in data) {
            result.push(key);
        }
        
        return result;
    }

    GM.listValues = function() {
        return new Promise(function(resolve, reject) {
            let result = [];
            let data = GM.__Data__ || {};
            
            for(let key in data) {
                result.push(key);
            }
            
            resolve(result);
        });
    };

    GM.getResourceText = function(key) {
        for(var index in GM.__ResourceData__) {
            var item = GM.__ResourceData__[index];
            if(item.key === key) {
                var text = item.text;
                return text ? text : null;
            }
        }
        
        return null;
    }
    
    GM._getResourceURL = function(key) {
        for(var index in GM.__ResourceData__) {
            var item = GM.__ResourceData__[index];
            if(item.key === key) {
                var url = item.url;
                return url ? url : null;
            }
        }
        
        return null;
    };

    GM.getResourceUrl = function(key) {
        return new Promise(function(resolve, reject) {
            for(var index in GM.__ResourceData__) {
                var item = GM.__ResourceData__[index];
                if(item.key === key) {
                    var url = item.url;
                    resolve(url ? url : null);
                }
            }
            
            resolve(null);
        });
    };
    
    GM._addStyle = function(css) {
        if (typeof css !== "string") {
            return;
        }

        let styleElement = document.createElement("style");
        styleElement.type = "text/css";

        try {
            styleElement.appendChild(document.createTextNode(css));
        } catch (error) {
            styleElement.styleSheet.cssText = css;
        }

        try {
            if (document.head) {
                document.head.appendChild(styleElement);
            } else {
                const htmlElement = document.documentElement;
                htmlElement.appendChild(styleElement);

                const observer = new MutationObserver(() => {
                    if (document.head) {
                        observer.disconnect();
                        if (styleElement.isConnected) {
                            document.head.appendChild(styleElement);
                        }
                    }
                });

                observer.observe(htmlElement, { childList: true });
            }
        } catch (error) {}
        
        return styleElement;
    }

    GM.addStyle = function(css) {
        if (typeof css !== "string") {
            return console.error("async addStyle invalid css arg");
        }
        return new Promise(resolve => {
            const message = {
                operate: "API_ADD_STYLE",
                css: css
            };
            browser.runtime.sendMessage(message, response => resolve(response));
        });
    };
    
    GM.addElement(parentNode, tagName, attributes = {}) {
        // Content script 可以直接访问 DOM
        let parent;
        if (typeof parentNode === 'string') {
          parent = document.querySelector(parentNode);
        } else if (parentNode instanceof Element) {
          parent = parentNode;
        } else {
          parent = document.body;
        }

        const element = document.createElement(tagName);
        
        Object.entries(attributes).forEach(([key, value]) => {
          if (key === 'style' && typeof value === 'object') {
            Object.assign(element.style, value);
          } else if (key === 'dataset' && typeof value === 'object') {
            Object.assign(element.dataset, value);
          } else if (key.startsWith('on') && typeof value === 'function') {
            // Content script 中的函数会在 content script 上下文中执行
            element.addEventListener(key.slice(2), value);
          } else {
            element.setAttribute(key, value);
          }
        });

        parent.appendChild(element);
        return element;
    }

    GM.getTab = function() {
        return new Promise(resolve => {
            const message = {
                operate: "API_GET_TAB"
            };
            browser.runtime.sendMessage(message, response => {
                resolve(response);
            });
        });
    };

    GM.saveTab = function(tab) {
        if (tab == null) return console.error("saveTab invalid arg");
        return new Promise(resolve => {
            const message = {
                operate: "API_SAVE_TAB",
                    tab: tab
            };
            browser.runtime.sendMessage(message, response => {
                resolve(response);
            });
        });
    };

    GM.setClipboard = function(data, type) {
        return new Promise(resolve => {
            const message = {
                operate: "API_SET_CLIPBOARD",
                data: data,
                type: type
            };
            browser.runtime.sendMessage(message, response => {
                resolve(response);
            });
        });
    }
    
    GM.xmlhttpRequest = function(details) {
        let url = new URL(details.url, window.document.baseURI);
        let randomId = _uuid();
                    
        window.__GMHandlers__[uuid][randomId] = {
            'onload': details.onload ? details.onload : null,
            'onerror': details.onerror ? details.onerror : null,
            'onreadystatechange': details.onreadystatechange ? details.onreadystatechange : null,
            'ontimeout': details.ontimeout ? details.ontimeout : null
        };
        
        let data = {
            'uuid': uuid,
            'randomId': randomId,
            'method': details.method ? details.method : 'GET',
            'url': url.href,
            'data': details.data ? details.data : null,
            'headers': details.headers ? details.headers : null,
            'timeout': details.timeout ? details.timeout : 30000, //注意单位是毫秒
            'responseType': details.responseType ? details.responseType : ''
        };
        
        const message = {
            operate: "API_Request",
            data: data,
            scriptId: scriptUUID(),
        };
                
        browser.runtime.sendMessage(message, function(response) {
            let data = response.body;
            if(data.randomId !== randomId || response.type !== "API_Request") return;
            
            try {
                let responseCode = data.responseCode;
                
                if(responseCode == 1 || responseCode == 3) {
                    GM.handleResponse(data);
                } else if(responseCode == 2) {
                    GM.handleTimeoutResponse(data);
                }
            } catch (e) {
                console.error("error: xmlhttpRequest response error");
            }
        });
        
        return {
            abort: () => {
            }
        };
    };
    
    GM.handleResponse = function(param) {        
        let uuid = param.uuid;
        let randomId = param.randomId;

        //https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders
        //responseHeader的格式化

        var data = {
            'readyState': 4,
            'responseHeaders': param.responseHeader,
            'finalUrl': param.responseUrl,
            'status': param.statusCode,
            'statusText': param.readyState == 4 ? 'OK' : ''
        };

        if(param.statusCode === 200) {
            var responseObject;

            let parser = new DOMParser();
            let domXML = parser.parseFromString(param.responseContent, "text/xml");

            switch (param.responseType) {
                case "":
                responseObject = param.responseContent;
                break;

                case "text":
                responseObject = param.responseContent;
                break;

                case "blob":
                {
                    let binaryString = atob(param.responseContent);
                    let bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }
                    let mimeType = param.mimeType;
                    let blob = new Blob([bytes], {type: mimeType});
                    responseObject = blob;
                }
                break;
                
                case "arraybuffer":
                {
                    let binaryString = atob(param.responseContent);
                    let bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }
                    responseObject = bytes.buffer;
                }
                break;

                case "document":
                responseObject = domXML;
                break;

                case "json":
                responseObject = JSON.parse(param.responseContent);
                break;
            }

            data.responseXML = domXML;
            data.responseText = param.responseContent;
            data.response = responseObject;

            if(window.__GMHandlers__[uuid] == null
               || window.__GMHandlers__[uuid][randomId] == null) {
                return;
            }

            let callback = window.__GMHandlers__[uuid][randomId]['onload'];
            if(callback != null) {
                callback.call(this, data);
            }
        } else {
            data.responseXML = null;
            data.responseText = "";
            data.response = null;

            if(window.__GMHandlers__[uuid] == null
               || window.__GMHandlers__[uuid][randomId] == null) {
                return;
            }

            let callback = window.__GMHandlers__[uuid][randomId]['onerror'];
            if(callback != null) {
                callback.call(this, data);
            }
        }
    };

    GM.handleTimeoutResponse = function(param) {
        let uuid = param.uuid;
        let randomId = param.randomId;
        
        if(window.__GMHandlers__[uuid] == null
           || window.__GMHandlers__[uuid][randomId] == null) {
            return;
        }

        let callback = window.__GMHandlers__[uuid][randomId]['ontimeout'];
        if(callback != null) {
            let uuid = param.uuid;
            let randomId = param.randomId;
            callback.call(this);
        }
    };
    
    function scriptUUID() {
        return GM.info.script.uuid;
    }
    
    function _uuid() {
        var temp_url = URL.createObjectURL(new Blob());
        var uuid = temp_url.toString();
        URL.revokeObjectURL(temp_url);
        return uuid.substr(uuid.lastIndexOf("/") + 1);
    }
    
    GM.registerMenuCommand = function(name, fn=()=>{}, accessKey=null) {
        let pid = _uuid();
        
        __GMUserScriptMenu__[pid] = fn;
    
        const message = {
             operate: "API_Register_Command",
            scriptId: uuid,
                name: name,
            randomId: pid,
        };
        browser.runtime.sendMessage(message, function(response) {
        });
        
        return pid;
    };
    
    function _runCommand() {
        browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if(request.operate == "API_Run_Command") {
                let injectMode = request.injectMode;
                if(injectMode == 0 || injectMode == 2) {
                    let scriptId = request.scriptId;

                    const uuid = scriptUUID();
                    if(scriptId == uuid) {
                        let pid = request.randomId;
                        let callback = __GMUserScriptMenu__[pid];
                        
                        if(callback != null) {
                            callback.call(this);
                        }
                    }
                    
                    sendResponse({});
                }
            }
        });
    }
    _runCommand();

    GM.unregisterMenuCommand = function(pid) {
        var element = __GMUserScriptMenu__[pid];

        delete __GMUserScriptMenu__[pid];
        
        const uuid = scriptUUID();
        const message = {
             operate: "API_UnRegister_Command",
            scriptId: uuid,
            randomId: pid,
        };
        browser.runtime.sendMessage(message, function(response) {
        });
    };
    
    GM.download = function(details) {
        
        if(isObjectNull(details)) return;
        
        let url = details.url;
        let name = details.name;
        
        if(url == null || name == null) return;
        
        let schemeUrl = "addons://download?src=" + btoa(url)
        + "&name=" + (name==null?"":name);

        window.open(schemeUrl);
        
        const abort = () => {};
        return {
            abort: abort
        };
    };
    
    GM._addValueChangeListener = function (name, fn) {
        const listenerId = _uuid();
        
        if (!valueListeners.has(name)) {
            valueListeners.set(name, new Map());
        }
            
        valueListeners.get(name).set(listenerId, fn);
        
        return listenerId;
    }
    
    GM.addValueChangeListener = function (name, fn) {
        return new Promise(resolve => {
            const listenerId = _uuid();
            
            if (!valueListeners.has(name)) {
                valueListeners.set(name, new Map());
            }
                
            valueListeners.get(name).set(listenerId, fn);
            
            resolve(listenerId);
        });
    }
    
    GM._removeValueChangeListener = function(listenerId) {
        if(listenerId == null) return false;
        
        for (const [name, listeners] of valueListeners) {
            if (listeners.has(listenerId)) {
                listeners.delete(listenerId);
                
                if (listeners.size === 0) {
                    valueListeners.delete(name);
                }
                
                return true;
            }
        }
        
        return false;
    }
    
    GM.removeValueChangeListener = function(listenerId) {
        return new Promise(resolve => {
            if(listenerId == null) {
                resolve();
                return;
            }
            
            for (const [name, listeners] of valueListeners) {
                if (listeners.has(listenerId)) {
                    listeners.delete(listenerId);
                    
                    if (listeners.size === 0) {
                        valueListeners.delete(name);
                    }
                }
            }
            
            resolve();
        })
    }
    
    let handleValueChangeIfNeed = function(name, oldValue, value) {
        // 触发监听器
        if (valueListeners.has(name)) {
            // 只有当值真正发生变化时才触发监听器
            if (!isValueEqual(oldValue, value)) {
                const listeners = valueListeners.get(name);
                listeners.forEach(callback => {
                    try {
                        callback(name, oldValue, value, false);
                    } catch (error) {
                        console.error('Error in value change listener:', error);
                    }
                });
            }
        }
    }
    
    //检查两个值是否相等（深度比较）
    function isValueEqual(value1, value2) {
        // 处理 null 和 undefined
        if (value1 === value2) return true;
        if (value1 == null || value2 == null) return false;
        
        // 获取值的类型
        const type1 = typeof value1;
        const type2 = typeof value2;
        
        // 类型不同，直接返回false
        if (type1 !== type2) return false;
        
        // 对于对象和数组进行深度比较
        if (type1 === 'object') {
            // 数组比较
            if (Array.isArray(value1) && Array.isArray(value2)) {
                if (value1.length !== value2.length) return false;
                return value1.every((item, index) => isValueEqual(item, value2[index]));
            }
            
            // 普通对象比较
            const keys1 = Object.keys(value1);
            const keys2 = Object.keys(value2);
            
            if (keys1.length !== keys2.length) return false;
            
            return keys1.every(key =>
                Object.prototype.hasOwnProperty.call(value2, key) &&
                isValueEqual(value1[key], value2[key])
            );
        }
        
        // 处理特殊类型
        if (value1 instanceof Date && value2 instanceof Date) {
            return value1.getTime() === value2.getTime();
        }
        
        if (value1 instanceof RegExp && value2 instanceof RegExp) {
            return value1.toString() === value2.toString();
        }
        
        // 其他类型直接比较
        return value1 === value2;
    }
    
    GM.log = console.log;
    GM.notification = function(text, title=null, image=null, onclick=()=>{}) {};
    
    const GM_info = GM.info ? GM.info : undefined;
    const GM_addStyle = GM._addStyle ? GM._addStyle : undefined;
    const GM_deleteValue = GM._deleteValue ? GM._deleteValue : undefined;
    const GM_listValues = GM._listValues ? GM._listValues : undefined;
    const GM_getValue = GM._getValue ? GM._getValue : undefined;
    const GM_setValue = GM._setValue ? GM._setValue : undefined;
    const GM_addValueChangeListener = GM._addValueChangeListener ? GM._addValueChangeListener : undefined;
    const GM_removeValueChangeListener = GM._removeValueChangeListener ? GM._removeValueChangeListener : undefined;
    
    GM.xmlHttpRequest = GM.xmlhttpRequest;
    const GM_xmlhttpRequest = GM.xmlhttpRequest ? GM.xmlhttpRequest : undefined;
    const GM_xmlHttpRequest = GM.xmlhttpRequest ? GM.xmlhttpRequest : undefined;
    
    const GM_getResourceText = GM.getResourceText ? GM.getResourceText : undefined;
    const GM_getResourceURL = GM._getResourceURL ? GM._getResourceURL : undefined;
    const GM_download = GM.download ? GM.download : undefined;
    const GM_setClipboard = GM.setClipboard ? GM.setClipboard : undefined;
    const GM_openInTab = GM.openInTab ? GM.openInTab : undefined;
    const GM_getTab = GM.getTab ? GM.getTab : undefined;
    const GM_saveTab = GM.saveTab ? GM.saveTab : undefined;
    const GM_getTabs = GM.getTabs ? GM.getTabs : undefined;
    
    const GM_log = GM.log ? GM.log : undefined;
    const GM_notification = GM.notification ? GM.notification : undefined;
    const GM_registerMenuCommand = GM.registerMenuCommand ? GM.registerMenuCommand : undefined;
    const GM_unregisterMenuCommand = GM.unregisterMenuCommand ? GM.unregisterMenuCommand : undefined;
    
    window.GM = GM;
    
    window.GM_info = GM_info;
    window.GM_addStyle = GM_addStyle;
    window.GM_deleteValue = GM_deleteValue;
    window.GM_listValues = GM_listValues;
    window.GM_getValue = GM_getValue;
    window.GM_setValue = GM_setValue;
    window.GM_addValueChangeListener = GM_addValueChangeListener;
    window.GM_removeValueChangeListener = GM_removeValueChangeListener;
    window.GM_xmlhttpRequest = GM_xmlhttpRequest;
    window.GM_xmlHttpRequest = GM_xmlHttpRequest;
    window.GM_getResourceText = GM_getResourceText;
    window.GM_getResourceURL = GM_getResourceURL;
    window.GM_download = GM_download;
    window.GM_log = GM_log;
    window.GM_notification = GM_notification;
    window.GM_getTab = GM_getTab;
    window.GM_saveTab = GM_saveTab;
    window.GM_getTabs = GM_getTabs;
    window.GM_openInTab = GM_openInTab;
    window.GM_registerMenuCommand = GM_registerMenuCommand;
    window.GM_unregisterMenuCommand = GM_unregisterMenuCommand;
    
    try {
        window.globalThis.unsafeWindow = window;
    } catch (error) {
        console.error(error);
    }
    
    _runUserScript(GM.info.script.runAt);
} ();
