));

let isActive = GM.info.script.isActive;
if(!isActive) {
    //没有激活
    return;
}

let uuid = GM.info.script.uuid;

let _includes = GM.info.script.includes ? GM.info.script.includes : [];
let _matches  = GM.info.script.matches ? GM.info.script.matches : [];
let _excludes = GM.info.script.excludes ? GM.info.script.excludes : [];

function isObjectNull(obj) {
    return obj == null || Object.keys(obj).length === 0;
}

function isMatchUrl() {
    let isMatched = false;
    
    function matchRule(regex, str) {
        var escapeRegex = (str) => str.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
        return new RegExp("^" + regex.split("*").map(escapeRegex).join(".*") + "$").test(str);
    }
    
    if(isObjectNull(_includes) == false) {
        for(let regex of _includes) {
            if(regex == '*' || matchRule(regex, urlString)) {
                isMatched = true;
                break;
            }
        }
    }
    
    if(isObjectNull(_matches) == false) {
        for(let regex of _matches) {
            if(regex == '*' || matchRule(regex, urlString)) {
                isMatched = true;
                break;
            }
        }
    }
    
    if(isObjectNull(_excludes) == false) {
        for(let regex of _excludes) {
            if(regex == '*' || matchRule(regex, urlString)) {
                isMatched = false;
                break;
            }
        }
    }
    
    return isMatched;
}

let isMatched = isMatchUrl();
    
if(!isMatched) {
    return;
} else {
}

const unsafeWindow = window;

if(window && window.__GMHandlers__ == null) {
    window.__GMHandlers__ = {};
}

window.__GMHandlers__[uuid] = {};

var __GMUserScriptMenu__ = {};

GM.__ResourceData__ = await _getResourceData() || {};
GM.__Data__ = await _getDataValue() || {};

function _getDataValue() {
    return new Promise(resolve => {
        const message = {
             operate: "API_LIST_VALUE",
            scriptId: uuid
        };
        browser.runtime.sendMessage(message, function(response) {
            resolve(response.body);
        });
    });
}

function _getResourceData() {
    return new Promise(resolve => {
        const message = {
             operate: "API_GET_RESOURCE_URLS",
            scriptId: uuid
        };
        browser.runtime.sendMessage(message, response => resolve(response.body));
    });
};

const _userScriptFunc = function() {
        
    try {



