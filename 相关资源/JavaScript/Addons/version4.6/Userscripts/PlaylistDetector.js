async function playlistDetector() {
    ///语言本地化
    function localizableKey(key) {
        let objs = {
            zh: {
                videolist: "视频列表",
                download: "下载",
                copySuccessful: "复制成功",
                encryptMessage: "这是一个加密链接，无法缓存！"
            },
            en: {
                videolist: "Video Lists",
                download: "Save",
                copySuccessful: "Copy Successful",
                encryptMessage: "This is an encrypted link and cannot be cached!"
            }
        }

        function _current() {
            let e = navigator.languages;
            if(e.length > 0) {
                e = navigator.languages[0];
            } else if(navigator.language) {
                e = navigator.language;
            } else if(navigator.userLanguage) {
                e = navigator.userLanguage;
            } else {
                e = "en";
            }
            e = (e = e.toLowerCase()).replace(/-/, "_");

            if(e.indexOf("zh") > -1) {
                return objs.zh;
            } else {
                return objs.en;
            }
        }

        let values = _current();
        return values[key];
    }
    
    /**
     * 从文档中提取文章标题
     * @param {Document} doc - 要提取标题的文档对象
     * @returns {string} 提取的标题
     */
    function extractArticleTitle(doc) {
        if (!doc) return '';

        try {
            // 1. 尝试从meta标签获取标题
            const getMetaTitle = (selector) => {
                const metaEl = doc.querySelector(selector);
                if (metaEl && metaEl.content) {
                    return metaEl.content.trim();
                }
                return null;
            };

            // 2. 获取各种可能的标题来源
            const docTitle = doc.title;
            const ogTitle = getMetaTitle("head meta[property='og:title']");
            const twitterTitle = getMetaTitle("head meta[name='twitter:title']");

            // 3. 查找页面中的标题元素
            const titleSelectors = "h1, h2, h3, h4, h5, h6, .headline, .article_title, #hn-headline, .inside-head, .instapaper_title";
            const titleElements = doc.querySelectorAll(titleSelectors);

            // 4. 如果有明显的标题元素，使用它
            // 使用倒序查找(因为有多个标题同时存在的情况)可见的标题元素
            for (const el of [...titleElements].reverse()) {
                const text = el.textContent.trim();
                if (text && el.offsetParent !== null) {
                    return text;
                }
            }

            // 5. 返回找到的最佳标题
            if (ogTitle) return ogTitle;
            if (twitterTitle) return twitterTitle;
            if (docTitle) return docTitle;

            return '';
        } catch (e) {
            return doc.title || '';
        }
    }

    /// toast
    function _showToast(msg) {
        const toast = $('<div class="addons_toast"></div>');
        $('body').append(toast);
        toast.css({
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          backgroundColor: '#303030',
          color: 'white',
          fontSize: '14px',
          fontWeight: 'bold',
          padding: '8px 12px',
          borderRadius: '15px',
          zIndex: '2147483647',
          whiteSpace: 'nowrap'
        });

        $(toast).text(msg);

        toast.fadeIn(250).delay(3000).fadeOut(250, function() {
          $(this).remove();
        });
    }

    /// 本地缓存帮助类
    class StorageHelper {
        // 保存数据
        static async setItem(key, value) {
            return new Promise((resolve, reject) => {
                let items = {};
                items[key] = value;
                chrome.storage.local.set(items, function() {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve();
                    }
                });
            });
        }

        // 读取数据
        static async getItem(key) {
            return new Promise((resolve, reject) => {
                chrome.storage.local.get([key], function(result) {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(result[key]);
                    }
                });
            });
        }

        // 删除数据
        static async removeItem(key) {
            return new Promise((resolve, reject) => {
                chrome.storage.local.remove([key], function() {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve();
                    }
                });
            });
        }

        //针对两层字典嵌套的情况，删除数据
        static async removeSubItem(key, objectId, subKey) {
            try {
                let objectMap = await this.getItem(key) || {};
                let objectData = objectMap[objectId] || {};

                delete objectData[subKey];
                objectMap[objectId] = objectData;

                await this.setItem(key, objectMap);
            } catch(error) {
            }
        }

        // 增量更新数据
        static async updateItem(key, value) {
            try {
                const existingValue = await this.getItem(key) || {};
                const newValue = { ...existingValue, ...value };
                await this.setItem(key, newValue);
            } catch (error) {
            }
        }

        //针对两层字典嵌套的情况（例如脚本数据），增量更新数据
        static async updateSubItem(key, objectId, subKey, value) {
            try {
                let objectMap = await this.getItem(key) || {};
                let objectData = objectMap[objectId] || {};

                objectData[subKey] = value;
                objectMap[objectId] = objectData;

                await this.setItem(key, objectMap);
            } catch (error) {
            }
        }

        // 获取所有数据
        static async getAllItems() {
            return new Promise((resolve, reject) => {
                chrome.storage.local.get(null, function(items) {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve(items);
                    }
                });
            });
        }

        // 清理所有数据
        static async clearAllItems() {
            return new Promise((resolve, reject) => {
                chrome.storage.local.clear(function() {
                    if (chrome.runtime.lastError) {
                        reject(chrome.runtime.lastError);
                    } else {
                        resolve();
                    }
                });
            });
        }
    }

    //获取开关信息
    let config = await StorageHelper.getItem('addonsConfig');

    //初始化，等待数据加载
    if(config == null || config.sniffer == null) return;
    //没有打开嗅探总开关，直接返回
    if(!config.sniffer.isActive) return;

    /// 视频相关逻辑功能
    function is_nan(value) {
        return typeof value === "number" && value !== value;
    }

    function is_infinite(value) {
        return typeof value === "number" && (value === Infinity || value === -Infinity);
    }

    function clamp_duration(value) {
        if (is_nan(value)) {
            return 0.0;
        }

        if (is_infinite(value)) {
            return Number.MAX_VALUE;
        }
        return value;
    }

    function sendMessage(message) {
        handleDetectResult(message);
    }

    //处理显示逻辑
    function handleDetectResult(elem) {
        if(elem.src == undefined
           || elem.src == null
           || elem.src == "") {
            return;
        }

        let pageSrc = elem.pageSrc;
        if(pageSrc == undefined
           || pageSrc == null
           || pageSrc == "") {
            return;
        }

        //先取top,否则有些多层嵌套的iframe没法显示嗅探按钮
        //ylu.cc
        let sender = window.top || window.parent;
        sender.parent.postMessage({
            from: "Playlist",
            operate: "API_Playlist_Detector",
            videoElement: elem,
        }, '*')

        //需要在iframe中设置长按操作
        setupLongPressDetect();
    }

    //获取elem元素的xpath路径
    function getXpathOfElement(elem) {
        if (!elem) return "";
        if (elem.nodeName == 'BODY') {
            return '/html/' + elem.tagName.toLowerCase();
        }
        let ix = 1;
        if(elem && elem.parentNode && elem.parentNode.childNodes.length > 0) {
            let siblings = elem.parentNode.childNodes;
            for (let i = 0, l = siblings.length; i < l; i++) {
                let sibling = siblings[i];
                if (sibling == elem) {
                    let r;
                    try {
                        r = getXpathOfElement(elem.parentNode) + '/' + elem.tagName.toLowerCase() + '[' + (ix) + ']';
                    } catch (e) {
                        r = "";
                    }
                    return r;
                } else if (sibling.nodeType == 1 && sibling.tagName == elem.tagName) {
                    ix++;
                }
            }
        }

        return "";
    }

    //根据xpath获取elem
    function getElemOfXpath(Xpath) {
        if (0 == Xpath.length) return null;
        var elem = document.evaluate(Xpath, document.documentElement, null, XPathResult.ANY_TYPE, null).iterateNext();
        return elem;
    }

    function setupDetector() {
        function notifyNodeSource(node, src, mimeType) {
            var name = node.title;
            
            try {
                if (name == null || name.length == 0) {
                    name = extractArticleTitle(document);
                }
            } catch (e) {
            }
            
            try {
                if (name == null || name.length == 0) {
                    name = extractArticleTitle(window.top.document);
                }
            } catch (e) {
            }

            if (name == null || typeof name == 'undefined' || name == "") {
                try {
                  name = window.top.document.title;
                } catch(error) {
                  name = document.title;
                }
            }

            if (mimeType == null || typeof mimeType == 'undefined' || mimeType == "") {
                if (node.constructor.name == 'HTMLVideoElement') {
                    mimeType = 'video';
                }

                if (node.constructor.name == 'HTMLAudioElement') {
                    mimeType = 'audio';
                }

                if (node.constructor.name == 'HTMLSourceElement') {
                    videoNode = node.closest('video');
                    if (videoNode != null && typeof videoNode != 'undefined') {
                        mimeType = 'video'
                    } else {
                        mimeType = 'audio'
                    }
                }
            }

            if (src !== "") {
                let xpath = getXpathOfElement(node);

                var location = window.location.href;
                if(window != window.top) {
                    location = "-1";
                }

                var pageTitle = "";

                try {
                  pageTitle = window.top.document.title;
                } catch(error) {
                  pageTitle = document.title;
                }

                sendMessage({
                    "name": name,
                    "src": src,
                    "pageSrc": location,
                    "pageTitle": pageTitle,
                    "mimeType": mimeType,
                    "duration": clamp_duration(node.duration),
                    "detected": true,
                    "xpath": xpath
                });
            } else {
                var target = node;
                document.querySelectorAll('source').forEach(function(node) {
                    if (node.src !== "") {
                        let xpath = getXpathOfElement(node);

                        var location = window.location.href;
                        if(window != window.top) {
                            location = "-1";
                        }

                        var pageTitle = "";

                        try {
                          pageTitle = window.top.document.title;
                        } catch(error) {
                          pageTitle = document.title;
                        }

                        if (node.closest('video') === target) {
                            sendMessage({
                                "name": name,
                                "src": node.src,
                                "pageSrc": location,
                                "pageTitle": pageTitle,
                                "mimeType": mimeType,
                                "duration": clamp_duration(target.duration),
                                "detected": true,
                                "xpath": xpath
                            });
                        }

                        if (node.closest('audio') === target) {
                            sendMessage({
                                "name": name,
                                "src": node.src,
                                "pageSrc": location,
                                "pageTitle": pageTitle,
                                "mimeType": mimeType,
                                "duration": clamp_duration(target.duration),
                                "detected": true,
                                "xpath": xpath
                            });
                        }
                    }
                });
            }
        }

        function notifyNode(node) {
            notifyNodeSource(node, node.src, node.type);
        }

        function observeNode(node) {
            function processNode(node) {
                if (node.observer == null || node.observer === undefined) {
                    node.observer = new MutationObserver(function (mutations) {
                        notifyNode(node);
                    });
                    node.observer.observe(node, { attributes: true, attributeFilter: ["src"] });
                    notifyNode(node);

                    node.addEventListener('loadedmetadata', function() {
                        notifyNode(node);
                    });
                }
            }

            for (const child of node.childNodes) {
                processNode(child);
            }

            processNode(node);
        }

        function observeDocument(node) {
            if (node.observer == null || node.observer === undefined) {
                node.observer = new MutationObserver(function (mutations) {
                    mutations.forEach(function (mutation) {
                        mutation.addedNodes.forEach(function (node) {
                            if (node.constructor.name == "HTMLVideoElement") {
                                observeNode(node);
                            }
                            else if (node.constructor.name == "HTMLAudioElement") {
                                observeNode(node);
                            }
                        });
                    });
                });
                node.observer.observe(node, { subtree: true, childList: true });
            }
        }

        function observeDynamicElements(node) {
            var original = node.createElement;
            node.createElement = function (tag) {
                if (tag === 'audio' || tag === 'video') {
                    var result = original.call(node, tag);
                    observeNode(result);
                    notifyNode(result);
                    return result;
                }
                return original.call(node, tag);
            };

            node.createElement.toString = function() {
                return "function () { [native code] }";
            };
        }

        function getAllVideoElements() {
            return document.querySelectorAll('video');
        }

        function getAllAudioElements() {
            return document.querySelectorAll('audio');
        }

        function onReady(fn) {
            if (document.readyState === "complete"
                || document.readyState === "interactive"
                || document.readyState === "ready") {
                setTimeout(fn, 1);
            } else {
                document.addEventListener("DOMContentLoaded", fn);
            }
        }

        function observePage() {
            observeDocument(document);
            observeDynamicElements(document);

            onReady(function() {
                getAllVideoElements().forEach(function(node) {
                    observeNode(node);
                });

                getAllAudioElements().forEach(function(node) {
                    observeNode(node);
                });
            });

            // Timeinterval is needed for DailyMotion as their DOM is bad
            let timerInterval = setInterval(function(){
                getAllVideoElements().forEach(function(node) {
                    observeNode(node);
                    notifyNode(node);
                });

                getAllAudioElements().forEach(function(node) {
                    observeNode(node);
                    notifyNode(node);
                });

            }, 1000);

            /// 检测20s
            let timeout = setTimeout(function() {
                clearInterval(timerInterval);
                clearTimeout(timeout);
            }, 20000);
        }

        observePage();
    }

    function pauseWithXpath(Xpath) {
        let elem = getElemOfXpath(Xpath);

        if(elem != null) {
            //https://developer.mozilla.org/zh-CN/docs/Web/API/HTMLMediaElement
            //https://developer.mozilla.org/zh-CN/docs/Web/API/HTMLVideoElement
            //https://developer.mozilla.org/zh-CN/docs/Web/API/HTMLAudioElement
            if (elem.constructor.name == 'HTMLVideoElement'
                || elem.constructor.name == 'HTMLAudioElement') {
                if(!elem.paused) {
                    elem.pause();
                }
                return;
            }
        } else {
           //节点变化了
            let videos = document.getElementsByTagName("video");
            for(let item of videos) {
                //youtube shorts
                item.pause();
            }

            let audios = document.getElementsByTagName("audio");
            for(let item of audios) {
                item.pause();
            }

            //abu22.com需要适配
        }
    }

    setupDetector();

    //处理blob视频链接
    function processBlobVideo() {
        // 检测太频繁了，增加限制，同一个网站，只检测5个视频链接
        // 添加计数器和时间戳
        let detectedVideoCount = 0;
        const MAX_VIDEO_COUNT = 5;
        // 修改为 Map 来存储 URL 和对应的 contentType 检查结果
        const checkedUrlsMap = new Map();

        //其他
        interceptVideo();
        function interceptVideo() {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.initiatorType === 'video' ||
                        entry.initiatorType === 'media' ||
                        entry.initiatorType === 'xmlhttprequest') {
                        let url = entry.name;

                        // 如果达到限制，提前退出
                        if (detectedVideoCount >= MAX_VIDEO_COUNT) {
                            return;
                        }

                        // 如果 URL 已经检查过，则使用缓存的结果
                        if (checkedUrlsMap.has(url)) {
                            const cachedResult = checkedUrlsMap.get(url);
                            if (cachedResult) {
                                let completedURL = new URL(url, window.document.baseURI);
                                notifyVideoURL(completedURL.href);
                            }
                            continue;
                        }

                        // 排除明显的非视频请求
                        if (url.endsWith('.js') ||
                            url.endsWith('.css') ||
                            url.endsWith('.ts') ||
                            url.endsWith('.json') ||
                            url.endsWith('.html') ||
                            url.endsWith('.png') ||
                            url.endsWith('.jpg') ||
                            url.endsWith('.jpeg') ||
                            url.endsWith('.svg') ||
                            url.endsWith('.gif')) {
                            continue;
                        }

                        // 扩展视频文件格式检测
                        const videoExtensions = [
                            '.m3u8', '.mp4', '.m4a', '.m4v', '.mp3', '.f4v', '.flv', '.mov'
                        ];

                        const isVideoExtension = videoExtensions.some(ext => url.toLowerCase().endsWith(ext));
                        if (isVideoExtension) {
                            let completedURL = new URL(url, window.document.baseURI);
                            notifyVideoURL(completedURL.href);
                            detectedVideoCount++;
                            continue;
                        }

                        // 2. 通过 Content-Type 判断
                        if (isVideoResource(url)) {
                            //先设置为不是视频资源，因为正在请求中，防止并发请求
                            checkedUrlsMap.set(url, false);

                            checkContentType(url).then(isVideo => {
                                // 存储检查结果
                                checkedUrlsMap.set(url, isVideo);
                                if (isVideo) {
                                    let completedURL = new URL(url, window.document.baseURI);
                                    notifyVideoURL(completedURL.href);
                                    detectedVideoCount++;
                                }
                            }).catch(error => {
                                // 存储失败结果
                                checkedUrlsMap.set(url, false);
                            });
                        } else {
                            // 如果不是视频资源，也记录结果
                            checkedUrlsMap.set(url, false);
                        }
                    }
                }
            });
            observer.observe({ type: 'resource', buffered: true });
        }

        // 检查 Content-Type 的辅助函数
        async function checkContentType(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                const contentType = response.headers.get('content-type');

                // 视频相关的 Content-Type 列表
                const videoContentTypes = [
                    'video/',
                    'application/x-mpegurl',
                    'application/vnd.apple.mpegurl',
                    'audio/mpegurl',
                    'audio/x-mpegurl',
                    'application/mpegurl',
                    'application/m3u',
                    'application/x-m4v',
                    'application/mp4',
                    'application/octet-stream', //有可能是ts
                    'binary/octet-stream',
                    'application/x-flv'
                ];

                return videoContentTypes.some(type => contentType.toLowerCase().includes(type));
            } catch (error) {
                return false;
            }
        }

        // 辅助函数：判断是否为视频资源
        function isVideoResource(url) {
            const videoExtensions = [
                '.m3u8', '.mp4', '.m4a', '.m4v', '.mp3',
                '.f4v', '.flv', '.mov', '.avi', '.mpg', '.mpeg', '.3gp', '.mkv'
            ];

            url = url.toLowerCase();

            // 检查扩展名
            const hasVideoExtension = videoExtensions.some(ext => url.includes(ext));
            return hasVideoExtension;
        }

        // 新增辅助函数
        function notifyVideoURL(url) {
            if (url != undefined) {
                let location = window.location.href;
                if(window != window.top) {
                    location = "-1";
                }

                let pageTitle = "";
                try {
                  pageTitle = window.top.document.title;
                } catch(error) {
                  pageTitle = document.title;
                }

                try {
                    if (name == null || name.length == 0) {
                        name = extractArticleTitle(document);
                    }
                } catch (e) {
                }
                
                try {
                    if (name == null || name.length == 0) {
                        name = extractArticleTitle(window.top.document);
                    }
                } catch (e) {
                }
                
                sendMessage({
                    "src": url,
                    "name": name,
                    "pageSrc": location,
                    "pageTitle": pageTitle,
                });
            }
        }
    }
    processBlobVideo();

    /// 视频按钮显示
    /// 有2个地方
    function _isCopyrightValid() {
        let hosts = ["youtube.com","youku.com","tudou.com","iqiyi.com","v.qq.com",
                     "mgtv.com","sogou.com","sogoucdn.com","wap.sogou.com"];

        let url = window.location.href;
        let host = new URL(url).host;

        for(let item of hosts) {
            if(host.indexOf(item) >= 0) {
                return false;
            }
        }

        return true;
    }

    function _setClipboard(data) {
        const onCopy = e => {
            e.stopImmediatePropagation();
            e.preventDefault();
            e.clipboardData.setData("text/plain", data);
            document.removeEventListener("copy", onCopy, true);

            let val = localizableKey("copySuccessful");
            _showToast(val);
        };

        const textarea = document.createElement("textarea");
        textarea.textContent = "<empty clipboard>";
        document.body.appendChild(textarea);
        textarea.select();
        document.addEventListener("copy", onCopy, true);
        try {
            return document.execCommand("copy");
        } catch (error) {
            document.removeEventListener("copy", onCopy, true);
            return false;
        } finally {
            document.body.removeChild(textarea);
        }
    }

    window.addEventListener('message', (event) => {
        // 仅在顶层窗口接收消息
        if (window == window.top || window == window.parent) {
            let videolist = window.__videolist__;
            if(videolist == null) {
                videolist = [];
            }

            let data = event.data;
            if(data.operate == "API_Playlist_Detector") {
                //版权检测
                if(_isCopyrightValid() == false) return;

                let elem = data.videoElement;
                //重复判断
                let isTheSameWebURL = true;
                let dict = {};
                let hasNormalVideoLink = false;
                for(let item of videolist) {
                    let key = item.pageSrc + item.src;
                    dict[key] = item;

                    //pageSrc==-1表示是跨域的
                    if (elem.pageSrc !== "-1" && item.pageSrc !== "-1" && elem.pageSrc != item.pageSrc) {
                        isTheSameWebURL = false;
                    }

                    if (item.src) {
                        const lowerUrl = item.src.toLowerCase();
                        if (lowerUrl.startsWith('http')) {
                            hasNormalVideoLink = true;
                        }
                    }
                }

                if(!isTheSameWebURL) {
                    videolist = [];
                } else {
                    let isExist = false;
                    let key = elem.pageSrc + elem.src;
                    isExist = dict[key];
                    if(isExist) {
                       //return;
                    } else {
                        videolist.push(elem);
                    }
                }

                //如果同时存在blob链接和非blob链接，说明非blob链接是从processBlobVideo中来的
                //此时可以隐藏blob链接
                if (hasNormalVideoLink) {
                    //过滤blob链接
                    videolist = videolist.filter(item => !item.src.toLowerCase().startsWith('blob'));
                }

                window.__videolist__ = videolist;

                _createAndUpdateNode(videolist);
                setupLongPressDetect();
            } else if(data.operate == "API_Playlist_Detector_LongPress") {
                //版权检测
                if(_isCopyrightValid() == false) return;

                if($(".__playlistDetector-video-list-dialog").length) {
                    //已存在
                } else {
                    showVideolist([data.videoElement]);
                }
            }
        }
    });

    // Shadow DOM相关变量
    let shadowHost = null;
    let shadowRoot = null;
    let snifferButton = null;
    let snifferBadge = null;

    //显示图标，更新数量 - 使用Shadow DOM实现
    function _createAndUpdateNode(videolist) {
        //没有打开嗅探按钮开关，直接返回
        if(!config || !config.showSnifferButton) return;
        if(!config.showSnifferButton.isActive) return;

        let __node_id__ = "__playlistDetector_scan_id__";

        // 检查Shadow Host是否存在
        if (!shadowHost || !document.contains(shadowHost)) {
            createShadowDOMButton();
        }

        // 更新徽章数量
        if (snifferBadge) {
            snifferBadge.textContent = videolist.length + "";
        }

        // 定期检查按钮是否被移除，如果被移除则重新创建
        ensureButtonVisibility();
    }

    // 创建Shadow DOM按钮
    function createShadowDOMButton() {
        try {
            // 移除旧的Shadow Host（如果存在）
            if (shadowHost && document.contains(shadowHost)) {
                shadowHost.remove();
            }

            // 创建Shadow Host
            shadowHost = document.createElement("div");
            shadowHost.id = "__playlistDetector_shadow_host__";

            // 设置Shadow Host的基本样式（确保不被页面样式影响）
            shadowHost.style.cssText = `
                position: fixed !important;
                right: 20px !important;
                bottom: 15% !important;
                width: 50px !important;
                height: 50px !important;
                z-index: 2147483647 !important;
                pointer-events: auto !important;
                visibility: visible !important;
                opacity: 1 !important;
                display: block !important;
            `;

            // 标记是addons的内部元素
            shadowHost.setAttribute("__internal__addons__", "true");

            // 创建Shadow Root
            shadowRoot = shadowHost.attachShadow({ mode: 'closed' });

            // 创建Shadow DOM内容
            createShadowContent();

            // 添加到页面
            document.documentElement.appendChild(shadowHost);

            // 添加事件监听器
            addShadowEventListeners();

        } catch (error) {
            // 降级到原始方案
            createFallbackButton();
        }
    }

    // 创建Shadow DOM内容
    function createShadowContent() {
        let logoUrl = browser.runtime.getURL("images/playlist_download.png");

        // 创建样式
        const style = document.createElement('style');
        style.textContent = `
            :host {
                position: fixed !important;
                right: 20px !important;
                bottom: 15% !important;
                width: 50px !important;
                height: 50px !important;
                z-index: 2147483647 !important;
                pointer-events: auto !important;
                visibility: visible !important;
                opacity: 1 !important;
                display: block !important;
            }

            .sniffer-button {
                position: relative !important;
                width: 50px !important;
                height: 50px !important;
                background-image: url("${logoUrl}") !important;
                background-color: #303030 !important;
                border-radius: 15px !important;
                background-size: 50px 50px !important;
                background-repeat: no-repeat !important;
                background-position: center !important;
                cursor: pointer !important;
                user-select: none !important;
                -webkit-user-select: none !important;
                -webkit-touch-callout: none !important;
                box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
                transition: transform 0.1s ease !important;
            }

            .sniffer-button:hover {
                transform: scale(1.05) !important;
            }

            .sniffer-button:active {
                transform: scale(0.95) !important;
            }

            .sniffer-badge {
                background-color: #FF2626 !important;
                position: absolute !important;
                right: -6px !important;
                top: -6px !important;
                height: 18px !important;
                line-height: 18px !important;
                border-radius: 9px !important;
                font-size: 13px !important;
                color: #fff !important;
                padding: 0px 6px !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                min-width: 18px !important;
                box-sizing: border-box !important;
                font-weight: bold !important;
                text-align: center !important;
            }
        `;

        // 创建按钮元素
        snifferButton = document.createElement('div');
        snifferButton.className = 'sniffer-button';

        // 创建徽章元素
        snifferBadge = document.createElement('div');
        snifferBadge.className = 'sniffer-badge';
        snifferBadge.textContent = '0';

        // 组装Shadow DOM
        shadowRoot.appendChild(style);
        shadowRoot.appendChild(snifferButton);
        snifferButton.appendChild(snifferBadge);
    }

    // 添加Shadow DOM事件监听器
    function addShadowEventListeners() {
        if (!snifferButton || !shadowHost) {
            return;
        }

        var startX, startY;
        var currentX, currentY;
        var offsetX = 0;
        var offsetY = 0;
        var isDragging = false;
        var hasMoved = false;
        var dragThreshold = 5; // 拖拽阈值，小于此值认为是点击
        var touchStartTime = 0;

        // 点击事件处理函数
        function handleClick(e) {
            e.preventDefault();
            e.stopPropagation();

            // 动态生成视频列表
            let videolist = window.__videolist__;
            showVideolist(videolist);
        }

        // 检测设备类型并选择合适的事件处理方式
        var isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

        if (!isTouchDevice) {
            // 桌面设备：使用鼠标点击事件
            snifferButton.addEventListener('click', function(e) {
                handleClick(e);
            });
        }
        // 触摸设备：只使用触摸事件，不使用click事件（避免冲突）

        // 触摸开始事件
        snifferButton.addEventListener('touchstart', function(e) {
            touchStartTime = Date.now();
            hasMoved = false;
            isDragging = false; // 重要：不要立即设置为true

            if (e.touches && e.touches.length > 0) {
                startX = e.touches[0].pageX;
                startY = e.touches[0].pageY;

                const rect = shadowHost.getBoundingClientRect();
                currentX = rect.left;
                currentY = rect.top;
                offsetX = startX - currentX;
                offsetY = startY - currentY;
            }
        }, { passive: true });

        // 触摸移动事件
        snifferButton.addEventListener('touchmove', function(e) {
            if (!e.touches || e.touches.length === 0) return;

            var currentTouchX = e.touches[0].pageX;
            var currentTouchY = e.touches[0].pageY;

            // 计算移动距离
            var deltaX = Math.abs(currentTouchX - startX);
            var deltaY = Math.abs(currentTouchY - startY);

            // 只有移动距离超过阈值才认为是拖拽
            if (deltaX > dragThreshold || deltaY > dragThreshold) {
                hasMoved = true;
                isDragging = true;

                e.preventDefault();

                var newX = currentTouchX - offsetX;
                var newY = currentTouchY - offsetY;

                // 限制在视窗范围内
                newX = Math.max(0, Math.min(newX, window.innerWidth - 50));
                newY = Math.max(0, Math.min(newY, window.innerHeight - 50));

                shadowHost.style.left = newX + "px";
                shadowHost.style.top = newY + "px";
                shadowHost.style.right = "auto";
                shadowHost.style.bottom = "auto";
            }
        }, { passive: false });

        // 触摸结束事件
        snifferButton.addEventListener('touchend', function(e) {
            var touchDuration = Date.now() - touchStartTime;

            if (isDragging && hasMoved) {
                e.preventDefault();

                const rect = shadowHost.getBoundingClientRect();
                const finalX = rect.left;
                const currentY = rect.top;

                // 自动吸附到左右边缘
                if (finalX < (window.innerWidth / 2)) {
                    shadowHost.style.left = "20px";
                    shadowHost.style.right = "auto";
                } else {
                    shadowHost.style.right = "20px";
                    shadowHost.style.left = "auto";
                }
                shadowHost.style.top = currentY + "px";
                shadowHost.style.bottom = "auto";
            } else if (!hasMoved && touchDuration < 500) {
                // 短时间内没有移动，认为是点击
                e.preventDefault(); // 阻止后续的click事件
                e.stopPropagation();
                handleClick(e);
            }

            // 重置状态
            setTimeout(() => {
                isDragging = false;
                hasMoved = false;
            }, 100);
        }, { passive: false });
    }

    // 确保按钮可见性的监控机制
    function ensureButtonVisibility() {
        // 防止重复设置定时器
        if (window.__playlistDetector_visibility_timer__) {
            return;
        }

        window.__playlistDetector_visibility_timer__ = setInterval(function() {
            // 检查Shadow Host是否还在DOM中
            if (!shadowHost || !document.contains(shadowHost)) {
                createShadowDOMButton();
                return;
            }

            // 检查Shadow Host的样式是否被篡改
            const computedStyle = window.getComputedStyle(shadowHost);
            if (computedStyle.display === 'none' ||
                computedStyle.visibility === 'hidden' ||
                computedStyle.opacity === '0') {
                shadowHost.style.cssText = `
                    position: fixed !important;
                    right: 20px !important;
                    bottom: 15% !important;
                    width: 50px !important;
                    height: 50px !important;
                    z-index: 2147483647 !important;
                    pointer-events: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    display: block !important;
                `;
            }
        }, 2000); // 每2秒检查一次
    }

    // 降级方案：如果Shadow DOM不支持，使用原始方案
    function createFallbackButton() {
        let __node_id__ = "__playlistDetector_scan_id__";
        let node = document.getElementById(__node_id__);

        if(!node) {
            let node = document.createElement("div");
            let logoUrl = browser.runtime.getURL("images/playlist_download.png");
            let logoURL = `url("` + logoUrl + `")`;

            // 使用jQuery设置样式
            if (typeof $ !== 'undefined') {
                $(node).css({
                    position: "fixed",
                    right: "20px",
                    bottom: "15%",
                    backgroundImage: logoURL,
                    backgroundColor: '#303030',
                    borderRadius: '15px',
                    height: "50px",
                    width: "50px",
                    backgroundSize: "50px 50px",
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "center",
                });
            } else {
                // 原生JavaScript设置样式
                node.style.cssText = `
                    position: fixed !important;
                    right: 20px !important;
                    bottom: 15% !important;
                    background-image: ${logoURL} !important;
                    background-color: #303030 !important;
                    border-radius: 15px !important;
                    height: 50px !important;
                    width: 50px !important;
                    background-size: 50px 50px !important;
                    background-repeat: no-repeat !important;
                    background-position: center !important;
                    z-index: 2147483647 !important;
                `;
            }

            // 标记是addons的内部元素
            node.setAttribute("__internal__addons__", "true");
            node.id = __node_id__;
            document.documentElement.appendChild(node);

            let badge = document.createElement("div");
            badge.id = "__playlistDetector_badge";
            badge.className = "__playlistDetector-countNumber";
            node.appendChild(badge);

            // 添加降级方案的事件监听器
            addFallbackEventListeners(node);
        }
    }

    // 降级方案的事件监听器
    function addFallbackEventListeners(node) {
        var startX, startY;
        var currentX, currentY;
        var offsetX = 0;
        var offsetY = 0;
        var isDragging = false;

        // 点击事件
        node.addEventListener('click', function(e) {
            if (isDragging) {
                e.preventDefault();
                e.stopPropagation();
                return;
            }
            let videolist = window.__videolist__;
            showVideolist(videolist);
        });

        // 触摸事件（简化版）
        node.addEventListener('touchstart', function(e) {
            startX = e.touches[0].pageX;
            startY = e.touches[0].pageY;
            const rect = node.getBoundingClientRect();
            currentX = rect.left;
            currentY = rect.top;
            offsetX = startX - currentX;
            offsetY = startY - currentY;
            isDragging = true;
        });

        node.addEventListener('touchmove', function(e) {
            if (!isDragging) return;
            e.preventDefault();
            var newX = e.touches[0].pageX - offsetX;
            var newY = e.touches[0].pageY - offsetY;
            node.style.left = newX + "px";
            node.style.top = newY + "px";
            node.style.right = "auto";
            node.style.bottom = "auto";
        });

        node.addEventListener('touchend', function(e) {
            if (!isDragging) return;
            const rect = node.getBoundingClientRect();
            const finalX = rect.left;
            const currentY = rect.top;

            if (finalX < (window.innerWidth / 2)) {
                node.style.left = "20px";
                node.style.right = "auto";
            } else {
                node.style.right = "20px";
                node.style.left = "auto";
            }
            node.style.top = currentY + "px";
            node.style.bottom = "auto";

            isDragging = false;
        });
    }
    
    //显示视频列表弹窗
    function showVideolist(data) {
        //遮罩
        let mask = document.createElement("div");
        $(mask).addClass("__playlistDetector-video-list-mask");
        //标记是addons的内部元素,防止被牛皮癣广告脚本屏蔽
        $(mask).attr("__internal__addons__", true);
        $(document.body).append(mask);

        //对话框
        let dialog = document.createElement("div");
        $(dialog).addClass("__playlistDetector-video-list-dialog");
        //标记是addons的内部元素,防止被牛皮癣广告脚本屏蔽
        $(dialog).attr("__internal__addons__", true);

        let titleLabel = document.createElement("div");
        $(titleLabel).addClass("__playlistDetector-titleLabel");
        let val = localizableKey("videolist");
        $(titleLabel).text(val);
        $(dialog).append(titleLabel);

        let closeBtn = document.createElement("div");
        $(closeBtn).addClass("__playlistDetector-close-logo");

        //暗黑模式适配
        let logoUrl = browser.runtime.getURL("images/sheet_close_button.png");
        let logoURL = `url("` + logoUrl + `")`;
        $(closeBtn).css("background-image", logoURL);
        $(dialog).append(closeBtn);

        let videolist = document.createElement("div");
        $(videolist).addClass("__playlistDetector-video-list");
        $(dialog).append(videolist);

        let height = 320;
        $(dialog).css("height", height);

        $(document.body).append(dialog);

        for(let obj of data) {
            let cell = document.createElement("div");
            $(cell).addClass("__playlistDetector-video-list-cell");
            $(videolist).append(cell);

            let fileName = lastPathComponent(obj.src);
            let titleLabel = document.createElement("div");
            $(titleLabel).addClass("__playlistDetector-title");
            $(titleLabel).text(fileName);
            $(cell).append(titleLabel);
            //点击复制链接
            $(titleLabel).click(function (e) {
                _setClipboard(obj.src);
            });
            
            let downloadButton = document.createElement("div");
            $(downloadButton).addClass("__playlistDetector-downloadButton");
            let val = localizableKey("download");
            $(downloadButton).text(val);
            $(cell).append(downloadButton);

            //点击下载事件
            $(downloadButton).click(function (e) {
                e.preventDefault();
                
                //判断链接是否为加密链接
                if(isBlobUrl(obj.src)) {
                    let val = localizableKey("encryptMessage");
                    _showToast(val);
                    return;
                }
                
                let schemeUrl = "addons://download?src=" + btoa(obj.src)
                + "&pageSrc=" + btoa(obj.pageSrc)
                + "&name=" + (obj.name==null?"":obj.name)
                + "&pageTitle=" + obj.pageTitle
                + "&mimeType=" + (obj.mimeType==null?"":obj.mimeType)
                + "&xpath=" + btoa(obj.xpath==null?"":obj.xpath);

                window.open(schemeUrl);

                hideMaskAndDialog();
            });
        }

        //动画显示
        $(mask).fadeIn(300);
        $(dialog).fadeIn(300);

        //点击事件
        $(closeBtn).click(function (e) {
            e.preventDefault();
            hideMaskAndDialog();
        });

        $(mask).click(function (e) {
            e.preventDefault();
            hideMaskAndDialog();
        });

        function hideMaskAndDialog() {
            $(mask).fadeOut(300, function() {
                $(mask).remove();
            });

            $(dialog).fadeOut(300, function() {
                $(dialog).remove();
            });
        }
        
        function lastPathComponent(url) {
            if(url == undefined || url == null || url == "") return "";

            return url.substring(url.lastIndexOf('/') + 1)
        }
    }
    
    // 创建Shadow DOM视频列表弹窗
    function createShadowVideoDialog(data) {
        // 创建遮罩的Shadow Host
        const maskShadowHost = document.createElement("div");
        maskShadowHost.id = "__playlistDetector_mask_shadow_host__";
        maskShadowHost.style.cssText = `
            position: fixed !important;
            left: 0px !important;
            top: 0px !important;
            right: 0px !important;
            bottom: 0px !important;
            z-index: 2147483646 !important;
            pointer-events: auto !important;
            visibility: visible !important;
            opacity: 1 !important;
            display: block !important;
        `;
        maskShadowHost.setAttribute("__internal__addons__", "true");

        // 创建对话框的Shadow Host
        const dialogShadowHost = document.createElement("div");
        dialogShadowHost.id = "__playlistDetector_dialog_shadow_host__";
        dialogShadowHost.style.cssText = `
            position: fixed !important;
            left: 50% !important;
            top: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 2147483647 !important;
            pointer-events: auto !important;
            visibility: visible !important;
            opacity: 1 !important;
            display: block !important;
            width: 90% !important;
            max-width: 768px !important;
            min-width: 375px !important;
            height: 320px !important;
        `;
        dialogShadowHost.setAttribute("__internal__addons__", "true");

        // 创建Shadow Root
        const maskShadowRoot = maskShadowHost.attachShadow({ mode: 'closed' });
        const dialogShadowRoot = dialogShadowHost.attachShadow({ mode: 'closed' });

        // 保存Shadow Root引用以便后续访问
        maskShadowHost._shadowRoot = maskShadowRoot;
        dialogShadowHost._shadowRoot = dialogShadowRoot;

        // 创建遮罩内容
        createShadowMaskContent(maskShadowRoot, function() {
            hideShadowVideoDialog(maskShadowHost, dialogShadowHost);
        });

        // 创建对话框内容
        createShadowDialogContent(dialogShadowRoot, data, function() {
            hideShadowVideoDialog(maskShadowHost, dialogShadowHost);
        });

        // 添加到页面
        document.documentElement.appendChild(maskShadowHost);
        document.documentElement.appendChild(dialogShadowHost);

        // 显示动画
        setTimeout(() => {
            if (maskShadowHost._shadowRoot) {
                const mask = maskShadowHost._shadowRoot.querySelector('.video-list-mask');
                if (mask) {
                    mask.style.opacity = '1';
                } else {
                }
            }
            if (dialogShadowHost._shadowRoot) {
                const dialog = dialogShadowHost._shadowRoot.querySelector('.video-list-dialog');
                if (dialog) {
                    dialog.style.opacity = '1';
                } else {
                }
            }
        }, 10);
    }

    // 创建Shadow DOM遮罩内容
    function createShadowMaskContent(shadowRoot, closeCallback) {
        const style = document.createElement('style');
        style.textContent = `
            .video-list-mask {
                position: fixed !important;
                left: 0px !important;
                top: 0px !important;
                right: 0px !important;
                bottom: 0px !important;
                background-color: rgba(0, 0, 0, .2) !important;
                opacity: 1 !important;
                transition: opacity 0.3s ease !important;
                cursor: pointer !important;
            }
        `;

        const mask = document.createElement('div');
        mask.className = 'video-list-mask';
        mask.addEventListener('click', closeCallback);

        shadowRoot.appendChild(style);
        shadowRoot.appendChild(mask);
    }

    // 创建Shadow DOM对话框内容
    function createShadowDialogContent(shadowRoot, data, closeCallback) {
        let logoUrl = browser.runtime.getURL("images/sheet_close_button.png");

        const style = document.createElement('style');
        style.textContent = `
            .video-list-dialog {
                position: relative !important;
                width: 100% !important;
                height: 100% !important;
                background-color: #fff !important;
                border-radius: 10px !important;
                box-shadow: 0px 5px 10px rgba(0, 0, 0, .4) !important;
                opacity: 1 !important;
                transition: opacity 0.3s ease !important;
                display: flex !important;
                flex-direction: column !important;
            }

            .dialog-title {
                position: absolute !important;
                top: 15px !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                text-align: center !important;
                font-size: 16px !important;
                color: #222 !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                font-weight: 600 !important;
            }

            .close-button {
                position: absolute !important;
                right: 10px !important;
                top: 10px !important;
                height: 30px !important;
                width: 30px !important;
                background-image: url("${logoUrl}") !important;
                background-size: 30px 30px !important;
                background-repeat: no-repeat !important;
                background-position: center !important;
                cursor: pointer !important;
                opacity: 0.7 !important;
                transition: opacity 0.2s ease !important;
            }

            .close-button:hover {
                opacity: 1 !important;
            }

            .video-list-container {
                position: absolute !important;
                top: 50px !important;
                left: 10px !important;
                right: 10px !important;
                bottom: 10px !important;
                border-radius: 10px !important;
                background-color: #f5f5f5 !important;
                overflow-y: auto !important;
                display: flex !important;
                flex-direction: column !important;
                padding: 10px !important;
                box-sizing: border-box !important;
            }

            .video-item {
                flex-shrink: 0 !important;
                height: 60px !important;
                background-color: #fff !important;
                border-bottom: 0.5px solid #f5f5f5 !important;
                position: relative !important;
                display: flex !important;
                flex-direction: row !important;
                justify-content: flex-start !important;
                align-items: center !important;
                margin-bottom: 5px !important;
                border-radius: 8px !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }

            .video-title {
                font-size: 16px !important;
                color: #333 !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                white-space: nowrap !important;
                text-align: left !important;
                position: absolute !important;
                right: 80px !important;
                left: 15px !important;
                cursor: pointer !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            }

            .video-title:hover {
                color: #2D7AFE !important;
            }

            .download-button {
                text-align: center !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
                align-self: center !important;
                color: #fff !important;
                background-color: #2D7AFE !important;
                font-size: 14px !important;
                border-radius: 12px !important;
                position: absolute !important;
                right: 10px !important;
                padding: 0 12px !important;
                height: 25px !important;
                cursor: pointer !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                font-weight: 500 !important;
                transition: background-color 0.2s ease !important;
            }

            .download-button:hover {
                background-color: #1E5FD9 !important;
            }

            .download-button:active {
                background-color: #1A56C4 !important;
            }
        `;

        // 创建对话框容器
        const dialog = document.createElement('div');
        dialog.className = 'video-list-dialog';

        // 创建标题
        const titleLabel = document.createElement('div');
        titleLabel.className = 'dialog-title';
        titleLabel.textContent = localizableKey("videolist");

        // 创建关闭按钮
        const closeBtn = document.createElement('div');
        closeBtn.className = 'close-button';
        closeBtn.addEventListener('click', closeCallback);

        // 创建视频列表容器
        const videoListContainer = document.createElement('div');
        videoListContainer.className = 'video-list-container';

        // 创建视频列表项
        for (let obj of data) {
            const videoItem = document.createElement('div');
            videoItem.className = 'video-item';

            const fileName = lastPathComponent(obj.src);
            const videoTitle = document.createElement('div');
            videoTitle.className = 'video-title';
            videoTitle.textContent = fileName;

            // 点击复制链接
            videoTitle.addEventListener('click', function(e) {
                _setClipboard(obj.src);
            });

            const downloadButton = document.createElement('div');
            downloadButton.className = 'download-button';
            downloadButton.textContent = localizableKey("download");

            // 点击下载事件
            downloadButton.addEventListener('click', function(e) {
                e.preventDefault();

                // 判断链接是否为加密链接
                if (isBlobUrl(obj.src)) {
                    let val = localizableKey("encryptMessage");
                    _showToast(val);
                    return;
                }

                let schemeUrl = "addons://download?src=" + btoa(obj.src)
                    + "&pageSrc=" + btoa(obj.pageSrc)
                    + "&name=" + (obj.name == null ? "" : obj.name)
                    + "&pageTitle=" + obj.pageTitle
                    + "&mimeType=" + (obj.mimeType == null ? "" : obj.mimeType)
                    + "&xpath=" + btoa(obj.xpath == null ? "" : obj.xpath);

                window.open(schemeUrl);
                closeCallback();
            });

            videoItem.appendChild(videoTitle);
            videoItem.appendChild(downloadButton);
            videoListContainer.appendChild(videoItem);
        }

        // 组装对话框
        dialog.appendChild(titleLabel);
        dialog.appendChild(closeBtn);
        dialog.appendChild(videoListContainer);

        shadowRoot.appendChild(style);
        shadowRoot.appendChild(dialog);
    }

    // 隐藏Shadow DOM视频对话框
    function hideShadowVideoDialog(maskHost, dialogHost) {
        // 淡出动画
        if (maskHost && maskHost._shadowRoot) {
            const mask = maskHost._shadowRoot.querySelector('.video-list-mask');
            if (mask) {
                mask.style.opacity = '0';
            }
        }

        if (dialogHost && dialogHost._shadowRoot) {
            const dialog = dialogHost._shadowRoot.querySelector('.video-list-dialog');
            if (dialog) {
                dialog.style.opacity = '0';
            }
        }

        // 延迟移除元素
        setTimeout(() => {
            if (maskHost && document.contains(maskHost)) {
                maskHost.remove();
            }
            if (dialogHost && document.contains(dialogHost)) {
                dialogHost.remove();
            }
        }, 300);
    }

    // 辅助函数：获取文件名
    function lastPathComponent(url) {
        if (url == undefined || url == null || url == "") return "";
        return url.substring(url.lastIndexOf('/') + 1);
    }

    // 本地化函数
    function localizableKey(key) {
        const translations = {
            videolist: "视频列表",
            download: "下载",
            copySuccessful: "复制成功",
            encryptMessage: "这是一个加密链接，无法缓存！"
        };
        return translations[key] || key;
    }

    //加密链接判断
    function isBlobUrl(url) {
      return url.toLowerCase().startsWith("blob:");
    }

    //长按识别视频
    function setupLongPressDetect() {
        //没有打开开关，直接返回
        if(!config || !config.longPressDetect) return;
        if(!config.longPressDetect.isActive) return;

        //长按视频嗅探是激活的
        var longPressTimeout = null;
        var touchDownX = 0;
        var touchDownY = 0;
        var touchHandled = false;

        function cancel() {
            if (longPressTimeout) {
                clearTimeout(longPressTimeout);
                longPressTimeout = null;
            }
        }

        document.addEventListener("touchstart", function(event) {
            if (event.touches.length !== 1) {
                cancel();
                return;
            }

            var data = {};
            var element = event.target;
            if (element.localName === "input") {
                cancel();
                return
            }

            do {
                try {
                    let elem = element.parentElement.firstElementChild
                    while (elem) {
                        if (elem.localName == 'video'
                            || elem.localName == 'audio') {
                            element = elem;
                            break;
                        }
                        //missav.com的视频在兄弟节点
                        elem = elem.nextElementSibling;
                    }
                } catch (e) {}

                if (!data.src && (element.localName === "video" || element.localName === "audio")) {
                    var src = element.currentSrc;
                    if (src == undefined) {
                        src = element.src;
                    }
                    if (src) {
                        data.src = src;
                        try {
                            data.pageTitle = window.top.document.title;
                        } catch (e) {
                            data.pageTitle = document.title;
                        }
                        try {
                            data.pageSrc = window.location.href;
                        } catch (e) {}
                    }

                    //找到视频,直接退出
                    break
                }
                element = element.parentElement;
            } while (element);

            if (!data.src) {
                try {
                    handleTouchStart2(event, data);
                } catch (e) {}
            }

            if (data.src) {
                var touch = event.touches[0];
                touchDownX = touch.screenX;
                touchDownY = touch.screenY;

                longPressTimeout = setTimeout(function() {
                    touchHandled = true;
                    cancel();

                    //只有音视频才设置, 否则长按复制文本会失效
                    let r = event.target;
                    r.style.webkitUserSelect = "none",
                    r.style.webkitTouchCallout = "none"

                    //版权检测
                    if(_isCopyrightValid() == false) {
                        return;
                    }

                    //先取top,否则有些多层嵌套的iframe没法显示嗅探按钮
                    //ylu.cc
                    //这是在iframe中触发的
                    let sender = window.top || window.parent;
                    sender.top.postMessage({
                        from: "Playlist",
                        operate: "API_Playlist_Detector_LongPress",
                        videoElement: data,
                    }, '*')
                }, 500);
            }
        }, true);

        function handleTouchStart2(event, data) {
            var x = event.changedTouches[0].pageX - window.scrollX;
            var y = event.changedTouches[0].pageY - window.scrollY;
            if (document.elementsFromPoint !== undefined) {
                var elements = document.elementsFromPoint(x, y)
                for (var i = 0; i < elements.length; i++) {
                    var e = elements[i];
                    if (detectElementType(e, data)) {
                        break;
                    }
                }
            } else {
                var e = document.elementFromPoint(x, y);
                detectElementType(e, data);
            }
        }

        function detectElementType(e, data) {
            var children = Array.prototype.slice.call(e.childNodes);
            children.forEach(function(child) {
                if (child.src != null && child.tagName == 'SOURCE') {
                    e = child;
                }
            });

            while (e && !e.src) {
                e = e.parentNode;
            }

            if (e && e.src) {
                if (e.tagName == 'VIDEO' || e.parentNode.tagName == 'VIDEO' || e.tagName == 'AUDIO' || e.parentNode.tagName == 'AUDIO') {
                    data.src = e.src;
                    try {
                        data.pageTitle = window.top.document.title;
                    } catch (e) {
                        data.pageTitle = document.title;
                    }
                    try {
                        data.pageSrc = window.location.href;
                    } catch (e) {}
                    return true;
                }
            }
            return false;
        }

        document.addEventListener("touchend",
            function(event) {
                cancel();
                if (touchHandled) {
                    touchHandled = false;
                    event.preventDefault();
                }
            },
            true);

        document.addEventListener("touchmove",
            function(event) {
                if (longPressTimeout) {
                    var {
                        screenX, screenY
                    } = event.touches[0];

                    if (Math.abs(touchDownX - screenX) > 10 || Math.abs(touchDownY - screenY) > 10) {
                        cancel();
                    }
                }
            });

        document.addEventListener("scroll", cancel);
    }
}

playlistDetector();
