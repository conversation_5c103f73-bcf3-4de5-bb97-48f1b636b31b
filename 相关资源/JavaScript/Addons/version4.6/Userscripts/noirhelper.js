 (() => {
     document.dispatchEvent(new CustomEvent("noir-helper-loaded"));
     const e = Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype, "addRule"),
         t = Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype, "insertRule"),
         o = Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype, "deleteRule"),
         n = Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype, "removeRule"),
         i = Object.getOwnPropertyDescriptor(Element.prototype, "attachShadow");
     Object.defineProperty(CSSStyleSheet.prototype, "addRule", Object.assign({}, e, {
         value: function(t, o, n) {
             if (e.value.call(this, t, o, n), this.ownerNode && !this.ownerNode.classList.contains(
                     "noir")) {
                 const e = new CustomEvent("noir-update", {
                     detail: {
                         type: "insert",
                         index: n
                     }
                 });
                 this.ownerNode.dispatchEvent(e)
             }
             return -1
         }
     })), Object.defineProperty(CSSStyleSheet.prototype, "insertRule", Object.assign({}, t, {
         value: function(e, o) {
             const n = t.value.call(this, e, o);
             if (this.ownerNode && !this.ownerNode.classList.contains("noir")) {
                 const e = new CustomEvent("noir-update", {
                     detail: {
                         type: "insert",
                         index: o
                     }
                 });
                 this.ownerNode.dispatchEvent(e)
             }
             return n
         }
     })), Object.defineProperty(CSSStyleSheet.prototype, "deleteRule", Object.assign({}, o, {
         value: function(e) {
             if (o.value.call(this, e), this.ownerNode && !this.ownerNode.classList.contains(
                 "noir")) {
                 const t = new CustomEvent("noir-update", {
                     detail: {
                         type: "delete",
                         index: e
                     }
                 });
                 this.ownerNode.dispatchEvent(t)
             }
         }
     })), Object.defineProperty(CSSStyleSheet.prototype, "removeRule", Object.assign({}, n, {
         value: function(e) {
             if (n.value.call(this, e), this.ownerNode && !this.ownerNode.classList.contains(
                 "noir")) {
                 const t = new CustomEvent("noir-update", {
                     detail: {
                         type: "delete",
                         index: e
                     }
                 });
                 this.ownerNode.dispatchEvent(t)
             }
         }
     })), Object.defineProperty(Element.prototype, "attachShadow", Object.assign({}, i, {
         value: function(e) {
             const t = i.value.call(this, e),
                 o = new CustomEvent("noir-attach-shadow", {
                     bubbles: !0
                 });
             return this.dispatchEvent(o), t
         }
     }))
 })();
