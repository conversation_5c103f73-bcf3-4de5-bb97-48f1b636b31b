//
//  ContentBlockerRequestHandler.m
//  AddonsBlock
//
//  Created by qingbin on 2023/4/10.
//

#import "ContentBlockerRequestHandler.h"

@interface ContentBlockerRequestHandler ()

@end

@implementation ContentBlockerRequestHandler

- (void)beginRequestWithExtensionContext:(NSExtensionContext *)context {
    NSItemProvider *attachment;
    
    NSUserDefaults* userDefaults = [[NSUserDefaults alloc]initWithSuiteName:[self kGroupPath]];
    BOOL isEnabled = [userDefaults boolForKey:[self kAdblockKey]];
    
    if(isEnabled) {
        NSURL* URL = [self loadRulesURL];
        //有文件
        attachment = [[NSItemProvider alloc] initWithContentsOfURL:URL];
        
        NSExtensionItem *item = [[NSExtensionItem alloc] init];
        item.attachments = @[attachment];
        
        [context completeRequestReturningItems:@[item] completionHandler:nil];
    } else {
        attachment = [[NSItemProvider alloc] initWithContentsOfURL:[[NSBundle mainBundle] URLForResource:@"blockerList" withExtension:@"json"]];
        
        NSExtensionItem *item = [[NSExtensionItem alloc] init];
        item.attachments = @[attachment];
        
        [context completeRequestReturningItems:@[item] completionHandler:nil];
    }
}

- (NSURL*)loadRulesURL
{
    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSURL* URL = [[[fileManager containerURLForSecurityApplicationGroupIdentifier:[self kGroupPath]] URLByAppendingPathComponent:@"Adblock"] URLByAppendingPathComponent:@"blockerList.json"];
    
    return URL;
}

- (NSString *)kGroupPath
{
    return @"group.com.qingbin.addons";
}

- (NSString *)kAdblockKey
{
    return @"Key_Addons_ContentBlocker_Enabled";
}

@end
