//
//  DarkConfigModel.m
//  Addons
//
//  Created by q<PERSON><PERSON> on 2023/4/24.
//

#import "DarkConfigModel.h"

@implementation DarkConfigModel

+ (DarkConfigModel*)defaultConfig
{
    DarkConfigModel* model = [DarkConfigModel new];
    model.enableValue = 1;
    model.brightnesssValue = 100;
    model.disableSiteList = [NSMutableArray array];
    
    return model;
}

- (NSMutableArray *)disableSiteList
{
    if(!_disableSiteList) {
        _disableSiteList = [NSMutableArray array];
    }
    
    return _disableSiteList;
}

@end
