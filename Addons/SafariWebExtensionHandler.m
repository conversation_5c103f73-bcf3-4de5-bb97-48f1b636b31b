//
//  SafariWebExtensionHandler.m
//  Addons
//
//  Created by qingbin on 2023/3/13.
//

#import "SafariWebExtensionHandler.h"

#import <SafariServices/SafariServices.h>

#import "UserScript.h"
#import "ExUserScriptHelper.h"
#import "GMKeyValueHelper.h"

#import "ReactiveCocoa.h"
#import "DatabaseUnit+AiSearch.h"
#import "DatabaseUnit+ExtConfig.h"
#import "ExtensionHelper.h"

#import "NSURL+Extension.h"
#import "ResourceHelper.h"
#import "BrowserUtils.h"

#import "AFNetworking.h"
#import "DarkConfigModel.h"

#import "UserComandModel.h"
#import "SyncEngine.h"

#import "PPEnums.h"

#import "NSString+Helper.h"
#import "CustomRequestSerializer.h"

@implementation SafariWebExtensionHandler

- (void)beginRequestWithExtensionContext:(NSExtensionContext *)context
{
    NSDictionary* message = [context.inputItems.firstObject userInfo][SFExtensionMessageKey];
    
    NSLog(@"Received message from browser.runtime.sendNativeMessage: %@", message);

    @autoreleasepool {
        __block id body = [NSNull null];
        NSString* type = message[@"type"];
        if([type isEqualToString:@"API_GET_DARK_CONFIG"]) {
            //暗黑模式
            //bootstrap.js处理了是否开启暗黑模式总开关的逻辑
            DarkConfigModel* config = [[GMKeyValueHelper shareInstance] valueForDarkConfig];
            body = [config toJSONString];
            NSExtensionItem *response = [[NSExtensionItem alloc] init];
            response.userInfo = @{ SFExtensionMessageKey: @{
                    @"config": body == nil? [NSNull null]:body,
            }};
            [context completeRequestReturningItems:@[ response ] completionHandler:nil];
            return;
        } else if([type isEqualToString:@"API_SAVE_DARK_CONFIG"]) {
            NSString* config = message[@"config"];
            [[GMKeyValueHelper shareInstance] saveDarkConfig:config];
        } else if([type isEqualToString:@"API_TAGIT_ACTIVE_CHANGE"]) {
            //popup中,激活或者暂停了一项tagit
            //更新标记模式单项的激活状态
            
            //iCloud初始化(必须在这里初始化一次，否则即使是单例，也会执行多次，非常奇怪)
            [SyncEngine shareInstance];
            
            NSString* tagitId = message[@"tagitId"];
            BOOL isActive = [message[@"isActive"] boolValue];
            
            [ExtensionHelper updateTagitWithId:tagitId isActive:isActive];
        } else if([type isEqualToString:@"API_REMOVE_TAGIT"]) {
            //popup中,删除了一项tagit
            //删除一项tagit
            
            //iCloud初始化(必须在这里初始化一次，否则即使是单例，也会执行多次，非常奇怪)
            [SyncEngine shareInstance];
            
            NSString* tagitId = message[@"tagitId"];
            [ExtensionHelper removeTagitWithId:tagitId];
        } else if([type isEqualToString:@"API_ReloadData"]) {
            //包括bootstrap/popup 2种情况
            //其中bootstrap的status=0
            //popup的初始化，status=1
            
            int status = [message[@"status"] intValue];
            
            //iCloud初始化(必须在这里初始化一次，否则即使是单例，也会执行多次，非常奇怪)
            [SyncEngine shareInstance];
            
            //获取当前网页可用的脚本
            [[ExtensionHelper shareInstance] asyncLoadAllDatas:status
                                             completionHandler:^(ExtensionModel *model) {
                //__firefox__
                {
                    //已经在manifest.json中引入
                }
                
                //添加视频嗅探脚本
                {
                    //有点玄学，PlaylistDetector.js改成这样子all_frame才会生效
                    //否则识别不了iframe中的视频，太坑爹了！
                }
                
                if(status == ExtensionReloadStatusBootstrap) {
                    //bootstrap的初始化
                    
                    //iOS18没法正常运行，将代码放到bootstrap中
                    //加载标记模式脚本
                    {
//                        NSString* executorJs = [ResourceHelper addons].tagitHelper;
//                        BOOL allframe = NO;
//                        NSString* runAt = @"document_end";
//
//                        UserScript* item = [UserScript new];
//                        item.name = @"标记模式";
//                        item.isActive = YES;
//
//                        item.executorJs = executorJs;
//                        item.noframes = !allframe;
//                        item.runAt = runAt;
//                        
//                        model.js_tagit = item;
                    }
                    
                    //屏蔽牛皮癣广告脚本
                    {
                        //容易错杀，直接去掉
                    }
                    
                    //添加greasyfork安装脚本
                    {
                        NSString* executorJs = [ResourceHelper addons].addons_installHelper;
                        BOOL allframe = NO;
                        NSString* runAt = @"document_end";
                        
                        UserScript* item = [UserScript new];
                        item.name = @"安装脚本";
                        item.isActive = YES;
                        
                        item.executorJs = executorJs;
                        item.noframes = !allframe;
                        item.runAt = runAt;
                        
                        model.js_installHelper = item;
                    }
                }
                
                @autoreleasepool {
                    body = [model toDictionary];
                    
                    NSExtensionItem *response = [[NSExtensionItem alloc] init];
                    response.userInfo = @{ SFExtensionMessageKey: @{ @"type": type,
                                                                     @"body": body == nil? [NSNull null]:body,
                    }};
                    [context completeRequestReturningItems:@[ response ] completionHandler:nil];
                }
            }];
            
            return;
        }  else if([type isEqualToString:@"API_SCRIPT_ACTIVE_CHANGE"]) {
            //激活状态改变
            NSString* scriptId = message[@"scriptId"];
            int isActive = [message[@"isActive"] intValue];
            [ExUserScriptHelper updateUserScriptWithId:scriptId isActive:isActive];
        } else if([type isEqualToString:@"API_TAGIT_ACTION"]) {
            //标记模式
            NSString* xpath = message[@"xpath"];
            NSString* url = message[@"url"];
            int status = [message[@"status"] intValue];

            if(url.length > 0) {
                if(status == 0) {
                    //屏蔽
                    TagitModel* model = [[TagitModel alloc]initWithUrl:url xpath:xpath];
                    [ExtensionHelper addTagitWithItem:model];
                } else if(status == 1) {
                    //撤回
                    NSURL* URL = [NSURL URLWithString:url];
                    if(URL) {
                        NSString* host = [URL normalizedHost];
                        [ExtensionHelper removeTagitWithHost:host completion:^(NSString *xpath) {
                            body = xpath;
                            NSExtensionItem *response = [[NSExtensionItem alloc] init];
                            response.userInfo = @{ SFExtensionMessageKey: @{ @"type": type,
                                                                             @"body": body == nil? [NSNull null]:body,
                            }};
                            [context completeRequestReturningItems:@[ response ] completionHandler:nil];
                        }];

                        return;
                    }
                }
            }
        } else if([type isEqualToString:@"API_Request"]) {
            NSDictionary* data = message[@"data"];
            UserComandModel* model = [[UserComandModel alloc] initWithDictionary:data error:nil];

            [self xmlhttpRequest:model ctx:context];
            return;
        }
        
        if(type == NULL) {
            type = @"";
        }
        
        //要注意,body必须是字典类型
        NSExtensionItem *response = [[NSExtensionItem alloc] init];
        response.userInfo = @{ SFExtensionMessageKey: @{ @"type": type,
                                                         @"body": body == nil? [NSNull null]:body,
        }};
        [context completeRequestReturningItems:@[ response ] completionHandler:nil];
    }
}

#pragma mark -- 网络请求
- (void)xmlhttpRequest:(UserComandModel*)item ctx:(NSExtensionContext *)context
{
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    
    /**常见的三种序列化器：
     1、application/x-www-form-urlencoded，key1=value1&key2=value2
     2、application/json， { "key1": "value1", "key2": "value2"}
     3、application/x-plist，
     <?xml version="1.0" encoding="UTF-8"?>
     <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
     <plist version="1.0">
     <dict>
         <key>key1</key>
         <string>value1</string>
         <key>key2</key>
         <string>value2</string>
     </dict>
     </plist>
     */
    //指定请求序列化器为JSON格式
    //根据Content-Type区分序列化器
    
    /// 请求体的处理
    //将body从字符串转换为字典，因为“沉浸式翻译”传了body之后，item.data是字符串形式，导致被AFNetworking拼接成"=item.data"的形式
    //key和value不对
    id body = item.data;
    if(item.data) {
        @try {
            body = [NSString jsonConvertToObject:item.data];
        } @catch (NSException *exception) {
            NSLog(@"Error, 脚本转换请求体失败: %@", exception);
        } @finally {
        }
    }
    
    //默认值
    //再做一个保险判断
    if(!body) {
        body = item.data;
        //json都转换失败了，说明只能是默认传送类型了，适配海角脚本
        manager.requestSerializer = [CustomRequestSerializer serializer];
    } else {
        //默认值
        manager.requestSerializer = [CustomRequestSerializer serializer];
        if(item.headers) {
            NSString* contentType = item.headers[@"Content-Type"];
            if(contentType.length == 0) {
                contentType = item.headers[@"content-type"];
            }
            if(contentType.length>0) {
                if([contentType rangeOfString:@"application/json"].location != NSNotFound) {
                    //json
                    manager.requestSerializer = [AFJSONRequestSerializer serializer];
                } else if([contentType rangeOfString:@"application/x-plist"].location != NSNotFound) {
                    manager.requestSerializer = [AFPropertyListRequestSerializer serializer];
                } else if([contentType rangeOfString:@"application/x-www-form-urlencoded"].location != NSNotFound) {
                    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
                }
            }
        }
    }
    
    //指定响应类型
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    
    ///请求头的处理
    //需要清除headers中为nil的值,否则会报错崩溃
    /*
     自动无缝翻页脚本
     Accept = "text/html,application/xhtml+xml,application/xml";
     Referer = "https://m.baidu.com/s?word=app";
     "User-Agent" = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like MAC OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16_0 Mobile/15E148 Safari/604.1";
     "x-requested-with" = "<null>";
     */
    
    NSMutableDictionary* headers = [NSMutableDictionary dictionary];
    for (NSString *headerField in item.headers.keyEnumerator) {
        if(headerField.length>0 && item.headers[headerField] && !isNull(item.headers[headerField])) {
            headers[headerField] = item.headers[headerField];
        }
    }
    
    //v2.5.6 请求头的值必须是字符串类型
    //否则AFHTTPSessionManager中的[request setValue:headers[headerField] forHTTPHeaderField:headerField];会报错
    NSMutableDictionary *mutableDictionary = [NSMutableDictionary dictionary];
    for (id key in headers) {
        id value = headers[key];
        if ([value isKindOfClass:[NSNumber class]]) {
            value = [value stringValue];
        }
        [mutableDictionary setObject:value forKey:key];
    }
    headers = [mutableDictionary copy];
    
    //设置超时时间(单位:秒)，上面requestSerializer重新new了，注意否则设置无效
    NSInteger timeout = 30;
    if(item.timeout > 0) {
        //xmlhttpRequest的timeout超时时间是以毫秒为单位
        timeout = item.timeout / 1000;
    }
    [manager.requestSerializer setTimeoutInterval:timeout];
    
    @weakify(self)
    if([item.method.lowercaseString isEqualToString:@"get"]) {
        [manager GET:item.url parameters:body headers:headers progress:^(NSProgress * _Nonnull downloadProgress) {
        } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
            @strongify(self)
            [self handleXmlhttpRequestSuccess:task responseObject:responseObject item:item ctx:context];
        } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
            @strongify(self)
            [self handleXmlhttpRequestFailure:task error:error item:item ctx:context];
        }];
    } else if([item.method.lowercaseString isEqualToString:@"post"]) {
        [manager POST:item.url parameters:body headers:headers progress:^(NSProgress * _Nonnull uploadProgress) {
        } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
            @strongify(self)
            [self handleXmlhttpRequestSuccess:task responseObject:responseObject item:item ctx:context];
        } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
            @strongify(self)
            [self handleXmlhttpRequestFailure:task error:error item:item ctx:context];
        }];
    }
}

- (void)handleXmlhttpRequestSuccess:(NSURLSessionDataTask*)task
                     responseObject:(id)responseObject
                               item:(UserComandModel*)item
                                ctx:(NSExtensionContext *)context
{
    NSDictionary* responseHeader;
    NSString* responseUrl;
    NSInteger statusCode = 0;
    
    if([task.response isKindOfClass:NSHTTPURLResponse.class]) {
        NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)task.response;
        responseHeader = httpResponse.allHeaderFields;
        responseUrl = [httpResponse.URL absoluteString];
        statusCode = httpResponse.statusCode;
        
        item.mimeType = httpResponse.MIMEType;
    }
    
    NSString* responseContent;
    //有可能是NSData类型，例如image/png，此时需要将其转换为base64格式, 在js中再通过atob解压
    if([responseObject isKindOfClass:NSData.class] && ([item.responseType isEqualToString:@"blob"] || [item.responseType isEqualToString:@"arraybuffer"])) {
        //只处理blob/arraybuffer
        NSData *imageData = (NSData *)responseObject;
        // 将 NSData 转换为 Base64 字符串
        responseContent = [imageData base64EncodedStringWithOptions:0];
    } else {
        responseContent = [[NSString alloc] initWithData:responseObject encoding:NSUTF8StringEncoding];
    }
    
    if(responseHeader.count > 0) {
        NSMutableString* header = [NSMutableString new];
        [responseHeader enumerateKeysAndObjectsUsingBlock:^(NSString*  _Nonnull key, NSString*  _Nonnull obj, BOOL * _Nonnull stop) {
            NSString* item = [NSString stringWithFormat:@"%@: %@\r\n",key,obj];
            [header appendString:item];
        }];
        item.responseHeader = header;
    } else {
        item.responseHeader = @"";
    }
    item.responseUrl = responseUrl;
    item.statusCode = statusCode;
    item.responseContent = responseContent;
    
    item.responseCode = 1;
    
    NSDictionary* body = [item toDictionary];
    
    //要注意,body必须是字典类型
    NSExtensionItem *response = [[NSExtensionItem alloc] init];
    response.userInfo = @{ SFExtensionMessageKey: @{ @"type": @"API_Request",
                                                     @"body": body == nil? [NSNull null]:body,
    }};
    [context completeRequestReturningItems:@[ response ] completionHandler:nil];
}

- (void)handleXmlhttpRequestFailure:(NSURLSessionDataTask*)task
                              error:(NSError*)error
                               item:(UserComandModel*)item
                                ctx:(NSExtensionContext *)context
{
    if([task.response isKindOfClass:NSHTTPURLResponse.class]) {
        NSHTTPURLResponse* httpResponse = (NSHTTPURLResponse*)task.response;
        NSInteger statusCode = httpResponse.statusCode;
        
        item.statusCode = statusCode;
    }
    
    if(error.code == NSURLErrorTimedOut) {
        //timeout
        item.responseCode = 2;
    } else {
        //错误
        item.responseCode = 3;
    }
    
    NSDictionary* body = [item toDictionary];
    
    //要注意,body必须是字典类型
    NSExtensionItem *response = [[NSExtensionItem alloc] init];
    response.userInfo = @{ SFExtensionMessageKey: @{ @"type": @"API_Request",
                                                     @"body": body == nil? [NSNull null]:body,
    }};
    [context completeRequestReturningItems:@[ response ] completionHandler:nil];
}


@end
