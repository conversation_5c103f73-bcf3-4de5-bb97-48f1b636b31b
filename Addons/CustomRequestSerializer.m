//
//  CustomRequestSerializer.m
//  PPBrowser
//
//  Created by qingbin on 2024/7/6.
//  Copyright © 2024 qingbin. All rights reserved.
//

#import "CustomRequestSerializer.h"
#import "NSString+Helper.h"

@implementation CustomRequestSerializer

- (NSMutableURLRequest *)requestWithMethod:(NSString *)method
                                 URLString:(NSString *)URLString
                                parameters:(nullable id)parameters 
                                     error:(NSError *__autoreleasing  _Nullable *)error {
    NSMutableURLRequest *request = [super requestWithMethod:method URLString:URLString parameters:parameters error:error];
    
    // 设置自定义的请求体字符串
    if(parameters) {
        if([parameters isKindOfClass:NSString.class]) {
            request.HTTPBody = [parameters dataUsingEncoding:NSUTF8StringEncoding];
        } else if([parameters isKindOfClass:NSDictionary.class]) {
            NSString* json = [NSString convertToJsonString:parameters options:YES];
            request.HTTPBody = [json dataUsingEncoding:NSUTF8StringEncoding];
        }
    }
    
    return request;
}

@end
