//
//  UserComandModel.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BaseModel.h"

#import "PPEnums.h"

@interface UserComandModel : BaseModel

@property (nonatomic, strong) NSString *uuid;

@property (nonatomic, strong) NSString *name;

@property (nonatomic, strong) NSString *value;

//@property (nonatomic, strong) NSString *type;

//用于对于同一个name产生的多个不同监听/或者网络请求
@property (nonatomic, strong) NSString *randomId;

//xmlhttprequest请求参数
@property (nonatomic, strong) NSString *method;
@property (nonatomic, strong) NSString *url;
@property (nonatomic, strong) NSString *data;
@property (nonatomic, strong) NSDictionary *headers;
@property (nonatomic, assign) int timeout;
@property (nonatomic, strong) NSString *responseType;

//xmlhttprequest响应参数
//responseHeader的格式参考https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders
@property (nonatomic, strong) NSString* responseHeader;
@property (nonatomic, strong) NSString* responseUrl;
@property (nonatomic, assign) NSInteger statusCode;
@property (nonatomic, strong) NSString* responseContent;
@property (nonatomic, strong) NSString* mimeType;

// 1-正常, 2-超时, 3-请求错误
@property (nonatomic, assign) NSInteger responseCode;

@end

@protocol UserComandModel <NSObject>
@end

