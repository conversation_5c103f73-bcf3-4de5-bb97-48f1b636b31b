//
//  SafariWebExtensionHandler.h
//  Addons
//
//  Created by qingbin on 2023/3/13.
//

#import <Foundation/Foundation.h>

@interface SafariWebExtensionHandler : NSObject <NSExtensionRequestHandling>

@end

//油猴脚本语法
//https://www.tampermonkey.net/documentation.php?locale=en

//background.js调试
//https://stackoverflow.com/questions/40316007/debugging-background-scripts-in-safari-extension

//js加密方法
//https://www.bejson.com/encrypt/jsobfuscate/
//js压缩成一行(background.js)
//https://www.toptal.com/developers/javascript-minifier

//min-height: 300px;
//主要是为了适配iPhone8/iPhone8 plus/iPad这三种情况,popup.html底部工具栏显示有问题的BUG

/**
 iOS17.5
 //EXC_RESOURCE (RESOURCE_TYPE_MEMORY: high watermark memory limit exceeded) (limit=80 MB)
 //内存报错
 
 iOS18，有两个错误：
 1、browser.storage.local.set报错，主要是脚本的内容太多，超过了Safari扩展的限制值
 1）将脚本的代码拆分，resourceJson、@require数组和纯script代码
 
 2、popup.js打开空白
 browser.tabs.getSelected已废弃，导致拿不到数据
 
 //iOS18 window.open也已失效, 但是iOS17以下，a标签的click方法不生效，因此两者并存
 let schemeUrl = "addons://activeAction";
 window.open(schemeUrl);
 
 background.js引入Dexie.js后，用常规的混淆方法已不起作用
 采用下面网站进行压缩: https://www.toptal.com/developers/javascript-minifier
 */


/**
 GM.xmlHttpRequest等API的入参和出参说明(UserScript)
 https://github.com/quoid/userscripts/tree/5ba90bc4f6fdf43bcadd5044b47030abe1006af3
 */


/**
 插件调试技巧：
 1、content_script.js / background.js，可以通过单步调试
 2、针对添加到head的脚本，可以通过打console.error来查看日志
 */


/**
 视频相关的测试链接：
 1、抖音，https://www.douyin.com/?is_from_mobile_home=1&recommend=1
 测试其是否会因为await GM.getValue中的prompt，从而会自动暂停播放抖音视频
 
 2、https://supersimple.com/song/hello/
 iframe中的视频，测试其按钮是否显示正常，长按播放是否正常
 
 3、http://www.857yhdm.com/play/8558-1-1.html
 樱花动漫，同上
 
 4、m3u8play.com，没有iframe的操作
 
 5、https://tvlabs.cn/post/189，测试多个iframe的显示情况

 */


/**
 IndexedDB操作库:
 https://github.com/dexie/Dexie.js
 使用例子:
 https://dexie.org/docs/API-Reference
 */


/**
 v4.1版本的暗黑模式
 Dark Night(主要)
 参考来源于https://github.com/darkreader/darkreader
 1、将所有的目录引用都删除，都是在Resource下，包括在bg_index, CONFIG_URLs的赋值中，删除了local中的config
 还有injectOrg目录
 2、替换isURLInList方法，因为在百度搜索中，禁止网页没法使用，isURLInList的逻辑有问题
 
 用法：
 开关：
 //Auto: data={automation: "system"}
 //off: data={enabled: false, automation: ""}
 //On: data={enabled: true, automation: ""}
 
 ui-change-settings
 browser.runtime.sendMessage({
     type: "ui-change-settings",
     data: {
       enabled: false,
       automation: ""
     }
 });
 
 图片亮度：
 ui-set-theme
 {imageBrightness: 79}
 
 是否允许当前网站开启暗黑模式
 ui-toggle-url
 
 v4.6
 PlaylistDetector.js，改为AugmentCode生成的shadowroot方案
 
 */
