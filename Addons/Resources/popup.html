<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta content="yes" name="apple-mobile-web-app-capable" />
        <meta content="black" name="apple-mobile-web-app-status-bar-style" />
        <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=5,viewport-fit=cover" />
    <link rel="stylesheet" href="popup.css">
    <script src="jquery-1.9.1.min.js"></script>
    <script type="module" src="popup.js"></script>
</head>
<body>
    <div class="global-container">
        <div class="header" id="id-header">已匹配脚本列表</div>

        <div class="content-container">
            <!-- 模块开关 -->
            <div class="enabled-container" id="id-enabled-container" style="display:none">
                <div class="enabled-text" id="id-enabled-text"></div>
                <div class="enabled-button" id="id-enabled-button"></div>
            </div>
                
            <!-- 油猴脚本 -->
            <div class="addons-fix-level1" id="id-script-fix">
                <div class="addons-fix-level2">
                    <div class="script-scrollView" id="id-script-scrollView">
                    </div>
                </div>
            </div>

            <!-- 标记模式 -->
            <div class="addons-fix-level1" id="id-tagit-fix" style="display:none">
                <div class="addons-fix-level2">
                    <div class="tagit-scrollView" id="id-tagit-scrollView">
                    </div>
                </div>
            </div>
            <div class="tagit-add-button" id="id-tagit-addbutton" style="display:none">进入标记模式</div>

            <!-- 暗黑模式 -->
            <div class="darkmode-container" id="id-darkmode-container" style="display:none">
                    
                <!-- 是否允许当前网站开启暗黑模式 -->
                <div id="id-scope-tips-text" class="__dark-enable-current-tips">
                    是否允许当前网站开启暗黑模式
                </div>
                <div class="__dark-row _dark-website">
                    <div class="__dark-row-label">
                        <img class="__dark-row-label-image __dark-row-website"></img>
                        <span class="__dark-row-label-text" id="id-scope-span"></span>
                    </div>

                    <div class="__dark-row-detail">
                        <div class="__dark-row-detail-container">
                            <div class="__dark-row-detail-container-cover">
                                <span id="id-scope-text">允许</span>
                                <img src="./images/arrow_up_down.svg" class="__dark-row-up-down-arrow">
                            </div>
                            
                            <select data-option="enabled" class="__dark-row-select" id="id-scope-select">
                                <optgroup>
                                    <option data-option="scope" value="1" id="id-scope-enable-text">允许</option>
                                    <option data-option="scope" value="2" id="id-scope-site-text">禁止</option>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                </div>

                    
                <!-- 启用状态 -->
                <div class="__dark-row _dark-enabled">
                    <div class="__dark-row-label">
                        <img class="__dark-row-label-image __dark-row-enabled"></img>
                        <span class="__dark-row-label-text" id="id-enabled-span">启用状态</span>
                    </div>

                    <div class="__dark-row-detail">
                        <div class="__dark-row-detail-container">
                            <div class="__dark-row-detail-container-cover">
                                <span id="id-enabled-text2">跟随系统</span>
                                <img src="./images/arrow_up_down.svg" class="__dark-row-up-down-arrow">
                            </div>
                            
                            <select data-option="enabled" class="__dark-row-select" id="id-enabled-select">
                                <optgroup>
                                    <option data-option="enabled" value="1" id="id-enabled-auto-text">跟随系统</option>
                                    <option data-option="enabled" value="2" id="id-enabled-on-text">开启</option>
                                    <option data-option="enabled" value="3" id="id-enabled-off-text">关闭</option>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                </div>


                <!-- 图片明亮度 -->
                <div class="__dark-row _dark-light" id="id-dimImages">
                    <div class="__dark-row-label">
                        <img class="__dark-row-label-image __dark-row-light"/>
                        <span class="__dark-row-label-text" id="id-dimImages-span">图片明亮度</span>
                    </div>
                    
                    <div class="__dark-row-detail">
                        <div class="__dark-row-detail-container">
                            <div class="__dark-row-detail-container-cover">
                                <span id="id-dimImages-text">100%</span>
                                <img src="./images/arrow_up_down.svg" class="__dark-row-up-down-arrow">
                            </div>
                            
                            <select data-option="dimImages" class="__dark-row-select" id="id-dimImages-select">
                                <optgroup>
                                    <option data-option="dimImages" value="100"  id="mode0">100%</option>
                                    <option data-option="dimImages" value="80"  id="mode1">80%</option>
                                    <option data-option="dimImages" value="60"  id="mode2">60%</option>
                                    <option data-option="dimImages" value="40"  id="mode3">40%</option>
                                    <option data-option="dimImages" value="20"  id="mode4">20%</option>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>

        <div class="content-footer" id="id-content-footer">
            <div id="id-footer-js" class="footer-item" tab="0" isSelect="">
            </div>
            <div id="id-footer-tagit" class="footer-item" tab="1">
            </div>
            <div id="id-footer-darkmode" class="footer-item" tab="2">
            </div>
        </div>
    </div>
</body>
</html>
