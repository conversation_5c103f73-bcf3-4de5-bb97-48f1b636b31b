!function(){function e(e){return null==e||0===Object.keys(e).length}n=this,r=function(){"use strict";var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)},t=function(){return(t=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function n(e,t,n){if(n||2===arguments.length)for(var r,i=0,a=t.length;i<a;i++)!r&&i in t||((r=r||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,i=Object.keys,a=Array.isArray;function o(e,t){return"object"!=typeof t||i(t).forEach(function(n){e[n]=t[n]}),e}"undefined"==typeof Promise||r.Promise||(r.Promise=Promise);var s=Object.getPrototypeOf,u={}.hasOwnProperty;function l(e,t){return u.call(e,t)}function c(e,t){"function"==typeof t&&(t=t(s(e))),("undefined"==typeof Reflect?i:Reflect.ownKeys)(t).forEach(function(n){d(e,n,t[n])})}var f=Object.defineProperty;function d(e,t,n,r){f(e,t,o(n&&l(n,"get")&&"function"==typeof n.get?{get:n.get,set:n.set,configurable:!0}:{value:n,configurable:!0,writable:!0},r))}function h(e){return{from:function(t){return e.prototype=Object.create(t.prototype),d(e.prototype,"constructor",e),{extend:c.bind(null,e.prototype)}}}}var p=Object.getOwnPropertyDescriptor,$=[].slice;function m(e,t,n){return $.call(e,t,n)}function y(e,t){return t(e)}function v(e){if(!e)throw Error("Assertion Failed")}function g(e){r.setImmediate?setImmediate(e):setTimeout(e,0)}function _(e,t){if("string"==typeof t&&l(e,t))return e[t];if(!t)return e;if("string"!=typeof t){for(var n=[],r=0,i=t.length;r<i;++r){var a=_(e,t[r]);n.push(a)}return n}var o=t.indexOf(".");if(-1!==o){var s=e[t.substr(0,o)];return null==s?void 0:_(s,t.substr(o+1))}}function b(e,t,n){if(e&&void 0!==t&&!("isFrozen"in Object&&Object.isFrozen(e))){if("string"!=typeof t&&"length"in t){v("string"!=typeof n&&"length"in n);for(var r=0,i=t.length;r<i;++r)b(e,t[r],n[r])}else{var o,s,u=t.indexOf(".");-1!==u?(o=t.substr(0,u),""===(s=t.substr(u+1))?void 0===n?a(e)&&!isNaN(parseInt(o))?e.splice(o,1):delete e[o]:e[o]=n:b(u=(u=e[o])&&l(e,o)?u:e[o]={},s,n)):void 0===n?a(e)&&!isNaN(parseInt(t))?e.splice(t,1):delete e[t]:e[t]=n}}}function w(e){var t,n={};for(t in e)l(e,t)&&(n[t]=e[t]);return n}var k=[].concat;function x(e){return k.apply([],e)}var E="BigUint64Array,BigInt64Array,Array,Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,FileSystemDirectoryHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey".split(",").concat(x([8,16,32,64].map(function(e){return["Int","Uint","Float"].map(function(t){return t+e+"Array"})}))).filter(function(e){return r[e]}),A=new Set(E.map(function(e){return r[e]})),I=null;function S(e){return I=new WeakMap,e=function e(t){if(!t||"object"!=typeof t)return t;var n=I.get(t);if(n)return n;if(a(t)){n=[],I.set(t,n);for(var r=0,i=t.length;r<i;++r)n.push(e(t[r]))}else if(A.has(t.constructor))n=t;else{var o,u=s(t);for(o in n=u===Object.prototype?{}:Object.create(u),I.set(t,n),t)l(t,o)&&(n[o]=e(t[o]))}return n}(e),I=null,e}var D={}.toString;function P(e){return D.call(e).slice(8,-1)}var O="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator",C="symbol"==typeof O?function(e){var t;return null!=e&&(t=e[O])&&t.apply(e)}:function(){return null};function R(e,t){return 0<=(t=e.indexOf(t))&&e.splice(t,1),0<=t}var T={};function z(e){var t,n,r,i;if(1===arguments.length){if(a(e))return e.slice();if(this===T&&"string"==typeof e)return[e];if(i=C(e)){for(n=[];!(r=i.next()).done;)n.push(r.value);return n}if(null==e||"number"!=typeof(t=e.length))return[e];for(n=Array(t);t--;)n[t]=e[t];return n}for(t=arguments.length,n=Array(t);t--;)n[t]=arguments[t];return n}var K="undefined"!=typeof Symbol?function(e){return"AsyncFunction"===e[Symbol.toStringTag]}:function(){return!1},q=["Unknown","Constraint","Data","TransactionInactive","ReadOnly","Version","NotFound","InvalidState","InvalidAccess","Abort","Timeout","QuotaExceeded","Syntax","DataClone"],B=["Modify","Bulk","OpenFailed","VersionChange","Schema","Upgrade","InvalidTable","MissingAPI","NoSuchDatabase","InvalidArgument","SubTransaction","Unsupported","Internal","DatabaseClosed","PrematureCommit","ForeignAwait"].concat(q),F={VersionChanged:"Database version changed by other database connection",DatabaseClosed:"Database has been closed",Abort:"Transaction aborted",TransactionInactive:"Transaction has already completed or failed",MissingAPI:"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb"};function N(e,t){this.name=e,this.message=t}function j(e,t){return e+". Errors: "+Object.keys(t).map(function(e){return t[e].toString()}).filter(function(e,t,n){return n.indexOf(e)===t}).join("\n")}function M(e,t,n,r){this.failures=t,this.failedKeys=r,this.successCount=n,this.message=j(e,t)}function L(e,t){this.name="BulkError",this.failures=Object.keys(t).map(function(e){return t[e]}),this.failuresByPos=t,this.message=j(e,this.failures)}h(N).from(Error).extend({toString:function(){return this.name+": "+this.message}}),h(M).from(N),h(L).from(N);var U=B.reduce(function(e,t){return e[t]=t+"Error",e},{}),Z=N,V=B.reduce(function(e,t){var n=t+"Error";function r(e,r){this.name=n,e?"string"==typeof e?(this.message="".concat(e).concat(r?"\n "+r:""),this.inner=r||null):"object"==typeof e&&(this.message="".concat(e.name," ").concat(e.message),this.inner=e):(this.message=F[t]||n,this.inner=null)}return h(r).from(Z),e[t]=r,e},{});V.Syntax=SyntaxError,V.Type=TypeError,V.Range=RangeError;var H=q.reduce(function(e,t){return e[t+"Error"]=V[t],e},{}),G=B.reduce(function(e,t){return -1===["Syntax","Type","Range"].indexOf(t)&&(e[t+"Error"]=V[t]),e},{});function W(){}function Y(e){return e}function J(e,t){return null==e||e===Y?t:function(n){return t(e(n))}}function X(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}function Q(e,t){return e===W?t:function(){var n=e.apply(this,arguments);void 0!==n&&(arguments[0]=n);var r=this.onsuccess,i=this.onerror;this.onsuccess=null,this.onerror=null;var a=t.apply(this,arguments);return r&&(this.onsuccess=this.onsuccess?X(r,this.onsuccess):r),i&&(this.onerror=this.onerror?X(i,this.onerror):i),void 0!==a?a:n}}function ee(e,t){return e===W?t:function(){e.apply(this,arguments);var n=this.onsuccess,r=this.onerror;this.onsuccess=this.onerror=null,t.apply(this,arguments),n&&(this.onsuccess=this.onsuccess?X(n,this.onsuccess):n),r&&(this.onerror=this.onerror?X(r,this.onerror):r)}}function et(e,t){return e===W?t:function(n){var r=e.apply(this,arguments);o(n,r);var i=this.onsuccess,a=this.onerror;return this.onsuccess=null,this.onerror=null,n=t.apply(this,arguments),i&&(this.onsuccess=this.onsuccess?X(i,this.onsuccess):i),a&&(this.onerror=this.onerror?X(a,this.onerror):a),void 0===r?void 0===n?void 0:n:o(r,n)}}function en(e,t){return e===W?t:function(){return!1!==t.apply(this,arguments)&&e.apply(this,arguments)}}function er(e,t){return e===W?t:function(){var n=e.apply(this,arguments);if(n&&"function"==typeof n.then){for(var r=this,i=arguments.length,a=Array(i);i--;)a[i]=arguments[i];return n.then(function(){return t.apply(r,a)})}return t.apply(this,arguments)}}G.ModifyError=M,G.DexieError=N,G.BulkError=L;var ei="undefined"!=typeof location&&/^(http|https):\/\/(localhost|127\.0\.0\.1)/.test(location.href);function ea(e){ei=e}var eo={},es=100,E="undefined"==typeof Promise?[]:function(){var e=Promise.resolve();if("undefined"==typeof crypto||!crypto.subtle)return[e,s(e),e];var t=crypto.subtle.digest("SHA-512",new Uint8Array([0]));return[t,s(t),e]}(),q=E[0],B=E[1],E=E[2],B=B&&B.then,eu=q&&q.constructor,el=!!E,ec=function(e,t){ey.push([e,t]),ed&&(queueMicrotask(e4),ed=!1)},ef=!0,ed=!0,eh=[],ep=[],e$=Y,em={id:"global",global:!0,ref:0,unhandleds:[],onunhandled:W,pgp:!1,env:{},finalize:W},e8=em,ey=[],ev=0,eg=[];function e_(e){if("object"!=typeof this)throw TypeError("Promises must be constructed via new");this._listeners=[],this._lib=!1;var t=this._PSD=e8;if("function"!=typeof e){if(e!==eo)throw TypeError("Not a function");return this._state=arguments[1],this._value=arguments[2],void(!1===this._state&&e0(this,this._value))}this._state=null,this._value=null,++t.ref,function e(t,n){try{n(function(n){if(null===t._state){if(n===t)throw TypeError("A promise cannot be resolved with itself.");var r=t._lib&&eE();n&&"function"==typeof n.then?e(t,function(e,t){n instanceof e_?n._then(e,t):n.then(e,t)}):(t._state=!0,t._value=n,ek(t)),r&&e1()}},e0.bind(null,t))}catch(r){e0(t,r)}}(this,e)}var eb={get:function(){var e=e8,t=eC;function n(n,r){var i=this,a=!e.global&&(e!==e8||t!==eC),o=a&&!e2(),s=new e_(function(t,s){ex(i,new ew(eN(n,e,a,o),eN(r,e,a,o),t,s,e))});return this._consoleTask&&(s._consoleTask=this._consoleTask),s}return n.prototype=eo,n},set:function(e){d(this,"then",e&&e.prototype===eo?eb:{get:function(){return e},set:eb.set})}};function ew(e,t,n,r,i){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r,this.psd=i}function e0(e,t){var n,r;ep.push(t),null===e._state&&(n=e._lib&&eE(),t=e$(t),e._state=!1,e._value=t,r=e,eh.some(function(e){return e._value===r._value})||eh.push(r),ek(e),n&&e1())}function ek(e){var t=e._listeners;e._listeners=[];for(var n=0,r=t.length;n<r;++n)ex(e,t[n]);var i=e._PSD;--i.ref||i.finalize(),0===ev&&(++ev,ec(function(){0==--ev&&eA()},[]))}function ex(e,t){if(null!==e._state){var n=e._state?t.onFulfilled:t.onRejected;if(null===n)return(e._state?t.resolve:t.reject)(e._value);++t.psd.ref,++ev,ec(e3,[n,e,t])}else e._listeners.push(t)}function e3(e,t,n){try{var r,i=t._value;!t._state&&ep.length&&(ep=[]),r=ei&&t._consoleTask?t._consoleTask.run(function(){return e(i)}):e(i),t._state||-1!==ep.indexOf(i)||function(e){for(var t=eh.length;t;)if(eh[--t]._value===e._value)return eh.splice(t,1)}(t),n.resolve(r)}catch(a){n.reject(a)}finally{0==--ev&&eA(),--n.psd.ref||n.psd.finalize()}}function e4(){eF(em,function(){eE()&&e1()})}function eE(){var e=ef;return ed=ef=!1,e}function e1(){var e,t,n;do for(;0<ey.length;)for(e=ey,ey=[],n=e.length,t=0;t<n;++t){var r=e[t];r[0].apply(null,r[1])}while(0<ey.length);ed=ef=!0}function eA(){var e=eh;eh=[],e.forEach(function(e){e._PSD.onunhandled.call(null,e._value,e)});for(var t=eg.slice(0),n=t.length;n;)t[--n]()}function eI(e){return new e_(eo,!1,e)}function eS(e,t){var n=e8;return function(){var r=eE(),i=e8;try{return e7(n,!0),e.apply(this,arguments)}catch(a){t&&t(a)}finally{e7(i,!1),r&&e1()}}}c(e_.prototype,{then:eb,_then:function(e,t){ex(this,new ew(null,null,e,t,e8))},catch:function(e){if(1===arguments.length)return this.then(null,e);var t=e,n=arguments[1];return"function"==typeof t?this.then(null,function(e){return(e instanceof t?n:eI)(e)}):this.then(null,function(e){return(e&&e.name===t?n:eI)(e)})},finally:function(e){return this.then(function(t){return e_.resolve(e()).then(function(){return t})},function(t){return e_.resolve(e()).then(function(){return eI(t)})})},timeout:function(e,t){var n=this;return e<1/0?new e_(function(r,i){var a=setTimeout(function(){return i(new V.Timeout(t))},e);n.then(r,i).finally(clearTimeout.bind(null,a))}):this}}),"undefined"!=typeof Symbol&&Symbol.toStringTag&&d(e_.prototype,Symbol.toStringTag,"Dexie.Promise"),em.env=eB(),c(e_,{all:function(){var e=z.apply(null,arguments).map(eK);return new e_(function(t,n){0===e.length&&t([]);var r=e.length;e.forEach(function(i,a){return e_.resolve(i).then(function(n){e[a]=n,--r||t(e)},n)})})},resolve:function(e){return e instanceof e_?e:e&&"function"==typeof e.then?new e_(function(t,n){e.then(t,n)}):new e_(eo,!0,e)},reject:eI,race:function(){var e=z.apply(null,arguments).map(eK);return new e_(function(t,n){e.map(function(e){return e_.resolve(e).then(t,n)})})},PSD:{get:function(){return e8},set:function(e){return e8=e}},totalEchoes:{get:function(){return eC}},newPSD:eT,usePSD:eF,scheduler:{get:function(){return ec},set:function(e){ec=e}},rejectionMapper:{get:function(){return e$},set:function(e){e$=e}},follow:function(e,t){return new e_(function(n,r){return eT(function(t,n){var r=e8;r.unhandleds=[],r.onunhandled=n,r.finalize=X(function(){var e,r=this;e=function(){0===r.unhandleds.length?t():n(r.unhandleds[0])},eg.push(function t(){e(),eg.splice(eg.indexOf(t),1)}),++ev,ec(function(){0==--ev&&eA()},[])},r.finalize),e()},t,n,r)})}}),eu&&(eu.allSettled&&d(e_,"allSettled",function(){var e=z.apply(null,arguments).map(eK);return new e_(function(t){0===e.length&&t([]);var n=e.length,r=Array(n);e.forEach(function(e,i){return e_.resolve(e).then(function(e){return r[i]={status:"fulfilled",value:e}},function(e){return r[i]={status:"rejected",reason:e}}).then(function(){return--n||t(r)})})})}),eu.any&&"undefined"!=typeof AggregateError&&d(e_,"any",function(){var e=z.apply(null,arguments).map(eK);return new e_(function(t,n){0===e.length&&n(AggregateError([]));var r=e.length,i=Array(r);e.forEach(function(e,a){return e_.resolve(e).then(function(e){return t(e)},function(e){i[a]=e,--r||n(AggregateError(i))})})})}));var eD={awaits:0,echoes:0,id:0},eP=0,e6=[],eO=0,eC=0,eR=0;function eT(e,t,n,r){var i=e8,a=Object.create(i);return a.parent=i,a.ref=0,a.global=!1,a.id=++eR,em.env,a.env=el?{Promise:e_,PromiseProp:{value:e_,configurable:!0,writable:!0},all:e_.all,race:e_.race,allSettled:e_.allSettled,any:e_.any,resolve:e_.resolve,reject:e_.reject}:{},t&&o(a,t),++i.ref,a.finalize=function(){--this.parent.ref||this.parent.finalize()},r=eF(a,e,n,r),0===a.ref&&a.finalize(),r}function ez(){return eD.id||(eD.id=++eP),++eD.awaits,eD.echoes+=es,eD.id}function e2(){return!!eD.awaits&&(0==--eD.awaits&&(eD.id=0),eD.echoes=eD.awaits*es,!0)}function eK(e){return eD.echoes&&e&&e.constructor===eu?(ez(),e.then(function(e){return e2(),e},function(e){return e2(),eM(e)})):e}function eq(){var e=e6[e6.length-1];e6.pop(),e7(e,!1)}function e7(e,t){var n,i=e8;(t?!eD.echoes||eO++&&e===e8:!eO||--eO&&e===e8)||queueMicrotask(t?(function(e){++eC,eD.echoes&&0!=--eD.echoes||(eD.echoes=eD.awaits=eD.id=0),e6.push(e8),e7(e,!0)}).bind(null,e):eq),e!==e8&&(e8=e,i===em&&(em.env=eB()),el&&(n=em.env.Promise,t=e.env,(i.global||e.global)&&(Object.defineProperty(r,"Promise",t.PromiseProp),n.all=t.all,n.race=t.race,n.resolve=t.resolve,n.reject=t.reject,t.allSettled&&(n.allSettled=t.allSettled),t.any&&(n.any=t.any))))}function eB(){var e=r.Promise;return el?{Promise:e,PromiseProp:Object.getOwnPropertyDescriptor(r,"Promise"),all:e.all,race:e.race,allSettled:e.allSettled,any:e.any,resolve:e.resolve,reject:e.reject}:{}}function eF(e,t,n,r,i){var a=e8;try{return e7(e,!0),t(n,r,i)}finally{e7(a,!1)}}function eN(e,t,n,r){return"function"!=typeof e?e:function(){var i=e8;n&&ez(),e7(t,!0);try{return e.apply(this,arguments)}finally{e7(i,!1),r&&queueMicrotask(e2)}}}function ej(e){Promise===eu&&0===eD.echoes?0===eO?e():enqueueNativeMicroTask(e):setTimeout(e,0)}-1===(""+B).indexOf("[native code]")&&(ez=e2=W);var eM=e_.reject,eL="Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.",e5="String expected.",eU=[],eZ="__dbnames",eV="readonly",eH="readwrite";function eG(e,t){return e?t?function(){return e.apply(this,arguments)&&t.apply(this,arguments)}:e:t}var eW={type:3,lower:-1/0,lowerOpen:!1,upper:[[]],upperOpen:!1};function eY(e){return"string"!=typeof e||/\./.test(e)?function(e){return e}:function(t){return void 0===t[e]&&e in t&&delete(t=S(t))[e],t}}function e9(){throw V.Type()}function eJ(e,t){try{var n=eX(e),r=eX(t);if(n!==r)return"Array"===n?1:"Array"===r?-1:"binary"===n?1:"binary"===r?-1:"string"===n?1:"string"===r?-1:"Date"===n?1:"Date"!==r?NaN:-1;switch(n){case"number":case"Date":case"string":return t<e?1:e<t?-1:0;case"binary":return function(e,t){for(var n=e.length,r=t.length,i=n<r?n:r,a=0;a<i;++a)if(e[a]!==t[a])return e[a]<t[a]?-1:1;return n===r?0:n<r?-1:1}(eQ(e),eQ(t));case"Array":return function(e,t){for(var n=e.length,r=t.length,i=n<r?n:r,a=0;a<i;++a){var o=eJ(e[a],t[a]);if(0!==o)return o}return n===r?0:n<r?-1:1}(e,t)}}catch(i){}return NaN}function eX(e){var t=typeof e;return"object"!=t?t:ArrayBuffer.isView(e)?"binary":"ArrayBuffer"===(e=P(e))?"binary":e}function eQ(e){return e instanceof Uint8Array?e:ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(e)}var te=(tt.prototype._trans=function(e,t,n){var r=this._tx||e8.trans,i=this.name,a=ei&&"undefined"!=typeof console&&console.createTask&&console.createTask("Dexie: ".concat("readonly"===e?"read":"write"," ").concat(this.name));function o(e,n,r){if(!r.schema[i])throw new V.NotFound("Table "+i+" not part of transaction");return t(r.idbtrans,r)}var s=eE();try{var u=r&&r.db._novip===this.db._novip?r===e8.trans?r._promise(e,o,n):eT(function(){return r._promise(e,o,n)},{trans:r,transless:e8.transless||e8}):function e(t,n,r,i){if(t.idbdb&&(t._state.openComplete||e8.letThrough||t._vip)){var a=t._createTransaction(n,r,t._dbSchema);try{a.create(),t._state.PR1398_maxLoop=3}catch(o){return o.name===U.InvalidState&&t.isOpen()&&0<--t._state.PR1398_maxLoop?(console.warn("Dexie: Need to reopen db"),t.close({disableAutoOpen:!1}),t.open().then(function(){return e(t,n,r,i)})):eM(o)}return a._promise(n,function(e,t){return eT(function(){return e8.trans=a,i(e,t,a)})}).then(function(e){if("readwrite"===n)try{a.idbtrans.commit()}catch(t){}return"readonly"===n?e:a._completion.then(function(){return e})})}if(t._state.openComplete)return eM(new V.DatabaseClosed(t._state.dbOpenError));if(!t._state.isBeingOpened){if(!t._state.autoOpen)return eM(new V.DatabaseClosed);t.open().catch(W)}return t._state.dbReadyPromise.then(function(){return e(t,n,r,i)})}(this.db,e,[this.name],o);return a&&(u._consoleTask=a,u=u.catch(function(e){return console.trace(e),eM(e)})),u}finally{s&&e1()}},tt.prototype.get=function(e,t){var n=this;return e&&e.constructor===Object?this.where(e).first(t):null==e?eM(new V.Type("Invalid argument to Table.get()")):this._trans("readonly",function(t){return n.core.get({trans:t,key:e}).then(function(e){return n.hook.reading.fire(e)})}).then(t)},tt.prototype.where=function(e){if("string"==typeof e)return new this.db.WhereClause(this,e);if(a(e))return new this.db.WhereClause(this,"[".concat(e.join("+"),"]"));var t=i(e);if(1===t.length)return this.where(t[0]).equals(e[t[0]]);var n=this.schema.indexes.concat(this.schema.primKey).filter(function(e){if(e.compound&&t.every(function(t){return 0<=e.keyPath.indexOf(t)})){for(var n=0;n<t.length;++n)if(-1===t.indexOf(e.keyPath[n]))return!1;return!0}return!1}).sort(function(e,t){return e.keyPath.length-t.keyPath.length})[0];if(n&&"￿"!==this.db._maxKey){var r=n.keyPath.slice(0,t.length);return this.where(r).equals(r.map(function(t){return e[t]}))}!n&&ei&&console.warn("The query ".concat(JSON.stringify(e)," on ").concat(this.name," would benefit from a ")+"compound index [".concat(t.join("+"),"]"));var o=this.schema.idxByName,s=this.db._deps.indexedDB;function u(e,t){return 0===s.cmp(e,t)}var l=t.reduce(function(t,n){var r=t[0],i=t[1],t=o[n],s=e[n];return[r||t,r||!t?eG(i,t&&t.multi?function(e){return e=_(e,n),a(e)&&e.some(function(e){return u(s,e)})}:function(e){return u(s,_(e,n))}):i]},[null,null]),r=l[0],l=l[1];return r?this.where(r.name).equals(e[r.keyPath]).filter(l):n?this.filter(l):this.where(t).equals("")},tt.prototype.filter=function(e){return this.toCollection().and(e)},tt.prototype.count=function(e){return this.toCollection().count(e)},tt.prototype.offset=function(e){return this.toCollection().offset(e)},tt.prototype.limit=function(e){return this.toCollection().limit(e)},tt.prototype.each=function(e){return this.toCollection().each(e)},tt.prototype.toArray=function(e){return this.toCollection().toArray(e)},tt.prototype.toCollection=function(){return new this.db.Collection(new this.db.WhereClause(this))},tt.prototype.orderBy=function(e){return new this.db.Collection(new this.db.WhereClause(this,a(e)?"[".concat(e.join("+"),"]"):e))},tt.prototype.reverse=function(){return this.toCollection().reverse()},tt.prototype.mapToClass=function(t){var n,r=this.db,i=this.name;function a(){return null!==n&&n.apply(this,arguments)||this}(this.schema.mappedClass=t).prototype instanceof e9&&(function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}(a,n=t),Object.defineProperty(a.prototype,"db",{get:function(){return r},enumerable:!1,configurable:!0}),a.prototype.table=function(){return i},t=a);for(var o=new Set,u=t.prototype;u;u=s(u))Object.getOwnPropertyNames(u).forEach(function(e){return o.add(e)});function l(e){if(!e)return e;var n,r=Object.create(t.prototype);for(n in e)if(!o.has(n))try{r[n]=e[n]}catch(i){}return r}return this.schema.readHook&&this.hook.reading.unsubscribe(this.schema.readHook),this.schema.readHook=l,this.hook("reading",l),t},tt.prototype.defineClass=function(){return this.mapToClass(function(e){o(this,e)})},tt.prototype.add=function(e,t){var n=this,r=this.schema.primKey,i=r.auto,a=r.keyPath,o=e;return a&&i&&(o=eY(a)(e)),this._trans("readwrite",function(e){return n.core.mutate({trans:e,type:"add",keys:null!=t?[t]:null,values:[o]})}).then(function(e){return e.numFailures?e_.reject(e.failures[0]):e.lastResult}).then(function(t){if(a)try{b(e,a,t)}catch(n){}return t})},tt.prototype.update=function(e,t){return"object"!=typeof e||a(e)?this.where(":id").equals(e).modify(t):void 0===(e=_(e,this.schema.primKey.keyPath))?eM(new V.InvalidArgument("Given object does not contain its primary key")):this.where(":id").equals(e).modify(t)},tt.prototype.put=function(e,t){var n=this,r=this.schema.primKey,i=r.auto,a=r.keyPath,o=e;return a&&i&&(o=eY(a)(e)),this._trans("readwrite",function(e){return n.core.mutate({trans:e,type:"put",values:[o],keys:null!=t?[t]:null})}).then(function(e){return e.numFailures?e_.reject(e.failures[0]):e.lastResult}).then(function(t){if(a)try{b(e,a,t)}catch(n){}return t})},tt.prototype.delete=function(e){var t=this;return this._trans("readwrite",function(n){return t.core.mutate({trans:n,type:"delete",keys:[e]})}).then(function(e){return e.numFailures?e_.reject(e.failures[0]):void 0})},tt.prototype.clear=function(){var e=this;return this._trans("readwrite",function(t){return e.core.mutate({trans:t,type:"deleteRange",range:eW})}).then(function(e){return e.numFailures?e_.reject(e.failures[0]):void 0})},tt.prototype.bulkGet=function(e){var t=this;return this._trans("readonly",function(n){return t.core.getMany({keys:e,trans:n}).then(function(e){return e.map(function(e){return t.hook.reading.fire(e)})})})},tt.prototype.bulkAdd=function(e,t,n){var r=this,i=Array.isArray(t)?t:void 0,a=(n=n||(i?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",function(t){var n=r.schema.primKey,o=n.auto,n=n.keyPath;if(n&&i)throw new V.InvalidArgument("bulkAdd(): keys argument invalid on tables with inbound keys");if(i&&i.length!==e.length)throw new V.InvalidArgument("Arguments objects and keys must have the same length");var s=e.length,n=n&&o?e.map(eY(n)):e;return r.core.mutate({trans:t,type:"add",keys:i,values:n,wantResults:a}).then(function(e){var t=e.numFailures,n=e.results,i=e.lastResult,e=e.failures;if(0===t)return a?n:i;throw new L("".concat(r.name,".bulkAdd(): ").concat(t," of ").concat(s," operations failed"),e)})})},tt.prototype.bulkPut=function(e,t,n){var r=this,i=Array.isArray(t)?t:void 0,a=(n=n||(i?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",function(t){var n=r.schema.primKey,o=n.auto,n=n.keyPath;if(n&&i)throw new V.InvalidArgument("bulkPut(): keys argument invalid on tables with inbound keys");if(i&&i.length!==e.length)throw new V.InvalidArgument("Arguments objects and keys must have the same length");var s=e.length,n=n&&o?e.map(eY(n)):e;return r.core.mutate({trans:t,type:"put",keys:i,values:n,wantResults:a}).then(function(e){var t=e.numFailures,n=e.results,i=e.lastResult,e=e.failures;if(0===t)return a?n:i;throw new L("".concat(r.name,".bulkPut(): ").concat(t," of ").concat(s," operations failed"),e)})})},tt.prototype.bulkUpdate=function(e){var t=this,n=this.core,r=e.map(function(e){return e.key}),i=e.map(function(e){return e.changes}),a=[];return this._trans("readwrite",function(o){return n.getMany({trans:o,keys:r,cache:"clone"}).then(function(s){var u=[],l=[];e.forEach(function(e,n){var r=e.key,i=e.changes,o=s[n];if(o){for(var c=0,f=Object.keys(i);c<f.length;c++){var d=f[c],h=i[d];if(d===t.schema.primKey.keyPath){if(0!==eJ(h,r))throw new V.Constraint("Cannot update primary key in bulkUpdate()")}else b(o,d,h)}a.push(n),u.push(r),l.push(o)}});var c=u.length;return n.mutate({trans:o,type:"put",keys:u,values:l,updates:{keys:r,changeSpecs:i}}).then(function(e){var n=e.numFailures,r=e.failures;if(0===n)return c;for(var i=0,o=Object.keys(r);i<o.length;i++){var s,u=o[i],l=a[Number(u)];null!=l&&(s=r[u],delete r[u],r[l]=s)}throw new L("".concat(t.name,".bulkUpdate(): ").concat(n," of ").concat(c," operations failed"),r)})})})},tt.prototype.bulkDelete=function(e){var t=this,n=e.length;return this._trans("readwrite",function(n){return t.core.mutate({trans:n,type:"delete",keys:e})}).then(function(e){var r=e.numFailures,i=e.lastResult,e=e.failures;if(0===r)return i;throw new L("".concat(t.name,".bulkDelete(): ").concat(r," of ").concat(n," operations failed"),e)})},tt);function tt(){}function tn(e){function t(t,r){if(r){for(var i=arguments.length,a=Array(i-1);--i;)a[i-1]=arguments[i];return n[t].subscribe.apply(null,a),e}if("string"==typeof t)return n[t]}var n={};t.addEventType=s;for(var r=1,o=arguments.length;r<o;++r)s(arguments[r]);return t;function s(e,r,o){if("object"!=typeof e){r=r||en;var u,l={subscribers:[],fire:o=o||W,subscribe:function(e){-1===l.subscribers.indexOf(e)&&(l.subscribers.push(e),l.fire=r(l.fire,e))},unsubscribe:function(e){l.subscribers=l.subscribers.filter(function(t){return t!==e}),l.fire=l.subscribers.reduce(r,o)}};return n[e]=t[e]=l}i(u=e).forEach(function(e){var t=u[e];if(a(t))s(e,u[e][0],u[e][1]);else{if("asap"!==t)throw new V.InvalidArgument("Invalid event config");var n=s(e,Y,function(){for(var e=arguments.length,t=Array(e);e--;)t[e]=arguments[e];n.subscribers.forEach(function(e){g(function(){e.apply(null,t)})})})}})}}function tr(e,t){return h(t).from({prototype:e}),t}function ti(e,t){return!(e.filter||e.algorithm||e.or)&&(t?e.justLimit:!e.replayFilter)}function ta(e,t){e.filter=eG(e.filter,t)}function to(e,t,n){var r=e.replayFilter;e.replayFilter=r?function(){return eG(r(),t())}:t,e.justLimit=n&&!r}function ts(e,t){if(e.isPrimKey)return t.primaryKey;var n=t.getIndexByKeyPath(e.index);if(!n)throw new V.Schema("KeyPath "+e.index+" on object store "+t.name+" is not indexed");return n}function tu(e,t,n){var r=ts(e,t.schema);return t.openCursor({trans:n,values:!e.keysOnly,reverse:"prev"===e.dir,unique:!!e.unique,query:{index:r,range:e.range}})}function tl(e,t,n,r){var i=e.replayFilter?eG(e.filter,e.replayFilter()):e.filter;if(e.or){var a={},o=function(e,n,r){var o,s;i&&!i(n,r,function(e){return n.stop(e)},function(e){return n.fail(e)})||("[object ArrayBuffer]"==(s=""+(o=n.primaryKey))&&(s=""+new Uint8Array(o)),l(a,s)||(a[s]=!0,t(e,n,r)))};return Promise.all([e.or._iterate(o,n),tc(tu(e,r,n),e.algorithm,o,!e.keysOnly&&e.valueMapper)])}return tc(tu(e,r,n),eG(e.algorithm,i),t,!e.keysOnly&&e.valueMapper)}function tc(e,t,n,r){var i=eS(r?function(e,t,i){return n(r(e),t,i)}:n);return e.then(function(e){if(e)return e.start(function(){var n=function(){return e.continue()};t&&!t(e,function(e){return n=e},function(t){e.stop(t),n=W},function(t){e.fail(t),n=W})||i(e.value,e,function(e){return n=e}),n()})})}var E=Symbol(),tf=(td.prototype.execute=function(e){if(void 0!==this.add){var t=this.add;if(a(t))return n(n([],a(e)?e:[],!0),t,!0).sort();if("number"==typeof t)return(Number(e)||0)+t;if("bigint"==typeof t)try{return BigInt(e)+t}catch(r){return BigInt(0)+t}throw TypeError("Invalid term ".concat(t))}if(void 0!==this.remove){var i=this.remove;if(a(i))return a(e)?e.filter(function(e){return!i.includes(e)}).sort():[];if("number"==typeof i)return Number(e)-i;if("bigint"==typeof i)try{return BigInt(e)-i}catch(o){return BigInt(0)-i}throw TypeError("Invalid subtrahend ".concat(i))}return(t=null===(t=this.replacePrefix)||void 0===t?void 0:t[0])&&"string"==typeof e&&e.startsWith(t)?this.replacePrefix[1]+e.substring(t.length):e},td);function td(e){Object.assign(this,e)}var th=(tp.prototype._read=function(e,t){var n=this._ctx;return n.error?n.table._trans(null,eM.bind(null,n.error)):n.table._trans("readonly",e).then(t)},tp.prototype._write=function(e){var t=this._ctx;return t.error?t.table._trans(null,eM.bind(null,t.error)):t.table._trans("readwrite",e,"locked")},tp.prototype._addAlgorithm=function(e){var t=this._ctx;t.algorithm=eG(t.algorithm,e)},tp.prototype._iterate=function(e,t){return tl(this._ctx,e,t,this._ctx.table.core)},tp.prototype.clone=function(e){var t=Object.create(this.constructor.prototype),n=Object.create(this._ctx);return e&&o(n,e),t._ctx=n,t},tp.prototype.raw=function(){return this._ctx.valueMapper=null,this},tp.prototype.each=function(e){var t=this._ctx;return this._read(function(n){return tl(t,e,n,t.table.core)})},tp.prototype.count=function(e){var t=this;return this._read(function(e){var n=t._ctx,r=n.table.core;if(ti(n,!0))return r.count({trans:e,query:{index:ts(n,r.schema),range:n.range}}).then(function(e){return Math.min(e,n.limit)});var i=0;return tl(n,function(){return++i,!1},e,r).then(function(){return i})}).then(e)},tp.prototype.sortBy=function(e,t){var n=e.split(".").reverse(),r=n[0],i=n.length-1;function a(e,t){return t?a(e[n[t]],t-1):e[r]}var o="next"===this._ctx.dir?1:-1;function s(e,t){return(e=a(e,i))<(t=a(t,i))?-o:t<e?o:0}return this.toArray(function(e){return e.sort(s)}).then(t)},tp.prototype.toArray=function(e){var t=this;return this._read(function(e){var n=t._ctx;if("next"===n.dir&&ti(n,!0)&&0<n.limit){var r=n.valueMapper,i=ts(n,n.table.core.schema);return n.table.core.query({trans:e,limit:n.limit,values:!0,query:{index:i,range:n.range}}).then(function(e){return e=e.result,r?e.map(r):e})}var a=[];return tl(n,function(e){return a.push(e)},e,n.table.core).then(function(){return a})},e)},tp.prototype.offset=function(e){var t=this._ctx;return e<=0||(t.offset+=e,ti(t)?to(t,function(){var t=e;return function(e,n){return 0===t||(1===t?--t:n(function(){e.advance(t),t=0}),!1)}}):to(t,function(){var t=e;return function(){return--t<0}})),this},tp.prototype.limit=function(e){return this._ctx.limit=Math.min(this._ctx.limit,e),to(this._ctx,function(){var t=e;return function(e,n,r){return--t<=0&&n(r),0<=t}},!0),this},tp.prototype.until=function(e,t){return ta(this._ctx,function(n,r,i){return!e(n.value)||(r(i),t)}),this},tp.prototype.first=function(e){return this.limit(1).toArray(function(e){return e[0]}).then(e)},tp.prototype.last=function(e){return this.reverse().first(e)},tp.prototype.filter=function(e){var t;return ta(this._ctx,function(t){return e(t.value)}),(t=this._ctx).isMatch=eG(t.isMatch,e),this},tp.prototype.and=function(e){return this.filter(e)},tp.prototype.or=function(e){return new this.db.WhereClause(this._ctx.table,e,this)},tp.prototype.reverse=function(){return this._ctx.dir="prev"===this._ctx.dir?"next":"prev",this._ondirectionchange&&this._ondirectionchange(this._ctx.dir),this},tp.prototype.desc=function(){return this.reverse()},tp.prototype.eachKey=function(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each(function(t,n){e(n.key,n)})},tp.prototype.eachUniqueKey=function(e){return this._ctx.unique="unique",this.eachKey(e)},tp.prototype.eachPrimaryKey=function(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each(function(t,n){e(n.primaryKey,n)})},tp.prototype.keys=function(e){var t=this._ctx;t.keysOnly=!t.isMatch;var n=[];return this.each(function(e,t){n.push(t.key)}).then(function(){return n}).then(e)},tp.prototype.primaryKeys=function(e){var t=this._ctx;if("next"===t.dir&&ti(t,!0)&&0<t.limit)return this._read(function(e){var n=ts(t,t.table.core.schema);return t.table.core.query({trans:e,values:!1,limit:t.limit,query:{index:n,range:t.range}})}).then(function(e){return e.result}).then(e);t.keysOnly=!t.isMatch;var n=[];return this.each(function(e,t){n.push(t.primaryKey)}).then(function(){return n}).then(e)},tp.prototype.uniqueKeys=function(e){return this._ctx.unique="unique",this.keys(e)},tp.prototype.firstKey=function(e){return this.limit(1).keys(function(e){return e[0]}).then(e)},tp.prototype.lastKey=function(e){return this.reverse().firstKey(e)},tp.prototype.distinct=function(){var e=this._ctx,e=e.index&&e.table.schema.idxByName[e.index];if(!e||!e.multi)return this;var t={};return ta(this._ctx,function(e){var n=e.primaryKey.toString(),e=l(t,n);return t[n]=!0,!e}),this},tp.prototype.modify=function(e){var t=this,n=this._ctx;return this._write(function(r){function a(e,t){var n=t.failures,t=t.numFailures;$+=e-t;for(var r=0,a=i(n);r<a.length;r++){var o=a[r];p.push(n[o])}}u="function"==typeof e?e:(s=(o=i(e)).length,function(t){for(var n=!1,r=0;r<s;++r){var i=o[r],a=e[i],u=_(t,i);a instanceof tf?(b(t,i,a.execute(u)),n=!0):u!==a&&(b(t,i,a),n=!0)}return n});var o,s,u,l=n.table.core,c=l.schema.primaryKey,f=c.outbound,d=c.extractKey,h=t.db._options.modifyChunkSize||200,p=[],$=0,m=[];return t.clone().primaryKeys().then(function(t){var i=ti(n)&&n.limit===1/0&&("function"!=typeof e||e===t$)&&{index:n.index,range:n.range};return(function n(o){var s=Math.min(h,t.length-o);return l.getMany({trans:r,keys:t.slice(o,o+s),cache:"immutable"}).then(function(c){for(var p=[],$=[],m=f?[]:null,y=[],v=0;v<s;++v){var g=c[v],_={value:S(g),primKey:t[o+v]};!1!==u.call(_,_.value,_)&&(null==_.value?y.push(t[o+v]):f||0===eJ(d(g),d(_.value))?($.push(_.value),f&&m.push(t[o+v])):(y.push(t[o+v]),p.push(_.value)))}return Promise.resolve(0<p.length&&l.mutate({trans:r,type:"add",values:p}).then(function(e){for(var t in e.failures)y.splice(parseInt(t),1);a(p.length,e)})).then(function(){return(0<$.length||i&&"object"==typeof e)&&l.mutate({trans:r,type:"put",keys:m,values:$,criteria:i,changeSpec:"function"!=typeof e&&e,isAdditionalChunk:0<o}).then(function(e){return a($.length,e)})}).then(function(){return(0<y.length||i&&e===t$)&&l.mutate({trans:r,type:"delete",keys:y,criteria:i,isAdditionalChunk:0<o}).then(function(e){return a(y.length,e)})}).then(function(){return t.length>o+s&&n(o+h)})})})(0).then(function(){if(0<p.length)throw new M("Error modifying one or more objects",p,$,m);return t.length})})})},tp.prototype.delete=function(){var e=this._ctx,t=e.range;return ti(e)&&(e.isPrimKey||3===t.type)?this._write(function(n){var r=e.table.core.schema.primaryKey,i=t;return e.table.core.count({trans:n,query:{index:r,range:i}}).then(function(t){return e.table.core.mutate({trans:n,type:"deleteRange",range:i}).then(function(e){var n=e.failures;if(e.lastResult,e.results,e=e.numFailures)throw new M("Could not delete some values",Object.keys(n).map(function(e){return n[e]}),t-e);return t-e})})}):this.modify(t$)},tp);function tp(){}var t$=function(e,t){return t.value=null};function tm(e,t){return e<t?-1:e===t?0:1}function t8(e,t){return t<e?-1:e===t?0:1}function ty(e,t,n){return(e=e instanceof tw?new e.Collection(e):e)._ctx.error=new(n||TypeError)(t),e}function tv(e){return new e.Collection(e,function(){return tb("")}).limit(0)}function tg(e,t,n,r){var i,a,o,s,u,l,c,f=n.length;if(!n.every(function(e){return"string"==typeof e}))return ty(e,e5);function d(e){i="next"===e?function(e){return e.toUpperCase()}:function(e){return e.toLowerCase()},a="next"===e?function(e){return e.toLowerCase()}:function(e){return e.toUpperCase()},o="next"===e?tm:t8;var t=n.map(function(e){return{lower:a(e),upper:i(e)}}).sort(function(e,t){return o(e.lower,t.lower)});s=t.map(function(e){return e.upper}),u=t.map(function(e){return e.lower}),c="next"===(l=e)?"":r}d("next"),(e=new e.Collection(e,function(){return t_(s[0],u[f-1]+r)}))._ondirectionchange=function(e){d(e)};var h=0;return e._addAlgorithm(function(e,n,r){var i=e.key;if("string"!=typeof i)return!1;var d=a(i);if(t(d,u,h))return!0;for(var p=null,$=h;$<f;++$){var m=function(e,t,n,r,i,a){for(var o=Math.min(e.length,r.length),s=-1,u=0;u<o;++u){var l=t[u];if(l!==r[u])return 0>i(e[u],n[u])?e.substr(0,u)+n[u]+n.substr(u+1):0>i(e[u],r[u])?e.substr(0,u)+r[u]+n.substr(u+1):0<=s?e.substr(0,s)+t[s]+n.substr(s+1):null;0>i(e[u],l)&&(s=u)}return o<r.length&&"next"===a?e+n.substr(e.length):o<e.length&&"prev"===a?e.substr(0,n.length):s<0?null:e.substr(0,s)+r[s]+n.substr(s+1)}(i,d,s[$],u[$],o,l);null===m&&null===p?h=$+1:(null===p||0<o(p,m))&&(p=m)}return n(null!==p?function(){e.continue(p+c)}:r),!1}),e}function t_(e,t,n,r){return{type:2,lower:e,upper:t,lowerOpen:n,upperOpen:r}}function tb(e){return{type:1,lower:e,upper:e}}var tw=(Object.defineProperty(t0.prototype,"Collection",{get:function(){return this._ctx.table.db.Collection},enumerable:!1,configurable:!0}),t0.prototype.between=function(e,t,n,r){n=!1!==n,r=!0===r;try{return 0<this._cmp(e,t)||0===this._cmp(e,t)&&(n||r)&&(!n||!r)?tv(this):new this.Collection(this,function(){return t_(e,t,!n,!r)})}catch(i){return ty(this,eL)}},t0.prototype.equals=function(e){return null==e?ty(this,eL):new this.Collection(this,function(){return tb(e)})},t0.prototype.above=function(e){return null==e?ty(this,eL):new this.Collection(this,function(){return t_(e,void 0,!0)})},t0.prototype.aboveOrEqual=function(e){return null==e?ty(this,eL):new this.Collection(this,function(){return t_(e,void 0,!1)})},t0.prototype.below=function(e){return null==e?ty(this,eL):new this.Collection(this,function(){return t_(void 0,e,!1,!0)})},t0.prototype.belowOrEqual=function(e){return null==e?ty(this,eL):new this.Collection(this,function(){return t_(void 0,e)})},t0.prototype.startsWith=function(e){return"string"!=typeof e?ty(this,e5):this.between(e,e+"￿",!0,!0)},t0.prototype.startsWithIgnoreCase=function(e){return""===e?this.startsWith(e):tg(this,function(e,t){return 0===e.indexOf(t[0])},[e],"￿")},t0.prototype.equalsIgnoreCase=function(e){return tg(this,function(e,t){return e===t[0]},[e],"")},t0.prototype.anyOfIgnoreCase=function(){var e=z.apply(T,arguments);return 0===e.length?tv(this):tg(this,function(e,t){return -1!==t.indexOf(e)},e,"")},t0.prototype.startsWithAnyOfIgnoreCase=function(){var e=z.apply(T,arguments);return 0===e.length?tv(this):tg(this,function(e,t){return t.some(function(t){return 0===e.indexOf(t)})},e,"￿")},t0.prototype.anyOf=function(){var e=this,t=z.apply(T,arguments),n=this._cmp;try{t.sort(n)}catch(r){return ty(this,eL)}if(0===t.length)return tv(this);var i=new this.Collection(this,function(){return t_(t[0],t[t.length-1])});i._ondirectionchange=function(r){n="next"===r?e._ascending:e._descending,t.sort(n)};var a=0;return i._addAlgorithm(function(e,r,i){for(var o=e.key;0<n(o,t[a]);)if(++a===t.length)return r(i),!1;return 0===n(o,t[a])||(r(function(){e.continue(t[a])}),!1)}),i},t0.prototype.notEqual=function(e){return this.inAnyRange([[-1/0,e],[e,this.db._maxKey]],{includeLowers:!1,includeUppers:!1})},t0.prototype.noneOf=function(){var e=z.apply(T,arguments);if(0===e.length)return new this.Collection(this);try{e.sort(this._ascending)}catch(t){return ty(this,eL)}var n=e.reduce(function(e,t){return e?e.concat([[e[e.length-1][1],t]]):[[-1/0,t]]},null);return n.push([e[e.length-1],this.db._maxKey]),this.inAnyRange(n,{includeLowers:!1,includeUppers:!1})},t0.prototype.inAnyRange=function(e,t){var n=this,r=this._cmp,i=this._ascending,a=this._descending,o=this._min,s=this._max;if(0===e.length)return tv(this);if(!e.every(function(e){return void 0!==e[0]&&void 0!==e[1]&&0>=i(e[0],e[1])}))return ty(this,"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower",V.InvalidArgument);var u,l=!t||!1!==t.includeLowers,c=t&&!0===t.includeUppers,f=i;function d(e,t){return f(e[0],t[0])}try{(u=e.reduce(function(e,t){for(var n=0,i=e.length;n<i;++n){var a=e[n];if(0>r(t[0],a[1])&&0<r(t[1],a[0])){a[0]=o(a[0],t[0]),a[1]=s(a[1],t[1]);break}}return n===i&&e.push(t),e},[])).sort(d)}catch(h){return ty(this,eL)}var p=0,$=c?function(e){return 0<i(e,u[p][1])}:function(e){return 0<=i(e,u[p][1])},m=l?function(e){return 0<a(e,u[p][0])}:function(e){return 0<=a(e,u[p][0])},y=$,e=new this.Collection(this,function(){return t_(u[0][0],u[u.length-1][1],!l,!c)});return e._ondirectionchange=function(e){f="next"===e?(y=$,i):(y=m,a),u.sort(d)},e._addAlgorithm(function(e,t,r){for(var a,o=e.key;y(o);)if(++p===u.length)return t(r),!1;return!$(a=o)&&!m(a)||(0===n._cmp(o,u[p][1])||0===n._cmp(o,u[p][0])||t(function(){f===i?e.continue(u[p][0]):e.continue(u[p][1])}),!1)}),e},t0.prototype.startsWithAnyOf=function(){var e=z.apply(T,arguments);return e.every(function(e){return"string"==typeof e})?0===e.length?tv(this):this.inAnyRange(e.map(function(e){return[e,e+"￿"]})):ty(this,"startsWithAnyOf() only works with strings")},t0);function t0(){}function tk(e){return eS(function(t){return tx(t),e(t.target.error),!1})}function tx(e){e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault()}var t3="storagemutated",t4="x-storagemutated-1",tE=tn(null,t3),t1=(tA.prototype._lock=function(){return v(!e8.global),++this._reculock,1!==this._reculock||e8.global||(e8.lockOwnerFor=this),this},tA.prototype._unlock=function(){if(v(!e8.global),0==--this._reculock)for(e8.global||(e8.lockOwnerFor=null);0<this._blockedFuncs.length&&!this._locked();){var e=this._blockedFuncs.shift();try{eF(e[1],e[0])}catch(t){}}return this},tA.prototype._locked=function(){return this._reculock&&e8.lockOwnerFor!==this},tA.prototype.create=function(e){var t=this;if(!this.mode)return this;var n=this.db.idbdb,r=this.db._state.dbOpenError;if(v(!this.idbtrans),!e&&!n)switch(r&&r.name){case"DatabaseClosedError":throw new V.DatabaseClosed(r);case"MissingAPIError":throw new V.MissingAPI(r.message,r);default:throw new V.OpenFailed(r)}if(!this.active)throw new V.TransactionInactive;return v(null===this._completion._state),(e=this.idbtrans=e||(this.db.core||n).transaction(this.storeNames,this.mode,{durability:this.chromeTransactionDurability})).onerror=eS(function(n){tx(n),t._reject(e.error)}),e.onabort=eS(function(n){tx(n),t.active&&t._reject(new V.Abort(e.error)),t.active=!1,t.on("abort").fire(n)}),e.oncomplete=eS(function(){t.active=!1,t._resolve(),"mutatedParts"in e&&tE.storagemutated.fire(e.mutatedParts)}),this},tA.prototype._promise=function(e,t,n){var r=this;if("readwrite"===e&&"readwrite"!==this.mode)return eM(new V.ReadOnly("Transaction is readonly"));if(!this.active)return eM(new V.TransactionInactive);if(this._locked())return new e_(function(i,a){r._blockedFuncs.push([function(){r._promise(e,t,n).then(i,a)},e8])});if(n)return eT(function(){var e=new e_(function(e,n){r._lock();var i=t(e,n,r);i&&i.then&&i.then(e,n)});return e.finally(function(){return r._unlock()}),e._lib=!0,e});var i=new e_(function(e,n){var i=t(e,n,r);i&&i.then&&i.then(e,n)});return i._lib=!0,i},tA.prototype._root=function(){return this.parent?this.parent._root():this},tA.prototype.waitFor=function(e){var t,n=this._root(),r=e_.resolve(e);n._waitingFor?n._waitingFor=n._waitingFor.then(function(){return r}):(n._waitingFor=r,n._waitingQueue=[],t=n.idbtrans.objectStore(n.storeNames[0]),function e(){for(++n._spinCount;n._waitingQueue.length;)n._waitingQueue.shift()();n._waitingFor&&(t.get(-1/0).onsuccess=e)}());var i=n._waitingFor;return new e_(function(e,t){r.then(function(t){return n._waitingQueue.push(eS(e.bind(null,t)))},function(e){return n._waitingQueue.push(eS(t.bind(null,e)))}).finally(function(){n._waitingFor===i&&(n._waitingFor=null)})})},tA.prototype.abort=function(){this.active&&(this.active=!1,this.idbtrans&&this.idbtrans.abort(),this._reject(new V.Abort))},tA.prototype.table=function(e){var t=this._memoizedTables||(this._memoizedTables={});if(l(t,e))return t[e];var n=this.schema[e];if(!n)throw new V.NotFound("Table "+e+" not part of transaction");return(n=new this.db.Table(e,n,this)).core=this.db.core.table(e),t[e]=n},tA);function tA(){}function tI(e,t,n,r,i,a,o){return{name:e,keyPath:t,unique:n,multi:r,auto:i,compound:a,src:(n&&!o?"&":"")+(r?"*":"")+(i?"++":"")+tS(t)}}function tS(e){return"string"==typeof e?e:e?"["+[].join.call(e,"+")+"]":""}function tD(e,t,n){var r;return{name:e,primKey:t,indexes:n,mappedClass:null,idxByName:(r=function(e){return[e.name,e]},n.reduce(function(e,t,n){return(n=r(t,n))&&(e[n[0]]=n[1]),e},{}))}}var tP=function(e){try{return e.only([[]]),tP=function(){return[[]]},[[]]}catch(t){return tP=function(){return"￿"},"￿"}};function t6(e){var t;return null==e?function(){}:"string"==typeof e?1===(t=e).split(".").length?function(e){return e[t]}:function(e){return _(e,t)}:function(t){return _(t,e)}}function tO(e){return[].slice.call(e)}var tC=0;function tR(e){return null==e?":id":"string"==typeof e?e:"[".concat(e.join("+"),"]")}function tT(e,n){var r,i,o,s,u,l=n.db,n=(r=e._middlewares,i=l,o=e._deps,s=n,u=o.IDBKeyRange,o.indexedDB,{dbcore:(s=function e(t,n,r){function i(e){if(3===e.type)return null;if(4===e.type)throw Error("Cannot convert never type to IDBKeyRange");var t=e.lower,r=e.upper,i=e.lowerOpen,e=e.upperOpen;return void 0===t?void 0===r?null:n.upperBound(r,!!e):void 0===r?n.lowerBound(t,!!i):n.bound(t,r,!!i,!!e)}var o,s,u,l=(s=r,u=tO((o=t).objectStoreNames),{schema:{name:o.name,tables:u.map(function(e){return s.objectStore(e)}).map(function(e){var t=e.keyPath,n=e.autoIncrement,r=a(t),i={},n={name:e.name,primaryKey:{name:null,isPrimaryKey:!0,outbound:null==t,compound:r,keyPath:t,autoIncrement:n,unique:!0,extractKey:t6(t)},indexes:tO(e.indexNames).map(function(t){return e.index(t)}).map(function(e){var t=e.name,n=e.unique,r=e.multiEntry,e=e.keyPath,r={name:t,compound:a(e),keyPath:e,unique:n,multiEntry:r,extractKey:t6(e)};return i[tR(e)]=r}),getIndexByKeyPath:function(e){return i[tR(e)]}};return i[":id"]=n.primaryKey,null!=t&&(i[tR(t)]=n.primaryKey),n})},hasGetAll:0<u.length&&"getAll"in s.objectStore(u[0])&&!("undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604)}),r=l.schema,c=l.hasGetAll,l=r.tables.map(function e(t){var n,r=t.name;return{name:r,schema:t,mutate:function(e){var t=e.trans,n=e.type,a=e.keys,o=e.values,s=e.range;return new Promise(function(e,u){e=eS(e);var l=t.objectStore(r),c=null==l.keyPath,f="put"===n||"add"===n;if(!f&&"delete"!==n&&"deleteRange"!==n)throw Error("Invalid operation type: "+n);var d,h=(a||o||{length:1}).length;if(a&&o&&a.length!==o.length)throw Error("Given keys array must have same length as given values array.");if(0===h)return e({numFailures:0,failures:{},results:[],lastResult:void 0});function p(e){++y,tx(e)}var $=[],m=[],y=0;if("deleteRange"===n){if(4===s.type)return e({numFailures:y,failures:m,results:[],lastResult:void 0});3===s.type?$.push(d=l.clear()):$.push(d=l.delete(i(s)))}else{var c=f?c?[o,a]:[o,null]:[a,null],v=c[0],g=c[1];if(f)for(var _=0;_<h;++_)$.push(d=g&&void 0!==g[_]?l[n](v[_],g[_]):l[n](v[_])),d.onerror=p;else for(_=0;_<h;++_)$.push(d=l[n](v[_])),d.onerror=p}function b(t){t=t.target.result,$.forEach(function(e,t){return null!=e.error&&(m[t]=e.error)}),e({numFailures:y,failures:m,results:"delete"===n?a:$.map(function(e){return e.result}),lastResult:t})}d.onerror=function(e){p(e),b(e)},d.onsuccess=b})},getMany:function(e){var t=e.trans,n=e.keys;return new Promise(function(e,i){e=eS(e);for(var a,o=t.objectStore(r),s=n.length,u=Array(s),l=0,c=0,f=function(t){u[(t=t.target)._pos]=t.result,++c===l&&e(u)},d=tk(i),h=0;h<s;++h)null!=n[h]&&((a=o.get(n[h]))._pos=h,a.onsuccess=f,a.onerror=d,++l);0===l&&e(u)})},get:function(e){var t=e.trans,n=e.key;return new Promise(function(e,i){e=eS(e);var a=t.objectStore(r).get(n);a.onsuccess=function(t){return e(t.target.result)},a.onerror=tk(i)})},query:(n=c,function(e){return new Promise(function(t,a){t=eS(t);var o,s,u,l=e.trans,c=e.values,f=e.limit,d=e.query,h=f===1/0?void 0:f,p=d.index,d=d.range,l=l.objectStore(r),p=p.isPrimaryKey?l:l.index(p.name),d=i(d);if(0===f)return t({result:[]});n?((h=c?p.getAll(d,h):p.getAllKeys(d,h)).onsuccess=function(e){return t({result:e.target.result})},h.onerror=tk(a)):(o=0,s=!c&&"openKeyCursor"in p?p.openKeyCursor(d):p.openCursor(d),u=[],s.onsuccess=function(e){var n=s.result;return n?(u.push(c?n.value:n.primaryKey),++o===f?t({result:u}):void n.continue()):t({result:u})},s.onerror=tk(a))})}),openCursor:function(e){var t=e.trans,n=e.values,a=e.query,o=e.reverse,s=e.unique;return new Promise(function(e,u){e=eS(e);var l=a.index,c=a.range,f=t.objectStore(r),f=l.isPrimaryKey?f:f.index(l.name),l=o?s?"prevunique":"prev":s?"nextunique":"next",d=!n&&"openKeyCursor"in f?f.openKeyCursor(i(c),l):f.openCursor(i(c),l);d.onerror=tk(u),d.onsuccess=eS(function(n){var r,i,a,o,s=d.result;s?(s.___id=++tC,s.done=!1,r=s.continue.bind(s),i=(i=s.continuePrimaryKey)&&i.bind(s),a=s.advance.bind(s),o=function(){throw Error("Cursor not stopped")},s.trans=t,s.stop=s.continue=s.continuePrimaryKey=s.advance=function(){throw Error("Cursor not started")},s.fail=eS(u),s.next=function(){var e=this,t=1;return this.start(function(){return t--?e.continue():e.stop()}).then(function(){return e})},s.start=function(e){function t(){if(d.result)try{e()}catch(t){s.fail(t)}else s.done=!0,s.start=function(){throw Error("Cursor behind last entry")},s.stop()}var n=new Promise(function(e,t){e=eS(e),d.onerror=tk(t),s.fail=t,s.stop=function(t){s.stop=s.continue=s.continuePrimaryKey=s.advance=o,e(t)}});return d.onsuccess=eS(function(e){d.onsuccess=t,t()}),s.continue=r,s.continuePrimaryKey=i,s.advance=a,t(),n},e(s)):e(null)},u)})},count:function(e){var t=e.query,n=e.trans,a=t.index,o=t.range;return new Promise(function(e,t){var s=n.objectStore(r),u=a.isPrimaryKey?s:s.index(a.name),s=i(o),u=s?u.count(s):u.count();u.onsuccess=eS(function(t){return e(t.target.result)}),u.onerror=tk(t)})}}}),f={};return l.forEach(function(e){return f[e.name]=e}),{stack:"dbcore",transaction:t.transaction.bind(t),table:function(e){if(!f[e])throw Error("Table '".concat(e,"' not found"));return f[e]},MIN_KEY:-1/0,MAX_KEY:tP(n),schema:r}}(i,u,s),r.dbcore.reduce(function(e,n){return n=n.create,t(t({},e),n(e))},s))});e.core=n.dbcore,e.tables.forEach(function(t){var n=t.name;e.core.schema.tables.some(function(e){return e.name===n})&&(t.core=e.core.table(n),e[n]instanceof e.Table&&(e[n].core=t.core))})}function tz(e,t,n,r){n.forEach(function(n){var i=r[n];t.forEach(function(t){var r=function e(t,n){return p(t,n)||(t=s(t))&&e(t,n)}(t,n);(!r||"value"in r&&void 0===r.value)&&(t===e.Transaction.prototype||t instanceof e.Transaction?d(t,n,{get:function(){return this.table(n)},set:function(e){f(this,n,{value:e,writable:!0,configurable:!0,enumerable:!0})}}):t[n]=new e.Table(n,i))})})}function t2(e,t){t.forEach(function(t){for(var n in t)t[n]instanceof e.Table&&delete t[n]})}function tK(e,t){return e._cfg.version-t._cfg.version}function tq(e,t){var n,r={del:[],add:[],change:[]};for(n in e)t[n]||r.del.push(n);for(n in t){var i=e[n],a=t[n];if(i){var o={name:n,def:a,recreate:!1,del:[],add:[],change:[]};if(""+(i.primKey.keyPath||"")!=""+(a.primKey.keyPath||"")||i.primKey.auto!==a.primKey.auto)o.recreate=!0,r.change.push(o);else{var s=i.idxByName,u=a.idxByName,l=void 0;for(l in s)u[l]||o.del.push(l);for(l in u){var c=s[l],f=u[l];c?c.src!==f.src&&o.change.push(f):o.add.push(f)}(0<o.del.length||0<o.add.length||0<o.change.length)&&r.change.push(o)}}else r.add.push([n,a])}return r}function t7(e,t,n,r){var i=e.db.createObjectStore(t,n.keyPath?{keyPath:n.keyPath,autoIncrement:n.auto}:{autoIncrement:n.auto});return r.forEach(function(e){return tF(i,e)}),i}function tB(e,t){i(e).forEach(function(n){t.db.objectStoreNames.contains(n)||(ei&&console.debug("Dexie: Creating missing table",n),t7(t,n,e[n].primKey,e[n].indexes))})}function tF(e,t){e.createIndex(t.name,t.keyPath,{unique:t.unique,multiEntry:t.multi})}function tN(e,t,n){var r={};return m(t.objectStoreNames,0).forEach(function(e){for(var t=n.objectStore(e),i=tI(tS(u=t.keyPath),u||"",!0,!1,!!t.autoIncrement,u&&"string"!=typeof u,!0),a=[],o=0;o<t.indexNames.length;++o){var s=t.index(t.indexNames[o]),u=s.keyPath,s=tI(s.name,u,!!s.unique,!!s.multiEntry,!1,u&&"string"!=typeof u,!1);a.push(s)}r[e]=tD(e,i,a)}),r}function tj(e,t,n){for(var i=n.db.objectStoreNames,a=0;a<i.length;++a){var o=i[a],s=n.objectStore(o);e._hasGetAll="getAll"in s;for(var u=0;u<s.indexNames.length;++u){var l=s.indexNames[u],c=s.index(l).keyPath,f="string"==typeof c?c:"["+m(c).join("+")+"]";!t[o]||(c=t[o].idxByName[f])&&(c.name=l,delete t[o].idxByName[f],t[o].idxByName[l]=c)}}"undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&r.WorkerGlobalScope&&r instanceof r.WorkerGlobalScope&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604&&(e._hasGetAll=!1)}function tM(e){return e.split(",").map(function(e,t){var n=(e=e.trim()).replace(/([&*]|\+\+)/g,""),r=/^\[/.test(n)?n.match(/^\[(.*)\]$/)[1].split("+"):n;return tI(n,r||null,/\&/.test(e),/\*/.test(e),/\+\+/.test(e),a(r),0===t)})}var tL=(t5.prototype._parseStoresSpec=function(e,t){i(e).forEach(function(n){if(null!==e[n]){var r=tM(e[n]),i=r.shift();if(i.unique=!0,i.multi)throw new V.Schema("Primary key cannot be multi-valued");r.forEach(function(e){if(e.auto)throw new V.Schema("Only primary key can be marked as autoIncrement (++)");if(!e.keyPath)throw new V.Schema("Index must have a name and cannot be an empty string")}),t[n]=tD(n,i,r)}})},t5.prototype.stores=function(e){var t=this.db;this._cfg.storesSource=this._cfg.storesSource?o(this._cfg.storesSource,e):e;var e=t._versions,n={},r={};return e.forEach(function(e){o(n,e._cfg.storesSource),r=e._cfg.dbschema={},e._parseStoresSpec(n,r)}),t._dbSchema=r,t2(t,[t._allTables,t,t.Transaction.prototype]),tz(t,[t._allTables,t,t.Transaction.prototype,this._cfg.tables],i(r),r),t._storeNames=i(r),this},t5.prototype.upgrade=function(e){return this._cfg.contentUpgrade=er(this._cfg.contentUpgrade||W,e),this},t5);function t5(){}function tU(e,t){var n=e._dbNamesDB;return n||(n=e._dbNamesDB=new nk(eZ,{addons:[],indexedDB:e,IDBKeyRange:t})).version(1).stores({dbnames:"name"}),n.table("dbnames")}function tZ(e){return e&&"function"==typeof e.databases}function tV(e){return eT(function(){return e8.letThrough=!0,e()})}function tH(e){return!("from"in e)}var tG=function(e,t){if(!this){var n=new tG;return e&&"d"in e&&o(n,e),n}o(this,arguments.length?{d:1,from:e,to:1<arguments.length?t:e}:{d:0})};function tW(e,t,n){var r=eJ(t,n);if(!isNaN(r)){if(0<r)throw RangeError();if(tH(e))return o(e,{from:t,to:n,d:1});var i=e.l,r=e.r;if(0>eJ(n,e.from))return i?tW(i,t,n):e.l={from:t,to:n,d:1,l:null,r:null},tX(e);if(0<eJ(t,e.to))return r?tW(r,t,n):e.r={from:t,to:n,d:1,l:null,r:null},tX(e);0>eJ(t,e.from)&&(e.from=t,e.l=null,e.d=r?r.d+1:1),0<eJ(n,e.to)&&(e.to=n,e.r=null,e.d=e.l?e.l.d+1:1),n=!e.r,i&&!e.l&&tY(e,i),r&&n&&tY(e,r)}}function tY(e,t){tH(t)||function e(t,n){var r=n.from,i=n.to,a=n.l,n=n.r;tW(t,r,i),a&&e(t,a),n&&e(t,n)}(e,t)}function t9(e,t){var n=tJ(t),r=n.next();if(r.done)return!1;for(var i=r.value,a=tJ(e),o=a.next(i.from),s=o.value;!r.done&&!o.done;){if(0>=eJ(s.from,i.to)&&0<=eJ(s.to,i.from))return!0;0>eJ(i.from,s.from)?i=(r=n.next(s.from)).value:s=(o=a.next(i.from)).value}return!1}function tJ(e){var t=tH(e)?null:{s:0,n:e};return{next:function(e){for(var n=0<arguments.length;t;)switch(t.s){case 0:if(t.s=1,n)for(;t.n.l&&0>eJ(e,t.n.from);)t={up:t,n:t.n.l,s:1};else for(;t.n.l;)t={up:t,n:t.n.l,s:1};case 1:if(t.s=2,!n||0>=eJ(e,t.n.to))return{value:t.n,done:!1};case 2:if(t.n.r){t.s=3,t={up:t,n:t.n.r,s:0};continue}case 3:t=t.up}return{done:!0}}}}function tX(e){var n,r,i=((null===(n=e.r)||void 0===n?void 0:n.d)||0)-((null===(r=e.l)||void 0===r?void 0:r.d)||0),a=1<i?"r":i<-1?"l":"";a&&(n="r"==a?"l":"r",r=t({},e),i=e[a],e.from=i.from,e.to=i.to,e[a]=i[a],r[a]=i[n],(e[n]=r).d=tQ(r)),e.d=tQ(e)}function tQ(e){var t=e.r,e=e.l;return(t?e?Math.max(t.d,e.d):t.d:e?e.d:0)+1}function ne(e,t){return i(t).forEach(function(n){e[n]?tY(e[n],t[n]):e[n]=function e(t){var n,r,i={};for(n in t)l(t,n)&&(r=t[n],i[n]=!r||"object"!=typeof r||A.has(r.constructor)?r:e(r));return i}(t[n])}),e}function nt(e,t){return e.all||t.all||Object.keys(e).some(function(n){return t[n]&&t9(t[n],e[n])})}c(tG.prototype,((B={add:function(e){return tY(this,e),this},addKey:function(e){return tW(this,e,e),this},addKeys:function(e){var t=this;return e.forEach(function(e){return tW(t,e,e)}),this}})[O]=function(){return tJ(this)},B));var nn={},nr={},ni=!1;function na(e){ne(nr,e),ni||(ni=!0,setTimeout(function(){ni=!1,no(nr,(nr={},!1))},0))}function no(e,t){void 0===t&&(t=!1);var n=new Set;if(e.all)for(var r=0,i=Object.values(nn);r<i.length;r++)ns(o=i[r],e,n,t);else for(var a in e){var o,s=/^idb\:\/\/(.*)\/(.*)\//.exec(a);s&&(a=s[1],s=s[2],(o=nn["idb://".concat(a,"/").concat(s)])&&ns(o,e,n,t))}n.forEach(function(e){return e()})}function ns(e,t,n,r){for(var i=[],a=0,o=Object.entries(e.queries.query);a<o.length;a++){for(var s=o[a],u=s[0],l=[],c=0,f=s[1];c<f.length;c++){var d=f[c];nt(t,d.obsSet)?d.subscribers.forEach(function(e){return n.add(e)}):r&&l.push(d)}r&&i.push([u,l])}if(r)for(var h=0,p=i;h<p.length;h++){var $=p[h],u=$[0],l=$[1];e.queries.query[u]=l}}function nu(e){function t(t){return e.next(t)}var n=i(t),r=i(function(t){return e.throw(t)});function i(e){return function(t){var i=e(t),t=i.value;return i.done?t:t&&"function"==typeof t.then?t.then(n,r):a(t)?Promise.all(t).then(n,r):n(t)}}return i(t)()}function nl(e,t,n){for(var r=a(e)?e.slice():[e],i=0;i<n;++i)r.push(t);return r}var nc={stack:"dbcore",name:"VirtualIndexMiddleware",level:1,create:function(e){return t(t({},e),{table:function(n){var r=e.table(n),i=r.schema,a={},o=[];function s(e,n,r){var i=tR(e),u=a[i]=a[i]||[],l=null==e?0:"string"==typeof e?1:e.length,c=0<n,c=t(t({},r),{name:c?"".concat(i,"(virtual-from:").concat(r.name,")"):r.name,lowLevelIndex:r,isVirtual:c,keyTail:n,keyLength:l,extractKey:t6(e),unique:!c&&r.unique});return u.push(c),c.isPrimaryKey||o.push(c),1<l&&s(2===l?e[0]:e.slice(0,l-1),n+1,r),u.sort(function(e,t){return e.keyTail-t.keyTail}),c}n=s(i.primaryKey.keyPath,0,i.primaryKey),a[":id"]=[n];for(var u=0,l=i.indexes;u<l.length;u++){var c=l[u];s(c.keyPath,0,c)}function f(n){var r,i=n.query.index;return i.isVirtual?t(t({},n),{query:{index:i.lowLevelIndex,range:(r=n.query.range,i=i.keyTail,{type:1===r.type?2:r.type,lower:nl(r.lower,r.lowerOpen?e.MAX_KEY:e.MIN_KEY,i),lowerOpen:!0,upper:nl(r.upper,r.upperOpen?e.MIN_KEY:e.MAX_KEY,i),upperOpen:!0})}}):n}return t(t({},r),{schema:t(t({},i),{primaryKey:n,indexes:o,getIndexByKeyPath:function(e){return(e=a[tR(e)])&&e[0]}}),count:function(e){return r.count(f(e))},query:function(e){return r.query(f(e))},openCursor:function(t){var n=t.query.index,i=n.keyTail,a=n.isVirtual,o=n.keyLength;return a?r.openCursor(f(t)).then(function(n){var r;return n&&(r=n,Object.create(r,{continue:{value:function(n){null!=n?r.continue(nl(n,t.reverse?e.MAX_KEY:e.MIN_KEY,i)):t.unique?r.continue(r.key.slice(0,o).concat(t.reverse?e.MIN_KEY:e.MAX_KEY,i)):r.continue()}},continuePrimaryKey:{value:function(t,n){r.continuePrimaryKey(nl(t,e.MAX_KEY,i),n)}},primaryKey:{get:function(){return r.primaryKey}},key:{get:function(){var e=r.key;return 1===o?e[0]:e.slice(0,o)}},value:{get:function(){return r.value}}}))}):r.openCursor(t)}})}})}};function nf(e,t,n,r){return n=n||{},r=r||"",i(e).forEach(function(i){var a,o,s;l(t,i)?(a=e[i],o=t[i],"object"==typeof a&&"object"==typeof o&&a&&o?(s=P(a))!==P(o)?n[r+i]=t[i]:"Object"===s?nf(a,o,n,r+i+"."):a!==o&&(n[r+i]=t[i]):a!==o&&(n[r+i]=t[i])):n[r+i]=void 0}),i(t).forEach(function(i){l(e,i)||(n[r+i]=t[i])}),n}function nd(e,t){return"delete"===t.type?t.keys:t.keys||t.values.map(e.extractKey)}var nh={stack:"dbcore",name:"HooksMiddleware",level:2,create:function(e){return t(t({},e),{table:function(r){var i=e.table(r),a=i.schema.primaryKey;return t(t({},i),{mutate:function(e){var o=e8.trans,s=o.table(r).hook,u=s.deleting,c=s.creating,f=s.updating;switch(e.type){case"add":if(c.fire===W)break;return o._promise("readwrite",function(){return d(e)},!0);case"put":if(c.fire===W&&f.fire===W)break;return o._promise("readwrite",function(){return d(e)},!0);case"delete":if(u.fire===W)break;return o._promise("readwrite",function(){return d(e)},!0);case"deleteRange":if(u.fire===W)break;return o._promise("readwrite",function(){return function e(n,r,o){return i.query({trans:n,values:!1,query:{index:a,range:r},limit:o}).then(function(i){var a=i.result;return d({type:"delete",keys:a,trans:n}).then(function(i){return 0<i.numFailures?Promise.reject(i.failures[0]):a.length<o?{failures:[],numFailures:0,lastResult:void 0}:e(n,t(t({},r),{lower:a[a.length-1],lowerOpen:!0}),o)})})}(e.trans,e.range,1e4)},!0)}return i.mutate(e);function d(e){var r,o,s,d=e8.trans,h=e.keys||nd(a,e);if(!h)throw Error("Keys missing");return"delete"!==(e="add"===e.type||"put"===e.type?t(t({},e),{keys:h}):t({},e)).type&&(e.values=n([],e.values,!0)),e.keys&&(e.keys=n([],e.keys,!0)),r=i,s=h,("add"===(o=e).type?Promise.resolve([]):r.getMany({trans:o.trans,keys:s,cache:"immutable"})).then(function(t){var n=h.map(function(n,r){var i,o,s,h=t[r],p={onerror:null,onsuccess:null};return"delete"===e.type?u.fire.call(p,n,h,d):"add"===e.type||void 0===h?(i=c.fire.call(p,n,e.values[r],d),null==n&&null!=i&&(e.keys[r]=n=i,a.outbound||b(e.values[r],a.keyPath,n))):(i=nf(h,e.values[r]),(o=f.fire.call(p,i,n,h,d))&&(s=e.values[r],Object.keys(o).forEach(function(e){l(s,e)?s[e]=o[e]:b(s,e,o[e])}))),p});return i.mutate(e).then(function(r){for(var i=r.failures,a=r.results,o=r.numFailures,r=r.lastResult,s=0;s<h.length;++s){var u=(a||h)[s],l=n[s];null==u?l.onerror&&l.onerror(i[s]):l.onsuccess&&l.onsuccess("put"===e.type&&t[s]?e.values[s]:u)}return{failures:i,results:a,numFailures:o,lastResult:r}}).catch(function(e){return n.forEach(function(t){return t.onerror&&t.onerror(e)}),Promise.reject(e)})})}}})}})}};function np(e,t,n){try{if(!t||t.keys.length<e.length)return null;for(var r=[],i=0,a=0;i<t.keys.length&&a<e.length;++i)0===eJ(t.keys[i],e[a])&&(r.push(n?S(t.values[i]):t.values[i]),++a);return r.length===e.length?r:null}catch(o){return null}}var n$={stack:"dbcore",level:-1,create:function(e){return{table:function(n){var r=e.table(n);return t(t({},r),{getMany:function(e){if(!e.cache)return r.getMany(e);var t=np(e.keys,e.trans._cache,"clone"===e.cache);return t?e_.resolve(t):r.getMany(e).then(function(t){return e.trans._cache={keys:e.keys,values:"clone"===e.cache?S(t):t},t})},mutate:function(e){return"add"!==e.type&&(e.trans._cache=null),r.mutate(e)}})}}}};function nm(e,t){return"readonly"===e.trans.mode&&!!e.subscr&&!e.trans.explicit&&"disabled"!==e.trans.db._options.cache&&!t.schema.primaryKey.outbound}function n8(e,t){switch(e){case"query":return t.values&&!t.unique;case"get":case"getMany":case"count":case"openCursor":return!1}}var ny={stack:"dbcore",level:0,name:"Observability",create:function(e){var n=e.schema.name,r=new tG(e.MIN_KEY,e.MAX_KEY);return t(t({},e),{transaction:function(t,n,r){if(e8.subscr&&"readonly"!==n)throw new V.ReadOnly("Readwrite transaction in liveQuery context. Querier source: ".concat(e8.querier));return e.transaction(t,n,r)},table:function(o){var s=e.table(o),u=s.schema,l=u.primaryKey,c=u.indexes,f=l.extractKey,d=l.outbound,h=l.autoIncrement&&c.filter(function(e){return e.compound&&e.keyPath.includes(l.keyPath)}),p=t(t({},s),{mutate:function(e){function t(e){return p[e="idb://".concat(n,"/").concat(o,"/").concat(e)]||(p[e]=new tG)}var i,c,f,d=e.trans,p=e.mutatedParts||(e.mutatedParts={}),$=t(""),m=t(":dels"),y=e.type,v="deleteRange"===e.type?[e.range]:"delete"===e.type?[e.keys]:e.values.length<50?[nd(l,e).filter(function(e){return e}),e.values]:[],g=v[0],_=v[1],v=e.trans._cache;return a(g)?($.addKeys(g),(v="delete"===y||g.length===_.length?np(g,v):null)||m.addKeys(g),(v||_)&&(i=t,c=v,f=_,u.indexes.forEach(function(e){var t=i(e.name||"");function n(t){return null!=t?e.extractKey(t):null}function r(n){return e.multiEntry&&a(n)?n.forEach(function(e){return t.addKey(e)}):t.addKey(n)}(c||f).forEach(function(e,t){var i=c&&n(c[t]),t=f&&n(f[t]);0!==eJ(i,t)&&(null!=i&&r(i),null!=t&&r(t))})}))):g?(_={from:g.lower,to:g.upper},m.add(_),$.add(_)):($.add(r),m.add(r),u.indexes.forEach(function(e){return t(e.name).add(r)})),s.mutate(e).then(function(n){return!g||"add"!==e.type&&"put"!==e.type||($.addKeys(n.results),h&&h.forEach(function(r){var i=e.values.map(function(e){return r.extractKey(e)}),a=r.keyPath.findIndex(function(e){return e===l.keyPath});n.results.forEach(function(e){return i[a]=e}),t(r.name).addKeys(i)})),d.mutatedParts=ne(d.mutatedParts||{},p),n})}}),c=function(t){var n=t.query,t=n.index,n=n.range;return[t,new tG(null!==(t=n.lower)&&void 0!==t?t:e.MIN_KEY,null!==(n=n.upper)&&void 0!==n?n:e.MAX_KEY)]},$={get:function(e){return[l,new tG(e.key)]},getMany:function(e){return[l,(new tG).addKeys(e.keys)]},count:c,query:c,openCursor:c};return i($).forEach(function(e){p[e]=function(i){var a=e8.subscr,u=!!a,l=nm(e8,s)&&n8(e,i)?i.obsSet={}:a;if(u){var c=function(e){return l[e="idb://".concat(n,"/").concat(o,"/").concat(e)]||(l[e]=new tG)},h=c(""),p=c(":dels"),a=$[e](i),u=a[0],a=a[1];if(("query"===e&&u.isPrimaryKey&&!i.values?p:c(u.name||"")).add(a),!u.isPrimaryKey){if("count"!==e){var m="query"===e&&d&&i.values&&s.query(t(t({},i),{values:!1}));return s[e].apply(this,arguments).then(function(t){if("query"===e){if(d&&i.values)return m.then(function(e){return e=e.result,h.addKeys(e),t});var n=i.values?t.result.map(f):t.result;(i.values?h:p).addKeys(n)}else if("openCursor"===e){var r=t,a=i.values;return r&&Object.create(r,{key:{get:function(){return p.addKey(r.primaryKey),r.key}},primaryKey:{get:function(){var e=r.primaryKey;return p.addKey(e),e}},value:{get:function(){return a&&h.addKey(r.primaryKey),r.value}}})}return t})}p.add(r)}}return s[e].apply(this,arguments)}}),p}})}};function nv(e,n,r){if(0===r.numFailures)return n;if("deleteRange"===n.type)return null;var i=n.keys?n.keys.length:"values"in n&&n.values?n.values.length:1;return r.numFailures===i?null:(n=t({},n),a(n.keys)&&(n.keys=n.keys.filter(function(e,t){return!(t in r.failures)})),"values"in n&&a(n.values)&&(n.values=n.values.filter(function(e,t){return!(t in r.failures)})),n)}function ng(e,t){var n,r;return n=e,(void 0===(r=t).lower||(r.lowerOpen?0<eJ(n,r.lower):0<=eJ(n,r.lower)))&&(void 0===t.upper||(t.upperOpen?0>eJ(e,t.upper):0>=eJ(e,t.upper)))}function n_(e,t,n,r,i,o){if(!n||0===n.length)return e;var s=t.query.index,u=s.multiEntry,l=t.query.range,c=r.schema.primaryKey.extractKey,f=s.extractKey,d=(s.lowLevelIndex||s).extractKey,n=n.reduce(function(e,n){var r=e,i="add"===n.type||"put"===n.type?n.values.filter(function(e){return e=f(e),u&&a(e)?e.some(function(e){return ng(e,l)}):ng(e,l)}).map(function(e){return e=S(e),o&&Object.freeze(e),e}):[];switch(n.type){case"add":r=e.concat(t.values?i:i.map(function(e){return c(e)}));break;case"put":var s=(new tG).addKeys(n.values.map(function(e){return c(e)})),r=e.filter(function(e){return e=t.values?c(e):e,!t9(new tG(e),s)}).concat(t.values?i:i.map(function(e){return c(e)}));break;case"delete":var d=(new tG).addKeys(n.keys);r=e.filter(function(e){return e=t.values?c(e):e,!t9(new tG(e),d)});break;case"deleteRange":var h=n.range;r=e.filter(function(e){return!ng(c(e),h)})}return r},e);return n===e?e:(n.sort(function(e,t){return eJ(d(e),d(t))||eJ(c(e),c(t))}),t.limit&&t.limit<1/0&&(n.length>t.limit?n.length=t.limit:e.length===t.limit&&n.length<t.limit&&(i.dirty=!0)),o?Object.freeze(n):n)}function nb(e,t){return 0===eJ(e.lower,t.lower)&&0===eJ(e.upper,t.upper)&&!!e.lowerOpen==!!t.lowerOpen&&!!e.upperOpen==!!t.upperOpen}var nw={stack:"dbcore",level:0,name:"Cache",create:function(e){var n=e.schema.name;return t(t({},e),{transaction:function(t,r,i){var a,o,s=e.transaction(t,r,i);return"readwrite"===r&&(o=(a=new AbortController).signal,i=function(i){return function(){if(a.abort(),"readwrite"===r){for(var o=new Set,u=0,l=t;u<l.length;u++){var c=l[u],f=nn["idb://".concat(n,"/").concat(c)];if(f){var d=e.table(c),h=f.optimisticOps.filter(function(e){return e.trans===s});if(s._explicit&&i&&s.mutatedParts)for(var p=0,$=Object.values(f.queries.query);p<$.length;p++)for(var m=0,y=(_=$[p]).slice();m<y.length;m++)nt((b=y[m]).obsSet,s.mutatedParts)&&(R(_,b),b.subscribers.forEach(function(e){return o.add(e)}));else if(0<h.length){f.optimisticOps=f.optimisticOps.filter(function(e){return e.trans!==s});for(var v=0,g=Object.values(f.queries.query);v<g.length;v++)for(var _,b,w,k=0,x=(_=g[v]).slice();k<x.length;k++)null!=(b=x[k]).res&&s.mutatedParts&&(i&&!b.dirty?(w=Object.isFrozen(b.res),w=n_(b.res,b.req,h,d,b,w),b.dirty?(R(_,b),b.subscribers.forEach(function(e){return o.add(e)})):w!==b.res&&(b.res=w,b.promise=e_.resolve({result:w}))):(b.dirty&&R(_,b),b.subscribers.forEach(function(e){return o.add(e)})))}}}o.forEach(function(e){return e()})}}},s.addEventListener("abort",i(!1),{signal:o}),s.addEventListener("error",i(!1),{signal:o}),s.addEventListener("complete",i(!0),{signal:o})),s},table:function(r){var i=e.table(r),a=i.schema.primaryKey;return t(t({},i),{mutate:function(e){var o=e8.trans;if(a.outbound||"disabled"===o.db._options.cache||o.explicit)return i.mutate(e);var s=nn["idb://".concat(n,"/").concat(r)];return s?(o=i.mutate(e),("add"===e.type||"put"===e.type)&&(50<=e.values.length||nd(a,e).some(function(e){return null==e}))?o.then(function(n){var r=nv(0,t(t({},e),{values:e.values.map(function(e,r){var i,e=null!==(i=a.keyPath)&&void 0!==i&&i.includes(".")?S(e):t({},e);return b(e,a.keyPath,n.results[r]),e})}),n);s.optimisticOps.push(r),queueMicrotask(function(){return e.mutatedParts&&na(e.mutatedParts)})}):(s.optimisticOps.push(e),e.mutatedParts&&na(e.mutatedParts),o.then(function(t){0<t.numFailures&&(R(s.optimisticOps,e),(t=nv(0,e,t))&&s.optimisticOps.push(t),e.mutatedParts&&na(e.mutatedParts))}),o.catch(function(){R(s.optimisticOps,e),e.mutatedParts&&na(e.mutatedParts)})),o):i.mutate(e)},query:function(e){if(!nm(e8,i)||!n8("query",e))return i.query(e);var t,a,o,s,u="immutable"===(null===(d=e8.trans)||void 0===d?void 0:d.db._options.cache),l=e8,c=l.requery,f=l.signal,d=function(e,t,n,r){var i=nn["idb://".concat(e,"/").concat(t)];if(!i)return[];if(!(t=i.queries[n]))return[null,!1,i,null];var a=t[(r.query?r.query.index.name:null)||""];if(!a)return[null,!1,i,null];switch(n){case"query":var o=a.find(function(e){return e.req.limit===r.limit&&e.req.values===r.values&&nb(e.req.query.range,r.query.range)});return o?[o,!0,i,a]:[a.find(function(e){var t,n;return("limit"in e.req?e.req.limit:1/0)>=r.limit&&(!r.values||e.req.values)&&(t=e.req.query.range,n=r.query.range,0>=function(e,t,n,r){if(void 0===e)return void 0!==t?-1:0;if(void 0===t)return 1;if(0===(t=eJ(e,t))){if(n&&r)return 0;if(n)return 1;if(r)return -1}return t}(t.lower,n.lower,t.lowerOpen,n.lowerOpen)&&0<=function(e,t,n,r){if(void 0===e)return void 0!==t?1:0;if(void 0===t)return -1;if(0===(t=eJ(e,t))){if(n&&r)return 0;if(n)return -1;if(r)return 1}return t}(t.upper,n.upper,t.upperOpen,n.upperOpen))}),!1,i,a];case"count":return[o=a.find(function(e){return nb(e.req.query.range,r.query.range)}),!!o,i,a]}}(n,r,"query",e),h=d[0],l=d[1],p=d[2],$=d[3];return h&&l?h.obsSet=e.obsSet:(l=i.query(e).then(function(e){var t=e.result;if(h&&(h.res=t),u){for(var n=0,r=t.length;n<r;++n)Object.freeze(t[n]);Object.freeze(t)}else e.result=S(t);return e}).catch(function(e){return $&&h&&R($,h),Promise.reject(e)}),h={obsSet:e.obsSet,promise:l,subscribers:new Set,type:"query",req:e,dirty:!1},$?$.push(h):($=[h],(p=p||(nn["idb://".concat(n,"/").concat(r)]={queries:{query:{},count:{}},objs:new Map,optimisticOps:[],unsignaledParts:{}})).queries.query[e.query.index.name||""]=$)),t=h,a=$,o=c,s=f,t.subscribers.add(o),s.addEventListener("abort",function(){var e,n;t.subscribers.delete(o),0===t.subscribers.size&&(e=t,n=a,setTimeout(function(){0===e.subscribers.size&&R(n,e)},3e3))}),h.promise.then(function(t){return{result:n_(t.result,e,null==p?void 0:p.optimisticOps,i,h,u)}})}})}})}};function n0(e,t){return new Proxy(e,{get:function(e,n,r){return"db"===n?t:Reflect.get(e,n,r)}})}var nk=(nx.prototype.version=function(e){if(isNaN(e)||e<.1)throw new V.Type("Given version is not a positive number");if(e=Math.round(10*e)/10,this.idbdb||this._state.isBeingOpened)throw new V.Schema("Cannot add version when database is open");this.verno=Math.max(this.verno,e);var t=this._versions,n=t.filter(function(t){return t._cfg.version===e})[0];return n||(n=new this.Version(e),t.push(n),t.sort(tK),n.stores({}),this._state.autoSchema=!1,n)},nx.prototype._whenReady=function(e){var t=this;return this.idbdb&&(this._state.openComplete||e8.letThrough||this._vip)?e():new e_(function(e,n){if(t._state.openComplete)return n(new V.DatabaseClosed(t._state.dbOpenError));if(!t._state.isBeingOpened){if(!t._state.autoOpen)return void n(new V.DatabaseClosed);t.open().catch(W)}t._state.dbReadyPromise.then(e,n)}).then(e)},nx.prototype.use=function(e){var t=e.stack,n=e.create,r=e.level,i=e.name;return i&&this.unuse({stack:t,name:i}),(e=this._middlewares[t]||(this._middlewares[t]=[])).push({stack:t,create:n,level:null==r?10:r,name:i}),e.sort(function(e,t){return e.level-t.level}),this},nx.prototype.unuse=function(e){var t=e.stack,n=e.name,r=e.create;return t&&this._middlewares[t]&&(this._middlewares[t]=this._middlewares[t].filter(function(e){return r?e.create!==r:!!n&&e.name!==n})),this},nx.prototype.open=function(){var e=this;return eF(em,function(){return function e(t){var n=t._state,r=t._deps.indexedDB;if(n.isBeingOpened||t.idbdb)return n.dbReadyPromise.then(function(){return n.dbOpenError?eM(n.dbOpenError):t});n.isBeingOpened=!0,n.dbOpenError=null,n.openComplete=!1;var a=n.openCanceller,o=Math.round(10*t.verno),s=!1;function u(){if(n.openCanceller!==a)throw new V.DatabaseClosed("db.open() was cancelled")}var l,c=n.dbReadyResolve,f=null,d=!1;return e_.race([a,("undefined"==typeof navigator?e_.resolve():!navigator.userAgentData&&/Safari\//.test(navigator.userAgent)&&!/Chrom(e|ium)\//.test(navigator.userAgent)&&indexedDB.databases?new Promise(function(e){function t(){return indexedDB.databases().finally(e)}l=setInterval(t,100),t()}).finally(function(){return clearInterval(l)}):Promise.resolve()).then(function e(){return new e_(function(a,l){if(u(),!r)throw new V.MissingAPI;var c=t.name,h=n.autoSchema||!o?r.open(c):r.open(c,o);if(!h)throw new V.MissingAPI;h.onerror=tk(l),h.onblocked=eS(t._fireOnBlocked),h.onupgradeneeded=eS(function(e){var a,o,u,p,$,m,y,v,g;f=h.transaction,n.autoSchema&&!t._options.allowEmptyDB?(h.onerror=tx,f.abort(),h.result.close(),(a=r.deleteDatabase(c)).onsuccess=a.onerror=eS(function(){l(new V.NoSuchDatabase("Database ".concat(c," doesnt exist")))})):(f.onerror=tk(l),d=(e=e.oldVersion>0x4000000000000000?0:e.oldVersion)<1,t.idbdb=h.result,s&&function e(t,n){tB(t._dbSchema,n),n.db.version%10!=0||n.objectStoreNames.contains("$meta")||n.db.createObjectStore("$meta").add(Math.ceil(n.db.version/10-1),"version");var r=tN(0,t.idbdb,n);tj(t,t._dbSchema,n);for(var i=0,a=tq(r,t._dbSchema).change;i<a.length;i++){var o=function(e){if(e.change.length||e.recreate)return console.warn("Unable to patch indexes of table ".concat(e.name," because it has changes on the type of index or primary key.")),{value:void 0};var t=n.objectStore(e.name);e.add.forEach(function(n){ei&&console.debug("Dexie upgrade patch: Creating missing index ".concat(e.name,".").concat(n.src)),tF(t,n)})}(a[i]);if("object"==typeof o)return o.value}}(t,f),o=t,u=e/10,p=f,$=l,m=o._dbSchema,p.objectStoreNames.contains("$meta")&&!m.$meta&&(m.$meta=tD("$meta",tM("")[0],[]),o._storeNames.push("$meta")),(y=o._createTransaction("readwrite",o._storeNames,m)).create(p),y._completion.catch($),v=y._reject.bind(y),g=e8.transless||e8,eT(function(){var e,t;return e8.trans=y,e8.transless=g,0!==u?(tT(o,p),t=u,((e=y).storeNames.includes("$meta")?e.table("$meta").get("version").then(function(e){return null!=e?e:t}):e_.resolve(t)).then(function(e){var t,n,r,a,s,u;return n=e,r=y,a=p,s=[],e=(t=o)._versions,u=t._dbSchema=tN(0,t.idbdb,a),0!==(e=e.filter(function(e){return e._cfg.version>=n})).length?(e.forEach(function(e){s.push(function(){var o=u,s=e._cfg.dbschema;tj(t,o,a),tj(t,s,a),u=t._dbSchema=s;var l=tq(o,s);l.add.forEach(function(e){t7(a,e[0],e[1].primKey,e[1].indexes)}),l.change.forEach(function(e){if(e.recreate)throw new V.Upgrade("Not yet support for changing primary key");var t=a.objectStore(e.name);e.add.forEach(function(e){return tF(t,e)}),e.change.forEach(function(e){t.deleteIndex(e.name),tF(t,e)}),e.del.forEach(function(e){return t.deleteIndex(e)})});var c=e._cfg.contentUpgrade;if(c&&e._cfg.version>n){tT(t,a),r._memoizedTables={};var f=w(s);l.del.forEach(function(e){f[e]=o[e]}),t2(t,[t.Transaction.prototype]),tz(t,[t.Transaction.prototype],i(f),f),r.schema=f;var d,h=K(c);return h&&ez(),l=e_.follow(function(){var e;(d=c(r))&&h&&(e=e2.bind(null,null),d.then(e,e))}),d&&"function"==typeof d.then?e_.resolve(d):l.then(function(){return d})}}),s.push(function(n){var i,a;i=e._cfg.dbschema,a=n,[].slice.call(a.db.objectStoreNames).forEach(function(e){return null==i[e]&&a.db.deleteObjectStore(e)}),t2(t,[t.Transaction.prototype]),tz(t,[t.Transaction.prototype],t._storeNames,t._dbSchema),r.schema=t._dbSchema}),s.push(function(n){t.idbdb.objectStoreNames.contains("$meta")&&(Math.ceil(t.idbdb.version/10)===e._cfg.version?(t.idbdb.deleteObjectStore("$meta"),delete t._dbSchema.$meta,t._storeNames=t._storeNames.filter(function(e){return"$meta"!==e})):n.objectStore("$meta").put(e._cfg.version,"version"))})}),(function e(){return s.length?e_.resolve(s.shift()(r.idbtrans)).then(e):e_.resolve()})().then(function(){tB(u,a)})):e_.resolve()}).catch(v)):(i(m).forEach(function(e){t7(p,e,m[e].primKey,m[e].indexes)}),tT(o,p),void e_.follow(function(){return o.on.populate.fire(y)}).catch(v))}))},l),h.onsuccess=eS(function(){f=null;var r,u,l,p,$,y=t.idbdb=h.result,v=m(y.objectStoreNames);if(0<v.length)try{var g=y.transaction(1===(p=v).length?p[0]:p,"readonly");if(n.autoSchema)u=y,l=g,(r=t).verno=u.version/10,l=r._dbSchema=tN(0,u,l),r._storeNames=m(u.objectStoreNames,0),tz(r,[r._allTables],i(l),l);else if(tj(t,t._dbSchema,g),(($=tq(tN(0,($=t).idbdb,g),$._dbSchema)).add.length||$.change.some(function(e){return e.add.length||e.change.length}))&&!s)return console.warn("Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Dexie will add missing parts and increment native version number to workaround this."),y.close(),o=y.version+1,s=!0,a(e());tT(t,g)}catch(_){}eU.push(t),y.onversionchange=eS(function(e){n.vcFired=!0,t.on("versionchange").fire(e)}),y.onclose=eS(function(e){t.on("close").fire(e)}),d&&($=t._deps,g=c,y=$.indexedDB,$=$.IDBKeyRange,tZ(y)||g===eZ||tU(y,$).put({name:g}).catch(W)),a()},l)}).catch(function(t){switch(null==t?void 0:t.name){case"UnknownError":if(0<n.PR1398_maxLoop)return n.PR1398_maxLoop--,console.warn("Dexie: Workaround for Chrome UnknownError on open()"),e();break;case"VersionError":if(0<o)return o=0,e()}return e_.reject(t)})})]).then(function(){return u(),n.onReadyBeingFired=[],e_.resolve(tV(function(){return t.on.ready.fire(t.vip)})).then(function e(){if(0<n.onReadyBeingFired.length){var r=n.onReadyBeingFired.reduce(er,W);return n.onReadyBeingFired=[],e_.resolve(tV(function(){return r(t.vip)})).then(e)}})}).finally(function(){n.openCanceller===a&&(n.onReadyBeingFired=null,n.isBeingOpened=!1)}).catch(function(e){n.dbOpenError=e;try{f&&f.abort()}catch(r){}return a===n.openCanceller&&t._close(),eM(e)}).finally(function(){n.openComplete=!0,c()}).then(function(){var e;return d&&(e={},t.tables.forEach(function(n){n.schema.indexes.forEach(function(r){r.name&&(e["idb://".concat(t.name,"/").concat(n.name,"/").concat(r.name)]=new tG(-1/0,[[[]]]))}),e["idb://".concat(t.name,"/").concat(n.name,"/")]=e["idb://".concat(t.name,"/").concat(n.name,"/:dels")]=new tG(-1/0,[[[]]])}),tE(t3).fire(e),no(e,!0)),t})}(e)})},nx.prototype._close=function(){var e=this._state,t=eU.indexOf(this);if(0<=t&&eU.splice(t,1),this.idbdb){try{this.idbdb.close()}catch(n){}this.idbdb=null}e.isBeingOpened||(e.dbReadyPromise=new e_(function(t){e.dbReadyResolve=t}),e.openCanceller=new e_(function(t,n){e.cancelOpen=n}))},nx.prototype.close=function(e){var t=(void 0===e?{disableAutoOpen:!0}:e).disableAutoOpen,e=this._state;t?(e.isBeingOpened&&e.cancelOpen(new V.DatabaseClosed),this._close(),e.autoOpen=!1,e.dbOpenError=new V.DatabaseClosed):(this._close(),e.autoOpen=this._options.autoOpen||e.isBeingOpened,e.openComplete=!1,e.dbOpenError=null)},nx.prototype.delete=function(e){var t=this;void 0===e&&(e={disableAutoOpen:!0});var n=0<arguments.length&&"object"!=typeof arguments[0],r=this._state;return new e_(function(i,a){function o(){t.close(e);var n=t._deps.indexedDB.deleteDatabase(t.name);n.onsuccess=eS(function(){var e,n,r;e=t._deps,n=t.name,r=e.indexedDB,e=e.IDBKeyRange,tZ(r)||n===eZ||tU(r,e).delete(n).catch(W),i()}),n.onerror=tk(a),n.onblocked=t._fireOnBlocked}if(n)throw new V.InvalidArgument("Invalid closeOptions argument to db.delete()");r.isBeingOpened?r.dbReadyPromise.then(o):o()})},nx.prototype.backendDB=function(){return this.idbdb},nx.prototype.isOpen=function(){return null!==this.idbdb},nx.prototype.hasBeenClosed=function(){var e=this._state.dbOpenError;return e&&"DatabaseClosed"===e.name},nx.prototype.hasFailed=function(){return null!==this._state.dbOpenError},nx.prototype.dynamicallyOpened=function(){return this._state.autoSchema},Object.defineProperty(nx.prototype,"tables",{get:function(){var e=this;return i(this._allTables).map(function(t){return e._allTables[t]})},enumerable:!1,configurable:!0}),nx.prototype.transaction=function(){var e=(function(e,t,n){var r=arguments.length;if(r<2)throw new V.InvalidArgument("Too few arguments");for(var i=Array(r-1);--r;)i[r-1]=arguments[r];return n=i.pop(),[e,x(i),n]}).apply(this,arguments);return this._transaction.apply(this,e)},nx.prototype._transaction=function(e,t,n){var r=this,i=e8.trans;i&&i.db===this&&-1===e.indexOf("!")||(i=null);var a,o,s=-1!==e.indexOf("?");e=e.replace("!","").replace("?","");try{if(o=t.map(function(e){if(e=e instanceof r.Table?e.name:e,"string"!=typeof e)throw TypeError("Invalid table argument to Dexie.transaction(). Only Table or String are allowed");return e}),"r"==e||e===eV)a=eV;else{if("rw"!=e&&e!=eH)throw new V.InvalidArgument("Invalid transaction mode: "+e);a=eH}if(i){if(i.mode===eV&&a===eH){if(!s)throw new V.SubTransaction("Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY");i=null}i&&o.forEach(function(e){if(i&&-1===i.storeNames.indexOf(e)){if(!s)throw new V.SubTransaction("Table "+e+" not included in parent transaction.");i=null}}),s&&i&&!i.active&&(i=null)}}catch(u){return i?i._promise(null,function(e,t){t(u)}):eM(u)}var l=(function e(t,n,r,i,a){return e_.resolve().then(function(){var o=e8.transless||e8,s=t._createTransaction(n,r,t._dbSchema,i);if(s.explicit=!0,o={trans:s,transless:o},i)s.idbtrans=i.idbtrans;else try{s.create(),s.idbtrans._explicit=!0,t._state.PR1398_maxLoop=3}catch(u){return u.name===U.InvalidState&&t.isOpen()&&0<--t._state.PR1398_maxLoop?(console.warn("Dexie: Need to reopen db"),t.close({disableAutoOpen:!1}),t.open().then(function(){return e(t,n,r,null,a)})):eM(u)}var l,c=K(a);return c&&ez(),o=e_.follow(function(){var e;(l=a.call(s,s))&&(c?(e=e2.bind(null,null),l.then(e,e)):"function"==typeof l.next&&"function"==typeof l.throw&&(l=nu(l)))},o),(l&&"function"==typeof l.then?e_.resolve(l).then(function(e){return s.active?e:eM(new V.PrematureCommit("Transaction committed too early. See http://bit.ly/2kdckMn"))}):o.then(function(){return l})).then(function(e){return i&&s._resolve(),s._completion.then(function(){return e})}).catch(function(e){return s._reject(e),eM(e)})})}).bind(null,this,a,o,i,n);return i?i._promise(a,l,"lock"):e8.trans?eF(e8.transless,function(){return r._whenReady(l)}):this._whenReady(l)},nx.prototype.table=function(e){if(!l(this._allTables,e))throw new V.InvalidTable("Table ".concat(e," does not exist"));return this._allTables[e]},nx);function nx(e,n){var r=this;this._middlewares={},this.verno=0;var i=nx.dependencies;this._options=n=t({addons:nx.addons,autoOpen:!0,indexedDB:i.indexedDB,IDBKeyRange:i.IDBKeyRange,cache:"cloned"},n),this._deps={indexedDB:n.indexedDB,IDBKeyRange:n.IDBKeyRange},i=n.addons,this._dbSchema={},this._versions=[],this._storeNames=[],this._allTables={},this.idbdb=null,this._novip=this;var a,o,s,u,l,c,f,d={dbOpenError:null,isBeingOpened:!1,onReadyBeingFired:null,openComplete:!1,dbReadyResolve:W,dbReadyPromise:null,cancelOpen:W,openCanceller:null,autoSchema:!0,PR1398_maxLoop:3,autoOpen:n.autoOpen};d.dbReadyPromise=new e_(function(e){d.dbReadyResolve=e}),d.openCanceller=new e_(function(e,t){d.cancelOpen=t}),this._state=d,this.name=e,this.on=tn(this,"populate","blocked","versionchange","close",{ready:[er,W]}),this.on.ready.subscribe=(c=this.on.ready.subscribe,(f=function(e){return function(t,n){nx.vip(function(){var i,a=r._state;a.openComplete?(a.dbOpenError||e_.resolve().then(t),n&&e(t)):a.onReadyBeingFired?(a.onReadyBeingFired.push(t),n&&e(t)):(e(t),i=r,n||e(function e(){i.on.ready.unsubscribe(t),i.on.ready.unsubscribe(e)}))})}})(c)),this.Collection=(a=this,tr(th.prototype,function(e,t){this.db=a;var n=eW,r=null;if(t)try{n=t()}catch(i){r=i}var o=e._ctx,t=o.table,e=t.hook.reading.fire;this._ctx={table:t,index:o.index,isPrimKey:!o.index||t.schema.primKey.keyPath&&o.index===t.schema.primKey.name,range:n,keysOnly:!1,dir:"next",unique:"",algorithm:null,filter:null,replayFilter:null,justLimit:!0,isMatch:null,offset:0,limit:1/0,error:r,or:o.or,valueMapper:e!==Y?e:null}})),this.Table=(o=this,tr(te.prototype,function(e,t,n){this.db=o,this._tx=n,this.name=e,this.schema=t,this.hook=o._allTables[e]?o._allTables[e].hook:tn(null,{creating:[Q,W],reading:[J,Y],updating:[et,W],deleting:[ee,W]})})),this.Transaction=(s=this,tr(t1.prototype,function(e,t,n,r,i){var a=this;this.db=s,this.mode=e,this.storeNames=t,this.schema=n,this.chromeTransactionDurability=r,this.idbtrans=null,this.on=tn(this,"complete","error","abort"),this.parent=i||null,this.active=!0,this._reculock=0,this._blockedFuncs=[],this._resolve=null,this._reject=null,this._waitingFor=null,this._waitingQueue=null,this._spinCount=0,this._completion=new e_(function(e,t){a._resolve=e,a._reject=t}),this._completion.then(function(){a.active=!1,a.on.complete.fire()},function(e){var t=a.active;return a.active=!1,a.on.error.fire(e),a.parent?a.parent._reject(e):t&&a.idbtrans&&a.idbtrans.abort(),eM(e)})})),this.Version=(u=this,tr(tL.prototype,function(e){this.db=u,this._cfg={version:e,storesSource:null,dbschema:{},tables:{},contentUpgrade:null}})),this.WhereClause=(l=this,tr(tw.prototype,function(e,t,n){if(this.db=l,this._ctx={table:e,index:":id"===t?null:t,or:n},this._cmp=this._ascending=eJ,this._descending=function(e,t){return eJ(t,e)},this._max=function(e,t){return 0<eJ(e,t)?e:t},this._min=function(e,t){return 0>eJ(e,t)?e:t},this._IDBKeyRange=l._deps.IDBKeyRange,!this._IDBKeyRange)throw new V.MissingAPI})),this.on("versionchange",function(e){0<e.newVersion?console.warn("Another connection wants to upgrade database '".concat(r.name,"'. Closing db now to resume the upgrade.")):console.warn("Another connection wants to delete database '".concat(r.name,"'. Closing db now to resume the delete request.")),r.close({disableAutoOpen:!1})}),this.on("blocked",function(e){!e.newVersion||e.newVersion<e.oldVersion?console.warn("Dexie.delete('".concat(r.name,"') was blocked")):console.warn("Upgrade '".concat(r.name,"' blocked by other connection holding version ").concat(e.oldVersion/10))}),this._maxKey=tP(n.IDBKeyRange),this._createTransaction=function(e,t,n,i){return new r.Transaction(e,t,n,r._options.chromeTransactionDurability,i)},this._fireOnBlocked=function(e){r.on("blocked").fire(e),eU.filter(function(e){return e.name===r.name&&e!==r&&!e._state.vcFired}).map(function(t){return t.on("versionchange").fire(e)})},this.use(n$),this.use(nw),this.use(ny),this.use(nc),this.use(nh);var h=new Proxy(this,{get:function(e,t,n){if("_vip"===t)return!0;if("table"===t)return function(e){return n0(r.table(e),h)};var i=Reflect.get(e,t,n);return i instanceof te?n0(i,h):"tables"===t?i.map(function(e){return n0(e,h)}):"_createTransaction"===t?function(){return n0(i.apply(this,arguments),h)}:i}});this.vip=h,i.forEach(function(e){return e(r)})}var n3,B="undefined"!=typeof Symbol&&"observable"in Symbol?Symbol.observable:"@@observable",n4=(nE.prototype.subscribe=function(e,t,n){return this._subscribe(e&&"function"!=typeof e?e:{next:e,error:t,complete:n})},nE.prototype[B]=function(){return this},nE);function nE(e){this._subscribe=e}try{n3={indexedDB:r.indexedDB||r.mozIndexedDB||r.webkitIndexedDB||r.msIndexedDB,IDBKeyRange:r.IDBKeyRange||r.webkitIDBKeyRange}}catch(n1){n3={indexedDB:null,IDBKeyRange:null}}function nA(e){var t,n=!1,r=new n4(function(r){var i,a=K(e),o=!1,s={},u={},c={get closed(){return o},unsubscribe:function(){o||(o=!0,i&&i.abort(),f&&tE.storagemutated.unsubscribe(h))}};r.start&&r.start(c);var f=!1,d=function(){return ej(p)},h=function(e){ne(s,e),nt(u,s)&&d()},p=function(){var c,p,$;!o&&n3.indexedDB&&(s={},c={},i&&i.abort(),i=new AbortController,Promise.resolve($=function(t){var n=eE();try{a&&ez();var r=eT(e,t);return r=a?r.finally(e2):r}finally{n&&e1()}}(p={subscr:c,signal:i.signal,requery:d,querier:e,trans:null})).then(function(e){n=!0,t=e,o||p.signal.aborted||(s={},function(e){for(var t in e)if(l(e,t))return;return 1}(u=c)||f||(tE(t3,h),f=!0),ej(function(){return!o&&r.next&&r.next(e)}))},function(e){n=!1,["DatabaseClosedError","AbortError"].includes(null==e?void 0:e.name)||o||ej(function(){o||r.error&&r.error(e)})}))};return setTimeout(d,0),c});return r.hasValue=function(){return n},r.getValue=function(){return t},r}var nI=nk;function nS(e){var t=n6;try{n6=!0,tE.storagemutated.fire(e),no(e,!0)}finally{n6=t}}c(nI,t(t({},G),{delete:function(e){return new nI(e,{addons:[]}).delete()},exists:function(e){return new nI(e,{addons:[]}).open().then(function(e){return e.close(),!0}).catch("NoSuchDatabaseError",function(){return!1})},getDatabaseNames:function(e){var t,n;try{return n=(t=nI.dependencies).indexedDB,t=t.IDBKeyRange,(tZ(n)?Promise.resolve(n.databases()).then(function(e){return e.map(function(e){return e.name}).filter(function(e){return e!==eZ})}):tU(n,t).toCollection().primaryKeys()).then(e)}catch(r){return eM(new V.MissingAPI)}},defineClass:function(){return function(e){o(this,e)}},ignoreTransaction:function(e){return e8.trans?eF(e8.transless,e):e()},vip:tV,async:function(e){return function(){try{var t=nu(e.apply(this,arguments));return t&&"function"==typeof t.then?t:e_.resolve(t)}catch(n){return eM(n)}}},spawn:function(e,t,n){try{var r=nu(e.apply(n,t||[]));return r&&"function"==typeof r.then?r:e_.resolve(r)}catch(i){return eM(i)}},currentTransaction:{get:function(){return e8.trans||null}},waitFor:function(e,t){return t=e_.resolve("function"==typeof e?nI.ignoreTransaction(e):e).timeout(t||6e4),e8.trans?e8.trans.waitFor(t):t},Promise:e_,debug:{get:function(){return ei},set:function(e){var t;ei=t=e}},derive:h,extend:o,props:c,override:y,Events:tn,on:tE,liveQuery:nA,extendObservabilitySet:ne,getByKeyPath:_,setByKeyPath:b,delByKeyPath:function(e,t){"string"==typeof t?b(e,t,void 0):"length"in t&&[].map.call(t,function(t){b(e,t,void 0)})},shallowClone:w,deepClone:S,getObjectDiff:nf,cmp:eJ,asap:g,minKey:-1/0,addons:[],connections:eU,errnames:U,dependencies:n3,cache:nn,semVer:"4.0.7",version:"4.0.7".split(".").map(function(e){return parseInt(e)}).reduce(function(e,t,n){return e+t/Math.pow(10,2*n)})})),nI.maxKey=tP(nI.dependencies.IDBKeyRange),"undefined"!=typeof dispatchEvent&&"undefined"!=typeof addEventListener&&(tE(t3,function(e){n6||(e=new CustomEvent(t4,{detail:e}),n6=!0,dispatchEvent(e),n6=!1)}),addEventListener(t4,function(e){e=e.detail,n6||nS(e)}));var nD,nP,n6=!1,nO=function(){};return"undefined"!=typeof BroadcastChannel&&((nO=function(){(nD=new BroadcastChannel(t4)).onmessage=function(e){return e.data&&nS(e.data)}})(),"function"==typeof nD.unref&&nD.unref(),tE(t3,function(e){n6||nD.postMessage(e)})),"undefined"!=typeof addEventListener&&(addEventListener("pagehide",function(e){if(!nk.disableBfCache&&e.persisted){ei&&console.debug("Dexie: handling persisted pagehide"),null!=nD&&nD.close();for(var t=0,n=eU;t<n.length;t++)n[t].close({disableAutoOpen:!1})}}),addEventListener("pageshow",function(e){!nk.disableBfCache&&e.persisted&&(ei&&console.debug("Dexie: handling persisted pageshow"),nO(),nS({all:new tG(-1/0,[[]])}))})),e_.rejectionMapper=function(e,t){return!e||e instanceof N||e instanceof TypeError||e instanceof SyntaxError||!e.name||!H[e.name]?e:(t=new H[e.name](t||e.message,e),"stack"in e&&d(t,"stack",{get:function(){return this.inner.stack}}),t)},t(nk,Object.freeze({__proto__:null,Dexie:nk,liveQuery:nA,Entity:e9,cmp:eJ,PropModSymbol:E,PropModification:tf,replacePrefix:function(e,t){return new tf({replacePrefix:[e,t]})},add:function(e){return new tf({add:e})},remove:function(e){return new tf({remove:e})},default:nk,RangeSet:tG,mergeRanges:tY,rangesOverlap:t9}),{default:nk}),nk},"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(n="undefined"!=typeof globalThis?globalThis:n||self).Dexie=r(),i=this,a=function(e){"use strict";function t(e){let t=e.length;for(;--t>=0;)e[t]=0}let n=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),r=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),i=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),a=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),o=Array(576);t(o);let s=Array(60);t(s);let u=Array(512);t(u);let l=Array(256);t(l);let c=Array(29);t(c);let f=Array(30);function d(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}let h,p,$;function m(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}t(f);let y=e=>e<256?u[e]:u[256+(e>>>7)],v=(e,t)=>{e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255},g=(e,t,n)=>{e.bi_valid>16-n?(e.bi_buf|=t<<e.bi_valid&65535,v(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=n-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)},_=(e,t,n)=>{g(e,n[2*t],n[2*t+1])},b=(e,t)=>{let n=0;do n|=1&e,e>>>=1,n<<=1;while(--t>0);return n>>>1},w=(e,t,n)=>{let r=Array(16),i,a,o=0;for(i=1;i<=15;i++)o=o+n[i-1]<<1,r[i]=o;for(a=0;a<=t;a++){let s=e[2*a+1];0!==s&&(e[2*a]=b(r[s]++,s))}},k=e=>{let t;for(t=0;t<286;t++)e.dyn_ltree[2*t]=0;for(t=0;t<30;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.sym_next=e.matches=0},x=e=>{e.bi_valid>8?v(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},E=(e,t,n,r)=>{let i=2*t,a=2*n;return e[i]<e[a]||e[i]===e[a]&&r[t]<=r[n]},A=(e,t,n)=>{let r=e.heap[n],i=n<<1;for(;i<=e.heap_len&&(i<e.heap_len&&E(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!E(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r},I=(e,t,i)=>{let a,o,s,u,d=0;if(0!==e.sym_next)do a=255&e.pending_buf[e.sym_buf+d++],a+=(255&e.pending_buf[e.sym_buf+d++])<<8,o=e.pending_buf[e.sym_buf+d++],0===a?_(e,o,t):(_(e,(s=l[o])+256+1,t),0!==(u=n[s])&&g(e,o-=c[s],u),s=y(--a),_(e,s,i),0!==(u=r[s])&&g(e,a-=f[s],u));while(d<e.sym_next);_(e,256,t)},S=(e,t)=>{let n=t.dyn_tree,r=t.stat_desc.static_tree,i=t.stat_desc.has_stree,a=t.stat_desc.elems,o,s,u,l=-1;for(e.heap_len=0,e.heap_max=573,o=0;o<a;o++)0!==n[2*o]?(e.heap[++e.heap_len]=l=o,e.depth[o]=0):n[2*o+1]=0;for(;e.heap_len<2;)n[2*(u=e.heap[++e.heap_len]=l<2?++l:0)]=1,e.depth[u]=0,e.opt_len--,i&&(e.static_len-=r[2*u+1]);for(t.max_code=l,o=e.heap_len>>1;o>=1;o--)A(e,n,o);u=a;do o=e.heap[1],e.heap[1]=e.heap[e.heap_len--],A(e,n,1),s=e.heap[1],e.heap[--e.heap_max]=o,e.heap[--e.heap_max]=s,n[2*u]=n[2*o]+n[2*s],e.depth[u]=(e.depth[o]>=e.depth[s]?e.depth[o]:e.depth[s])+1,n[2*o+1]=n[2*s+1]=u,e.heap[1]=u++,A(e,n,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],((e,t)=>{let n=t.dyn_tree,r=t.max_code,i=t.stat_desc.static_tree,a=t.stat_desc.has_stree,o=t.stat_desc.extra_bits,s=t.stat_desc.extra_base,u=t.stat_desc.max_length,l,c,f,d,h,p,$=0;for(d=0;d<=15;d++)e.bl_count[d]=0;for(n[2*e.heap[e.heap_max]+1]=0,l=e.heap_max+1;l<573;l++)(d=n[2*n[2*(c=e.heap[l])+1]+1]+1)>u&&(d=u,$++),n[2*c+1]=d,c>r||(e.bl_count[d]++,h=0,c>=s&&(h=o[c-s]),p=n[2*c],e.opt_len+=p*(d+h),a&&(e.static_len+=p*(i[2*c+1]+h)));if(0!==$){do{for(d=u-1;0===e.bl_count[d];)d--;e.bl_count[d]--,e.bl_count[d+1]+=2,e.bl_count[u]--,$-=2}while($>0);for(d=u;0!==d;d--)for(c=e.bl_count[d];0!==c;)(f=e.heap[--l])>r||(n[2*f+1]!==d&&(e.opt_len+=(d-n[2*f+1])*n[2*f],n[2*f+1]=d),c--)}})(e,t),w(n,l,e.bl_count)},D=(e,t,n)=>{let r,i,a=-1,o=t[1],s=0,u=7,l=4;for(0===o&&(u=138,l=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=o,o=t[2*(r+1)+1],++s<u&&i===o||(s<l?e.bl_tree[2*i]+=s:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[32]++):s<=10?e.bl_tree[34]++:e.bl_tree[36]++,s=0,a=i,0===o?(u=138,l=3):i===o?(u=6,l=3):(u=7,l=4))},P=(e,t,n)=>{let r,i,a=-1,o=t[1],s=0,u=7,l=4;for(0===o&&(u=138,l=3),r=0;r<=n;r++)if(i=o,o=t[2*(r+1)+1],!(++s<u&&i===o)){if(s<l)do _(e,i,e.bl_tree);while(0!=--s);else 0!==i?(i!==a&&(_(e,i,e.bl_tree),s--),_(e,16,e.bl_tree),g(e,s-3,2)):s<=10?(_(e,17,e.bl_tree),g(e,s-3,3)):(_(e,18,e.bl_tree),g(e,s-11,7));s=0,a=i,0===o?(u=138,l=3):i===o?(u=6,l=3):(u=7,l=4)}},O=!1,C=(e,t,n,r)=>{g(e,0+(r?1:0),3),x(e),v(e,n),v(e,~n),n&&e.pending_buf.set(e.window.subarray(t,t+n),e.pending),e.pending+=n};var R=(e,t,n,r)=>{let i,u,l=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=(e=>{let t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<256;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0})(e)),S(e,e.l_desc),S(e,e.d_desc),l=(e=>{let t;for(D(e,e.dyn_ltree,e.l_desc.max_code),D(e,e.dyn_dtree,e.d_desc.max_code),S(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*a[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t})(e),i=e.opt_len+3+7>>>3,(u=e.static_len+3+7>>>3)<=i&&(i=u)):i=u=n+5,n+4<=i&&-1!==t?C(e,t,n,r):4===e.strategy||u===i?(g(e,2+(r?1:0),3),I(e,o,s)):(g(e,4+(r?1:0),3),((e,t,n,r)=>{let i;for(g(e,t-257,5),g(e,n-1,5),g(e,r-4,4),i=0;i<r;i++)g(e,e.bl_tree[2*a[i]+1],3);P(e,e.dyn_ltree,t-1),P(e,e.dyn_dtree,n-1)})(e,e.l_desc.max_code+1,e.d_desc.max_code+1,l+1),I(e,e.dyn_ltree,e.dyn_dtree)),k(e),r&&x(e)},T={_tr_init(e){O||((()=>{let e,t,a,m,y,v=Array(16);for(a=0,m=0;m<28;m++)for(c[m]=a,e=0;e<1<<n[m];e++)l[a++]=m;for(l[a-1]=m,y=0,m=0;m<16;m++)for(f[m]=y,e=0;e<1<<r[m];e++)u[y++]=m;for(y>>=7;m<30;m++)for(f[m]=y<<7,e=0;e<1<<r[m]-7;e++)u[256+y++]=m;for(t=0;t<=15;t++)v[t]=0;for(e=0;e<=143;)o[2*e+1]=8,e++,v[8]++;for(;e<=255;)o[2*e+1]=9,e++,v[9]++;for(;e<=279;)o[2*e+1]=7,e++,v[7]++;for(;e<=287;)o[2*e+1]=8,e++,v[8]++;for(w(o,287,v),e=0;e<30;e++)s[2*e+1]=5,s[2*e]=b(e,5);h=new d(o,n,257,286,15),p=new d(s,r,0,30,15),$=new d([],i,0,19,7)})(),O=!0),e.l_desc=new m(e.dyn_ltree,h),e.d_desc=new m(e.dyn_dtree,p),e.bl_desc=new m(e.bl_tree,$),e.bi_buf=0,e.bi_valid=0,k(e)},_tr_stored_block:C,_tr_flush_block:R,_tr_tally:(e,t,n)=>(e.pending_buf[e.sym_buf+e.sym_next++]=t,e.pending_buf[e.sym_buf+e.sym_next++]=t>>8,e.pending_buf[e.sym_buf+e.sym_next++]=n,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(l[n]+256+1)]++,e.dyn_dtree[2*y(t)]++),e.sym_next===e.sym_end),_tr_align(e){var t;g(e,2,3),_(e,256,o),16===(t=e).bi_valid?(v(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},z=(e,t,n,r)=>{let i=65535&e|0,a=e>>>16&65535|0,o=0;for(;0!==n;){o=n>2e3?2e3:n,n-=o;do a=a+(i=i+t[r++]|0)|0;while(--o);i%=65521,a%=65521}return i|a<<16|0};let K=new Uint32Array((()=>{let e,t=[];for(var n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t})());var q=(e,t,n,r)=>{let i=K,a=r+n;e^=-1;for(let o=r;o<a;o++)e=e>>>8^i[255&(e^t[o])];return -1^e},B={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},F={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};let{_tr_init:N,_tr_stored_block:j,_tr_flush_block:M,_tr_tally:L,_tr_align:U}=T,{Z_NO_FLUSH:Z,Z_PARTIAL_FLUSH:V,Z_FULL_FLUSH:H,Z_FINISH:G,Z_BLOCK:W,Z_OK:Y,Z_STREAM_END:J,Z_STREAM_ERROR:X,Z_DATA_ERROR:Q,Z_BUF_ERROR:ee,Z_DEFAULT_COMPRESSION:et,Z_FILTERED:en,Z_HUFFMAN_ONLY:er,Z_RLE:ei,Z_FIXED:ea,Z_DEFAULT_STRATEGY:eo,Z_UNKNOWN:es,Z_DEFLATED:eu}=F,el=(e,t)=>(e.msg=B[t],t),ec=e=>2*e-(e>4?9:0),ef=e=>{let t=e.length;for(;--t>=0;)e[t]=0},ed=e=>{let t,n,r,i=e.w_size;r=t=e.hash_size;do n=e.head[--r],e.head[r]=n>=i?n-i:0;while(--t);r=t=i;do n=e.prev[--r],e.prev[r]=n>=i?n-i:0;while(--t)},eh=(e,t,n)=>(t<<e.hash_shift^n)&e.hash_mask,ep=e=>{let t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(e.output.set(t.pending_buf.subarray(t.pending_out,t.pending_out+n),e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))},e$=(e,t)=>{M(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,ep(e.strm)},em=(e,t)=>{e.pending_buf[e.pending++]=t},e8=(e,t)=>{e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t},ey=(e,t,n,r)=>{let i=e.avail_in;return i>r&&(i=r),0===i?0:(e.avail_in-=i,t.set(e.input.subarray(e.next_in,e.next_in+i),n),1===e.state.wrap?e.adler=z(e.adler,t,i,n):2===e.state.wrap&&(e.adler=q(e.adler,t,i,n)),e.next_in+=i,e.total_in+=i,i)},ev=(e,t)=>{let n,r,i=e.max_chain_length,a=e.strstart,o=e.prev_length,s=e.nice_match,u=e.strstart>e.w_size-262?e.strstart-(e.w_size-262):0,l=e.window,c=e.w_mask,f=e.prev,d=e.strstart+258,h=l[a+o-1],p=l[a+o];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do if(l[(n=t)+o]===p&&l[n+o-1]===h&&l[n]===l[a]&&l[++n]===l[a+1]){a+=2,n++;do;while(l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&a<d);if(r=258-(d-a),a=d-258,r>o){if(e.match_start=t,o=r,r>=s)break;h=l[a+o-1],p=l[a+o]}}while((t=f[t&c])>u&&0!=--i);return o<=e.lookahead?o:e.lookahead},eg=e=>{let t=e.w_size,n,r,i;do{if(r=e.window_size-e.lookahead-e.strstart,e.strstart>=t+(t-262)&&(e.window.set(e.window.subarray(t,t+t-r),0),e.match_start-=t,e.strstart-=t,e.block_start-=t,e.insert>e.strstart&&(e.insert=e.strstart),ed(e),r+=t),0===e.strm.avail_in)break;if(n=ey(e.strm,e.window,e.strstart+e.lookahead,r),e.lookahead+=n,e.lookahead+e.insert>=3)for(i=e.strstart-e.insert,e.ins_h=e.window[i],e.ins_h=eh(e,e.ins_h,e.window[i+1]);e.insert&&(e.ins_h=eh(e,e.ins_h,e.window[i+3-1]),e.prev[i&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=i,i++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<262&&0!==e.strm.avail_in)},e_=(e,t)=>{let n,r,i,a=e.pending_buf_size-5>e.w_size?e.w_size:e.pending_buf_size-5,o=0,s=e.strm.avail_in;do{if(n=65535,i=e.bi_valid+42>>3,e.strm.avail_out<i||(i=e.strm.avail_out-i,n>(r=e.strstart-e.block_start)+e.strm.avail_in&&(n=r+e.strm.avail_in),n>i&&(n=i),n<a&&(0===n&&t!==G||t===Z||n!==r+e.strm.avail_in)))break;o=t===G&&n===r+e.strm.avail_in?1:0,j(e,0,0,o),e.pending_buf[e.pending-4]=n,e.pending_buf[e.pending-3]=n>>8,e.pending_buf[e.pending-2]=~n,e.pending_buf[e.pending-1]=~n>>8,ep(e.strm),r&&(r>n&&(r=n),e.strm.output.set(e.window.subarray(e.block_start,e.block_start+r),e.strm.next_out),e.strm.next_out+=r,e.strm.avail_out-=r,e.strm.total_out+=r,e.block_start+=r,n-=r),n&&(ey(e.strm,e.strm.output,e.strm.next_out,n),e.strm.next_out+=n,e.strm.avail_out-=n,e.strm.total_out+=n)}while(0===o);return(s-=e.strm.avail_in)&&(s>=e.w_size?(e.matches=2,e.window.set(e.strm.input.subarray(e.strm.next_in-e.w_size,e.strm.next_in),0),e.strstart=e.w_size,e.insert=e.strstart):(e.window_size-e.strstart<=s&&(e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,e.insert>e.strstart&&(e.insert=e.strstart)),e.window.set(e.strm.input.subarray(e.strm.next_in-s,e.strm.next_in),e.strstart),e.strstart+=s,e.insert+=s>e.w_size-e.insert?e.w_size-e.insert:s),e.block_start=e.strstart),e.high_water<e.strstart&&(e.high_water=e.strstart),o?4:t!==Z&&t!==G&&0===e.strm.avail_in&&e.strstart===e.block_start?2:(i=e.window_size-e.strstart,e.strm.avail_in>i&&e.block_start>=e.w_size&&(e.block_start-=e.w_size,e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,i+=e.w_size,e.insert>e.strstart&&(e.insert=e.strstart)),i>e.strm.avail_in&&(i=e.strm.avail_in),i&&(ey(e.strm,e.window,e.strstart,i),e.strstart+=i,e.insert+=i>e.w_size-e.insert?e.w_size-e.insert:i),e.high_water<e.strstart&&(e.high_water=e.strstart),i=e.bi_valid+42>>3,a=(i=e.pending_buf_size-i>65535?65535:e.pending_buf_size-i)>e.w_size?e.w_size:i,((r=e.strstart-e.block_start)>=a||(r||t===G)&&t!==Z&&0===e.strm.avail_in&&r<=i)&&(n=r>i?i:r,o=t===G&&0===e.strm.avail_in&&n===r?1:0,j(e,e.block_start,n,o),e.block_start+=n,ep(e.strm)),o?3:1)},eb=(e,t)=>{let n,r;for(;;){if(e.lookahead<262){if(eg(e),e.lookahead<262&&t===Z)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=eh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-262&&(e.match_length=ev(e,n)),e.match_length>=3){if(r=L(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do e.strstart++,e.ins_h=eh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=eh(e,e.ins_h,e.window[e.strstart+1])}else r=L(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(e$(e,!1),0===e.strm.avail_out))return 1}return e.insert=e.strstart<2?e.strstart:2,t===G?(e$(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(e$(e,!1),0===e.strm.avail_out)?1:2},ew=(e,t)=>{let n,r,i;for(;;){if(e.lookahead<262){if(eg(e),e.lookahead<262&&t===Z)return 1;if(0===e.lookahead)break}if(n=0,e.lookahead>=3&&(e.ins_h=eh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-262&&(e.match_length=ev(e,n),e.match_length<=5&&(e.strategy===en||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-3,r=L(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=i&&(e.ins_h=eh(e,e.ins_h,e.window[e.strstart+3-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,r&&(e$(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((r=L(e,0,e.window[e.strstart-1]))&&e$(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=L(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,t===G?(e$(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(e$(e,!1),0===e.strm.avail_out)?1:2};function e0(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}let ek=[new e0(0,0,0,0,e_),new e0(4,4,8,4,eb),new e0(4,5,16,8,eb),new e0(4,6,32,32,eb),new e0(4,4,16,16,ew),new e0(8,16,32,32,ew),new e0(8,16,128,128,ew),new e0(8,32,128,256,ew),new e0(32,128,258,1024,ew),new e0(32,258,258,4096,ew)];function ex(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=eu,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(1146),this.dyn_dtree=new Uint16Array(122),this.bl_tree=new Uint16Array(78),ef(this.dyn_ltree),ef(this.dyn_dtree),ef(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(16),this.heap=new Uint16Array(573),ef(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(573),ef(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}let e3=e=>{if(!e)return 1;let t=e.state;return t&&t.strm===e&&(42===t.status||57===t.status||69===t.status||73===t.status||91===t.status||103===t.status||113===t.status||666===t.status)?0:1},e4=e=>{if(e3(e))return el(e,X);e.total_in=e.total_out=0,e.data_type=es;let t=e.state;return t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=2===t.wrap?57:t.wrap?42:113,e.adler=2===t.wrap?0:1,t.last_flush=-2,N(t),Y},eE=e=>{var t;let n=e4(e);return n===Y&&((t=e.state).window_size=2*t.w_size,ef(t.head),t.max_lazy_match=ek[t.level].max_lazy,t.good_match=ek[t.level].good_length,t.nice_match=ek[t.level].nice_length,t.max_chain_length=ek[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=2,t.match_available=0,t.ins_h=0),n},e1=(e,t,n,r,i,a)=>{if(!e)return X;let o=1;if(t===et&&(t=6),r<0?(o=0,r=-r):r>15&&(o=2,r-=16),i<1||i>9||n!==eu||r<8||r>15||t<0||t>9||a<0||a>ea||8===r&&1!==o)return el(e,X);8===r&&(r=9);let s=new ex;return e.state=s,s.strm=e,s.status=42,s.wrap=o,s.gzhead=null,s.w_bits=r,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+3-1)/3),s.window=new Uint8Array(2*s.w_size),s.head=new Uint16Array(s.hash_size),s.prev=new Uint16Array(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Uint8Array(s.pending_buf_size),s.sym_buf=s.lit_bufsize,s.sym_end=3*(s.lit_bufsize-1),s.level=t,s.strategy=a,s.method=n,eE(e)};var eA={deflateInit:(e,t)=>e1(e,t,eu,15,8,eo),deflateInit2:e1,deflateReset:eE,deflateResetKeep:e4,deflateSetHeader:(e,t)=>e3(e)||2!==e.state.wrap?X:(e.state.gzhead=t,Y),deflate(e,t){if(e3(e)||t>W||t<0)return e?el(e,X):X;let n=e.state;if(!e.output||0!==e.avail_in&&!e.input||666===n.status&&t!==G)return el(e,0===e.avail_out?ee:X);let r=n.last_flush;if(n.last_flush=t,0!==n.pending){if(ep(e),0===e.avail_out)return n.last_flush=-1,Y}else if(0===e.avail_in&&ec(t)<=ec(r)&&t!==G)return el(e,ee);if(666===n.status&&0!==e.avail_in)return el(e,ee);if(42===n.status&&0===n.wrap&&(n.status=113),42===n.status){let i=eu+(n.w_bits-8<<4)<<8,a=-1;if(i|=(a=n.strategy>=er||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(i|=32),e8(n,i+=31-i%31),0!==n.strstart&&(e8(n,e.adler>>>16),e8(n,65535&e.adler)),e.adler=1,n.status=113,ep(e),0!==n.pending)return n.last_flush=-1,Y}if(57===n.status){if(e.adler=0,em(n,31),em(n,139),em(n,8),n.gzhead)em(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),em(n,255&n.gzhead.time),em(n,n.gzhead.time>>8&255),em(n,n.gzhead.time>>16&255),em(n,n.gzhead.time>>24&255),em(n,9===n.level?2:n.strategy>=er||n.level<2?4:0),em(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(em(n,255&n.gzhead.extra.length),em(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(e.adler=q(e.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69;else if(em(n,0),em(n,0),em(n,0),em(n,0),em(n,0),em(n,9===n.level?2:n.strategy>=er||n.level<2?4:0),em(n,3),n.status=113,ep(e),0!==n.pending)return n.last_flush=-1,Y}if(69===n.status){if(n.gzhead.extra){let o=n.pending,s=(65535&n.gzhead.extra.length)-n.gzindex;for(;n.pending+s>n.pending_buf_size;){let u=n.pending_buf_size-n.pending;if(n.pending_buf.set(n.gzhead.extra.subarray(n.gzindex,n.gzindex+u),n.pending),n.pending=n.pending_buf_size,n.gzhead.hcrc&&n.pending>o&&(e.adler=q(e.adler,n.pending_buf,n.pending-o,o)),n.gzindex+=u,ep(e),0!==n.pending)return n.last_flush=-1,Y;o=0,s-=u}let l=new Uint8Array(n.gzhead.extra);n.pending_buf.set(l.subarray(n.gzindex,n.gzindex+s),n.pending),n.pending+=s,n.gzhead.hcrc&&n.pending>o&&(e.adler=q(e.adler,n.pending_buf,n.pending-o,o)),n.gzindex=0}n.status=73}if(73===n.status){if(n.gzhead.name){let c,f=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>f&&(e.adler=q(e.adler,n.pending_buf,n.pending-f,f)),ep(e),0!==n.pending)return n.last_flush=-1,Y;f=0}c=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,em(n,c)}while(0!==c);n.gzhead.hcrc&&n.pending>f&&(e.adler=q(e.adler,n.pending_buf,n.pending-f,f)),n.gzindex=0}n.status=91}if(91===n.status){if(n.gzhead.comment){let d,h=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>h&&(e.adler=q(e.adler,n.pending_buf,n.pending-h,h)),ep(e),0!==n.pending)return n.last_flush=-1,Y;h=0}d=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,em(n,d)}while(0!==d);n.gzhead.hcrc&&n.pending>h&&(e.adler=q(e.adler,n.pending_buf,n.pending-h,h))}n.status=103}if(103===n.status){if(n.gzhead.hcrc){if(n.pending+2>n.pending_buf_size&&(ep(e),0!==n.pending))return n.last_flush=-1,Y;em(n,255&e.adler),em(n,e.adler>>8&255),e.adler=0}if(n.status=113,ep(e),0!==n.pending)return n.last_flush=-1,Y}if(0!==e.avail_in||0!==n.lookahead||t!==Z&&666!==n.status){let p=0===n.level?e_(n,t):n.strategy===er?((e,t)=>{let n;for(;;){if(0===e.lookahead&&(eg(e),0===e.lookahead)){if(t===Z)return 1;break}if(e.match_length=0,n=L(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(e$(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===G?(e$(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(e$(e,!1),0===e.strm.avail_out)?1:2})(n,t):n.strategy===ei?((e,t)=>{let n,r,i,a,o=e.window;for(;;){if(e.lookahead<=258){if(eg(e),e.lookahead<=258&&t===Z)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(r=o[i=e.strstart-1])===o[++i]&&r===o[++i]&&r===o[++i]){a=e.strstart+258;do;while(r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&i<a);e.match_length=258-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(n=L(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=L(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(e$(e,!1),0===e.strm.avail_out))return 1}return e.insert=0,t===G?(e$(e,!0),0===e.strm.avail_out?3:4):e.sym_next&&(e$(e,!1),0===e.strm.avail_out)?1:2})(n,t):ek[n.level].func(n,t);if(3!==p&&4!==p||(n.status=666),1===p||3===p)return 0===e.avail_out&&(n.last_flush=-1),Y;if(2===p&&(t===V?U(n):t!==W&&(j(n,0,0,!1),t===H&&(ef(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),ep(e),0===e.avail_out))return n.last_flush=-1,Y}return t!==G?Y:n.wrap<=0?J:(2===n.wrap?(em(n,255&e.adler),em(n,e.adler>>8&255),em(n,e.adler>>16&255),em(n,e.adler>>24&255),em(n,255&e.total_in),em(n,e.total_in>>8&255),em(n,e.total_in>>16&255),em(n,e.total_in>>24&255)):(e8(n,e.adler>>>16),e8(n,65535&e.adler)),ep(e),n.wrap>0&&(n.wrap=-n.wrap),0!==n.pending?Y:J)},deflateEnd(e){if(e3(e))return X;let t=e.state.status;return e.state=null,113===t?el(e,Q):Y},deflateSetDictionary(e,t){let n=t.length;if(e3(e))return X;let r=e.state,i=r.wrap;if(2===i||1===i&&42!==r.status||r.lookahead)return X;if(1===i&&(e.adler=z(e.adler,t,n,0)),r.wrap=0,n>=r.w_size){0===i&&(ef(r.head),r.strstart=0,r.block_start=0,r.insert=0);let a=new Uint8Array(r.w_size);a.set(t.subarray(n-r.w_size,n),0),t=a,n=r.w_size}let o=e.avail_in,s=e.next_in,u=e.input;for(e.avail_in=n,e.next_in=0,e.input=t,eg(r);r.lookahead>=3;){let l=r.strstart,c=r.lookahead-2;do r.ins_h=eh(r,r.ins_h,r.window[l+3-1]),r.prev[l&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=l,l++;while(--c);r.strstart=l,r.lookahead=2,eg(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,e.next_in=s,e.input=u,e.avail_in=o,r.wrap=i,Y},deflateInfo:"pako deflate (from Nodeca project)"};let eI=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var eS=function(e){let t=Array.prototype.slice.call(arguments,1);for(;t.length;){let n=t.shift();if(n){if("object"!=typeof n)throw TypeError(n+"must be non-object");for(let r in n)eI(n,r)&&(e[r]=n[r])}}return e},eD=e=>{let t=0;for(let n=0,r=e.length;n<r;n++)t+=e[n].length;let i=new Uint8Array(t);for(let a=0,o=0,s=e.length;a<s;a++){let u=e[a];i.set(u,o),o+=u.length}return i};let eP=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e6){eP=!1}let eO=new Uint8Array(256);for(let eC=0;eC<256;eC++)eO[eC]=eC>=252?6:eC>=248?5:eC>=240?4:eC>=224?3:eC>=192?2:1;eO[254]=eO[254]=1;var eR=e=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(e);let t,n,r,i,a,o=e.length,s=0;for(i=0;i<o;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),s+=n<128?1:n<2048?2:n<65536?3:4;for(t=new Uint8Array(s),a=0,i=0;a<s;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),n<128?t[a++]=n:n<2048?(t[a++]=192|n>>>6,t[a++]=128|63&n):n<65536?(t[a++]=224|n>>>12,t[a++]=128|n>>>6&63,t[a++]=128|63&n):(t[a++]=240|n>>>18,t[a++]=128|n>>>12&63,t[a++]=128|n>>>6&63,t[a++]=128|63&n);return t},eT=(e,t)=>{let n=t||e.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(e.subarray(0,t));let r,i,a=Array(2*n);for(i=0,r=0;r<n;){let o=e[r++];if(o<128){a[i++]=o;continue}let s=eO[o];if(s>4)a[i++]=65533,r+=s-1;else{for(o&=2===s?31:3===s?15:7;s>1&&r<n;)o=o<<6|63&e[r++],s--;s>1?a[i++]=65533:o<65536?a[i++]=o:(o-=65536,a[i++]=55296|o>>10&1023,a[i++]=56320|1023&o)}}return((e,t)=>{if(t<65534&&e.subarray&&eP)return String.fromCharCode.apply(null,e.length===t?e:e.subarray(0,t));let n="";for(let r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n})(a,i)},ez=(e,t)=>{(t=t||e.length)>e.length&&(t=e.length);let n=t-1;for(;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+eO[e[n]]>t?n:t},e2=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};let eK=Object.prototype.toString,{Z_NO_FLUSH:eq,Z_SYNC_FLUSH:e7,Z_FULL_FLUSH:eB,Z_FINISH:eF,Z_OK:eN,Z_STREAM_END:ej,Z_DEFAULT_COMPRESSION:eM,Z_DEFAULT_STRATEGY:eL,Z_DEFLATED:e5}=F;function eU(e){this.options=eS({level:eM,method:e5,chunkSize:16384,windowBits:15,memLevel:8,strategy:eL},e||{});let t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new e2,this.strm.avail_out=0;let n=eA.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==eN)throw Error(B[n]);if(t.header&&eA.deflateSetHeader(this.strm,t.header),t.dictionary){let r;if(r="string"==typeof t.dictionary?eR(t.dictionary):"[object ArrayBuffer]"===eK.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(n=eA.deflateSetDictionary(this.strm,r))!==eN)throw Error(B[n]);this._dict_set=!0}}function eZ(e,t){let n=new eU(t);if(n.push(e,!0),n.err)throw n.msg||B[n.err];return n.result}eU.prototype.push=function(e,t){let n=this.strm,r=this.options.chunkSize,i,a;if(this.ended)return!1;for(a=t===~~t?t:!0===t?eF:eq,"string"==typeof e?n.input=eR(e):"[object ArrayBuffer]"===eK.call(e)?n.input=new Uint8Array(e):n.input=e,n.next_in=0,n.avail_in=n.input.length;;)if(0===n.avail_out&&(n.output=new Uint8Array(r),n.next_out=0,n.avail_out=r),(a===e7||a===eB)&&n.avail_out<=6)this.onData(n.output.subarray(0,n.next_out)),n.avail_out=0;else{if((i=eA.deflate(n,a))===ej)return n.next_out>0&&this.onData(n.output.subarray(0,n.next_out)),i=eA.deflateEnd(this.strm),this.onEnd(i),this.ended=!0,i===eN;if(0!==n.avail_out){if(a>0&&n.next_out>0)this.onData(n.output.subarray(0,n.next_out)),n.avail_out=0;else if(0===n.avail_in)break}else this.onData(n.output)}return!0},eU.prototype.onData=function(e){this.chunks.push(e)},eU.prototype.onEnd=function(e){e===eN&&(this.result=eD(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var eV=function(e,t){let n,r,i,a,o,s,u,l,c,f,d,h,p,$,m,y,v,g,_,b,w,k,x,E,A=e.state;n=e.next_in,x=e.input,r=n+(e.avail_in-5),i=e.next_out,E=e.output,a=i-(t-e.avail_out),o=i+(e.avail_out-257),s=A.dmax,u=A.wsize,l=A.whave,c=A.wnext,f=A.window,d=A.hold,h=A.bits,p=A.lencode,$=A.distcode,m=(1<<A.lenbits)-1,y=(1<<A.distbits)-1;t:do{h<15&&(d+=x[n++]<<h,h+=8,d+=x[n++]<<h,h+=8),v=p[d&m];e:for(;;){if(d>>>=g=v>>>24,h-=g,0==(g=v>>>16&255))E[i++]=65535&v;else{if(!(16&g)){if(0==(64&g)){v=p[(65535&v)+(d&(1<<g)-1)];continue e}if(32&g){A.mode=16191;break t}e.msg="invalid literal/length code",A.mode=16209;break t}_=65535&v,(g&=15)&&(h<g&&(d+=x[n++]<<h,h+=8),_+=d&(1<<g)-1,d>>>=g,h-=g),h<15&&(d+=x[n++]<<h,h+=8,d+=x[n++]<<h,h+=8),v=$[d&y];a:for(;;){if(d>>>=g=v>>>24,h-=g,!(16&(g=v>>>16&255))){if(0==(64&g)){v=$[(65535&v)+(d&(1<<g)-1)];continue a}e.msg="invalid distance code",A.mode=16209;break t}if(b=65535&v,h<(g&=15)&&(d+=x[n++]<<h,(h+=8)<g&&(d+=x[n++]<<h,h+=8)),(b+=d&(1<<g)-1)>s){e.msg="invalid distance too far back",A.mode=16209;break t}if(d>>>=g,h-=g,b>(g=i-a)){if((g=b-g)>l&&A.sane){e.msg="invalid distance too far back",A.mode=16209;break t}if(w=0,k=f,0===c){if(w+=u-g,g<_){_-=g;do E[i++]=f[w++];while(--g);w=i-b,k=E}}else if(c<g){if(w+=u+c-g,(g-=c)<_){_-=g;do E[i++]=f[w++];while(--g);if(w=0,c<_){_-=g=c;do E[i++]=f[w++];while(--g);w=i-b,k=E}}}else if(w+=c-g,g<_){_-=g;do E[i++]=f[w++];while(--g);w=i-b,k=E}for(;_>2;)E[i++]=k[w++],E[i++]=k[w++],E[i++]=k[w++],_-=3;_&&(E[i++]=k[w++],_>1&&(E[i++]=k[w++]))}else{w=i-b;do E[i++]=E[w++],E[i++]=E[w++],E[i++]=E[w++],_-=3;while(_>2);_&&(E[i++]=E[w++],_>1&&(E[i++]=E[w++]))}break}}break}}while(n<r&&i<o);n-=_=h>>3,h-=_<<3,d&=(1<<h)-1,e.next_in=n,e.next_out=i,e.avail_in=n<r?r-n+5:5-(n-r),e.avail_out=i<o?o-i+257:257-(i-o),A.hold=d,A.bits=h};let eH=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),eG=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),eW=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),eY=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);var e9=(e,t,n,r,i,a,o,s)=>{let u=s.bits,l,c,f,d,h,p,$=0,m=0,y=0,v=0,g=0,_=0,b=0,w=0,k=0,x=0,E=null,A=new Uint16Array(16),I=new Uint16Array(16),S,D,P,O=null;for($=0;$<=15;$++)A[$]=0;for(m=0;m<r;m++)A[t[n+m]]++;for(g=u,v=15;v>=1&&0===A[v];v--);if(g>v&&(g=v),0===v)return i[a++]=20971520,i[a++]=20971520,s.bits=1,0;for(y=1;y<v&&0===A[y];y++);for(g<y&&(g=y),w=1,$=1;$<=15;$++)if(w<<=1,(w-=A[$])<0)return -1;if(w>0&&(0===e||1!==v))return -1;for(I[1]=0,$=1;$<15;$++)I[$+1]=I[$]+A[$];for(m=0;m<r;m++)0!==t[n+m]&&(o[I[t[n+m]]++]=m);if(0===e?(E=O=o,p=20):1===e?(E=eH,O=eG,p=257):(E=eW,O=eY,p=0),x=0,m=0,$=y,h=a,_=g,b=0,f=-1,d=(k=1<<g)-1,1===e&&k>852||2===e&&k>592)return 1;for(;;){S=$-b,o[m]+1<p?(D=0,P=o[m]):o[m]>=p?(D=O[o[m]-p],P=E[o[m]-p]):(D=96,P=0),l=1<<$-b,y=c=1<<_;do i[h+(x>>b)+(c-=l)]=S<<24|D<<16|P|0;while(0!==c);for(l=1<<$-1;x&l;)l>>=1;if(0!==l?(x&=l-1,x+=l):x=0,m++,0==--A[$]){if($===v)break;$=t[n+o[m]]}if($>g&&(x&d)!==f){for(0===b&&(b=g),h+=y,w=1<<(_=$-b);_+b<v&&!((w-=A[_+b])<=0);)_++,w<<=1;if(k+=1<<_,1===e&&k>852||2===e&&k>592)return 1;i[f=x&d]=g<<24|_<<16|h-a|0}}return 0!==x&&(i[h+x]=$-b<<24|4194304),s.bits=g,0};let{Z_FINISH:eJ,Z_BLOCK:eX,Z_TREES:eQ,Z_OK:te,Z_STREAM_END:tt,Z_NEED_DICT:tn,Z_STREAM_ERROR:tr,Z_DATA_ERROR:ti,Z_MEM_ERROR:ta,Z_BUF_ERROR:to,Z_DEFLATED:ts}=F,tu=e=>(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24);function tl(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}let tc=e=>{if(!e)return 1;let t=e.state;return!t||t.strm!==e||t.mode<16180||t.mode>16211?1:0},tf=e=>{if(tc(e))return tr;let t=e.state;return e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=16180,t.last=0,t.havedict=0,t.flags=-1,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(852),t.distcode=t.distdyn=new Int32Array(592),t.sane=1,t.back=-1,te},td=e=>{if(tc(e))return tr;let t=e.state;return t.wsize=0,t.whave=0,t.wnext=0,tf(e)},th=(e,t)=>{let n;if(tc(e))return tr;let r=e.state;return t<0?(n=0,t=-t):(n=5+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?tr:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,td(e))},tp=(e,t)=>{if(!e)return tr;let n=new tl;e.state=n,n.strm=e,n.window=null,n.mode=16180;let r=th(e,t);return r!==te&&(e.state=null),r},t$,tm,t8=!0,ty=e=>{if(t8){t$=new Int32Array(512),tm=new Int32Array(32);let t=0;for(;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(e9(1,e.lens,0,288,t$,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;e9(2,e.lens,0,32,tm,0,e.work,{bits:5}),t8=!1}e.lencode=t$,e.lenbits=9,e.distcode=tm,e.distbits=5},tv=(e,t,n,r)=>{let i,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new Uint8Array(a.wsize)),r>=a.wsize?(a.window.set(t.subarray(n-a.wsize,n),0),a.wnext=0,a.whave=a.wsize):((i=a.wsize-a.wnext)>r&&(i=r),a.window.set(t.subarray(n-r,n-r+i),a.wnext),(r-=i)?(a.window.set(t.subarray(n-r,n),0),a.wnext=r,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0};var tg={inflateReset:td,inflateReset2:th,inflateResetKeep:tf,inflateInit:e=>tp(e,15),inflateInit2:tp,inflate(e,t){let n,r,i,a,o,s,u,l,c,f,d,h,p,$,m,y,v,g,_,b,w,k,x=0,E=new Uint8Array(4),A,I,S=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(tc(e)||!e.output||!e.input&&0!==e.avail_in)return tr;16191===(n=e.state).mode&&(n.mode=16192),o=e.next_out,i=e.output,u=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,l=n.hold,c=n.bits,f=s,d=u,k=te;t:for(;;)switch(n.mode){case 16180:if(0===n.wrap){n.mode=16192;break}for(;c<16;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(2&n.wrap&&35615===l){0===n.wbits&&(n.wbits=15),n.check=0,E[0]=255&l,E[1]=l>>>8&255,n.check=q(n.check,E,2,0),l=0,c=0,n.mode=16181;break}if(n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&l)<<8)+(l>>8))%31){e.msg="incorrect header check",n.mode=16209;break}if((15&l)!==ts){e.msg="unknown compression method",n.mode=16209;break}if(l>>>=4,c-=4,w=8+(15&l),0===n.wbits&&(n.wbits=w),w>15||w>n.wbits){e.msg="invalid window size",n.mode=16209;break}n.dmax=1<<n.wbits,n.flags=0,e.adler=n.check=1,n.mode=512&l?16189:16191,l=0,c=0;break;case 16181:for(;c<16;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(n.flags=l,(255&n.flags)!==ts){e.msg="unknown compression method",n.mode=16209;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=16209;break}n.head&&(n.head.text=l>>8&1),512&n.flags&&4&n.wrap&&(E[0]=255&l,E[1]=l>>>8&255,n.check=q(n.check,E,2,0)),l=0,c=0,n.mode=16182;case 16182:for(;c<32;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}n.head&&(n.head.time=l),512&n.flags&&4&n.wrap&&(E[0]=255&l,E[1]=l>>>8&255,E[2]=l>>>16&255,E[3]=l>>>24&255,n.check=q(n.check,E,4,0)),l=0,c=0,n.mode=16183;case 16183:for(;c<16;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}n.head&&(n.head.xflags=255&l,n.head.os=l>>8),512&n.flags&&4&n.wrap&&(E[0]=255&l,E[1]=l>>>8&255,n.check=q(n.check,E,2,0)),l=0,c=0,n.mode=16184;case 16184:if(1024&n.flags){for(;c<16;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}n.length=l,n.head&&(n.head.extra_len=l),512&n.flags&&4&n.wrap&&(E[0]=255&l,E[1]=l>>>8&255,n.check=q(n.check,E,2,0)),l=0,c=0}else n.head&&(n.head.extra=null);n.mode=16185;case 16185:if(1024&n.flags&&((h=n.length)>s&&(h=s),h&&(n.head&&(w=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Uint8Array(n.head.extra_len)),n.head.extra.set(r.subarray(a,a+h),w)),512&n.flags&&4&n.wrap&&(n.check=q(n.check,r,h,a)),s-=h,a+=h,n.length-=h),n.length))break t;n.length=0,n.mode=16186;case 16186:if(2048&n.flags){if(0===s)break t;h=0;do w=r[a+h++],n.head&&w&&n.length<65536&&(n.head.name+=String.fromCharCode(w));while(w&&h<s);if(512&n.flags&&4&n.wrap&&(n.check=q(n.check,r,h,a)),s-=h,a+=h,w)break t}else n.head&&(n.head.name=null);n.length=0,n.mode=16187;case 16187:if(4096&n.flags){if(0===s)break t;h=0;do w=r[a+h++],n.head&&w&&n.length<65536&&(n.head.comment+=String.fromCharCode(w));while(w&&h<s);if(512&n.flags&&4&n.wrap&&(n.check=q(n.check,r,h,a)),s-=h,a+=h,w)break t}else n.head&&(n.head.comment=null);n.mode=16188;case 16188:if(512&n.flags){for(;c<16;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(4&n.wrap&&l!==(65535&n.check)){e.msg="header crc mismatch",n.mode=16209;break}l=0,c=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=16191;break;case 16189:for(;c<32;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}e.adler=n.check=tu(l),l=0,c=0,n.mode=16190;case 16190:if(0===n.havedict)return e.next_out=o,e.avail_out=u,e.next_in=a,e.avail_in=s,n.hold=l,n.bits=c,tn;e.adler=n.check=1,n.mode=16191;case 16191:if(t===eX||t===eQ)break t;case 16192:if(n.last){l>>>=7&c,c-=7&c,n.mode=16206;break}for(;c<3;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}switch(n.last=1&l,c-=1,3&(l>>>=1)){case 0:n.mode=16193;break;case 1:if(ty(n),n.mode=16199,t===eQ){l>>>=2,c-=2;break t}break;case 2:n.mode=16196;break;case 3:e.msg="invalid block type",n.mode=16209}l>>>=2,c-=2;break;case 16193:for(l>>>=7&c,c-=7&c;c<32;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if((65535&l)!=(l>>>16^65535)){e.msg="invalid stored block lengths",n.mode=16209;break}if(n.length=65535&l,l=0,c=0,n.mode=16194,t===eQ)break t;case 16194:n.mode=16195;case 16195:if(h=n.length){if(h>s&&(h=s),h>u&&(h=u),0===h)break t;i.set(r.subarray(a,a+h),o),s-=h,a+=h,u-=h,o+=h,n.length-=h;break}n.mode=16191;break;case 16196:for(;c<14;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(n.nlen=257+(31&l),l>>>=5,c-=5,n.ndist=1+(31&l),l>>>=5,c-=5,n.ncode=4+(15&l),l>>>=4,c-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=16209;break}n.have=0,n.mode=16197;case 16197:for(;n.have<n.ncode;){for(;c<3;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}n.lens[S[n.have++]]=7&l,l>>>=3,c-=3}for(;n.have<19;)n.lens[S[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,A={bits:n.lenbits},k=e9(0,n.lens,0,19,n.lencode,0,n.work,A),n.lenbits=A.bits,k){e.msg="invalid code lengths set",n.mode=16209;break}n.have=0,n.mode=16198;case 16198:for(;n.have<n.nlen+n.ndist;){for(;m=(x=n.lencode[l&(1<<n.lenbits)-1])>>>24,y=x>>>16&255,v=65535&x,!(m<=c);){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(v<16)l>>>=m,c-=m,n.lens[n.have++]=v;else{if(16===v){for(I=m+2;c<I;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(l>>>=m,c-=m,0===n.have){e.msg="invalid bit length repeat",n.mode=16209;break}w=n.lens[n.have-1],h=3+(3&l),l>>>=2,c-=2}else if(17===v){for(I=m+3;c<I;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}l>>>=m,c-=m,w=0,h=3+(7&l),l>>>=3,c-=3}else{for(I=m+7;c<I;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}l>>>=m,c-=m,w=0,h=11+(127&l),l>>>=7,c-=7}if(n.have+h>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=16209;break}for(;h--;)n.lens[n.have++]=w}}if(16209===n.mode)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=16209;break}if(n.lenbits=9,A={bits:n.lenbits},k=e9(1,n.lens,0,n.nlen,n.lencode,0,n.work,A),n.lenbits=A.bits,k){e.msg="invalid literal/lengths set",n.mode=16209;break}if(n.distbits=6,n.distcode=n.distdyn,A={bits:n.distbits},k=e9(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,A),n.distbits=A.bits,k){e.msg="invalid distances set",n.mode=16209;break}if(n.mode=16199,t===eQ)break t;case 16199:n.mode=16200;case 16200:if(s>=6&&u>=258){e.next_out=o,e.avail_out=u,e.next_in=a,e.avail_in=s,n.hold=l,n.bits=c,eV(e,d),o=e.next_out,i=e.output,u=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,l=n.hold,c=n.bits,16191===n.mode&&(n.back=-1);break}for(n.back=0;m=(x=n.lencode[l&(1<<n.lenbits)-1])>>>24,y=x>>>16&255,v=65535&x,!(m<=c);){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(y&&0==(240&y)){for(g=m,_=y,b=v;m=(x=n.lencode[b+((l&(1<<g+_)-1)>>g)])>>>24,y=x>>>16&255,v=65535&x,!(g+m<=c);){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}l>>>=g,c-=g,n.back+=g}if(l>>>=m,c-=m,n.back+=m,n.length=v,0===y){n.mode=16205;break}if(32&y){n.back=-1,n.mode=16191;break}if(64&y){e.msg="invalid literal/length code",n.mode=16209;break}n.extra=15&y,n.mode=16201;case 16201:if(n.extra){for(I=n.extra;c<I;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}n.length+=l&(1<<n.extra)-1,l>>>=n.extra,c-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=16202;case 16202:for(;m=(x=n.distcode[l&(1<<n.distbits)-1])>>>24,y=x>>>16&255,v=65535&x,!(m<=c);){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(0==(240&y)){for(g=m,_=y,b=v;m=(x=n.distcode[b+((l&(1<<g+_)-1)>>g)])>>>24,y=x>>>16&255,v=65535&x,!(g+m<=c);){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}l>>>=g,c-=g,n.back+=g}if(l>>>=m,c-=m,n.back+=m,64&y){e.msg="invalid distance code",n.mode=16209;break}n.offset=v,n.extra=15&y,n.mode=16203;case 16203:if(n.extra){for(I=n.extra;c<I;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}n.offset+=l&(1<<n.extra)-1,l>>>=n.extra,c-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=16209;break}n.mode=16204;case 16204:if(0===u)break t;if(h=d-u,n.offset>h){if((h=n.offset-h)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=16209;break}h>n.wnext?(h-=n.wnext,p=n.wsize-h):p=n.wnext-h,h>n.length&&(h=n.length),$=n.window}else $=i,p=o-n.offset,h=n.length;h>u&&(h=u),u-=h,n.length-=h;do i[o++]=$[p++];while(--h);0===n.length&&(n.mode=16200);break;case 16205:if(0===u)break t;i[o++]=n.length,u--,n.mode=16200;break;case 16206:if(n.wrap){for(;c<32;){if(0===s)break t;s--,l|=r[a++]<<c,c+=8}if(d-=u,e.total_out+=d,n.total+=d,4&n.wrap&&d&&(e.adler=n.check=n.flags?q(n.check,i,d,o-d):z(n.check,i,d,o-d)),d=u,4&n.wrap&&(n.flags?l:tu(l))!==n.check){e.msg="incorrect data check",n.mode=16209;break}l=0,c=0}n.mode=16207;case 16207:if(n.wrap&&n.flags){for(;c<32;){if(0===s)break t;s--,l+=r[a++]<<c,c+=8}if(4&n.wrap&&l!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=16209;break}l=0,c=0}n.mode=16208;case 16208:k=tt;break t;case 16209:k=ti;break t;case 16210:return ta;default:return tr}return e.next_out=o,e.avail_out=u,e.next_in=a,e.avail_in=s,n.hold=l,n.bits=c,(n.wsize||d!==e.avail_out&&n.mode<16209&&(n.mode<16206||t!==eJ))&&tv(e,e.output,e.next_out,d-e.avail_out),f-=e.avail_in,d-=e.avail_out,e.total_in+=f,e.total_out+=d,n.total+=d,4&n.wrap&&d&&(e.adler=n.check=n.flags?q(n.check,i,d,e.next_out-d):z(n.check,i,d,e.next_out-d)),e.data_type=n.bits+(n.last?64:0)+(16191===n.mode?128:0)+(16199===n.mode||16194===n.mode?256:0),(0===f&&0===d||t===eJ)&&k===te&&(k=to),k},inflateEnd(e){if(tc(e))return tr;let t=e.state;return t.window&&(t.window=null),e.state=null,te},inflateGetHeader(e,t){if(tc(e))return tr;let n=e.state;return 0==(2&n.wrap)?tr:(n.head=t,t.done=!1,te)},inflateSetDictionary(e,t){let n=t.length,r,i,a;return tc(e)?tr:0!==(r=e.state).wrap&&16190!==r.mode?tr:16190===r.mode&&(i=z(i=1,t,n,0))!==r.check?ti:(a=tv(e,t,n,n))?(r.mode=16210,ta):(r.havedict=1,te)},inflateInfo:"pako inflate (from Nodeca project)"},t_=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1};let tb=Object.prototype.toString,{Z_NO_FLUSH:tw,Z_FINISH:t0,Z_OK:tk,Z_STREAM_END:tx,Z_NEED_DICT:t3,Z_STREAM_ERROR:t4,Z_DATA_ERROR:tE,Z_MEM_ERROR:t1}=F;function tA(e){this.options=eS({chunkSize:65536,windowBits:15,to:""},e||{});let t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new e2,this.strm.avail_out=0;let n=tg.inflateInit2(this.strm,t.windowBits);if(n!==tk||(this.header=new t_,tg.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=eR(t.dictionary):"[object ArrayBuffer]"===tb.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(n=tg.inflateSetDictionary(this.strm,t.dictionary))!==tk)))throw Error(B[n])}function tI(e,t){let n=new tA(t);if(n.push(e),n.err)throw n.msg||B[n.err];return n.result}tA.prototype.push=function(e,t){let n=this.strm,r=this.options.chunkSize,i=this.options.dictionary,a,o,s;if(this.ended)return!1;for(o=t===~~t?t:!0===t?t0:tw,"[object ArrayBuffer]"===tb.call(e)?n.input=new Uint8Array(e):n.input=e,n.next_in=0,n.avail_in=n.input.length;;){for(0===n.avail_out&&(n.output=new Uint8Array(r),n.next_out=0,n.avail_out=r),(a=tg.inflate(n,o))===t3&&i&&((a=tg.inflateSetDictionary(n,i))===tk?a=tg.inflate(n,o):a===tE&&(a=t3));n.avail_in>0&&a===tx&&n.state.wrap>0&&0!==e[n.next_in];)tg.inflateReset(n),a=tg.inflate(n,o);switch(a){case t4:case tE:case t3:case t1:return this.onEnd(a),this.ended=!0,!1}if(s=n.avail_out,n.next_out&&(0===n.avail_out||a===tx)){if("string"===this.options.to){let u=ez(n.output,n.next_out),l=n.next_out-u,c=eT(n.output,u);n.next_out=l,n.avail_out=r-l,l&&n.output.set(n.output.subarray(u,u+l),0),this.onData(c)}else this.onData(n.output.length===n.next_out?n.output:n.output.subarray(0,n.next_out))}if(a!==tk||0!==s){if(a===tx)return a=tg.inflateEnd(this.strm),this.onEnd(a),this.ended=!0,!0;if(0===n.avail_in)break}}return!0},tA.prototype.onData=function(e){this.chunks.push(e)},tA.prototype.onEnd=function(e){e===tk&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=eD(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};let{Deflate:tS,deflate:tD,deflateRaw:tP,gzip:t6}={Deflate:eU,deflate:eZ,deflateRaw:function(e,t){return(t=t||{}).raw=!0,eZ(e,t)},gzip:function(e,t){return(t=t||{}).gzip=!0,eZ(e,t)},constants:F},{Inflate:tO,inflate:tC,inflateRaw:tR,ungzip:tT}={Inflate:tA,inflate:tI,inflateRaw:function(e,t){return(t=t||{}).raw=!0,tI(e,t)},ungzip:tI,constants:F};var tz=tS,t2=tD,tK=tP,tq=t6,t7=tO,tB=tC,tF=tR,tN=tT,tj=F;e.Deflate=tz,e.Inflate=t7,e.constants=tj,e.default={Deflate:tz,deflate:t2,deflateRaw:tK,gzip:tq,Inflate:t7,inflate:tB,inflateRaw:tF,ungzip:tN,constants:tj},e.deflate=t2,e.deflateRaw=tK,e.gzip=tq,e.inflate=tB,e.inflateRaw=tF,e.ungzip=tN,Object.defineProperty(e,"__esModule",{value:!0})},"object"==typeof exports&&"undefined"!=typeof module?a(exports):"function"==typeof define&&define.amd?define(["exports"],a):a((i="undefined"!=typeof globalThis?globalThis:i||self).pako={});class t{static async setItem(e,t){return new Promise((n,r)=>{let i={};i[e]=t,chrome.storage.local.set(i,function(){chrome.runtime.lastError?r(chrome.runtime.lastError):n()})})}static async getItem(e){return new Promise((t,n)=>{chrome.storage.local.get([e],function(r){chrome.runtime.lastError?n(chrome.runtime.lastError):t(r[e])})})}static async getSubItem(e,n,r){try{return((await t.getItem(e)||{})[n]||{})[r]}catch(i){console.error(i)}}static async removeItem(e){return new Promise((t,n)=>{chrome.storage.local.remove([e],function(){chrome.runtime.lastError?n(chrome.runtime.lastError):t()})})}static async removeSubItem(e,t,n){try{let r=await this.getItem(e)||{},i=r[t]||{};delete i[n],r[t]=i,await this.setItem(e,r)}catch(a){console.error(a)}}static async updateItem(e,t){try{let n=await this.getItem(e)||{},r={...n,...t};await this.setItem(e,r)}catch(i){console.error(i)}}static async updateSubItem(e,t,n,r){try{let i=await this.getItem(e)||{},a=i[t]||{};a[n]=r,i[t]=a,await this.setItem(e,i)}catch(o){console.error(o)}}static async getAllItems(){return new Promise((e,t)=>{chrome.storage.local.get(null,function(n){chrome.runtime.lastError?t(chrome.runtime.lastError):e(n)})})}static async clearAllItems(){return new Promise((e,t)=>{chrome.storage.local.clear(function(){chrome.runtime.lastError?t(chrome.runtime.lastError):e()})})}}var n,r,i,a,o=new Dexie("AddonsDatabase");async function s(e,t){let n=new Map(e.map(e=>[e.uuid,e])),r=await t.toArray(),i=[];for(let a of r){let s=n.get(a.uuid);s?((null==a.updateTime||s.updateTime>a.updateTime)&&await t.put(s),n.delete(a.uuid)):(await t.delete(a.uuid),i.push(a.uuid))}for(let u of n.values())await t.add(u);if(t===o.userScriptData)try{i.length>0&&await o.scriptLocalData.bulkDelete(i)}catch(l){console.error("删除脚本缓存数据失败: "+l)}}function u(e){if(null==e||0==e.length)return"";let t=atob(e),n=t.split("").map(e=>e.charCodeAt(0)),r=new Uint8Array(n),i=pako.inflate(r,{to:"string"});return i}async function l(n){!1==e(n.js_installHelper)&&t.setItem("js_install",n.js_installHelper),!1==e(n.js_tagit)&&t.setItem("js_tagit",n.js_tagit),e(n.config)&&(n.config=[]),t.setItem("addonsConfig",n.config);let r=[];for(let i of n.userScripts){let a={uuid:i.uuid,isActive:i.isActive,executorJs:i.executorJs,noframes:i.noframes,runAt:i.runAt,injectMode:i.injectMode,resourceJson:i.resourceJson?i.resourceJson:null,includes:i.includes?i.includes:[],matches:i.matches?i.matches:[],excludes:i.excludes?i.excludes:[],iconUrl:i.iconUrl?i.iconUrl:null,name:i.name,desc:i.desc,version:i.version};r.push(a)}try{await s(r,o.userScriptData)}catch(u){console.error("Failed to add batch to userScriptData:",u)}let l=[];for(let c of n.tagits){let f={uuid:c.uuid,isActive:c.isActive,host:c.host,xpath:c.xpath};l.push(f)}try{await s(l,o.tagitData)}catch(d){console.error("Failed to add batch to tagitData:",d)}let h=[];for(let p of n.aiSearchModels){let $={uuid:p.uuid,isActive:p.isActive,url:p.url,sql_rule_keys:p.sql_rule_keys};h.push($)}try{await s(h,o.searchData)}catch(m){console.error("Failed to add batch to searchData:",m)}}async function c(e,t){try{let n=e.scriptId,r=await o.scriptLocalData.where("uuid").equals(n).toArray(),i={};r.forEach(e=>{i[e.key]=e.value}),t({body:i})}catch(a){console.error(a)}}async function f(e,t){let n=e.uuid,r=e.key,i=e.value;try{let a=await o.scriptLocalData.where("uuid").equals(n).and(e=>e.key===r).first();a?(await o.scriptLocalData.where("uuid").equals(n).and(e=>e.key===r).modify({value:i}),console.log(`Updated data: UUID '${n}', Key '${r}', New value '${i}'`)):(await o.scriptLocalData.add({uuid:n,key:r,value:i}),console.log(`Inserted data: UUID '${n}', Key '${r}', Value '${i}'`));let s=i;null!=a&&(s=a.value),t(s!==i?{isValueChanged:!0,uuid:n,operate:"API_VALUE_CHANGE",key:r,old_value:s,new_value:i,remote:!1}:{isValueChanged:!1})}catch(u){console.error(u)}}async function d(e,t){let n=e.uuid,r=e.key;try{let i=await o.scriptLocalData.where("uuid").equals(n).and(e=>e.key===r).first();t(i)}catch(a){console.error(a)}}async function h(e,t){let n=e.uuid,r=e.key;try{let i=await o.scriptLocalData.where("uuid").equals(n).and(e=>e.key===r).first();await o.scriptLocalData.where("uuid").equals(n).and(e=>e.key===r).delete(),t(void 0!==i?{isValueChanged:!0,uuid:n,operate:"API_VALUE_CHANGE",key:r,old_value:i,new_value:void 0,remote:!1}:{isValueChanged:!1})}catch(a){console.error(a)}}async function p(e){return new Promise(t=>{let n=new FileReader;n.readAsDataURL(e),n.onloadend=()=>t(n.result)})}o.version(1).stores({userScriptData:"&uuid, isActive, executorJs, noframes, runAt, injectMode, resourceJson, includes, matches, excludes, iconUrl, name, desc, version, updateTime",tagitData:"&uuid, isActive, host, xpath, updateTime",searchData:"&uuid, isActive, url, sql_rule_keys, updateTime",scriptLocalData:"++id, uuid, key, value"}),o.open().catch(e=>{console.error("Error opening database: ",e)}),browser.runtime.onMessage.addListener((n,r,i)=>{switch(n.operate){case"API_ReloadData":{let a=n.status,s={type:n.operate,status:n.status};return browser.runtime.sendNativeMessage("application.id",s,function(e){if("0"==a){let n=e.body;try{(function e(t){for(let n of t.userScripts)try{null==n.executorJs?n.executorJs="":n.executorJs=u(n.executorJs),null==n.resourceJson?n.resourceJson=null:n.resourceJson=u(n.resourceJson),null==n.iconUrl?n.iconUrl=void 0:n.iconUrl=u(n.iconUrl)}catch(r){console.log("error = "+r)}})(n),l(n)}catch(r){console.error(r)}i(e)}else if("1"==a){(async()=>{let n="0";try{n=await t.getItem("lastSelectModuleIndex")||"0"}catch(r){console.error("Failed to get lastSelectModuleIndex:",r),n="0"}let a=e.body;a.lastSelectModuleIndex=n,e.body=a;try{let o=await window.loadDarkConfig();a.darkModel=o}catch(s){console.error(s)}i(e)})();return}}),!0}case"API_TAGIT_ACTIVE_CHANGE":return browser.runtime.sendNativeMessage("application.id",{type:n.operate,tagitId:n.tagitId,isActive:n.isActive},function(e){i(e)}),(async()=>{let t;try{t=await o.tagitData.where("uuid").equals(n.tagitId).first(),!1==e(t)&&(t.isActive=n.isActive,await o.tagitData.update(t.uuid,{isActive:t.isActive}),browser.tabs.reload())}catch(r){console.error("Failed to get tagitData for the given tagitId:",r),t=null}})(),!0;case"API_REMOVE_TAGIT":return browser.runtime.sendNativeMessage("application.id",{type:n.operate,tagitId:n.tagitId,isActive:n.isActive},function(e){i(e)}),(async()=>{try{await o.tagitData.where("uuid").equals(n.tagitId).delete(),browser.tabs.reload()}catch(e){console.error("Failed to remove tagitData for the given tagitId:",e)}})(),!0;case"API_SCRIPT_ACTIVE_CHANGE":return browser.runtime.sendNativeMessage("application.id",{type:n.operate,scriptId:n.scriptId,isActive:n.isActive},function(e){i(e)}),(async()=>{let t;try{t=await o.userScriptData.where("uuid").equals(n.scriptId).first(),!1==e(t)&&(t.isActive=n.isActive,await o.userScriptData.update(t.uuid,{isActive:t.isActive}),browser.tabs.reload())}catch(r){console.error("Failed to get userScriptData for the given scriptId:",r),t=null}})(),!0;case"API_SAVE_MODULE_INDEX":{let p=n.index||"0";return(async()=>{try{await t.setItem("lastSelectModuleIndex",p)}catch(e){console.error("Failed to set lastSelectModuleIndex:",e)}i({success:!0})})(),!0}case"API_ENTER_TAGIT":return browser.tabs.query({active:!0,contentWindow:!0},e=>{browser.tabs.sendMessage(e[0].id,{from:"background",operate:n.operate},function(e){i(e)})}),!0;case"API_TAGIT_ACTION":return browser.runtime.sendNativeMessage("application.id",{type:n.operate,xpath:n.xpath,url:n.url,status:n.status},function(e){i(e)}),!0;case"API_GET_TABID":return i(r.tab.id),!0;case"API_INSERT_CSS":{let $=n.executorJs,m=n.allframe,y=n.runAt;return browser.tabs.query({active:!0,contentWindow:!0},e=>{browser.tabs.insertCSS(e[0].id,{code:$,allFrames:m,runAt:y})}),!0}case"API_INJECT_SCRIPT":{let v=n.executorJs,g=n.allframe,b=n.runAt;return browser.tabs.query({active:!0,contentWindow:!0},e=>{browser.tabs.executeScript(e[0].id,{code:v,allFrames:g,runAt:b})}),!0}case"API_GET_RESOURCE_URLS":return(async()=>{let e=n.scriptId,t=[];try{let r;try{r=await o.userScriptData.where("uuid").equals(e).first()}catch(a){console.error("Failed to get userScriptData for the given scriptId:",a),r=null}r&&null!=r.resourceJson&&r.resourceJson.length>0&&(t=JSON.parse(r.resourceJson))}catch(s){console.error("获取@resource失败..")}i({body:t})})(),!0;case"API_SET_VALUE":return f(n,i),!0;case"API_GET_VALUE":return d(n,i),!0;case"API_DELETE_VALUE":return h(n,i),!0;case"API_LIST_VALUE":return c(n,i),!0;case"API_CLOSE_TAB":{let w=n.tabId||r.tab.id;return browser.tabs.remove(w,()=>i({success:1})),!0}case"API_OPEN_TAB":{let k={active:n.active,index:r.tab.index+1,url:n.url};return browser.tabs.create(k,e=>i(e)),!0}case"API_ADD_STYLE":{let x=r.tab.id;return browser.tabs.insertCSS(x,{code:n.css},()=>{i(n.css)}),!0}case"API_GET_TAB":{let E=null;if(void 0!==r.tab){let A="tab-"+r.tab.id,I=sessionStorage.getItem(A);try{E=JSON.parse(I)}catch(S){}}return i(null==E?{}:E),!0}case"API_SAVE_TAB":if(null!=r.tab&&r.tab.id){let D="tab-"+r.tab.id;sessionStorage.setItem(D,JSON.stringify(n.tab)),i({success:!0})}else i({success:!1});return!0;case"API_SET_CLIPBOARD":{let P=function e(t,n="text/plain"){let r=e=>{e.stopImmediatePropagation(),e.preventDefault(),e.clipboardData.setData(n,t),document.removeEventListener("copy",r,!0)},i=document.createElement("textarea");i.textContent="<empty clipboard>",document.body.appendChild(i),i.select(),document.addEventListener("copy",r,!0);try{return document.execCommand("copy")}catch(a){return console.warn("setClipboard failed",a),document.removeEventListener("copy",r,!0),!1}finally{document.body.removeChild(i)}}(n.data,n.type);i(P);break}case"API_Request":{let O={type:n.operate,scriptId:n.scriptId,data:n.data};return browser.runtime.sendNativeMessage("application.id",O,function(e){i(e)}),!0}case"API_GET_DARK_CONFIG":{let C={type:n.operate};return browser.runtime.sendNativeMessage("application.id",C,function(e){i(e)}),!0}case"API_Register_Command":{let R=n.scriptId,T=n.name,z=n.randomId;return browser.tabs.query({active:!0,contentWindow:!0},e=>{browser.tabs.sendMessage(e[0].id,{from:"background",operate:n.operate,scriptId:R,name:T,randomId:z},function(e){i(e)})}),!0}case"API_UnRegister_Command":{let K=n.scriptId,q=n.randomId;return browser.tabs.query({active:!0,contentWindow:!0},e=>{browser.tabs.sendMessage(e[0].id,{from:"background",operate:n.operate,scriptId:K,randomId:q},function(e){i(e)})}),!0}case"API_GET_TAB_ID":return i({tabId:r.tab.id}),!0;case"API_Background_Change":return _(),i({}),!0;case"API_GET_TAGIT_DATA":return(async()=>{let e;try{e=await o.tagitData.toArray()}catch(t){console.error("Failed to get tagitData:",t),e=[]}i(e)})(),!0;case"API_GET_USERSCRIPT_DATA":return(async()=>{let e=[];try{e=await o.userScriptData.toArray()}catch(t){console.error("Failed to get userScriptData:",t),e=[]}i(e)})(),!0}});let $={"cn.search.yahoo.com":{query:"p",part:"p"},"search.yahoo.com":{query:"p",part:"p"},"www.search.yahoo.com":{query:"p",part:"p"},"hk.www.search.yahoo.com":{query:"p",part:"p"},"google.com":{query:"q",part:"client=safari"},"www.google.cn":{query:"q",part:"client=safari"},"www.google.com":{query:"q",part:"client=safari"},"www.google.com.hk":{query:"q",part:"client=safari"},"www.google.com.tw":{query:"q",part:"client=safari"},"duckduckgo.com":{query:"q",part:"t=iphone"},"www.duckduckgo.com":{query:"q",part:"t=iphone"},"www.bing.com":{query:"q",part:"form=APIPH1&PC=APPL"},"ecosia.org":{query:"q",part:"q"},"www.ecosia.org":{query:"q",part:"q"},"m.baidu.com":{query:"word",part:"/s?from"},"m.so.com":{query:"q",part:"srcg=safariwap_1"},"m.sogou.com":{query:"keyword",part:"web/sl"},"www.baidu.com":{query:"wd",part:""},"www.sogou.com":{query:"query",part:""},"www.so.com":{query:"q",part:""}},m=Object.keys($).map(e=>({hostContains:e}));var y="",v=!1,g=[];async function _(){let n=await t.getItem("addonsConfig");if(null==n)return;let r=!1;if(n.aiSearch&&n.aiSearch.isActive&&(r=!0),!r){g=void 0;return}let i=n.aiSearch.option;v=null!=i&&0!=i;let a;try{a=await o.searchData.toArray()}catch(s){console.error("Failed to get searchData:",s),a=[]}let u=[];if(!1==e(a))for(let l of a){let c={};c.rule_keys=l.sql_rule_keys.split(","),c.url=l.url,u.push(c)}g=u}function b(e){if(-1==e.parentFrameId&&e.tabId>0){let t=new URL(e.url),n=t.host,r=t.searchParams.get($[n].query);if(r){let i="",a="";if(!1==v){let o=r.match(/^(\S+)\s+(.+)$/);null==o&&(o=r.match(/^(\S+)\s*$/)),o&&o.length>=2&&(i=o[1]),o&&o.length>=3&&(a=o[2])}else{let s=r.match(/^\s*(.+)\s+(\S+)$/);null==s&&(s=r.match(/^\s*(\S+)\s*$/)),s&&s.length>=2&&(a=s[1]),s&&s.length>=3&&(i=s[2])}if(void 0!==g&&0==g.length&&_(),null!=g&&g.length>0){var u,l;let c=!1;for(let f of g)for(let d of f.rule_keys){if(d==i){c=!0,y=f.url;break}if(c)break}c&&(u=a,l=e.tabId,browser.tabs.update(l,{url:y.replace("%s",encodeURIComponent(u))}))}}}}_(),browser.webNavigation.onBeforeNavigate.addListener(function(e){console.log(e)}),browser.webNavigation.onBeforeNavigate.addListener(b,{url:m}),browser.webNavigation.onCommitted.addListener(b,{url:m}),browser.tabs.onUpdated.addListener(async(e,t,n)=>{"complete"===t.status&&(browser.webNavigation.getFrame({tabId:n.id,frameId:0}),browser.webNavigation.onBeforeNavigate.addListener(async e=>{e.url.toLowerCase().startsWith("https://boxer.baidu.com")&&await browser.webNavigation.cancel(e.tabId)},{url:[{urlPrefix:"http"}]}))})}();
