function localizableKey(e){let t={zh:{matchScripts:"已匹配脚本列表",tagit:"标记模式",darkMode:"暗黑模式",openInApp:"打开APP",openWithScript:"打开APP，开启油猴脚本功能",openWithTagit:"打开APP，开启标记广告功能",openWithDarkMode:"打开APP，开启网页暗黑模式",emptyScript:"没有匹配到适用的脚本，赶紧去添加吧~",emptyData:"暂无数据，空空如也~",version:"版本号",startTagit:"进入标记模式",themeDefault:"默认(跟随系统)",themeDark:"开启",themeAuto:"跟随系统",themeLight:"关闭",dimImagesDefault:"默认(100%)",scopeDefault:"默认(允许)",scopeEnable:"允许",scopeDisable:"禁止",scopeTips:"是否允许当前网站开启暗黑模式",enabled:"启用状态",theme:"主题",dimImages:"图片明亮度",menu:"菜单"},en:{matchScripts:"Match Scripts",tagit:"Tagit",darkMode:"Dark Mode",openInApp:"Open in Addons",openWithScript:"Open in Addons and Enable Script",openWithTagit:"Open in Addons and Enable Tagit",openWithDarkMode:"Open in Addons and Enable DarkMode",emptyScript:"No matching script found~",emptyData:"empty~",version:"version",startTagit:"Enter Tagit",themeDefault:"Default(Auto)",themeDark:"On",themeAuto:"Auto",themeLight:"Off",dimImagesDefault:"Default(100%)",scopeDefault:"Default(Global)",scopeEnable:"Allow",scopeDisable:"Forbidden",scopeTips:"Allow dark mode on this website?",enabled:"Enabled",theme:"Theme",dimImages:"ImageBrightness",menu:"menu"}},i;return((i=(i=(i=(i=navigator.languages).length>0?navigator.languages[0]:navigator.language?navigator.language:navigator.userLanguage?navigator.userLanguage:"en").toLowerCase()).replace(/-/,"_")).indexOf("zh")>-1?t.zh:t.en)[e]}function setupObservers(){$("#id-footer-js").click(function(e){e.preventDefault(),handleToolbarTapAction(e.target)}),$("#id-footer-tagit").click(function(e){e.preventDefault(),handleToolbarTapAction(e.target)}),$("#id-footer-darkmode").click(function(e){e.preventDefault(),handleToolbarTapAction(e.target)}),$("#id-enabled-button").click(function(e){e.preventDefault();let t="addons://activeAction";var i=document.createElement("a");i.setAttribute("href",t),i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i);try{window.open(t)}catch(a){}window.close()}),$("#id-tagit-addbutton").click(function(e){e.preventDefault(),browser.runtime.sendMessage({from:"popup",operate:"API_ENTER_TAGIT"},function(e){window.close()})})}function handleToolbarTapAction(e){if(void 0==e||null==e)return;let t=e.getAttribute("tab"),i=e.getAttribute("isSelect");if(void 0==i||null==i){if("0"==t){let a=localizableKey("matchScripts");$("#id-header").text(a)}else if("1"==t){let n=localizableKey("tagit");$("#id-header").text(n)}else if("2"==t){let l=localizableKey("darkMode");$("#id-header").text(l)}$("#id-enabled-container").hide(),$("#id-content-footer").children().each(function(e,i){let a=$(this).attr("tab");if(a!=t)$(this).removeAttr("isSelect"),"0"==a?($(this).css("background-image",'url("./images/script_normal_icon.png")'),$("#id-script-fix").hide()):"1"==a?($(this).css("background-image",'url("./images/tagit_normal_icon.png")'),$("#id-tagit-fix").hide(),$("#id-tagit-addbutton").hide()):"2"==a&&($(this).css("background-image",'url("./images/dark_normal_icon.png")'),$("#id-darkmode-container").hide());else if($(this).attr("isSelect",""),"0"==a){if($(this).css("background-image",'url("./images/script_select_icon.png")'),isScriptActive())$("#id-enabled-container").hide(),$("#id-script-fix").show();else{$("#id-enabled-container").show(),$("#id-script-fix").hide();let n=localizableKey("openInApp");$("#id-enabled-button").text(n),n=localizableKey("openWithScript"),$("#id-enabled-text").text(n)}saveMoudleIndex("0")}else if("1"==a){if($(this).css("background-image",'url("./images/tagit_select_icon.png")'),isTagitActive())$("#id-enabled-container").hide(),$("#id-tagit-fix").show(),$("#id-tagit-addbutton").show();else{$("#id-enabled-container").show(),$("#id-tagit-fix").hide(),$("#id-tagit-addbutton").hide();let l=localizableKey("openInApp");$("#id-enabled-button").text(l),l=localizableKey("openWithTagit"),$("#id-enabled-text").text(l)}saveMoudleIndex("1")}else if("2"==a){if($(this).css("background-image",'url("./images/dark_select_icon.png")'),$("#id-darkmode-container").show(),isDarkModeActive())$("#id-enabled-container").hide(),$("#id-darkmode-container").show();else{$("#id-enabled-container").show(),$("#id-darkmode-container").hide();let d=localizableKey("openInApp");$("#id-enabled-button").text(d),d=localizableKey("openWithDarkMode"),$("#id-enabled-text").text(d)}saveMoudleIndex("2")}})}}function saveMoudleIndex(e){browser.runtime.sendMessage({from:"popup",operate:"API_SAVE_MODULE_INDEX",index:e},function(e){})}function isScriptActive(){return null!=__data__&&__data__.config.tampermonkey.isActive}function isTagitActive(){return null!=__data__&&__data__.config.tagit.isActive}function isDarkModeActive(){return null!=__data__&&__data__.config.darkMode.isActive}function isObjectNull(e){return null==e||0===Object.keys(e).length}function isMatchTheCurrentURL(e,t){function i(e,t){var i=e=>e.replace(/([.*+?^=!:${}()|\[\]\/\\])/g,"\\$1");return RegExp("^"+e.split("*").map(i).join(".*")+"$").test(t)}let a=!1;if(!1==isObjectNull(e.includes)){for(let n of e.includes)if("*"==n||i(n,t)){a=!0;break}}if(!1==isObjectNull(e.matches)){for(let l of e.matches)if("*"==l||i(l,t)){a=!0;break}}if(!1==isObjectNull(e.excludes)){for(let d of e.excludes)if("*"==d||i(d,t)){a=!1;break}}return a}var __data__=null,__hostname__="",__url__="",__darkModel__=null;function loadingAllData(){browser.tabs.query({active:!0,contentWindow:!0},e=>{let t=e[0];__url__=t.url,null!=(__hostname__=new URL(t.url).hostname)&&$("#id-scope-span").text(__hostname__),_loadingAllData(e[0])})}function _loadingAllData(e){browser.runtime.sendMessage({from:"popup",operate:"API_ReloadData",status:"1"},t=>{try{let i=t.body;__data__=i;let a=i.config.tampermonkey.isActive,n=i.config.tagit.isActive,l=i.config.darkMode.isActive,d=i.lastSelectModuleIndex;if(a){let o=[];for(let s of i.userScripts)isMatchTheCurrentURL(s,e.url)&&o.push(s);renderScriptData(o)}if(n&&renderTagitData(i.tagits),__darkModel__=i.darkModel,l){let c=i.darkModel;initDarkMode(c)}let r=null;0==d?r=document.querySelector("#id-footer-js"):1==d?r=document.querySelector("#id-footer-tagit"):2==d&&(r=document.querySelector("#id-footer-darkmode")),$(r).removeAttr("isSelect",""),handleToolbarTapAction(r)}catch(p){console.error(p)}})}function renderScriptData(e){if($("#id-script-scrollView").empty(),$("#id-script-empty-tips").hide(),null==e||void 0==e||0==e.length){let t=document.createElement("div");t.id="id-script-empty-tips",$(t).addClass("empty-tips");let i=localizableKey("emptyScript");$(t).text(i),$("#id-script-scrollView").append(t);return}for(let a of e){let n=document.createElement("div");if(n.id="id-content-cell",$(n).addClass("content-cell"),void 0!=a.iconUrl&&null!=a.iconUrl&&""!=a.iconUrl){let l=document.createElement("img");l.src=a.iconUrl,$(l).addClass("content-logo"),$(n).append(l)}else{let d=document.createElement("div");$(d).css("background-image",'url("./images/script_default_logo.png")'),$(d).addClass("content-default-logo"),$(n).append(d)}let o=document.createElement("div");$(o).addClass("content-title"),$(o).text(a.name),$(n).append(o);let s=document.createElement("div");$(s).addClass("content-desc"),$(s).text(a.desc),$(n).append(s);let c=document.createElement("div");$(c).addClass("content-bottom-container"),$(n).append(c);let r=document.createElement("div");$(r).addClass("content-version");var p=localizableKey("version");$(r).text(p+": "+a.version),$(c).append(r);let u=document.createElement("div");$(u).addClass("content-menu"),p=localizableKey("menu"),$(u).text(p),$(u).hide(),$(c).append(u),handleScriptMenuWithModel(n,a);let g=document.createElement("div"),m="";m=isDark()?'url("./images/menu_arrow_white.png")':'url("./images/menu_arrow_black.png")',$(g).css("background-image",m),$(g).addClass("content-menu-logo"),$(u).append(g);let h=document.createElement("div");$(h).addClass("content-control-button");let f="";f=isDark()?a.isActive?'url("./images/icon_pause_dark.png")':'url("./images/icon_play_dark.png")':a.isActive?'url("./images/icon_pause.png")':'url("./images/icon_play.png")',$(h).css("background-image",f),$(n).append(h),$("#id-script-scrollView").append(n),$(h).click(function(e){e.preventDefault(),handleScriptActive(e.target,a)})}}function handleScriptActive(e,t){let i=!t.isActive;t.isActive=i,isDark()?i?$(e).css("background-image",'url("./images/icon_pause_dark.png")'):$(e).css("background-image",'url("./images/icon_play_dark.png")'):i?$(e).css("background-image",'url("./images/icon_pause.png")'):$(e).css("background-image",'url("./images/icon_play.png")'),browser.runtime.sendMessage({from:"popup",operate:"API_SCRIPT_ACTIVE_CHANGE",scriptId:t.uuid,isActive:i},function(e){})}function handleScriptMenuWithModel(e,t){let i=t.uuid,a=e.querySelector(".content-menu"),n=t.injectMode;browser.tabs.query({active:!0,contentWindow:!0},e=>{let t={from:"popup",operate:"API_GET_Script_Menu",scriptId:i,tabId:e[0].id};browser.tabs.sendMessage(e[0].id,t,function(e){if("API_GET_Script_Menu"!=e.type)return;let t=e.body;if(i!=t.scriptId)return;let l=t.array;for(let d of l)d.injectMode=n;handleScriptMenuAction(a,l)})})}function handleScriptMenuAction(e,t){let i=e.querySelector(".__dark-row-select");if(i&&i.remove(),null==t||void 0==t||0==t.length){$(e).hide();return}$(e).show();let a=document.createElement("select");a.className="__dark-row-select",t.forEach(e=>{let t=document.createElement("option");t.text=e.name,t.dataset.optionData=JSON.stringify(e),a.add(t)}),e.appendChild(a),a.selectedIndex=-1,$(a).change(function(e){handleOptionClick($(a).find("option:selected"))})}function handleOptionClick(e){let t=e.data("option-data");browser.tabs.query({active:!0,contentWindow:!0},e=>{browser.tabs.sendMessage(e[0].id,{from:"popup",operate:"API_Run_Command",scriptId:t.uuid,randomId:t.randomId,injectMode:t.injectMode},function(e){window.close()})})}function renderTagitData(e){$("#id-tagit-scrollView").empty();let t=localizableKey("startTagit");if($("#id-tagit-addbutton").text(t),null==e||void 0==e||0==e.length){let i=document.createElement("div");i.id="id-tagit-empty-tips",$(i).addClass("empty-tips");let a=localizableKey("emptyData");$(i).text(a),$("#id-tagit-scrollView").append(i);return}for(let n of e){let l=document.createElement("div");l.id="id-tagit-cell",$(l).addClass("tagit-cell");let d=document.createElement("div");$(d).addClass("tagit-host"),$(d).text(n.host),$(l).append(d);let o=document.createElement("div");$(o).addClass("tagit-xpath"),$(o).text(n.xpath),$(l).append(o);let s=document.createElement("div");$(s).addClass("tagit-delete-button"),isDark()?$(s).css("background-image",'url("./images/icon_delete_dark.png")'):$(s).css("background-image",'url("./images/icon_delete.png")'),$(l).append(s);let c=document.createElement("div");$(c).addClass("tagit-active-button");let r="";r=isDark()?n.isActive?'url("./images/icon_pause_dark.png")':'url("./images/icon_play_dark.png")':n.isActive?'url("./images/icon_pause.png")':'url("./images/icon_play.png")',$(c).css("background-image",r),$(l).append(c),$("#id-tagit-scrollView").append(l),$(c).click(function(e){e.preventDefault(),handleTagitActive(e.target,n)}),$(s).click(function(e){e.preventDefault(),handleDeleteTagitItem(l,n)})}}function handleTagitActive(e,t){let i=!t.isActive;t.isActive=i,isDark()?i?$(e).css("background-image",'url("./images/icon_pause_dark.png")'):$(e).css("background-image",'url("./images/icon_play_dark.png")'):i?$(e).css("background-image",'url("./images/icon_pause.png")'):$(e).css("background-image",'url("./images/icon_play.png")'),browser.runtime.sendMessage({from:"popup",operate:"API_TAGIT_ACTIVE_CHANGE",tagitId:t.uuid,isActive:i},function(e){})}function handleDeleteTagitItem(e,t){$(e).remove(),browser.runtime.sendMessage({from:"popup",operate:"API_REMOVE_TAGIT",tagitId:t.uuid},function(e){})}function handleInitDarkMode(e){if(!e)return;let t=0,i=e.disableSiteList;i&&i.length>0&&__hostname__&&__hostname__.length>0&&i.includes(__hostname__)&&(t=2),$("#id-scope-select").val(t);let a=$("#id-scope-select").find("option:selected").text();$("#id-scope-text").text(a);let n=e.enableValue;$("#id-enabled-select").val(n),a=$("#id-enabled-select").find("option:selected").text(),$("#id-enabled-text2").text(a);let l=e.brightnesssValue;$("#id-dimImages-select").val(l),a=$("#id-dimImages-select").find("option:selected").text(),$("#id-dimImages-text").text(a)}function handleDarkModeAction(){$("#id-scope-select").change(function(e){$("#id-scope-select").val();let t=$("#id-scope-select").find("option:selected").text();$("#id-scope-text").text(t),handleDarkModeOptionAction(0)}),$("#id-enabled-select").change(function(e){$("#id-enabled-select").val();let t=$("#id-enabled-select").find("option:selected").text();$("#id-enabled-text2").text(t),handleDarkModeOptionAction(1)}),$("#id-dimImages-select").change(function(e){$("#id-dimImages-select").val();let t=$("#id-dimImages-select").find("option:selected").text();$("#id-dimImages-text").text(t),handleDarkModeOptionAction(2)})}function handleDarkModeOptionAction(e){let t=$("#id-scope-select").val();t=Number(t);let i=$("#id-enabled-select").val();i=Number(i);let a=$("#id-dimImages-select").val();if(a=Number(a),0===e){let n=__url__;if(null==n||null==__darkModel__)return;let l=1,d=__darkModel__.disableSiteList;if(d&&d.length>0&&__hostname__&&__hostname__.length>0&&d.includes(__hostname__)&&(l=2),l===t)return;let o=__darkModel__.disableSiteList.indexOf(__hostname__);o<0?__darkModel__.disableSiteList.push(__hostname__):__darkModel__.disableSiteList.splice(o,1),browser.runtime.sendMessage({type:"ui-toggle-url",data:n})}else 1===e?2===i?browser.runtime.sendMessage({type:"ui-change-settings",data:{enabled:!0,automation:""}}):3===i?browser.runtime.sendMessage({type:"ui-change-settings",data:{enabled:!1,automation:""}}):(0===i||1===i)&&browser.runtime.sendMessage({type:"ui-change-settings",data:{automation:"system"}}):2===e&&browser.runtime.sendMessage({type:"ui-set-theme",data:{imageBrightness:a}})}function initDarkMode(e){localizableDarkMode(),handleInitDarkMode(e)}function localizableDarkMode(){let e=localizableKey("themeDefault");$("#id-enabled-default-text").text(e),e=localizableKey("themeDark"),$("#id-enabled-on-text").text(e),e=localizableKey("themeAuto"),$("#id-enabled-auto-text").text(e),$("#id-enabled-text2").text(e),e=localizableKey("themeLight"),$("#id-enabled-off-text").text(e),e=localizableKey("dimImagesDefault"),$("#id-dimImages-default-text").text(e),e=localizableKey("scopeDefault"),$("#id-scope-default-text").text(e),e=localizableKey("scopeEnable"),$("#id-scope-enable-text").text(e),e=localizableKey("scopeDisable"),$("#id-scope-site-text").text(e),e=localizableKey("enabled"),$("#id-enabled-span").text(e),e=localizableKey("theme"),$("#id-theme-span").text(e),e=localizableKey("dimImages"),$("#id-dimImages-span").text(e),e=localizableKey("scopeTips"),$("#id-scope-tips-text").text(e)}function isDark(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches}function scaleToPad(){/iPad/i.test(navigator.userAgent)&&$(document.body).css("min-height","400px"),/Macintosh/i.test(navigator.userAgent)&&$(document.body).css("min-height","400px")}$(document).ready(function(){try{scaleToPad(),setupObservers(),handleDarkModeAction(),loadingAllData()}catch(e){console.error(e)}});
