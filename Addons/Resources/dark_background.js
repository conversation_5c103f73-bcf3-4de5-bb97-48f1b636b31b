function blz_chrome_runtime_sendMessage(e,t){chrome.runtime.sendMessage(e,(...e)=>{t?.(...e)})}chrome.runtime.onMessage.addListener((e,t)=>{let{type:s,data:r,error:a}=e;console.log(e),"inject-index"==s&&chrome.tabs.executeScript(t.tab.id,{allFrames:!0,frameId:t.frameId,file:"dark_content.js"})}),function(){"use strict";let e="undefined"==typeof navigator?"some useragent":navigator.userAgent.toLowerCase(),t="undefined"==typeof navigator?"some platform":navigator.platform.toLowerCase(),s=e.includes("chrome")||e.includes("chromium"),r=e.includes("safari")&&!s,a=t.startsWith("mac"),i=(()=>{let t=e.match(/chrom[e|ium]\/([^ ]+)/);return t&&t[1]?t[1]:""})();(()=>{try{return document.querySelector(":defined"),!0}catch(e){return!1}})();let n="function"==typeof XMLHttpRequest,o="function"==typeof fetch;async function l(e,t,s){let r=await fetch(e,{cache:"force-cache",credentials:"omit",referrer:s});if(t&&!r.headers.get("Content-Type").startsWith(t))throw Error(`Mime type mismatch when loading ${e}`);if(!r.ok)throw Error(`Unable to load ${e} ${r.status} ${r.statusText}`);return r}async function c(e,t){let s=await l(e,t);return await u(s)}async function u(e){let t=await e.blob(),s=await new Promise(e=>{let s=new FileReader;s.onloadend=()=>e(s.result),s.readAsDataURL(t)});return s}async function h(e,t,s){let r=await l(e,t,s);return await r.text()}function $(e){return e.replace(/\r/g,"").split("\n").map(e=>e.trim()).filter(e=>e)}function g(e){return e.split(":").map(e=>parseInt(e))}function d(e,t){return e[0]===t[0]&&e[1]===t[1]?0:e[0]<t[0]||e[0]===t[0]&&e[1]<t[1]?-1:1}function f(e,t,s=new Date){let r=g(e),a=g(t),i=[s.getHours(),s.getMinutes()];return d(r,a)>0?f(t,e,s):0===d(r,a)?null:0>d(i,r)?(s.setHours(r[0]),s.setMinutes(r[1]),s.getTime()):0>d(i,a)?(s.setHours(a[0]),s.setMinutes(a[1]),s.getTime()):new Date(s.getFullYear(),s.getMonth(),s.getDate()+1,r[0],r[1]).getTime()}function m(e,t,s=new Date){let r=g(e),a=g(t),i=[s.getHours(),s.getMinutes()];return d(r,a)>0?0>=d(r,i)||0>d(i,a):0>=d(r,i)&&0>d(i,a)}function b(e){let t=0;return e.seconds&&(t+=1e3*e.seconds),e.minutes&&(t+=6e4*e.minutes),e.hours&&(t+=36e5*e.hours),e.days&&(t+=864e5*e.days),t}function p(e){return b(e)/1e3/60}function E(e,t,s){let r=Date.UTC(s.getUTCFullYear(),0,0,0,0,0,0),a=b({days:1}),i=Math.floor((s.getTime()-r)/a),n=Math.PI/180,o=180/Math.PI,l=t/15;function c(t){let s=i+((t?6:18)-l)/24,r=.9856*s-3.289,a=r+1.916*Math.sin(r*n)+.02*Math.sin(2*r*n)+282.634;a>360?a-=360:a<0&&(a+=360);let c=o*Math.atan(.91764*Math.tan(a*n));c>360?c-=360:c<0&&(c+=360);let u=90*Math.floor(a/90),h=90*Math.floor(c/90);c+=u-h,c/=15;let $=.39782*Math.sin(a*n),g=(Math.cos(90.83333333333333*n)-$*Math.sin(e*n))/(Math.cos(Math.asin($))*Math.cos(e*n));if(g>1)return{alwaysDay:!1,alwaysNight:!0,time:0};if(g<-1)return{alwaysDay:!0,alwaysNight:!1,time:0};let d=(t?360-o*Math.acos(g):o*Math.acos(g))/15+c-.06571*s-6.622,f=d-l;return f>24?f-=24:f<0&&(f+=24),{alwaysDay:!1,alwaysNight:!1,time:Math.round(f*b({hours:1}))}}let u=c(!0),h=c(!1);return u.alwaysDay||h.alwaysDay?{alwaysDay:!0}:u.alwaysNight||h.alwaysNight?{alwaysNight:!0}:{sunriseTime:u.time,sunsetTime:h.time}}function T(e,t,s=new Date){var r,a,i;let n=E(e,t,s);if(n.alwaysDay)return!1;if(n.alwaysNight)return!0;let o=n.sunriseTime,l=n.sunsetTime,c=s.getUTCHours()*b({hours:1})+s.getUTCMinutes()*b({minutes:1})+s.getUTCSeconds()*b({seconds:1})+s.getUTCMilliseconds();return r=l,a=o,i=c,a<r?i<=a||r<=i:r<i&&i<a}function S(e,t,s=new Date){let r=E(e,t,s);if(r.alwaysDay||r.alwaysNight)return s.getTime()+b({days:1});let[a,i]=r.sunriseTime<r.sunsetTime?[r.sunriseTime,r.sunsetTime]:[r.sunsetTime,r.sunriseTime],n=s.getUTCHours()*b({hours:1})+s.getUTCMinutes()*b({minutes:1})+s.getUTCSeconds()*b({seconds:1})+s.getUTCMilliseconds();return n<=a?Date.UTC(s.getUTCFullYear(),s.getUTCMonth(),s.getUTCDate(),0,0,0,a):n<=i?Date.UTC(s.getUTCFullYear(),s.getUTCMonth(),s.getUTCDate(),0,0,0,i):Date.UTC(s.getUTCFullYear(),s.getUTCMonth(),s.getUTCDate()+1,0,0,0,a)}async function x(e){return new Promise((t,s)=>{if(n){let r=new XMLHttpRequest;r.overrideMimeType("text/plain"),r.open("GET",e.url,!0),r.onload=()=>{r.status>=200&&r.status<300?t(r.responseText):s(Error(`${r.status}: ${r.statusText}`))},r.onerror=()=>s(Error(`${r.status}: ${r.statusText}`)),e.timeout&&(r.timeout=e.timeout,r.ontimeout=()=>s(Error("File loading stopped due to timeout"))),r.send()}else if(o){let a,i,l=!1;e.timeout&&(i=(a=new AbortController).signal,setTimeout(()=>{a.abort(),l=!0},e.timeout)),fetch(e.url,{signal:i}).then(e=>{e.status>=200&&e.status<300?t(e.text()):s(Error(`${e.status}: ${e.statusText}`))}).catch(e=>{l?s(Error("File loading stopped due to timeout")):s(e)})}else s(Error("Neither XMLHttpRequest nor Fetch API are accessible!"))})}class _{constructor(){this.bytesInUse=0,this.records=new Map}has(e){return this.records.has(e)}get(e){if(this.records.has(e)){let t=this.records.get(e);return t.expires=Date.now()+_.TTL,this.records.delete(e),this.records.set(e,t),t.value}return null}set(e,t){var s;let r=2*(s=t).length;if(r>_.QUOTA_BYTES)return;for(let[a,i]of this.records)if(this.bytesInUse+r>_.QUOTA_BYTES)this.records.delete(a),this.bytesInUse-=i.size;else break;let n=Date.now()+_.TTL;this.records.set(e,{url:e,value:t,size:r,expires:n}),this.bytesInUse+=r}removeExpiredRecords(){let e=Date.now();for(let[t,s]of this.records)if(s.expires<e)this.records.delete(t),this.bytesInUse-=s.size;else break}}function y(e){let t=e.indexOf("[");if(t<0)return!1;let s=e.indexOf("?");return!(s>=0)||!(t>s)}_.QUOTA_BYTES=16777216*(navigator.deviceMemory||4),_.TTL=b({minutes:10});let w=/\[.*?\](\:\d+)?/;function I(e,t){let s={},r=[];for(let a=t.length;a--;){let i=t[a],n=i.slice(0,2);s[n]||(s[n]=[]),s[n].push(i),i.includes("*")&&r.push(i)}let o=r,l=new URL(e).hostname.split(".").slice(0,-1);for(let c=l.length;c--;){let u=l[c].slice(0,2);o.push.apply(o,s[u]||[])}var h=!1;for(let $=0;$<o.length;$++)if(v(e,o[$])){h=!0;break}return h}function v(e,t){let s=y(e),r=y(t);if(s&&r)return function e(t,s){let r=t.match(w)[0],a=s.match(w)[0];return r===a}(e,t);if(!s&&!r){let a=function e(t){t=t.trim();let s="^"===t[0],r="$"===t[t.length-1],a,i,n;(a=(t=t.replace(/^\^/,"").replace(/\$$/,"").replace(/^.*?\/{2,3}/,"").replace(/\?.*$/,"").replace(/\/$/,"")).indexOf("/"))>=0?(i=t.substring(0,a),n=t.replace(/\$/g,"").substring(a)):i=t.replace(/\$/g,"");let o=s?"^(.*?\\:\\/{2,3})?":"^(.*?\\:\\/{2,3})?([^/]*?\\.)?",l=i.split(".");o+="(";for(let c=0;c<l.length;c++)"*"===l[c]&&(l[c]="[^\\.\\/]+?");return o+=l.join("\\."),o+=")",n&&(o+="(",o+=n.replace("/","\\/"),o+=")"),RegExp(o+=r?"(\\/?(\\?[^/]*?)?)$":"(\\/?.*?)$","i")}(t);return Boolean(e.match(a))}return!1}function A(e){return/^[a-z0-9.-]+$/.test(e)}function C(e,t){let s=[],r=e.replace(/\r/g,"").split(/^\s*={2,}\s*$/gm);return r.forEach(e=>{let r=e.split("\n"),a=[];if(r.forEach((e,t)=>{e.match(/^[A-Z]+(\s[A-Z]+){0,2}$/)&&a.push(t)}),0===a.length)return;let i={url:$(r.slice(0,a[0]).join("\n"))};a.forEach((e,s)=>{let n=r[e].trim(),o=r.slice(e+1,s===a.length-1?r.length:a[s+1]).join("\n"),l=t.getCommandPropName(n);if(!l)return;let c=t.parseCommandValue(n,o);i[l]=c}),s.push(i)}),s}function k(e){try{return new URL(e).hostname.toLowerCase()}catch(t){return e.split("/")[0].toLowerCase()}}function R(e,t){let s=7*t,r=parseInt(e.substring(s+0,s+4),36),a=parseInt(e.substring(s+4,s+4+3),36);return[r,r+a]}function D(e){var t;let s={},r={},a=[];function i(t,i,n){let o=e.substring(t,i),l=o.split("\n"),c=[];if(l.forEach((e,t)=>{e.match(/^[A-Z]+(\s[A-Z]+){0,2}$/)&&c.push(t)}),0===c.length)return;let u=$(l.slice(0,c[0]).join("\n"));for(let h of u){let g=k(h);if(A(g)){s[g]?"number"==typeof s[g]&&s[g]!==n?s[g]=[s[g],n]:"object"!=typeof s[g]||s[g].includes(n)||s[g].push(n):s[g]=n;continue}r[g]?"number"==typeof r[g]&&r[g]!==n?r[g]=[r[g],n]:"object"!=typeof r[g]||r[g].includes(n)||r[g].push(n):r[g]=n}a.push([t,i-t])}let n=0,o=/\s*={2,}\s*/gm,l,c=0;for(;l=o.exec(e);){let u=l.index,h=l.index+l[0].length;i(n,u,c),n=h,c++}return i(n,e.length,c),{offsets:(t=a).map(([e,t])=>{let s=e.toString(36),r=t.toString(36);return"0".repeat(4-s.length)+s+"0".repeat(3-r.length)+r}).join(""),domains:s,domainPatterns:r,cache:{}}}function B(e,t,s,r){let a=e.substring(s,r);return C(a,t)[0]}function N(e,t,s,r){let a=[],i=[],n=k(e);for(let o of Object.keys(s.domainPatterns))v(e,o)&&(i=i.concat(s.domainPatterns[o]));let l=n.split(".");for(let c=0;c<l.length;c++){let u=l.slice(c).join(".");s.domains[u]&&v(e,u)&&(i=i.concat(s.domains[u]))}let h=new Set;for(let $ of i)if(!h.has($)){if(h.add($),!s.cache[$]){let[g,d]=R(s.offsets,$);s.cache[$]=B(t,r,g,d)}a.push(s.cache[$])}return a}let M={darkSites:{remote:"https://raw.githubusercontent.com/darkreader/darkreader/master/src/config/dark-sites.config",local:"dark-sites.config"},dynamicThemeFixes:{remote:"https://raw.githubusercontent.com/darkreader/darkreader/master/src/config/dynamic-theme-fixes.config",local:"dynamic-theme-fixes.config"},inversionFixes:{remote:"https://raw.githubusercontent.com/darkreader/darkreader/master/src/config/inversion-fixes.config",local:"inversion-fixes.config"},staticThemes:{remote:"https://raw.githubusercontent.com/darkreader/darkreader/master/src/config/static-themes.config",local:"static-themes.config"}},F=b({seconds:10});function L(e,t,s,r,a){return(e-t)*(a-r)/(s-t)+r}function U(e,t){let s=[];for(let r=0,a=e.length;r<a;r++){s[r]=[];for(let i=0,n=t[0].length;i<n;i++){let o=0;for(let l=0,c=e[0].length;l<c;l++)o+=e[r][l]*t[l][i];s[r][i]=o}}return s}function O(e){let t=V.identity();return 0!==e.sepia&&(t=U(t,V.sepia(e.sepia/100))),0!==e.grayscale&&(t=U(t,V.grayscale(e.grayscale/100))),100!==e.contrast&&(t=U(t,V.contrast(e.contrast/100))),100!==e.brightness&&(t=U(t,V.brightness(e.brightness/100))),1===e.mode&&(t=U(t,V.invertNHue())),t}function G([e,t,s],r){let a=U(r,[[e/255],[t/255],[s/255],[1],[1]]);return[0,1,2].map(e=>{var t;return Math.min(255,Math.max(0,t=Math.round(255*a[e][0])))})}let V={identity:()=>[[1,0,0,0,0],[0,1,0,0,0],[0,0,1,0,0],[0,0,0,1,0],[0,0,0,0,1]],invertNHue:()=>[[.333,-.667,-.667,0,1],[-.667,.333,-.667,0,1],[-.667,-.667,.333,0,1],[0,0,0,1,0],[0,0,0,0,1]],brightness:e=>[[e,0,0,0,0],[0,e,0,0,0],[0,0,e,0,0],[0,0,0,1,0],[0,0,0,0,1]],contrast(e){let t=(1-e)/2;return[[e,0,0,0,t],[0,e,0,0,t],[0,0,e,0,t],[0,0,0,1,0],[0,0,0,0,1]]},sepia:e=>[[.393+.607*(1-e),.769-.769*(1-e),.189-.189*(1-e),0,0],[.349-.349*(1-e),.686+.314*(1-e),.168-.168*(1-e),0,0],[.272-.272*(1-e),.534-.534*(1-e),.131+.869*(1-e),0,0],[0,0,0,1,0],[0,0,0,0,1]],grayscale:e=>[[.2126+.7874*(1-e),.7152-.7152*(1-e),.0722-.0722*(1-e),0,0],[.2126-.2126*(1-e),.7152+.2848*(1-e),.0722-.0722*(1-e),0,0],[.2126-.2126*(1-e),.7152-.7152*(1-e),.0722+.9278*(1-e),0,0],[0,0,0,1,0],[0,0,0,0,1]]};function H(e){let t=[];return t.push('*:not(pre, pre *, code, .far, .fa, .glyphicon, [class*="vjs-"], .fab, .fa-github, .fas, .material-icons, .icofont, .typcn, mu, [class*="mu-"], .glyphicon, .icon) {'),e.useFont&&e.fontFamily&&t.push(`  font-family: ${e.fontFamily} !important;`),e.textStroke>0&&(t.push(`  -webkit-text-stroke: ${e.textStroke}px !important;`),t.push(`  text-stroke: ${e.textStroke}px !important;`)),t.push("}"),t.join("\n")}function P(e,t,r,a,n,o,l){var c;let u=function e(t,s,r){let a=N(t,s,r,{commands:Object.keys(X),getCommandPropName:e=>X[e],parseCommandValue:(e,t)=>"CSS"===e?t.trim():$(t)}),i={url:a[0].url,invert:a[0].invert||[],noinvert:a[0].noinvert||[],removebg:a[0].removebg||[],css:a[0].css||""};if(t){let n=a.slice(1).filter(e=>I(t,e.url)).sort((e,t)=>t.url[0].length-e.url[0].length);if(n.length>0){let o=n[0];return{url:o.url,invert:i.invert.concat(o.invert||[]),noinvert:i.noinvert.concat(o.noinvert||[]),removebg:i.removebg.concat(o.removebg||[]),css:[i.css,o.css].filter(e=>e).join("\n")}}}return i}(n||a,o,l),h=[];if(h.push("@media screen {"),e&&!n&&(h.push(""),h.push("/* Leading rule */"),h.push((c=e,`html {
  -webkit-filter: ${c} !important;
  filter: ${c} !important;
}`))),r.mode===eS.dark&&(h.push(""),h.push("/* Reverse rule */"),h.push(function e(t,s){let r=[];return s.invert.length>0&&(r.push(`${j(s.invert)} {`),r.push(`  -webkit-filter: ${t} !important;`),r.push(`  filter: ${t} !important;`),r.push("}")),s.noinvert.length>0&&(r.push(`${j(s.noinvert)} {`),r.push("  -webkit-filter: none !important;"),r.push("  filter: none !important;"),r.push("}")),s.removebg.length>0&&(r.push(`${j(s.removebg)} {`),r.push("  background: white !important;"),r.push("}")),r.join("\n")}(t,u))),(r.useFont||r.textStroke>0)&&(h.push(""),h.push("/* Font */"),h.push(H(r))),h.push(""),h.push("/* Text contrast */"),h.push("html {"),h.push("  text-shadow: 0 0 0 !important;"),h.push("}"),h.push(""),h.push("/* Full screen */"),[":-webkit-full-screen",":-moz-full-screen",":fullscreen"].forEach(e=>{h.push(`${e}, ${e} * {`),h.push("  -webkit-filter: none !important;"),h.push("  filter: none !important;"),h.push("}")}),!n){let g=[255,255,255],d=Boolean(s&&function e(t,s){let r=t.split(".").map(e=>parseInt(e)),a=s.split(".").map(e=>parseInt(e));for(let i=0;i<r.length;i++)if(r[i]!==a[i])return r[i]<a[i]?-1:1;return 0}(i,"81.0.4035.0")>=0)||r.mode!==eS.dark?g:G(g,O(r)).map(Math.round);h.push(""),h.push("/* Page background */"),h.push("html {"),h.push(`  background: rgb(${d.join(",")}) !important;`),h.push("}")}return u.css&&u.css.length>0&&r.mode===eS.dark&&(h.push(""),h.push("/* Custom rules */"),h.push(u.css)),h.push(""),h.push("}"),h.join("\n")}function j(e){return e.map(e=>e.replace(/\,$/,"")).join(",\n")}(ep=eS||(eS={}))[ep.light=0]="light",ep[ep.dark=1]="dark";let X={INVERT:"invert","NO INVERT":"noinvert","REMOVE BG":"removebg",CSS:"css"},Y={INVERT:"invert",CSS:"css","IGNORE INLINE STYLE":"ignoreInlineStyle","IGNORE IMAGE ANALYSIS":"ignoreImageAnalysis"},W={neutralBg:[16,20,23],neutralText:[167,158,139],redBg:[64,12,32],redText:[247,142,102],greenBg:[32,64,48],greenText:[128,204,148],blueBg:[32,48,64],blueText:[128,182,204],fadeBg:[16,20,23,.5],fadeText:[167,158,139,.5]},q={neutralBg:[255,242,228],neutralText:[0,0,0],redBg:[255,85,170],redText:[140,14,48],greenBg:[192,255,170],greenText:[0,128,0],blueBg:[173,215,229],blueText:[28,16,171],fadeBg:[0,0,0,.5],fadeText:[0,0,0,.5]};function z([e,t,s,r]){return"number"==typeof r?`rgba(${e}, ${t}, ${s}, ${r})`:`rgb(${e}, ${t}, ${s})`}function K(e,t,s){return e.map((e,r)=>Math.round(e*(1-s)+t[r]*s))}function Z(e,t,s=e=>e){return(r,a)=>{let i=e(r);if(null==i||0===i.length)return null;let n=[];i.forEach((e,t)=>{let r=s(e);t<i.length-1?r+=",":r+=" {",n.push(r)});let o=t(a);return o.forEach(e=>n.push(`    ${e} !important;`)),n.push("}"),n.join("\n")}}let J={bg:{hover:.075,active:.1},fg:{hover:.25,active:.5},border:.5},Q=[Z(e=>e.neutralBg,e=>[`background-color: ${z(e.neutralBg)}`]),Z(e=>e.neutralBgActive,e=>[`background-color: ${z(e.neutralBg)}`]),Z(e=>e.neutralBgActive,e=>[`background-color: ${z(K(e.neutralBg,[255,255,255],J.bg.hover))}`],e=>`${e}:hover`),Z(e=>e.neutralBgActive,e=>[`background-color: ${z(K(e.neutralBg,[255,255,255],J.bg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.neutralText,e=>[`color: ${z(e.neutralText)}`]),Z(e=>e.neutralTextActive,e=>[`color: ${z(e.neutralText)}`]),Z(e=>e.neutralTextActive,e=>[`color: ${z(K(e.neutralText,[255,255,255],J.fg.hover))}`],e=>`${e}:hover`),Z(e=>e.neutralTextActive,e=>[`color: ${z(K(e.neutralText,[255,255,255],J.fg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.neutralBorder,e=>[`border-color: ${z(K(e.neutralBg,e.neutralText,J.border))}`]),Z(e=>e.redBg,e=>[`background-color: ${z(e.redBg)}`]),Z(e=>e.redBgActive,e=>[`background-color: ${z(e.redBg)}`]),Z(e=>e.redBgActive,e=>[`background-color: ${z(K(e.redBg,[255,0,64],J.bg.hover))}`],e=>`${e}:hover`),Z(e=>e.redBgActive,e=>[`background-color: ${z(K(e.redBg,[255,0,64],J.bg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.redText,e=>[`color: ${z(e.redText)}`]),Z(e=>e.redTextActive,e=>[`color: ${z(e.redText)}`]),Z(e=>e.redTextActive,e=>[`color: ${z(K(e.redText,[255,255,0],J.fg.hover))}`],e=>`${e}:hover`),Z(e=>e.redTextActive,e=>[`color: ${z(K(e.redText,[255,255,0],J.fg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.redBorder,e=>[`border-color: ${z(K(e.redBg,e.redText,J.border))}`]),Z(e=>e.greenBg,e=>[`background-color: ${z(e.greenBg)}`]),Z(e=>e.greenBgActive,e=>[`background-color: ${z(e.greenBg)}`]),Z(e=>e.greenBgActive,e=>[`background-color: ${z(K(e.greenBg,[128,255,182],J.bg.hover))}`],e=>`${e}:hover`),Z(e=>e.greenBgActive,e=>[`background-color: ${z(K(e.greenBg,[128,255,182],J.bg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.greenText,e=>[`color: ${z(e.greenText)}`]),Z(e=>e.greenTextActive,e=>[`color: ${z(e.greenText)}`]),Z(e=>e.greenTextActive,e=>[`color: ${z(K(e.greenText,[182,255,224],J.fg.hover))}`],e=>`${e}:hover`),Z(e=>e.greenTextActive,e=>[`color: ${z(K(e.greenText,[182,255,224],J.fg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.greenBorder,e=>[`border-color: ${z(K(e.greenBg,e.greenText,J.border))}`]),Z(e=>e.blueBg,e=>[`background-color: ${z(e.blueBg)}`]),Z(e=>e.blueBgActive,e=>[`background-color: ${z(e.blueBg)}`]),Z(e=>e.blueBgActive,e=>[`background-color: ${z(K(e.blueBg,[0,128,255],J.bg.hover))}`],e=>`${e}:hover`),Z(e=>e.blueBgActive,e=>[`background-color: ${z(K(e.blueBg,[0,128,255],J.bg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.blueText,e=>[`color: ${z(e.blueText)}`]),Z(e=>e.blueTextActive,e=>[`color: ${z(e.blueText)}`]),Z(e=>e.blueTextActive,e=>[`color: ${z(K(e.blueText,[182,224,255],J.fg.hover))}`],e=>`${e}:hover`),Z(e=>e.blueTextActive,e=>[`color: ${z(K(e.blueText,[182,224,255],J.fg.active))}`],e=>`${e}:active, ${e}:focus`),Z(e=>e.blueBorder,e=>[`border-color: ${z(K(e.blueBg,e.blueText,J.border))}`]),Z(e=>e.fadeBg,e=>[`background-color: ${z(e.fadeBg)}`]),Z(e=>e.fadeText,e=>[`color: ${z(e.fadeText)}`]),Z(e=>e.transparentBg,()=>["background-color: transparent"]),Z(e=>e.noImage,()=>["background-image: none"]),Z(e=>e.invert,()=>["filter: invert(100%) hue-rotate(180deg)"])],ee={"NO COMMON":"noCommon","NEUTRAL BG":"neutralBg","NEUTRAL BG ACTIVE":"neutralBgActive","NEUTRAL TEXT":"neutralText","NEUTRAL TEXT ACTIVE":"neutralTextActive","NEUTRAL BORDER":"neutralBorder","RED BG":"redBg","RED BG ACTIVE":"redBgActive","RED TEXT":"redText","RED TEXT ACTIVE":"redTextActive","RED BORDER":"redBorder","GREEN BG":"greenBg","GREEN BG ACTIVE":"greenBgActive","GREEN TEXT":"greenText","GREEN TEXT ACTIVE":"greenTextActive","GREEN BORDER":"greenBorder","BLUE BG":"blueBg","BLUE BG ACTIVE":"blueBgActive","BLUE TEXT":"blueText","BLUE TEXT ACTIVE":"blueTextActive","BLUE BORDER":"blueBorder","FADE BG":"fadeBg","FADE TEXT":"fadeText","TRANSPARENT BG":"transparentBg","NO IMAGE":"noImage",INVERT:"invert"};function et(e){return e.replace(/([a-z])([A-Z])/g,"$1 $2").toUpperCase()}function es(e){let t=e.slice().sort((e,t)=>{var s,r;return s=e.url[0],r=t.url[0],s.localeCompare(r)});return function e(t,s){let r=[];return t.forEach((e,a)=>{var i,n;i=r,function e(t,s){var r;if(null!=(r=t).length)for(let a=0,i=t.length;a<i;a++)s(t[a]);else for(let n of t)s(n)}(n=e.url,e=>i.push(e)),s.props.forEach(t=>{let a=s.getPropCommandName(t),i=e[t];if(s.shouldIgnoreProp(t,i))return;r.push(""),r.push(a);let n=s.formatPropValue(t,i);n&&r.push(n)}),a<t.length-1&&(r.push(""),r.push("=".repeat(32)),r.push(""))}),r.push(""),r.join("\n")}(t,{props:Object.values(ee),getPropCommandName:et,formatPropValue(e,t){var s;return"noCommon"===e?"":(s=t).concat("").join("\n").trim()},shouldIgnoreProp:(e,t)=>"noCommon"===e?!t:!(Array.isArray(t)&&t.length>0)})}class er{get(e){try{return localStorage.getItem(e)}catch(t){return console.error(t),null}}set(e,t){try{localStorage.setItem(e,t)}catch(s){console.error(s)}}remove(e){try{localStorage.removeItem(e)}catch(t){console.error(t)}}has(e){try{return null!=localStorage.getItem(e)}catch(t){return console.error(t),!1}}}class ea{constructor(){this.map=new Map}get(e){return this.map.get(e)}set(e,t){this.map.set(e,t)}remove(e){this.map.delete(e)}has(e){return this.map.has(e)}}let ei={UI_GET_DATA:"ui-get-data",UI_GET_ACTIVE_TAB_INFO:"ui-get-active-tab-info",UI_SUBSCRIBE_TO_CHANGES:"ui-subscribe-to-changes",UI_UNSUBSCRIBE_FROM_CHANGES:"ui-unsubscribe-from-changes",UI_CHANGE_SETTINGS:"ui-change-settings",UI_SET_THEME:"ui-set-theme",UI_SET_SHORTCUT:"ui-set-shortcut",UI_TOGGLE_URL:"ui-toggle-url",UI_MARK_NEWS_AS_READ:"ui-mark-news-as-read",UI_LOAD_CONFIG:"ui-load-config",UI_APPLY_DEV_DYNAMIC_THEME_FIXES:"ui-apply-dev-dynamic-theme-fixes",UI_RESET_DEV_DYNAMIC_THEME_FIXES:"ui-reset-dev-dynamic-theme-fixes",UI_APPLY_DEV_INVERSION_FIXES:"ui-apply-dev-inversion-fixes",UI_RESET_DEV_INVERSION_FIXES:"ui-reset-dev-inversion-fixes",UI_APPLY_DEV_STATIC_THEMES:"ui-apply-dev-static-themes",UI_RESET_DEV_STATIC_THEMES:"ui-reset-dev-static-themes",UI_SAVE_FILE:"ui-save-file",UI_REQUEST_EXPORT_CSS:"ui-request-export-css",BG_CHANGES:"bg-changes",BG_ADD_CSS_FILTER:"bg-add-css-filter",BG_ADD_STATIC_THEME:"bg-add-static-theme",BG_ADD_SVG_FILTER:"bg-add-svg-filter",BG_ADD_DYNAMIC_THEME:"bg-add-dynamic-theme",BG_EXPORT_CSS:"bg-export-css",BG_UNSUPPORTED_SENDER:"bg-unsupported-sender",BG_CLEAN_UP:"bg-clean-up",BG_RELOAD:"bg-reload",BG_FETCH_RESPONSE:"bg-fetch-response",BG_UI_UPDATE:"bg-ui-update",BG_CSS_UPDATE:"bg-css-update",CS_COLOR_SCHEME_CHANGE:"cs-color-scheme-change",CS_FRAME_CONNECT:"cs-frame-connect",CS_FRAME_FORGET:"cs-frame-forget",CS_FRAME_FREEZE:"cs-frame-freeze",CS_FRAME_RESUME:"cs-frame-resume",CS_EXPORT_CSS_RESPONSE:"cs-export-css-response",CS_FETCH:"cs-fetch"};function en(...e){}function eo(...e){}ei.UI_GET_DATA,ei.UI_GET_ACTIVE_TAB_INFO,ei.CS_FRAME_CONNECT,ei.CS_FETCH,window.syncClient=window.syncClient||new class e{constructor(){this.id=Date.now(),this.counter=0}generateSyncJobId(){return this.id+"-"+this.counter++}addIdToSyncJob(e){return e.syncJobId=this.generateSyncJobId()}isItAnInsideJob(e){let t=(e.syncJobId||"").split("-")[0];return this.id!==t}};let el=window.syncClient,ec=new class e{constructor(){this.awaitingResolves=[],this.locked=!1}isLocked(){return this.locked}async lock(){if(!this.locked){this.locked=!0;return}return new Promise(e=>{this.awaitingResolves.push(e)})}unlock(){this.locked&&(this.locked=!1,setTimeout(()=>this.executeNextOperation()))}executeNextOperation(){if(this.awaitingResolves.length>0){let e=this.awaitingResolves.shift();e()}}};async function eu(e){return new Promise(t=>{chrome.storage.local.get(e,s=>{if(chrome.runtime.lastError){console.error(chrome.runtime.lastError.message),t(e);return}t(s)})})}async function eh(e){return new Promise(t=>{chrome.storage.local.get(e,s=>{if(chrome.runtime.lastError){console.error(chrome.runtime.lastError.message),t(e);return}t(s)})})}async function e$(e){return new Promise(async(t,s)=>{await ec.lock(),el.addIdToSyncJob(e),console.log("syncWrite",window.lastWriteSync=Date.now()),chrome.storage.local.set(e,()=>{if(chrome.runtime.lastError){s(chrome.runtime.lastError),ec.unlock();return}t(),setTimeout(()=>ec.unlock(),500)})})}async function eg(e){return new Promise(async t=>{await ec.lock(),chrome.storage.local.set(e,()=>{t(),setTimeout(()=>ec.unlock(),500)})})}class ed{constructor(){this.resolves=[],this.rejects=[],this.wasResolved=!1,this.wasRejected=!1}async entry(){return this.wasResolved?Promise.resolve(this.resolution):this.wasRejected?Promise.reject(this.resolution):new Promise((e,t)=>{this.resolves.push(e),this.rejects.push(t)})}async resolve(e){if(!this.wasRejected&&!this.wasResolved)return this.wasResolved=!0,this.resolution=e,this.resolves.forEach(t=>t(e)),this.resolves=null,this.rejects=null,new Promise(e=>setTimeout(()=>e()))}async reject(e){if(!this.wasRejected&&!this.wasResolved)return this.wasRejected=!0,this.resolution=e,this.rejects.forEach(t=>t(e)),this.resolves=null,this.rejects=null,new Promise(e=>setTimeout(()=>e()))}isPending(){return!this.wasResolved&&!this.wasRejected}isFulfilled(){return this.wasResolved}isRejected(){return this.wasRejected}}(eE=ex||(ex={}))[eE.INITIAL=0]="INITIAL",eE[eE.DISABLED=1]="DISABLED",eE[eE.LOADING=2]="LOADING",eE[eE.READY=3]="READY",eE[eE.SAVING=4]="SAVING",eE[eE.SAVING_OVERRIDE=5]="SAVING_OVERRIDE";class ef{constructor(e,t,a){if(this.meta=ex.INITIAL,this.loadStateBarrier=null,!function e(){if(!s&&!r)return!1;let t=chrome.runtime.getManifest().background;return"persistent"in t?!1===t.persistent:"service_worker"in t||void 0}()){this.meta=ex.DISABLED;return}this.localStorageKey=e,this.parent=t,this.defaults=a}collectState(){let e={};for(let t of Object.keys(this.defaults))e[t]=this.parent[t]||this.defaults[t];return e}async saveState(){switch(this.meta){case ex.DISABLED:return;case ex.LOADING:case ex.INITIAL:this.loadStateBarrier&&await this.loadStateBarrier.entry(),this.meta=ex.SAVING;break;case ex.READY:this.meta=ex.SAVING;break;case ex.SAVING:this.meta=ex.SAVING_OVERRIDE;return;case ex.SAVING_OVERRIDE:return}chrome.storage.local.set({[this.localStorageKey]:this.collectState()},()=>{switch(this.meta){case ex.INITIAL:case ex.DISABLED:case ex.LOADING:case ex.READY:case ex.SAVING:this.meta=ex.READY;break;case ex.SAVING_OVERRIDE:this.meta=ex.READY,this.saveState()}})}async loadState(){switch(this.meta){case ex.INITIAL:return this.meta=ex.LOADING,this.loadStateBarrier=new ed,new Promise(e=>{chrome.storage.local.get(this.localStorageKey,t=>{this.meta=ex.READY,t[this.localStorageKey]?Object.assign(this.parent,t[this.localStorageKey]):Object.assign(this.parent,this.defaults),this.loadStateBarrier.resolve(),this.loadStateBarrier=null,e()})});case ex.DISABLED:case ex.READY:case ex.SAVING:case ex.SAVING_OVERRIDE:return;case ex.LOADING:return this.loadStateBarrier.entry()}}}async function em(e){return new Promise(t=>{chrome.tabs.query(e,e=>t(e))})}window.StateManager=ef,(eT=e_||(e_={}))[eT.ACTIVE=0]="ACTIVE",eT[eT.PASSIVE=1]="PASSIVE",eT[eT.HIDDEN=2]="HIDDEN",eT[eT.FROZEN=3]="FROZEN",eT[eT.TERMINATED=4]="TERMINATED",eT[eT.DISCARDED=5]="DISCARDED";class eb{constructor({getConnectionMessage:e,onColorSchemeChange:t,getTabMessage:s,waitForStateAndSettings:r}){this.fileLoader=null,this.timestamp=null,this.stateManager=new ef(eb.LOCAL_STORAGE_KEY,this,{tabs:{},timestamp:0}),this.tabs={},this.getTabMessage=s;let a={[ei.CS_FRAME_CONNECT]:1,[ei.CS_FETCH]:1},i=async(s,a,i)=>{await r();let n=this.tabs;function o(e,t,s){s&&s.frameId&&(t.senderFrameURL=n[e][s.frameId].url),chrome.tabs.sendMessage(e,t,s)}switch(s.type){case ei.CS_FRAME_CONNECT:{var l,u,$,g,d;await this.stateManager.loadState();let f=a.tab?.id??0,{frameId:m}=a,b=a.url,p=a.tab?.url??a.url,E;l=this.tabs,u=f,$=m,g=b,d=this.timestamp,l[u]?E=l[u]:(E={},l[u]=E),E[$]={url:g,state:e_.ACTIVE,timestamp:d},((t,s)=>{let r=e(t);r instanceof Promise?r.then(e=>e&&s(e,{frameId:a.frameId})):r&&s(r,{frameId:a.frameId})})({url:p,frameURL:m?b:null},i),this.stateManager.saveState();break}case ei.CS_FRAME_FORGET:{if(await this.stateManager.loadState(),!a.tab)break;let T=a.tab.id,S=a.frameId;0===S&&delete this.tabs[T],this.tabs[T]&&this.tabs[T][S]&&delete this.tabs[T][S],this.stateManager.saveState();break}case ei.CS_FRAME_FREEZE:await this.stateManager.loadState();let x=this.tabs[a.tab.id][a.frameId];x.state=e_.FROZEN,x.url=null,this.stateManager.saveState();break;case ei.CS_FRAME_RESUME:{await this.stateManager.loadState();let y=a.tab.id,w=a.frameId,I=a.url;if(this.tabs[y][w].timestamp<this.timestamp){let v=this.getTabMessage(this.getTabURL(a.tab),I);o(y,v,{frameId:w})}this.tabs[a.tab.id][a.frameId]={url:a.url,state:e_.ACTIVE,timestamp:this.timestamp},this.stateManager.saveState();break}case ei.CS_FETCH:{await this.stateManager.loadState();let A=s.id,C=e=>o(a.tab.id,{type:ei.BG_FETCH_RESPONSE,id:A,...e});try{let{url:k,responseType:R,mimeType:D,origin:B}=s.data;this.fileLoader||(this.fileLoader=function e(){let t={"data-url":new _,text:new _},s={"data-url":c,text:h};async function r({url:e,responseType:r,mimeType:a,origin:i}){let n=t[r],o=s[r];if(n.has(e))return n.get(e);let l=await o(e,a,i);return n.set(e,l),l}return{get:r}}());let N=await this.fileLoader.get({url:k,responseType:R,mimeType:D,origin:B});C({data:N})}catch(M){C({error:M&&M.message?M.message:M})}break}case ei.CS_COLOR_SCHEME_CHANGE:await this.stateManager.loadState(),t(s.data);break;case ei.UI_SAVE_FILE:{await this.stateManager.loadState();let{content:F,name:L}=s.data,U=document.createElement("a");U.href=URL.createObjectURL(new Blob([F])),U.download=L,U.click();break}case ei.UI_REQUEST_EXPORT_CSS:{await this.stateManager.loadState();let O=await this.getActiveTab();chrome.tabs.sendMessage(O.id,{type:ei.BG_EXPORT_CSS},{frameId:0})}}};chrome.runtime.onMessage.addListener((e,t,s)=>(i(e,t,s),a[e.type]))}getTabURL(e){return e.url||"about:blank"}async sendMessage(){this.timestamp++,await this.stateManager.loadState(),(await em({})).filter(e=>Boolean(this.tabs[e.id])).forEach(e=>{let t=this.tabs[e.id];Object.entries(t).filter(([,{state:e}])=>e===e_.ACTIVE||e===e_.PASSIVE).forEach(([,{url:t}],s)=>{let r=this.getTabMessage(this.getTabURL(e),0===s?null:t);e.active&&0===s?chrome.tabs.sendMessage(e.id,r,{frameId:s}):setTimeout(()=>chrome.tabs.sendMessage(e.id,r,{frameId:s})),this.tabs[e.id][s]&&(this.tabs[e.id][s].timestamp=this.timestamp)})})}async canAccessActiveTab(){await this.stateManager.loadState();let e=await this.getActiveTab();return Boolean(this.tabs[e.id])}async getActiveTabURL(){return this.getTabURL(await this.getActiveTab())}async getActiveTab(){let e=(await em({active:!0,lastFocusedWindow:!0}))[0],t=e=>e.startsWith("chrome-extension:")||e.startsWith("moz-extension:");if(!e||t(e.url)){let s=await em({active:!0});e=s.find(e=>!t(e.url))||e}return e}}eb.LOCAL_STORAGE_KEY="TabManager-state";var ep,eE,eT,eS,ex,e_,e8={cssFilter:"cssFilter",svgFilter:"svgFilter",staticTheme:"staticTheme",dynamicTheme:"dynamicTheme"};let ey={darkScheme:{background:"#181a1b",text:"#e8e6e3"},lightScheme:{background:"#dcdad7",text:"#181a1b"}},ew={mode:1,brightness:100,contrast:100,grayscale:0,sepia:0,useFont:!1,fontFamily:a?"Helvetica Neue":"Open Sans",textStroke:0,engine:e8.dynamicTheme,stylesheet:"",darkSchemeBackgroundColor:ey.darkScheme.background,darkSchemeTextColor:ey.darkScheme.text,lightSchemeBackgroundColor:ey.lightScheme.background,lightSchemeTextColor:ey.lightScheme.text,scrollbarColor:a?"":"auto",selectionColor:"auto",styleSystemControls:!0,imageBrightness:100},eI={enabled:!0,fetchNews:!1,theme:ew,presets:[],customThemes:[],siteList:[],siteListEnabled:[],applyToListedOnly:!1,changeBrowserTheme:!1,syncSettings:!0,syncSitesFixes:!1,automation:"system",time:{activation:"18:00",deactivation:"9:00"},location:{latitude:null,longitude:null},previewNewDesign:!1,enableForPDF:!0,enableForProtectedPages:!1,enableContextMenus:!1};function ev({h:e,s:t,l:s,a:r=1}){if(0===t){let[a,i,n]=[s,s,s].map(e=>Math.round(255*e));return{r:a,g:n,b:i,a:r}}let o=(1-Math.abs(2*s-1))*t,l=o*(1-Math.abs(e/60%2-1)),c=s-o/2,[u,h,$]=(e<60?[o,l,0]:e<120?[l,o,0]:e<180?[0,o,l]:e<240?[0,l,o]:e<300?[l,0,o]:[o,0,l]).map(e=>Math.round((e+c)*255));return{r:u,g:h,b:$,a:r}}function eA({r:e,g:t,b:s,a:r=1}){let a=e/255,i=t/255,n=s/255,o=Math.max(a,i,n),l=Math.min(a,i,n),c=o-l,u=(o+l)/2;if(0===c)return{h:0,s:0,l:u,a:r};let h=(o===a?(i-n)/c%6:o===i?(n-a)/c+2:(a-i)/c+4)*60;return h<0&&(h+=360),{h,s:c/(1-Math.abs(2*u-1)),l:u,a:r}}function eC(e,t=0){let s=e.toFixed(t);if(0===t)return s;let r=s.indexOf(".");if(r>=0){let a=s.match(/0+$/);if(a)return a.index===r+1?s.substring(0,r):s.substring(0,a.index)}return s}let e0=/^rgba?\([^\(\)]+\)$/,ek=/^hsla?\([^\(\)]+\)$/,e3=/^#[0-9a-f]+$/i;function eR(e){let t=e.trim().toLowerCase();if(t.match(e0))return function e(t){let[s,r,a,i=1]=eD(t,eB,eN);return{r:s,g:r,b:a,a:i}}(t);if(t.match(ek))return function e(t){let[s,r,a,i=1]=eD(t,eM,eF);return ev({h:s,s:r,l:a,a:i})}(t);if(t.match(e3))return function e(t){let s=t.substring(1);switch(s.length){case 3:case 4:{let[r,a,i]=[0,1,2].map(e=>parseInt(`${s[e]}${s[e]}`,16)),n=3===s.length?1:parseInt(`${s[3]}${s[3]}`,16)/255;return{r,g:a,b:i,a:n}}case 6:case 8:{let[o,l,c]=[0,2,4].map(e=>parseInt(s.substring(e,e+2),16)),u=6===s.length?1:parseInt(s.substring(6,8),16)/255;return{r:o,g:l,b:c,a:u}}}throw Error(`Unable to parse ${t}`)}(t);if(e9.has(t))return function e(t){let s=e9.get(t);return{r:s>>16&255,g:s>>8&255,b:s>>0&255,a:1}}(t);if(eL.has(t))return function e(t){let s=eL.get(t);return{r:s>>16&255,g:s>>8&255,b:s>>0&255,a:1}}(t);if("transparent"===e)return{r:0,g:0,b:0,a:0};throw Error(`Unable to parse ${e}`)}function eD(e,t,s){let r=function e(t){let s=[],r=0,a=!1,i=t.indexOf("(");t=t.substring(i+1,t.length-1);for(let n=0;n<t.length;n++){let o=t[n];o>="0"&&o<="9"||"."===o||"+"===o||"-"===o?a=!0:a&&(" "===o||","===o)?(s.push(t.substring(r,n)),a=!1,r=n+1):a||(r=n+1)}return a&&s.push(t.substring(r,t.length)),s}(e),a=Object.entries(s),i=r.map(e=>e.trim()).map((e,s)=>{let r,i=a.find(([t])=>e.endsWith(t));return(r=i?parseFloat(e.substring(0,e.length-i[0].length))/i[1]*t[s]:parseFloat(e),t[s]>1)?Math.round(r):r});return i}let eB=[255,255,255,1],eN={"%":100},eM=[360,1,1,1],eF={"%":100,deg:360,rad:2*Math.PI,turn:1},e9=new Map(Object.entries({aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgrey:11119017,darkgreen:25600,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,grey:8421504,green:32768,greenyellow:11403055,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgrey:13882323,lightgreen:9498256,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074})),eL=new Map(Object.entries({ActiveBorder:3906044,ActiveCaption:0,AppWorkspace:11184810,Background:6513614,ButtonFace:16777215,ButtonHighlight:15329769,ButtonShadow:10461343,ButtonText:0,CaptionText:0,GrayText:8355711,Highlight:11720703,HighlightText:0,InactiveBorder:16777215,InactiveCaption:16777215,InactiveCaptionText:0,InfoBackground:16514245,InfoText:0,Menu:16185078,MenuText:16777215,Scrollbar:11184810,ThreeDDarkShadow:0,ThreeDFace:12632256,ThreeDHighlight:16777215,ThreeDLightShadow:16777215,ThreeDShadow:0,Window:15527148,WindowFrame:11184810,WindowText:0,"-webkit-focus-ring-color":15046400}).map(([e,t])=>[e.toLowerCase(),t]));function eU(e){let t=1===e.mode;return e[t?"darkSchemeBackgroundColor":"lightSchemeBackgroundColor"]}function e6(e){let t=1===e.mode;return e[t?"darkSchemeTextColor":"lightSchemeTextColor"]}let e4=new Map,eO=new Map;function e1(e){if(eO.has(e))return eO.get(e);let t=eR(e),s=eA(t);return eO.set(e,s),s}let eG=["r","g","b","a"],e2=["mode","brightness","contrast","grayscale","sepia","darkSchemeBackgroundColor","darkSchemeTextColor","lightSchemeBackgroundColor","lightSchemeTextColor"];function eV(e,t,s,r,a){var i,n;let o;e4.has(s)?o=e4.get(s):(o=new Map,e4.set(s,o));let l,c=(i=e,n=t,l="",eG.forEach(e=>{l+=`${i[e]};`}),e2.forEach(e=>{l+=`${n[e]};`}),l);if(o.has(c))return o.get(c);let u=eA(e),h=null==r?null:e1(r),$=null==a?null:e1(a),g=s(u,h,$),{r:d,g:f,b:m,a:b}=ev(g),p=O(t),[E,T,S]=G([d,f,m],p),x=1===b?function e({r:t,g:s,b:r,a}){return`#${(null!=a&&a<1?[t,s,r,Math.round(255*a)]:[t,s,r]).map(e=>`${e<16?"0":""}${e.toString(16)}`).join("")}`}({r:E,g:T,b:S}):function e(t){let{r:s,g:r,b:a,a:i}=t;return null!=i&&i<1?`rgba(${eC(s)}, ${eC(r)}, ${eC(a)}, ${eC(i,2)})`:`rgb(${eC(s)}, ${eC(r)}, ${eC(a)})`}({r:E,g:T,b:S,a:b});return o.set(c,x),x}function e5(e,t){let s=eU(t),r=e6(t);return eV(e,t,e7,r,s)}function e7({h:e,s:t,l:s,a:r},a,i){let n=s<.5,o;o=n?s<.2||t<.12:t<.24||s>.8&&e>200&&e<280;let l=e,c=s;o&&(n?(l=a.h,c=a.s):(l=i.h,c=i.s));let u=L(s,0,1,a.l,i.l);return{h:l,s:c,l:u,a:r}}function eH({h:e,s:t,l:s,a:r},a){let i=t<.12||s>.8&&e>200&&e<280;if(s<.5){let n=L(s,0,.5,0,.4);if(i){let o=a.h,l=a.s;return{h:o,s:l,l:n,a:r}}return{h:e,s:t,l:n,a:r}}let c=L(s,.5,1,.4,a.l);if(i){let u=a.h,h=a.s;return{h:u,s:h,l:c,a:r}}let $=e;return e>60&&e<180&&($=e>120?L(e,120,180,135,180):L(e,60,120,60,105)),{h:$,s:t,l:c,a:r}}function eP(e,t){if(0===t.mode)return e5(e,t);let s=eU(t);return eV(e,{...t,mode:0},eH,s)}function ej(e){return L(e,205,245,205,220)}function eX({h:e,s:t,l:s,a:r},a){let i=s<.2||t<.24,n=!i&&e>205&&e<245;if(s>.5){let o=L(s,.5,1,.55,a.l);if(i){let l=a.h,c=a.s;return{h:l,s:c,l:o,a:r}}let u=e;return n&&(u=ej(e)),{h:u,s:t,l:o,a:r}}if(i){let h=a.h,$=a.s,g=L(s,0,.5,a.l,.55);return{h:h,s:$,l:g,a:r}}let d=e,f;return n?(d=ej(e),f=L(s,0,.5,a.l,Math.min(1,.55+.05))):f=L(s,0,.5,a.l,.55),{h:d,s:t,l:f,a:r}}function eY(e,t){if(0===t.mode)return e5(e,t);let s=e6(t);return eV(e,{...t,mode:0},eX,s)}function eW({h:e,s:t,l:s,a:r},a,i){let n=e,o=t;(s<.2||t<.24)&&(s<.5?(n=a.h,o=a.s):(n=i.h,o=i.s));let l=L(s,0,1,.5,.2);return{h:n,s:o,l:l,a:r}}function eq(e,t){if(0===t.mode)return e5(e,t);let s=e6(t),r=eU(t);return eV(e,{...t,mode:0},eW,s,r)}let ez={accentcolor:"bg",button_background_active:"text",button_background_hover:"text",frame:"bg",icons:"text",icons_attention:"text",ntp_background:"bg",ntp_text:"text",popup:"bg",popup_border:"bg",popup_highlight:"bg",popup_highlight_text:"text",popup_text:"text",sidebar:"bg",sidebar_border:"border",sidebar_text:"text",tab_background_text:"text",tab_line:"bg",tab_loading:"bg",tab_selected:"bg",textcolor:"text",toolbar:"bg",toolbar_bottom_separator:"border",toolbar_field:"bg",toolbar_field_border:"border",toolbar_field_border_focus:"border",toolbar_field_focus:"bg",toolbar_field_separator:"border",toolbar_field_text:"text",toolbar_field_text_focus:"text",toolbar_text:"text",toolbar_top_separator:"border",toolbar_vertical_separator:"border"},eK={accentcolor:"#111111",frame:"#111111",ntp_background:"white",ntp_text:"black",popup:"#cccccc",popup_text:"black",sidebar:"#cccccc",sidebar_border:"#333",sidebar_text:"black",tab_background_text:"white",tab_loading:"#23aeff",textcolor:"white",toolbar:"#707070",toolbar_field:"lightgray",toolbar_field_text:"black"};function eZ(e){let t=Object.entries(eK).reduce((t,[s,r])=>{let a=ez[s],i=eR(r),n=(0,({bg:eP,text:eY,border:eq})[a])(i,e);return t[s]=n,t},{});"undefined"!=typeof browser&&browser.theme&&browser.theme.update&&browser.theme.update({colors:t})}function eJ(){"undefined"!=typeof browser&&browser.theme&&browser.theme.reset&&browser.theme.reset()}function eQ(e){return e.slice(0,4).map(e=>e.map(e=>e.toFixed(3)).join(" ")).join(" ")}class te{constructor(){this.isEnabled=null,this.wasEnabledOnLastCheck=null,this.wasLastColorSchemeDark=null,this.startBarrier=null,this.stateManager=null,this.onColorSchemeChange=async({isDark:e})=>{this.user.settings||await this.user.loadSettings(),"system"===this.user.settings.automation&&this.handleAutoCheck(e)},this.getTabMessage=(e,t)=>{let s=this.getURLInfo(e);if(this.isEnabled&&function e(t,s,{isProtected:r,isInDarkList:a}){if(r&&!s.enableForProtectedPages)return!1;if(function e(t){if(t.includes(".pdf")){if(t.includes("?")&&(t=t.substring(0,t.lastIndexOf("?"))),t.includes("#")&&(t=t.substring(0,t.lastIndexOf("#"))),t.match(/(wikipedia|wikimedia).org/i)&&t.match(/(wikipedia|wikimedia)\.org\/.*\/[a-z]+\:[^\:\/]+\.pdf/i)||t.match(/timetravel\.mementoweb\.org\/reconstruct/i)&&t.match(/\.pdf$/i))return!1;if(t.endsWith(".pdf")){for(let s=t.length;s>0&&"="!==t[s];s--)if("/"===t[s])return!0}}return!1}(t))return s.enableForPDF;let i=function e(t,s){let r;try{r=new URL(t)}catch(a){try{r=new URL("http://"+t)}catch(i){return!1}}let n=r.hostname;return n=n.replace(/^\[|\]$/g,""),s.some(e=>e instanceof RegExp?e.test(n):e.toLowerCase()===n.toLowerCase())}(t,s.siteList),n=I(t,s.siteListEnabled);return s.applyToListedOnly&&!n?i:!!n&&!!a||!a&&!i}(e,this.user.settings,s)){let r=this.user.settings.customThemes.find(({url:t})=>I(e,t)),a=r?null:this.user.settings.presets.find(({urls:t})=>I(e,t)),i=r?r.theme:a?a.theme:this.user.settings.theme;switch(i.engine){case e8.cssFilter:return{type:ei.BG_ADD_CSS_FILTER,data:function e(t,s,r,a,i){let n=function e(t){let s=[];return(t.mode===eS.dark&&s.push("invert(100%) hue-rotate(180deg)"),100!==t.brightness&&s.push(`brightness(${t.brightness}%)`),100!==t.contrast&&s.push(`contrast(${t.contrast}%)`),0!==t.grayscale&&s.push(`grayscale(${t.grayscale}%)`),0!==t.sepia&&s.push(`sepia(${t.sepia}%)`),0===s.length)?null:s.join(" ")}(t);return P(n,"invert(100%) hue-rotate(180deg)",t,s,r,a,i)}(i,e,t,this.config.INVERSION_FIXES_RAW,this.config.INVERSION_FIXES_INDEX)};case e8.svgFilter:var n,o,l,c,u,h;let g,d;return{type:ei.BG_ADD_SVG_FILTER,data:{css:(n=i,o=e,l=t,c=this.config.INVERSION_FIXES_RAW,P(g="url(#dark-reader-filter)",d="url(#dark-reader-reverse-filter)",n,o,l,c,u=this.config.INVERSION_FIXES_INDEX)),svgMatrix:eQ(O(h=i)),svgReverseMatrix:eQ(V.invertNHue())}};case e8.staticTheme:return{type:ei.BG_ADD_STATIC_THEME,data:i.stylesheet&&i.stylesheet.trim()?i.stylesheet:function e(t,s,r,a,i){let n=1===t.mode?W:q,o=Object.entries(n).reduce((e,[s,r])=>{let[a,i,n,o]=r;return e[s]=G([a,i,n],O({...t,mode:0})),void 0!==o&&e[s].push(o),e},{}),l=function e(t,s){var r;let a=parseInt(s.offsets.substring(4,7),36),i=t.substring(0,a);return(r=i,C(r,{commands:Object.keys(ee),getCommandPropName:e=>ee[e],parseCommandValue:(e,t)=>"NO COMMON"===e||$(t)}))[0]}(a,i),c=function e(t,s,r){let a=N(t,s,r,{commands:Object.keys(ee),getCommandPropName:e=>ee[e],parseCommandValue:(e,t)=>"NO COMMON"===e||$(t)}),i=a.slice(1).map(e=>({specificity:I(t,e.url)?e.url[0].length:0,theme:e})).filter(({specificity:e})=>e>0).sort((e,t)=>t.specificity-e.specificity);return 0===i.length?null:i[0].theme}(r||s,a,i),u=[];return c&&c.noCommon||(u.push("/* Common theme */"),u.push(...Q.map(e=>e(l,o)))),c&&(u.push(`/* Theme for ${c.url.join(" ")} */`),u.push(...Q.map(e=>e(c,o)))),(t.useFont||t.textStroke>0)&&(u.push("/* Font */"),u.push(H(t))),u.filter(e=>e).join("\n")}(i,e,t,this.config.STATIC_THEMES_RAW,this.config.STATIC_THEMES_INDEX)};case e8.dynamicTheme:{let f={...i};delete f.engine;let m=function e(t,s,r,a,i){let n=N(s||t,r,a,{commands:Object.keys(Y),getCommandPropName:e=>Y[e],parseCommandValue:(e,t)=>"CSS"===e?t.trim():$(t)});if(0===n.length||"*"!==n[0].url[0])return null;let o=n[0],l={url:o.url,invert:o.invert||[],css:o.css||"",ignoreInlineStyle:o.ignoreInlineStyle||[],ignoreImageAnalysis:o.ignoreImageAnalysis||[]};i&&(l.css+='\nembed[type="application/pdf"] { filter: invert(100%) contrast(90%); }');let c=n.slice(1).map(e=>({specificity:I(s||t,e.url)?e.url[0].length:0,theme:e})).filter(({specificity:e})=>e>0).sort((e,t)=>t.specificity-e.specificity);if(0===c.length)return l;let u=c[0].theme;return{url:u.url,invert:l.invert.concat(u.invert||[]),css:[l.css,u.css].filter(e=>e).join("\n"),ignoreInlineStyle:l.ignoreInlineStyle.concat(u.ignoreInlineStyle||[]),ignoreImageAnalysis:l.ignoreImageAnalysis.concat(u.ignoreImageAnalysis||[])}}(e,t,this.config.DYNAMIC_THEME_FIXES_RAW,this.config.DYNAMIC_THEME_FIXES_INDEX,this.user.settings.enableForPDF);return{type:ei.BG_ADD_DYNAMIC_THEME,data:{filter:f,fixes:m,isIFrame:null!=t}}}default:throw Error(`Unknown engine ${i.engine}`)}}return{type:ei.BG_CLEAN_UP}},this.waitForStateAndSettings=async()=>{this.user.settings||await this.user.loadSettings(),await this.stateManager.loadState()},this.config=new class e{constructor(){this.raw={darkSites:null,dynamicThemeFixes:null,inversionFixes:null,staticThemes:null},this.overrides={darkSites:null,dynamicThemeFixes:null,inversionFixes:null,staticThemes:null}}async loadConfig({name:e,local:t,localURL:s,remoteURL:r}){let a,i=async()=>await x({url:s});if(t)a=await i();else try{a=await x({url:`${r}?nocache=${Date.now()}`,timeout:F})}catch(n){console.error(`${e} remote load error`,n),a=await i()}return a}async loadDarkSites({local:e}){let t=await this.loadConfig({name:"Dark Sites",local:e,localURL:M.darkSites.local,remoteURL:M.darkSites.remote});this.raw.darkSites=t,this.handleDarkSites()}async loadDynamicThemeFixes({local:e}){let t=await this.loadConfig({name:"Dynamic Theme Fixes",local:e,localURL:M.dynamicThemeFixes.local,remoteURL:M.dynamicThemeFixes.remote});this.raw.dynamicThemeFixes=t,this.handleDynamicThemeFixes()}async loadInversionFixes({local:e}){let t=await this.loadConfig({name:"Inversion Fixes",local:e,localURL:M.inversionFixes.local,remoteURL:M.inversionFixes.remote});this.raw.inversionFixes=t,this.handleInversionFixes()}async loadStaticThemes({local:e}){let t=await this.loadConfig({name:"Static Themes",local:e,localURL:M.staticThemes.local,remoteURL:M.staticThemes.remote});this.raw.staticThemes=t,this.handleStaticThemes()}async load(e){let t=await eh({config:this}),s=t.config;if(s.DYNAMIC_THEME_FIXES_INDEX&&s.DYNAMIC_THEME_FIXES_RAW){Object.assign(this,s);return}await Promise.all([this.loadDarkSites(e),this.loadDynamicThemeFixes(e),this.loadInversionFixes(e),this.loadStaticThemes(e)]).catch(e=>console.error("Fatality",e)),eg({config:this})}handleDarkSites(){let e=this.overrides.darkSites||this.raw.darkSites;this.DARK_SITES=$(e)}handleDynamicThemeFixes(){let e=this.overrides.dynamicThemeFixes||this.raw.dynamicThemeFixes;this.DYNAMIC_THEME_FIXES_INDEX=D(e),this.DYNAMIC_THEME_FIXES_RAW=e}handleInversionFixes(){let e=this.overrides.inversionFixes||this.raw.inversionFixes;this.INVERSION_FIXES_INDEX=D(e),this.INVERSION_FIXES_RAW=e}handleStaticThemes(){let e=this.overrides.staticThemes||this.raw.staticThemes;this.STATIC_THEMES_INDEX=D(e),this.STATIC_THEMES_RAW=e}},this.user=new class e{constructor({onRemoteSettingsChange:e}){var t,s;let r;this.saveSettingsIntoStorage=(s=async()=>{if(this.saveStorageBarrier){await this.saveStorageBarrier.entry();return}this.saveStorageBarrier=new ed;let e=this.settings;if(e.syncSettings)try{await e$(e)}catch(t){eo("Settings synchronization was disabled due to error:",chrome.runtime.lastError),this.set({syncSettings:!1}),await this.saveSyncSetting(!1),await eg(e)}else await eg(e);this.saveStorageBarrier.resolve(),this.saveStorageBarrier=null},r=null,(...e)=>{r&&clearTimeout(r),r=setTimeout(()=>{r=null,s(...e)},1e3)}),this.settings=null,t=async()=>{await this.loadSettings(),e()},chrome.storage.onChanged.addListener((e,s)=>{if("sync"!=s)return console.log("our boring sync");console.log("syncChangeArgs",e),console.log("syncChanged",Date.now()-window.lastWriteSync),ec.isLocked()||(console.log("SYNC FROM OUTSIDE !!!!"),t())})}async loadSettings(){this.settings=await this.loadSettingsFromStorage()}fillDefaults(e){e.theme={...ew,...e.theme},e.time={...eI.time,...e.time},e.presets.forEach(e=>{e.theme={...ew,...e.theme}}),e.customThemes.forEach(e=>{e.theme={...ew,...e.theme}})}async loadSettingsFromStorage(){if(this.loadBarrier)return await this.loadBarrier.entry();this.loadBarrier=new ed;let e=await eh(eI);if(null==e.syncSettings&&(e.syncSettings=eI.syncSettings),!e.syncSettings)return this.fillDefaults(e),this.loadBarrier.resolve(e),e;let t=await eu(eI);if(!t)return e.syncSettings=!1,this.set({syncSettings:!1}),this.saveSyncSetting(!1),this.loadBarrier.resolve(e),e;let s=await eu(eI);return this.fillDefaults(s),this.loadBarrier.resolve(s),s}async saveSettings(){await this.saveSettingsIntoStorage()}async saveSyncSetting(e){let t={syncSettings:e};await eg(t);try{await e$(t)}catch(s){eo("Settings synchronization was disabled due to error:",chrome.runtime.lastError),this.set({syncSettings:!1})}}set(e){if(e.siteList){if(!Array.isArray(e.siteList)){let t=[];for(let s in e.siteList){let r=Number(s);isNaN(r)||(t[r]=e.siteList[s])}e.siteList=t}let a=e.siteList.filter(e=>{let t=!1;try{v("https://google.com/",e),v("[::1]:1337",e),t=!0}catch(s){}return t&&"/"!==e});e={...e,siteList:a}}this.settings={...this.settings,...e}}}({onRemoteSettingsChange:()=>this.onRemoteSettingsChange()}),this.startBarrier=new ed,this.stateManager=new ef(te.LOCAL_STORAGE_KEY,this,{isEnabled:null,wasEnabledOnLastCheck:null,registeredContextMenus:null}),this.messenger=new class e{constructor(e){this.adapter=e,this.changeListenerCount=0,chrome.runtime.onMessage.addListener((e,t,s)=>(this.adapter.waitForStateAndSettings().then(()=>{this.onUIMessage(e,s)}),[ei.UI_GET_DATA,ei.UI_GET_ACTIVE_TAB_INFO].includes(e.type))),window.loadDarkConfig=this.adapter.loadDarkConfig}onUIMessage({type:e,data:t},s){switch(e){case ei.UI_GET_DATA:this.adapter.collect().then(e=>s({data:e}));break;case ei.UI_GET_ACTIVE_TAB_INFO:this.adapter.getActiveTabInfo().then(e=>s({data:e}));break;case ei.UI_SUBSCRIBE_TO_CHANGES:this.changeListenerCount++;break;case ei.UI_UNSUBSCRIBE_FROM_CHANGES:this.changeListenerCount--;break;case ei.UI_CHANGE_SETTINGS:this.adapter.changeSettings(t);break;case ei.UI_SET_THEME:this.adapter.setTheme(t);break;case ei.UI_TOGGLE_URL:this.adapter.toggleURL(t);break;case ei.UI_LOAD_CONFIG:this.adapter.loadConfig(t);break;case ei.UI_APPLY_DEV_DYNAMIC_THEME_FIXES:{let r=this.adapter.applyDevDynamicThemeFixes(t);s({error:r?r.message:null});break}case ei.UI_RESET_DEV_DYNAMIC_THEME_FIXES:this.adapter.resetDevDynamicThemeFixes();break;case ei.UI_APPLY_DEV_INVERSION_FIXES:{let a=this.adapter.applyDevInversionFixes(t);s({error:a?a.message:null});break}case ei.UI_RESET_DEV_INVERSION_FIXES:this.adapter.resetDevInversionFixes();break;case ei.UI_APPLY_DEV_STATIC_THEMES:{let i=this.adapter.applyDevStaticThemes(t);s({error:i?i.message:null});break}case ei.UI_RESET_DEV_STATIC_THEMES:this.adapter.resetDevStaticThemes()}}reportChanges(e){}}(this.getMessengerAdapter()),this.tabs=new eb({getConnectionMessage:({url:e,frameURL:t,unsupportedSender:s})=>s?this.getUnsupportedSenderMessage():this.getConnectionMessage(e,t),getTabMessage:this.getTabMessage,onColorSchemeChange:this.onColorSchemeChange,waitForStateAndSettings:this.waitForStateAndSettings})}async recalculateIsEnabled(){if(this.user.settings||await this.user.loadSettings(),await this.stateManager.loadState(),!this.user.settings)return!1;let{automation:e}=this.user.settings;return"system"===e?this.isEnabled=await this.getDarkStatus():this.isEnabled=this.user.settings.enabled,this.isEnabled}async getDarkStatus(){return new Promise(e=>{browser.tabs.query({active:!0,contentWindow:!0},t=>{browser.tabs.sendMessage(t[0].id,{operate:"API_GET_Dark_Status"},function(t){e(t.isDark)})})})}async start(){await this.config.load({local:!0}),await this.user.loadSettings(),this.user.settings.syncSitesFixes&&await this.config.load({local:!1}),await this.onAppToggle(),this.startBarrier.resolve()}getMessengerAdapter(){return{waitForStateAndSettings:async()=>await this.waitForStateAndSettings(),collect:async()=>await this.collectData(),getActiveTabInfo:async()=>{this.user.settings||await this.user.loadSettings(),await this.stateManager.loadState();let e=await this.tabs.getActiveTabURL(),t=this.getURLInfo(e);return t.isInjected=await this.tabs.canAccessActiveTab(),t},changeSettings:e=>this.changeSettings(e),setTheme:e=>this.setTheme(e),toggleURL:e=>this.toggleURL(e),loadConfig:async e=>await this.config.load(e),loadDarkConfig:async()=>{this.user.settings||await this.user.loadSettings();let e=this.user.settings.siteList??[],t=this.user.settings.automation??"system",s=this.user.settings.enabled??!0,r=this.user.settings.theme.imageBrightness??100,a={};return a.disableSiteList=e,"system"===t?a.enableValue=1:s?a.enableValue=2:a.enableValue=3,a.brightnesssValue=r,a}}}async collectData(){return this.user.settings||await this.user.loadSettings(),await this.stateManager.loadState(),{isEnabled:this.isEnabled,isReady:!0,settings:this.user.settings}}getConnectionMessage(e,t){return this.user.settings?this.getTabMessage(e,t):new Promise(s=>{this.user.loadSettings().then(()=>s(this.getTabMessage(e,t)))})}getUnsupportedSenderMessage(){return{type:ei.BG_UNSUPPORTED_SENDER}}async handleAutoCheck(e){this.user.settings||await this.user.loadSettings(),await this.stateManager.loadState(),"boolean"==typeof e?this.isEnabled=e:await this.recalculateIsEnabled();let t=this.isEnabled;(null===this.wasEnabledOnLastCheck||this.wasEnabledOnLastCheck!==t)&&(this.wasEnabledOnLastCheck=t,await this.onAppToggle(),"boolean"==typeof e&&(this.isEnabled=e),this.tabs.sendMessage(),this.reportChanges(),this.stateManager.saveState())}async changeSettings(e){this.user.settings||await this.user.loadSettings();let t={...this.user.settings};this.user.set(e),(t.enabled!==this.user.settings.enabled||t.automation!==this.user.settings.automation||t.time.activation!==this.user.settings.time.activation||t.time.deactivation!==this.user.settings.time.deactivation||t.location.latitude!==this.user.settings.location.latitude||t.location.longitude!==this.user.settings.location.longitude)&&await this.onAppToggle(),t.syncSettings!==this.user.settings.syncSettings&&this.user.saveSyncSetting(this.user.settings.syncSettings),this.isEnabled&&null!=e.changeBrowserTheme&&t.changeBrowserTheme!==e.changeBrowserTheme&&(e.changeBrowserTheme?eZ(this.user.settings.theme):eJ()),this.onSettingsChanged()}async setTheme(e){this.user.settings||await this.user.loadSettings(),this.user.set({theme:{...this.user.settings.theme,...e}}),this.isEnabled&&this.user.settings.changeBrowserTheme&&eZ(this.user.settings.theme),this.onSettingsChanged()}async reportChanges(){let e=await this.collectData();this.messenger.reportChanges(e)}async toggleURL(e){this.user.settings||await this.user.loadSettings();let t=I(e,this.config.DARK_SITES),s=t?this.user.settings.siteListEnabled.slice():this.user.settings.siteList.slice(),r=function e(t){let s=new URL(t);return s.host?s.host:s.protocol}(e),a=s.indexOf(r);a<0?s.push(r):s.splice(a,1),t?await this.changeSettings({siteListEnabled:s}):await this.changeSettings({siteList:s})}async toggleCurrentSite(){let e=await this.tabs.getActiveTabURL();this.toggleURL(e)}async onAppToggle(){await this.recalculateIsEnabled(),this.isEnabled?this.user.settings.changeBrowserTheme&&eZ(this.user.settings.theme):this.user.settings.changeBrowserTheme&&eJ()}async onSettingsChanged(){this.user.settings||await this.user.loadSettings(),await this.stateManager.loadState(),this.wasEnabledOnLastCheck=this.isEnabled,this.tabs.sendMessage(),this.saveUserSettings(),this.reportChanges(),this.stateManager.saveState()}onRemoteSettingsChange(){}getURLInfo(e){var t;let{DARK_SITES:s}=this.config,r=I(e,s),a=!(t=e)||!!t.startsWith("chrome")||!!t.startsWith("https://chrome.google.com/webstore")||!!t.startsWith("data")||!!t.startsWith("devtools")||!!t.startsWith("view-source");return{url:e,isInDarkList:r,isProtected:a,isInjected:null}}async saveUserSettings(){await this.user.saveSettings();var{enabled:e,automation:t}=this.user.settings}}te.LOCAL_STORAGE_KEY="Extension-state";let tt=new te;window.nn=tt,tt.start()}();
