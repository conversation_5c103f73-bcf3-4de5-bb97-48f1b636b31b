function blz_chrome_runtime_onMessage_addListener(e){chrome.runtime.onMessage.addListener((n,t)=>{n.senderFrameURL&&document.URL!=n.senderFrameURL||e(n,t)})}!function e(){"use strict";if("about:blank"!==document.URL){if(window!=top&&!document.documentElement.offsetWidth){"loading"==document.readyState&&window.addEventListener("DOMContentLoaded",e);return}parent==top&&self!=top&&chrome.runtime.sendMessage({type:"inject-index"})}}();
