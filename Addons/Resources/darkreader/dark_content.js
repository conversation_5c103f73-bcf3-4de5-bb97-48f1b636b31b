function luminance(e,t,r){var n=[e,t,r].map(function(e){return(e/=255)<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)});return .2126*n[0]+.7152*n[1]+.0722*n[2]}function contrast(e,t){var r=luminance(e.r,e.g,e.b),n=luminance(t.r,t.g,t.b);return(Math.max(r,n)+.05)/(Math.min(r,n)+.05)}function heartbeat(){heartbeat.last=heartbeat.last||0,Date.now()-heartbeat.last>5e3&&(heartbeat.last=Date.now(),chrome.runtime.sendMessage({type:"interactive-wake"})),clearTimeout(heartbeat.timer),heartbeat.timer=setTimeout(e=>{heartbeat.last=Date.now(),chrome.runtime.sendMessage({type:"interactive-wake"})},1e4)}if(window.postMessage({type:"blz-neo-noir-init"},"*"),/Edg/i.test(navigator.useragent)&&(window.blz_chrome_runtime_onMessage_addListener=function e(t){chrome.runtime.onMessage.addListener((e,r)=>{e.senderFrameURL&&document.URL!=e.senderFrameURL||t(e,r)})}),top==window){var e=document.hidden;document.addEventListener("visibilitychange",function(){if(e!=document.hidden){if(document.hidden);else{chrome.runtime.sendMessage({type:"interactive-wake"});let t=isSystemDarkModeEnabled();chrome.runtime.sendMessage({type:"cs-color-scheme-change",data:{isDark:t}})}e=document.hidden}},!1)}!function(){let e=e=>"window"in globalThis&&Boolean(window.matchMedia(e).matches),t=()=>e("(prefers-color-scheme: dark)"),r=t()||e("(prefers-color-scheme: light)");window.isSystemDarkModeEnabled=function e(){return!!r&&t()}}(),function e(){"use strict";var t,r;if("about:blank"===document.URL)return;if(window!=top&&!document.documentElement.offsetWidth){"loading"==document.readyState&&window.addEventListener("DOMContentLoaded",e);return}function n(...e){}function a(...e){}function i(e){let t=!1,r=null,n,a=(...a)=>{n=a,r?t=!0:(e(...n),r=requestAnimationFrame(()=>{r=null,t&&(e(...n),t=!1)}))},i=()=>{cancelAnimationFrame(r),t=!1,r=null};return Object.assign(a,{cancel:i})}function l(e,t){var r;if(null!=(r=e).length)for(let n=0,a=e.length;n<a;n++)t(e[n]);else for(let i of e)t(i)}function s(e,t){l(t,t=>e.push(t))}function o(e){let t=0;return e.seconds&&(t+=1e3*e.seconds),e.minutes&&(t+=6e4*e.minutes),e.hours&&(t+=36e5*e.hours),e.days&&(t+=864e5*e.days),t}function u({selectNode:e,createNode:t,updateNode:r,selectTarget:n,createTarget:a,isTargetMutation:i}){let l=n();if(l){let s=e();s?r(s):t(l)}else{let o=new MutationObserver(r=>{let a=r.find(i);if(a){d();let l=n();e()||t(l)}}),u=()=>{if("complete"!==document.readyState)return;d();let r=n()||a();e()||t(r)},d=()=>{document.removeEventListener("readystatechange",u),o.disconnect()};"complete"===document.readyState?u():(document.addEventListener("readystatechange",u),o.observe(document,{childList:!0,subtree:!0}))}}function d(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function c(e,t,r=Function.prototype){let n=o({seconds:2}),a=o({seconds:10}),l=e.previousSibling,s=e.parentNode;if(!s)throw Error("Unable to watch for node position: parent element not found");if("prev-sibling"===t&&!l)throw Error("Unable to watch for node position: there is no previous sibling");let u=0,d=null,c=null,f=i(()=>{if(c)return;u++;let i=Date.now();if(null==d)d=i;else if(u>=10){if(i-d<a){c=setTimeout(()=>{d=null,u=0,c=null,f()},n);return}d=i,u=1}if("parent"===t&&l&&l.parentNode!==s){p();return}if("prev-sibling"===t){if(null==l.parentNode){p();return}l.parentNode!==s&&m(l.parentNode)}s.insertBefore(e,l?l.nextSibling:s.firstChild),$.takeRecords(),r&&r()}),$=new MutationObserver(()=>{("parent"===t&&e.parentNode!==s||"prev-sibling"===t&&e.previousSibling!==l)&&f()}),h=()=>{$.observe(s,{childList:!0})},p=()=>{clearTimeout(c),$.disconnect(),f.cancel()},g=()=>{$.takeRecords()},m=e=>{s=e,p(),h()};return h(),{run:h,stop:p,skip:g}}function f(e,t){if(null==e)return;let r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>null==e.shadowRoot?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT});for(let n=e.shadowRoot?r.currentNode:r.nextNode();null!=n;n=r.nextNode())t(n),f(n.shadowRoot,t)}function $(){return"complete"===document.readyState||"interactive"===document.readyState}let h=new Set;function p(e){h.add(e)}function g(e){h.delete(e)}function m(){return"complete"===document.readyState}let b=new Set;function y(e){b.add(e)}if(!$()){let x=()=>{$()&&(h.forEach(e=>e()),h.clear(),m()&&(document.removeEventListener("readystatechange",x),b.forEach(e=>e()),b.clear()))};document.addEventListener("readystatechange",x)}let k=new Map,w=new WeakMap;function _(e,t){let r,n,a;if(k.has(e))r=k.get(e),n=w.get(r);else{let i=!1,s=!1;(r=new MutationObserver(t=>{if(function e(t){if(t.length>1e3)return!0;let r=0;for(let n=0;n<t.length;n++)if((r+=t[n].addedNodes.length)>1e3)return!0;return!1}(t))!i||$()?n.forEach(({onHugeMutations:t})=>t(e)):s||(p(a=()=>n.forEach(({onHugeMutations:t})=>t(e))),s=!0),i=!0;else{let r=function e(t){let r=new Set,n=new Set,a=new Set;t.forEach(e=>{l(e.addedNodes,e=>{e instanceof Element&&e.isConnected&&r.add(e)}),l(e.removedNodes,e=>{e instanceof Element&&(e.isConnected?a.add(e):n.add(e))})}),a.forEach(e=>r.delete(e));let i=[],s=[];return r.forEach(e=>{r.has(e.parentElement)&&i.push(e)}),n.forEach(e=>{n.has(e.parentElement)&&s.push(e)}),i.forEach(e=>r.delete(e)),s.forEach(e=>n.delete(e)),{additions:r,moves:a,deletions:n}}(t);n.forEach(({onMinorMutations:e})=>e(r))}})).observe(e,{childList:!0,subtree:!0}),k.set(e,r),n=new Set,w.set(r,n)}return n.add(t),{disconnect(){n.delete(t),a&&g(a),0===n.size&&(r.disconnect(),w.delete(r),k.delete(e))}}}function E(e,t){u({selectNode:()=>document.getElementById("dark-reader-style"),createNode(r){document.documentElement.setAttribute("data-darkreader-mode",t);let n=document.createElement("style");n.id="dark-reader-style",n.type="text/css",n.textContent=e,r.appendChild(n)},updateNode(t){e.replace(/^\s+/gm,"")!==t.textContent.replace(/^\s+/gm,"")&&(t.textContent=e)},selectTarget:()=>document.head,createTarget(){let e=document.createElement("head");return document.documentElement.insertBefore(e,document.documentElement.firstElementChild),e},isTargetMutation:e=>"head"===e.target.nodeName.toLowerCase()})}function v(){d(document.getElementById("dark-reader-style"))}function S(){d(document.getElementById("dark-reader-svg"))}let C="undefined"==typeof navigator?"some useragent":navigator.userAgent.toLowerCase(),T="undefined"==typeof navigator?"some platform":navigator.platform.toLowerCase(),V=C.includes("chrome")||C.includes("chromium"),R=C.includes("thunderbird"),A=C.includes("firefox")||R;C.includes("vivaldi"),C.includes("yabrowser"),C.includes("opr")||C.includes("opera"),C.includes("edg");let L=C.includes("safari")&&!V;T.startsWith("win"),T.startsWith("mac"),C.includes("mobile");let D="function"==typeof ShadowRoot,N="function"==typeof MediaQueryList&&"function"==typeof MediaQueryList.prototype.addEventListener;(()=>{let e=C.match(/chrom[e|ium]\/([^ ]+)/);return e&&e[1]?e[1]:""})();let I=(()=>{try{return document.querySelector(":defined"),!0}catch(e){return!1}})();globalThis.chrome&&globalThis.chrome.runtime&&globalThis.chrome.runtime.getManifest&&globalThis.chrome.runtime.getManifest().manifest_version;let P,B=new Map;function M(e){return P||(P=document.createElement("a")),P.href=e,P.href}function F(e,t=null){let r=`${e}${t?`;${t}`:""}`;if(B.has(r))return B.get(r);if(t){let n=new URL(e,M(t));return B.set(r,n),n}let a=new URL(M(e));return B.set(e,a),a}function O(e,t){if(t.match(/^data\\?\:/))return t;if(/^\/\//.test(t))return`${location.protocol}${t}`;let r=F(e),n=F(t,r.href);return n.href}function U(e,t,r){l(e,e=>{if(e.selectorText)t(e);else if(e.href)try{U(e.styleSheet.cssRules,t,r)}catch(n){r&&r()}else if(e.media){let a=Array.from(e.media),i=a.some(e=>e.startsWith("screen")||e.startsWith("all")),l=a.some(e=>e.startsWith("print")||e.startsWith("speech"));(i||!l)&&U(e.cssRules,t,r)}else e.conditionText&&CSS.supports(e.conditionText)&&U(e.cssRules,t,r)})}let q=["background","border","border-color","border-bottom","border-left","border-right","border-top","outline","outline-color"],G=L?q.map(e=>{let t=RegExp(`${e}:\\s*(.*?)\\s*;`);return[e,t]}):null;function W(e,t){l(e,r=>{let n=e.getPropertyValue(r).trim();n&&t(r,n)});let r=e.cssText;r.includes("var(")&&(L?G.forEach(([e,n])=>{let a=r.match(n);if(a&&a[1]){let i=a[1].trim();t(e,i)}}):q.forEach(r=>{let n=e.getPropertyValue(r);n&&n.includes("var(")&&t(r,n)}))}let H=/url\((('.+?')|(".+?")|([^\)]*?))\)/g,j=/@import\s*(url\()?(('.+?')|(".+?")|([^\)]*?))\)?;?/g;function z(e){return e.replace(/^url\((.*)\)$/,"$1").trim().replace(/^"(.*)"$/,"$1").replace(/^'(.*)'$/,"$1")}function Q(e){let t=F(e);return`${t.origin}${t.pathname.replace(/\?.*$/,"").replace(/(\/)([^\/]+)$/i,"$1")}`}let X=/\/\*[\s\S]*?\*\//g,Y=/@font-face\s*{[^}]*}/g;function K({h:e,s:t,l:r,a:n=1}){if(0===t){let[a,i,l]=[r,r,r].map(e=>Math.round(255*e));return{r:a,g:l,b:i,a:n}}let s=(1-Math.abs(2*r-1))*t,o=s*(1-Math.abs(e/60%2-1)),u=r-s/2,[d,c,f]=(e<60?[s,o,0]:e<120?[o,s,0]:e<180?[0,s,o]:e<240?[0,o,s]:e<300?[o,0,s]:[s,0,o]).map(e=>Math.round((e+u)*255));return{r:d,g:c,b:f,a:n}}function Z({r:e,g:t,b:r,a:n=1}){let a=e/255,i=t/255,l=r/255,s=Math.max(a,i,l),o=Math.min(a,i,l),u=s-o,d=(s+o)/2;if(0===u)return{h:0,s:0,l:d,a:n};let c=(s===a?(i-l)/u%6:s===i?(l-a)/u+2:(a-i)/u+4)*60;return c<0&&(c+=360),{h:c,s:u/(1-Math.abs(2*d-1)),l:d,a:n}}function J(e,t=0){let r=e.toFixed(t);if(0===t)return r;let n=r.indexOf(".");if(n>=0){let a=r.match(/0+$/);if(a)return a.index===n+1?r.substring(0,n):r.substring(0,a.index)}return r}function ee(e){let{h:t,s:r,l:n,a}=e;return null!=a&&a<1?`hsla(${J(t)}, ${J(100*r)}%, ${J(100*n)}%, ${J(a,2)})`:`hsl(${J(t)}, ${J(100*r)}%, ${J(100*n)}%)`}let et=/^rgba?\([^\(\)]+\)$/,er=/^hsla?\([^\(\)]+\)$/,en=/^#[0-9a-f]+$/i;function ea(e){let t=e.trim().toLowerCase();if(t.match(et))return function e(t){let[r,n,a,i=1]=ei(t,el,es);return{r,g:n,b:a,a:i}}(t);if(t.match(er))return function e(t){let[r,n,a,i=1]=ei(t,eo,eu);return K({h:r,s:n,l:a,a:i})}(t);if(t.match(en))return function e(t){let r=t.substring(1);switch(r.length){case 3:case 4:{let[n,a,i]=[0,1,2].map(e=>parseInt(`${r[e]}${r[e]}`,16)),l=3===r.length?1:parseInt(`${r[3]}${r[3]}`,16)/255;return{r:n,g:a,b:i,a:l}}case 6:case 8:{let[s,o,u]=[0,2,4].map(e=>parseInt(r.substring(e,e+2),16)),d=6===r.length?1:parseInt(r.substring(6,8),16)/255;return{r:s,g:o,b:u,a:d}}}throw Error(`Unable to parse ${t}`)}(t);if(ed.has(t))return function e(t){let r=ed.get(t);return{r:r>>16&255,g:r>>8&255,b:r>>0&255,a:1}}(t);if(ec.has(t))return function e(t){let r=ec.get(t);return{r:r>>16&255,g:r>>8&255,b:r>>0&255,a:1}}(t);if("transparent"===e)return{r:0,g:0,b:0,a:0};throw Error(`Unable to parse ${e}`)}function ei(e,t,r){let n=function e(t){let r=[],n=0,a=!1,i=t.indexOf("(");t=t.substring(i+1,t.length-1);for(let l=0;l<t.length;l++){let s=t[l];s>="0"&&s<="9"||"."===s||"+"===s||"-"===s?a=!0:a&&(" "===s||","===s)?(r.push(t.substring(n,l)),a=!1,n=l+1):a||(n=l+1)}return a&&r.push(t.substring(n,t.length)),r}(e),a=Object.entries(r),i=n.map(e=>e.trim()).map((e,r)=>{let n,i=a.find(([t])=>e.endsWith(t));return(n=i?parseFloat(e.substring(0,e.length-i[0].length))/i[1]*t[r]:parseFloat(e),t[r]>1)?Math.round(n):n});return i}let el=[255,255,255,1],es={"%":100},eo=[360,1,1,1],eu={"%":100,deg:360,rad:2*Math.PI,turn:1},ed=new Map(Object.entries({aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgrey:11119017,darkgreen:25600,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,grey:8421504,green:32768,greenyellow:11403055,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgrey:13882323,lightgreen:9498256,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074})),ec=new Map(Object.entries({ActiveBorder:3906044,ActiveCaption:0,AppWorkspace:11184810,Background:6513614,ButtonFace:16777215,ButtonHighlight:15329769,ButtonShadow:10461343,ButtonText:0,CaptionText:0,GrayText:8355711,Highlight:11720703,HighlightText:0,InactiveBorder:16777215,InactiveCaption:16777215,InactiveCaptionText:0,InfoBackground:16514245,InfoText:0,Menu:16185078,MenuText:16777215,Scrollbar:11184810,ThreeDDarkShadow:0,ThreeDFace:12632256,ThreeDHighlight:16777215,ThreeDLightShadow:16777215,ThreeDShadow:0,Window:15527148,WindowFrame:11184810,WindowText:0,"-webkit-focus-ring-color":15046400}).map(([e,t])=>[e.toLowerCase(),t]));function ef(e,t,r,n,a){return(e-t)*(a-n)/(r-t)+n}function e$(e,t,r){return Math.min(r,Math.max(t,e))}function eh(e,t){let r=[];for(let n=0,a=e.length;n<a;n++){r[n]=[];for(let i=0,l=t[0].length;i<l;i++){let s=0;for(let o=0,u=e[0].length;o<u;o++)s+=e[n][o]*t[o][i];r[n][i]=s}}return r}function ep(e,t,r=0){let n=[],a;for(;a=e.exec(t);)n.push(a[r]);return n}function eg(e){let t=em.identity();return 0!==e.sepia&&(t=eh(t,em.sepia(e.sepia/100))),0!==e.grayscale&&(t=eh(t,em.grayscale(e.grayscale/100))),100!==e.contrast&&(t=eh(t,em.contrast(e.contrast/100))),100!==e.brightness&&(t=eh(t,em.brightness(e.brightness/100))),1===e.mode&&(t=eh(t,em.invertNHue())),t}let em={identity:()=>[[1,0,0,0,0],[0,1,0,0,0],[0,0,1,0,0],[0,0,0,1,0],[0,0,0,0,1]],invertNHue:()=>[[.333,-.667,-.667,0,1],[-.667,.333,-.667,0,1],[-.667,-.667,.333,0,1],[0,0,0,1,0],[0,0,0,0,1]],brightness:e=>[[e,0,0,0,0],[0,e,0,0,0],[0,0,e,0,0],[0,0,0,1,0],[0,0,0,0,1]],contrast(e){let t=(1-e)/2;return[[e,0,0,0,t],[0,e,0,0,t],[0,0,e,0,t],[0,0,0,1,0],[0,0,0,0,1]]},sepia:e=>[[.393+.607*(1-e),.769-.769*(1-e),.189-.189*(1-e),0,0],[.349-.349*(1-e),.686+.314*(1-e),.168-.168*(1-e),0,0],[.272-.272*(1-e),.534-.534*(1-e),.131+.869*(1-e),0,0],[0,0,0,1,0],[0,0,0,0,1]],grayscale:e=>[[.2126+.7874*(1-e),.7152-.7152*(1-e),.0722-.0722*(1-e),0,0],[.2126-.2126*(1-e),.7152+.2848*(1-e),.0722-.0722*(1-e),0,0],[.2126-.2126*(1-e),.7152-.7152*(1-e),.0722+.9278*(1-e),0,0],[0,0,0,1,0],[0,0,0,0,1]]};function eb(e){let t=1===e.mode;return e[t?"darkSchemeBackgroundColor":"lightSchemeBackgroundColor"]}function ey(e){let t=1===e.mode;return e[t?"darkSchemeTextColor":"lightSchemeTextColor"]}let e8=new Map,ex=new Map;function ek(e){if(ex.has(e))return ex.get(e);let t=ea(e),r=Z(t);return ex.set(e,r),r}let ew=["r","g","b","a"],e_=["mode","brightness","contrast","grayscale","sepia","darkSchemeBackgroundColor","darkSchemeTextColor","lightSchemeBackgroundColor","lightSchemeTextColor"];function eE(e,t,r,n,a){var i,l;let s;e8.has(r)?s=e8.get(r):(s=new Map,e8.set(r,s));let o,u=(i=e,l=t,o="",ew.forEach(e=>{o+=`${i[e]};`}),e_.forEach(e=>{o+=`${l[e]};`}),o);if(s.has(u))return s.get(u);let d=Z(e),c=null==n?null:ek(n),f=null==a?null:ek(a),$=r(d,c,f),{r:h,g:p,b:g,a:m}=K($),b=eg(t),[y,x,k]=function e([t,r,n],a){let i=eh(a,[[t/255],[r/255],[n/255],[1],[1]]);return[0,1,2].map(e=>e$(Math.round(255*i[e][0]),0,255))}([h,p,g],b),w=1===m?function e({r:t,g:r,b:n,a}){return`#${(null!=a&&a<1?[t,r,n,Math.round(255*a)]:[t,r,n]).map(e=>`${e<16?"0":""}${e.toString(16)}`).join("")}`}({r:y,g:x,b:k}):function e(t){let{r,g:n,b:a,a:i}=t;return null!=i&&i<1?`rgba(${J(r)}, ${J(n)}, ${J(a)}, ${J(i,2)})`:`rgb(${J(r)}, ${J(n)}, ${J(a)})`}({r:y,g:x,b:k,a:m});return s.set(u,w),w}function ev(e){return e}function eS(e,t){let r=eb(t),n=ey(t);return eE(e,t,e0,n,r)}function e0({h:e,s:t,l:r,a:n},a,i){let l=r<.5,s;s=l?r<.2||t<.12:t<.24||r>.8&&e>200&&e<280;let o=e,u=r;s&&(l?(o=a.h,u=a.s):(o=i.h,u=i.s));let d=ef(r,0,1,a.l,i.l);return{h:o,s:u,l:d,a:n}}function eC({h:e,s:t,l:r,a:n},a){let i=t<.12||r>.8&&e>200&&e<280;if(r<.5){let l=ef(r,0,.5,0,.4);if(i){let s=a.h,o=a.s;return{h:s,s:o,l:l,a:n}}return{h:e,s:t,l:l,a:n}}let u=ef(r,.5,1,.4,a.l);if(i){let d=a.h,c=a.s;return{h:d,s:c,l:u,a:n}}let f=e;return e>60&&e<180&&(f=e>120?ef(e,120,180,135,180):ef(e,60,120,60,105)),{h:f,s:t,l:u,a:n}}function e9(e,t){if(0===t.mode)return eS(e,t);let r=eb(t);return eE(e,{...t,mode:0},eC,r)}function e3(e){return ef(e,205,245,205,220)}function eT({h:e,s:t,l:r,a:n},a){let i=r<.2||t<.24,l=!i&&e>205&&e<245;if(r>.5){let s=ef(r,.5,1,.55,a.l);if(i){let o=a.h,u=a.s;return{h:o,s:u,l:s,a:n}}let d=e;return l&&(d=e3(e)),{h:d,s:t,l:s,a:n}}if(i){let c=a.h,f=a.s,$=ef(r,0,.5,a.l,.55);return{h:c,s:f,l:$,a:n}}let h=e,p;return l?(h=e3(e),p=ef(r,0,.5,a.l,Math.min(1,.55+.05))):p=ef(r,0,.5,a.l,.55),{h:h,s:t,l:p,a:n}}function eV(e,t){if(0===t.mode)return eS(e,t);let r=ey(t);return eE(e,{...t,mode:0},eT,r)}function eR({h:e,s:t,l:r,a:n},a,i){let l=e,s=t;(r<.2||t<.24)&&(r<.5?(l=a.h,s=a.s):(l=i.h,s=i.s));let o=ef(r,0,1,.5,.2);return{h:l,s:s,l:o,a:n}}function eA(e,t){if(0===t.mode)return eS(e,t);let r=ey(t),n=eb(t);return eE(e,{...t,mode:0},eR,r,n)}function e4(e,t){return e9(e,t)}function e6(e,t){return e9(e,t)}function eL(e){let t=[];return(e.mode===r.dark&&t.push("invert(100%) hue-rotate(180deg)"),100!==e.brightness&&t.push(`brightness(${e.brightness}%)`),100!==e.contrast&&t.push(`contrast(${e.contrast}%)`),0!==e.grayscale&&t.push(`grayscale(${e.grayscale}%)`),0!==e.sepia&&t.push(`sepia(${e.sepia}%)`),0===t.length)?null:t.join(" ")}(t=r||(r={}))[t.light=0]="light",t[t.dark=1]="dark";let e7={UI_GET_DATA:"ui-get-data",UI_GET_ACTIVE_TAB_INFO:"ui-get-active-tab-info",UI_SUBSCRIBE_TO_CHANGES:"ui-subscribe-to-changes",UI_UNSUBSCRIBE_FROM_CHANGES:"ui-unsubscribe-from-changes",UI_CHANGE_SETTINGS:"ui-change-settings",UI_SET_THEME:"ui-set-theme",UI_SET_SHORTCUT:"ui-set-shortcut",UI_TOGGLE_URL:"ui-toggle-url",UI_MARK_NEWS_AS_READ:"ui-mark-news-as-read",UI_LOAD_CONFIG:"ui-load-config",UI_APPLY_DEV_DYNAMIC_THEME_FIXES:"ui-apply-dev-dynamic-theme-fixes",UI_RESET_DEV_DYNAMIC_THEME_FIXES:"ui-reset-dev-dynamic-theme-fixes",UI_APPLY_DEV_INVERSION_FIXES:"ui-apply-dev-inversion-fixes",UI_RESET_DEV_INVERSION_FIXES:"ui-reset-dev-inversion-fixes",UI_APPLY_DEV_STATIC_THEMES:"ui-apply-dev-static-themes",UI_RESET_DEV_STATIC_THEMES:"ui-reset-dev-static-themes",UI_SAVE_FILE:"ui-save-file",UI_REQUEST_EXPORT_CSS:"ui-request-export-css",BG_CHANGES:"bg-changes",BG_ADD_CSS_FILTER:"bg-add-css-filter",BG_ADD_STATIC_THEME:"bg-add-static-theme",BG_ADD_SVG_FILTER:"bg-add-svg-filter",BG_ADD_DYNAMIC_THEME:"bg-add-dynamic-theme",BG_EXPORT_CSS:"bg-export-css",BG_UNSUPPORTED_SENDER:"bg-unsupported-sender",BG_CLEAN_UP:"bg-clean-up",BG_RELOAD:"bg-reload",BG_FETCH_RESPONSE:"bg-fetch-response",BG_UI_UPDATE:"bg-ui-update",BG_CSS_UPDATE:"bg-css-update",CS_COLOR_SCHEME_CHANGE:"cs-color-scheme-change",CS_FRAME_CONNECT:"cs-frame-connect",CS_FRAME_FORGET:"cs-frame-forget",CS_FRAME_FREEZE:"cs-frame-freeze",CS_FRAME_RESUME:"cs-frame-resume",CS_EXPORT_CSS_RESPONSE:"cs-export-css-response",CS_FETCH:"cs-fetch"},eD=0,eN=new Map,eI=new Map;async function e2(e){return new Promise((t,r)=>{let n=++eD;eN.set(n,t),eI.set(n,r),chrome.runtime.sendMessage({type:e7.CS_FETCH,data:e,id:n})})}async function eP(e,t,r){let n=await fetch(e,{cache:"force-cache",credentials:"omit",referrer:r});if(A&&"text/css"===t&&e.startsWith("moz-extension://")&&e.endsWith(".css"))return n;if(t&&!n.headers.get("Content-Type").startsWith(t))throw Error(`Mime type mismatch when loading ${e}`);if(!n.ok)throw Error(`Unable to load ${e} ${n.status} ${n.statusText}`);return n}async function eB(e,t){let r=await eP(e,t);return await e5(r)}async function e5(e){let t=await e.blob(),r=await new Promise(e=>{let r=new FileReader;r.onloadend=()=>e(r.result),r.readAsDataURL(t)});return r}blz_chrome_runtime_onMessage_addListener(({type:e,data:t,error:r,id:n})=>{if(e===e7.BG_FETCH_RESPONSE){let a=eN.get(n),i=eI.get(n);eN.delete(n),eI.delete(n),r?i&&i(r):a&&a(t)}});let e1=new class e{constructor(){this.queue=[],this.timerId=null,this.frameDuration=1e3/60}addToQueue(e){this.queue.push(e),this.startQueue()}stopQueue(){null!==this.timerId&&(cancelAnimationFrame(this.timerId),this.timerId=null),this.queue=[]}startQueue(){!this.timerId&&(this.timerId=requestAnimationFrame(()=>{this.timerId=null;let e=Date.now(),t;for(;t=this.queue.shift();)if(t(),Date.now()-e>=this.frameDuration){this.startQueue();break}}))}};async function eM(e){return new Promise(async(t,r)=>{let n;if(e.startsWith("data:"))n=e;else try{n=await eF(e)}catch(i){r(i)}try{let l=await eO(n);e1.addToQueue(()=>{t({src:e,dataURL:n,width:l.naturalWidth,height:l.naturalHeight,...function e(t){eU||((eU=document.createElement("canvas")).width=1024,eU.height=1024,(eq=eU.getContext("2d")).imageSmoothingEnabled=!1);let{naturalWidth:r,naturalHeight:n}=t;if(0===n||0===r)return a(`logWarn(Image is empty ${t.currentSrc})`),null;if(r*n*4>5242880)return{isDark:!1,isLight:!1,isTransparent:!1,isLarge:!1,isTooLarge:!0};let i=r*n,l=Math.min(1,Math.sqrt(1024/i)),s=Math.ceil(r*l),o=Math.ceil(n*l);eq.clearRect(0,0,s,o),eq.drawImage(t,0,0,r,n,0,0,s,o);let u=eq.getImageData(0,0,s,o),d=u.data,c=0,f=0,$=0,h,p,g,m,b,y,x,k;for(g=0;g<o;g++)for(p=0;p<s;p++)m=d[(h=4*(g*s+p))+0]/255,b=d[h+1]/255,y=d[h+2]/255,(x=d[h+3]/255)<.05?c++:((k=.2126*m+.7152*b+.0722*y)<.4&&f++,k>.7&&$++);let w=s*o,_=w-c;return{isDark:f/_>=.7,isLight:$/_>=.7,isTransparent:c/w>=.1,isLarge:i>=48e4,isTooLarge:!1}}(l)})})}catch(s){r(s)}})}async function eF(e){let t=new URL(e);return t.origin===location.origin?await eB(e):await e2({url:e,responseType:"data-url"})}async function eO(e){return new Promise((t,r)=>{let n=new Image;n.onload=()=>t(n),n.onerror=()=>r(`Unable to load image ${e}`),n.src=e})}let eU,eq;function eG({dataURL:e,width:t,height:r},n){var a,i;let l=(a=n,(i=eg(a)).slice(0,4).map(e=>e.map(e=>e.toFixed(3)).join(" ")).join(" ")),s=`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="${t}" height="${r}"><defs><filter id="darkreader-image-filter"><feColorMatrix type="matrix" values="${l}" /></filter></defs><image width="${t}" height="${r}" filter="url(#darkreader-image-filter)" xlink:href="${e}" /></svg>`;return`data:image/svg+xml;base64,${btoa(s)}`}function eW(e,t){return Boolean(e&&e.getPropertyPriority(t))}function eH(e,t,r,n,a,i){var l,s,o,u,d,c,f,$,h;if(e.startsWith("--")){let p=(l=n,s=e,o=t,u=r,d=a,c=i,l.getModifierForVariable({varName:s,sourceValue:o,rule:u,ignoredImgSelectors:d,isCancelled:c}));if(p)return{property:e,value:p,important:eW(r.style,e),sourceValue:t}}else if(t.includes("var(")){let g=(f=n,$=e,h=t,f.getModifierForVarDependant($,h));if(g)return{property:e,value:g,important:eW(r.style,e),sourceValue:t}}else if(e.includes("color")&&"-webkit-print-color-adjust"!==e||"fill"===e||"stroke"===e||"stop-color"===e){let m=function e(t,r){if(eQ.has(r.toLowerCase()))return r;try{let n=eY(r);if(t.includes("background"))return e=>e9(n,e);if(t.includes("border")||t.includes("outline"))return e=>eA(n,e);return e=>eV(n,e)}catch(a){return null}}(e,t);if(m)return{property:e,value:m,important:eW(r.style,e),sourceValue:t}}else if("background-image"===e||"list-style-image"===e){let b=tt(t,r,a,i);if(b)return{property:e,value:b,important:eW(r.style,e),sourceValue:t}}else if(e.includes("shadow")){let y=tr(t);if(y)return{property:e,value:y,important:eW(r.style,e),sourceValue:t}}return null}function ej(e){let t,r;if("auto"===e.selectionColor)t=e9({r:0,g:96,b:212},{...e,grayscale:0}),r=eV({r:255,g:255,b:255},{...e,grayscale:0});else{let n=ea(e.selectionColor),a=Z(n);t=e.selectionColor,r=a.l<.5?"#FFF":"#000"}return{backgroundColorSelection:t,foregroundColorSelection:r}}function ez(e,{strict:t}){let r=[];return r.push(`html, body, ${t?"body :not(iframe)":"body > :not(iframe)"} {`),r.push(`    background-color: ${e9({r:255,g:255,b:255},e)} !important;`),r.push(`    border-color: ${eA({r:64,g:64,b:64},e)} !important;`),r.push(`    color: ${eV({r:0,g:0,b:0},e)} !important;`),r.push("}"),r.join("\n")}let eQ=new Set(["inherit","transparent","initial","currentcolor","none","unset"]),eX=new Map;function eY(e){if(e=e.trim(),eX.has(e))return eX.get(e);let t=ea(e);return eX.set(e,t),t}function eK(e){try{return eY(e)}catch(t){return null}}let eZ=/[\-a-z]+gradient\(([^\(\)]*(\(([^\(\)]*(\(.*?\)))*[^\(\)]*\))){0,15}[^\(\)]*\)/g,eJ=new Map,te=new Map;function tt(e,t,r,i){try{let l=ep(eZ,e),s=ep(H,e);if(0===s.length&&0===l.length)return e;let o=t=>{let r=0;return t.map(t=>{let n=e.indexOf(t,r);return r=n+t.length,{match:t,index:n}})},u=o(s).map(e=>({type:"url",...e})).concat(o(l).map(e=>({type:"gradient",...e}))).sort((e,t)=>e.index-t.index),d=e=>{let t=e.match(/^(.*-gradient)\((.*)\)$/),r=t[1],n=t[2],a=/^(from|color-stop|to)\(([^\(\)]*?,\s*)?(.*?)\)$/,i=ep(/([^\(\),]+(\([^\(\)]*(\([^\(\)]*\)*[^\(\)]*)?\))?[^\(\),]*),?/g,n,1).map(e=>{e=e.trim();let t=eK(e);if(t)return e=>{var r,n;return r=t,n=e,e9(r,n)};let r=e.lastIndexOf(" ");if(t=eK(e.substring(0,r)))return n=>{var a,i;return`${a=t,i=n,e9(a,i)} ${e.substring(r+1)}`};let n=e.match(a);return n&&(t=eK(n[3]))?e=>{var r,a;return`${n[1]}(${n[2]?`${n[2]}, `:""}${r=t,a=e,e9(r,a)})`}:()=>e});return e=>`${r}(${i.map(t=>t(e)).join(", ")})`},c=e=>{var n;if(function e(t,r){if(!t||0===r.length)return!1;if(r.some(e=>"*"===e))return!0;let n=t.split(/,\s*/g);for(let a=0;a<r.length;a++){let i=r[a];if(n.some(e=>e===i))return!0}return!1}(t.selectorText,r))return null;let l=z(e),{parentStyleSheet:s}=t,o=s&&s.href?Q(s.href):(null===(n=s.ownerNode)||void 0===n?void 0:n.baseURI)||location.origin;l=O(o,l);let u=`url("${l}")`;return async e=>{let t;if(eJ.has(l))t=eJ.get(l);else try{if(te.has(l)){let r=te.get(l);if(!(t=await new Promise(e=>r.push(e))))return null}else te.set(l,[]),t=await eM(l),eJ.set(l,t),te.get(l).forEach(e=>e(t)),te.delete(l);if(i())return null}catch(n){return a(n),te.has(l)&&(te.get(l).forEach(e=>e(null)),te.delete(l)),u}let s=f(t,e)||u;return s}},f=(e,t)=>{let{isDark:r,isLight:a,isTransparent:i,isLarge:l,isTooLarge:s,width:o}=e,u;if(s)u=`url("${e.src}")`;else if(r&&i&&1===t.mode&&!l&&o>2){n(`Inverting dark image ${e.src}`);let d=eG(e,{...t,sepia:e$(t.sepia+10,0,100)});u=`url("${d}")`}else if(a&&!i&&1===t.mode){if(l)u="none";else{n(`Dimming light image ${e.src}`);let c=eG(e,t);u=`url("${c}")`}}else if(0===t.mode&&a&&!l){n(`Applying filter to image ${e.src}`);let f=eG(e,{...t,brightness:e$(t.brightness-10,5,200),sepia:e$(t.sepia+10,0,100)});u=`url("${f}")`}else u=null;return u},$=[],h=0;return u.forEach(({match:t,type:r,index:n},a)=>{let i=h,l=n+t.length;h=l,$.push(()=>e.substring(i,n)),$.push("url"===r?c(t):d(t)),a===u.length-1&&$.push(()=>e.substring(l))}),e=>{let t=$.filter(Boolean).map(t=>t(e));return t.some(e=>e instanceof Promise)?Promise.all(t).then(e=>e.join("")):t.join("")}}catch(p){return null}}function tr(e){try{let t=0,r=ep(/(^|\s)(?!calc)([a-z]+\(.+?\)|#[0-9a-f]+|[a-z]+)(.*?(inset|outset)?($|,))/gi,e,2),n=r.map((n,a)=>{let i=t,l=e.indexOf(n,t),s=l+n.length;t=s;let o=eK(n);return o?t=>{var n,u;return`${e.substring(i,l)}${n=o,u=t,e9(n,u)}${a===r.length-1?e.substring(s):""}`}:()=>e.substring(i,s)});return e=>n.map(t=>t(e)).join("")}catch(a){return null}}let tn=new class e{constructor(){this.varTypes=new Map,this.rulesQueue=[],this.definedVars=new Set,this.varRefs=new Map,this.unknownColorVars=new Set,this.unknownBgVars=new Set,this.undefinedVars=new Set,this.initialVarTypes=new Map,this.changedTypeVars=new Set,this.typeChangeSubscriptions=new Map,this.unstableVarValues=new Map}clear(){this.varTypes.clear(),this.rulesQueue.splice(0),this.definedVars.clear(),this.varRefs.clear(),this.unknownColorVars.clear(),this.unknownBgVars.clear(),this.undefinedVars.clear(),this.initialVarTypes.clear(),this.changedTypeVars.clear(),this.typeChangeSubscriptions.clear(),this.unstableVarValues.clear()}isVarType(e,t){return this.varTypes.has(e)&&(this.varTypes.get(e)&t)>0}addRulesForMatching(e){this.rulesQueue.push(e)}matchVariablesAndDependants(){this.changedTypeVars.clear(),this.initialVarTypes=new Map(this.varTypes),this.collectRootVariables(),this.collectVariablesAndVarDep(this.rulesQueue),this.rulesQueue.splice(0),this.collectRootVarDependants(),this.varRefs.forEach((e,t)=>{e.forEach(e=>{this.varTypes.has(t)&&this.resolveVariableType(e,this.varTypes.get(t))})}),this.unknownColorVars.forEach(e=>{this.unknownBgVars.has(e)?(this.unknownColorVars.delete(e),this.unknownBgVars.delete(e),this.resolveVariableType(e,1)):this.isVarType(e,7)?this.unknownColorVars.delete(e):this.undefinedVars.add(e)}),this.unknownBgVars.forEach(e=>{let t=null!=this.findVarRef(e,e=>this.unknownColorVars.has(e)||this.isVarType(e,6));t?this.itarateVarRefs(e,e=>{this.resolveVariableType(e,1)}):this.isVarType(e,9)?this.unknownBgVars.delete(e):this.undefinedVars.add(e)}),this.changedTypeVars.forEach(e=>{this.typeChangeSubscriptions.has(e)&&this.typeChangeSubscriptions.get(e).forEach(e=>{e()})}),this.changedTypeVars.clear()}getModifierForVariable(e){return t=>{let{varName:r,sourceValue:n,rule:a,ignoredImgSelectors:i,isCancelled:l}=e,s=()=>{let e=[],s=(a,i,l)=>{if(!this.isVarType(r,a))return;let s=i(r),o;if(t$(n)){if(th(n)){let u=tb(n,this.unstableVarValues);u||(u=1===a?"#ffffff":"#000000"),o=l(u,t)}else o=ts(n,e=>i(e),e=>l(e,t))}else o=l(n,t);e.push({property:s,value:o})};if(s(1,to,tp),s(2,tu,tg),s(4,td,tm),this.isVarType(r,8)){let o=tc(r),u=n;t$(n)&&(u=ts(n,e=>to(e),e=>tp(e,t)));let d=tt(u,a,i,l);u="function"==typeof d?d(t):d,e.push({property:o,value:u})}return e},o=new Set,u=e=>{let t=()=>{let t=s();e(t)};o.add(t),this.subscribeForVarTypeChange(r,t)},d=()=>{o.forEach(e=>{this.unsubscribeFromVariableTypeChanges(r,e)})};return{declarations:s(),onTypeChange:{addListener:u,removeListeners:d}}}}getModifierForVarDependant(e,t){if(t.match(/^\s*(rgb|hsl)a?\(/)){let r=e.startsWith("background"),n="color"===e||"caret-color"===e;return e=>{let a=tb(t,this.unstableVarValues);return a||(a=r?"#ffffff":"#000000"),(r?tp:n?tg:tm)(a,e)}}if("background-color"===e)return e=>ts(t,e=>to(e),t=>tp(t,e));if("color"===e||"caret-color"===e)return e=>ts(t,e=>tu(e),t=>tg(t,e));if("background"===e||"background-image"===e||"box-shadow"===e)return r=>{let n=new Set,a=()=>{let a=ts(t,e=>this.isVarType(e,1)?to(e):this.isVarType(e,8)?tc(e):(n.add(e),e),e=>tp(e,r));if("box-shadow"===e){let i=tr(a);return i(r)||a}return a},i=a();return n.size>0?new Promise(e=>{let t=n.values().next().value,r=()=>{this.unsubscribeFromVariableTypeChanges(t,r);let n=a();e(n)};this.subscribeForVarTypeChange(t,r)}):i};if(e.startsWith("border")||e.startsWith("outline")){if(t.endsWith(")")){let a=t.match(/((rgb|hsl)a?)\(/);if(a){let i=a.index;return e=>{let r=tb(t,this.unstableVarValues);if(!r)return t;let n=t.substring(0,i),a=t.substring(i,t.length),l=tb(a,this.unstableVarValues),s=tm(l,e);return`${n}${s}`}}}return e=>ts(t,e=>td(e),t=>tg(t,e))}return null}subscribeForVarTypeChange(e,t){this.typeChangeSubscriptions.has(e)||this.typeChangeSubscriptions.set(e,new Set);let r=this.typeChangeSubscriptions.get(e);r.has(t)||r.add(t)}unsubscribeFromVariableTypeChanges(e,t){this.typeChangeSubscriptions.has(e)&&this.typeChangeSubscriptions.get(e).delete(t)}collectVariablesAndVarDep(e){e.forEach(e=>{U(e,e=>{e.style&&W(e.style,(e,t)=>{tf(e)&&this.inspectVariable(e,t),t$(t)&&this.inspectVarDependant(e,t)})})})}collectRootVariables(){W(document.documentElement.style,(e,t)=>{tf(e)&&this.inspectVariable(e,t)})}inspectVariable(e,t){if(this.unstableVarValues.set(e,t),t$(t)&&th(t)&&(this.unknownColorVars.add(e),this.definedVars.add(e)),this.definedVars.has(e))return;this.definedVars.add(e);let r=eK(t);r?this.unknownColorVars.add(e):(t.includes("url(")||t.includes("linear-gradient(")||t.includes("radial-gradient("))&&this.resolveVariableType(e,8)}resolveVariableType(e,t){let r=this.initialVarTypes.get(e)||0,n=this.varTypes.get(e)||0,a=n|t;this.varTypes.set(e,a),(a!==r||this.undefinedVars.has(e))&&(this.changedTypeVars.add(e),this.undefinedVars.delete(e)),this.unknownColorVars.delete(e),this.unknownBgVars.delete(e)}collectRootVarDependants(){W(document.documentElement.style,(e,t)=>{t$(t)&&this.inspectVarDependant(e,t)})}inspectVarDependant(e,t){tf(e)?this.iterateVarDeps(t,t=>{this.varRefs.has(e)||this.varRefs.set(e,new Set),this.varRefs.get(e).add(t)}):"background-color"===e||"box-shadow"===e?this.iterateVarDeps(t,e=>this.resolveVariableType(e,1)):"color"===e||"caret-color"===e?this.iterateVarDeps(t,e=>this.resolveVariableType(e,2)):e.startsWith("border")||e.startsWith("outline")?this.iterateVarDeps(t,e=>this.resolveVariableType(e,4)):("background"===e||"background-image"===e)&&this.iterateVarDeps(t,e=>{if(this.isVarType(e,9))return;let t=null!=this.findVarRef(e,e=>this.unknownColorVars.has(e)||this.isVarType(e,6));this.itarateVarRefs(e,e=>{t?this.resolveVariableType(e,1):this.unknownBgVars.add(e)})})}iterateVarDeps(e,t){var r,n;let a=new Set;r=e,n=e=>a.add(e),ts(r,e=>(n(e),e)),a.forEach(e=>t(e))}findVarRef(e,t,r=new Set){if(r.has(e))return null;r.add(e);let n=t(e);if(n)return e;let a=this.varRefs.get(e);if(!a||0===a.size)return null;for(let i of a){let l=this.findVarRef(i,t,r);if(l)return l}return null}itarateVarRefs(e,t){this.findVarRef(e,e=>(t(e),!1))}setOnRootVariableChange(e){this.onRootVariableDefined=e}putRootVars(e,t){let r=e.sheet;r.cssRules.length>0&&r.deleteRule(0);let n=new Map;W(document.documentElement.style,(e,r)=>{tf(e)&&(this.isVarType(e,1)&&n.set(to(e),tp(r,t)),this.isVarType(e,2)&&n.set(tu(e),tg(r,t)),this.isVarType(e,4)&&n.set(td(e),tm(r,t)),this.subscribeForVarTypeChange(e,this.onRootVariableDefined))});let a=[];for(let[i,l]of(a.push(":root {"),n))a.push(`    ${i}: ${l};`);a.push("}");let s=a.join("\n");r.insertRule(s)}};function ta(e,t=0){let r=e.indexOf("var(",t);if(r>=0){let n=function e(t,r=0){let n=t.length,a=0,i=-1;for(let l=r;l<n;l++)if(0===a){let s=t.indexOf("(",l);if(s<0)break;i=s,a++,l=s}else{let o=t.indexOf(")",l);if(o<0)break;let u=t.indexOf("(",l);if(u<0||o<u){if(0==--a)return{start:i,end:o+1};l=o}else a++,l=u}return null}(e,r+3);return n?{start:r,end:n.end}:null}}function ti(e,t){let r=function e(t){let r=[],n=0,a;for(;a=ta(t,n);){let{start:i,end:l}=a;r.push({start:i,end:l,value:t.substring(i,l)}),n=a.end+1}return r}(e),n=r.length;if(0===n)return e;let a=e.length,i=r.map(e=>t(e.value)),l=[];l.push(e.substring(0,r[0].start));for(let s=0;s<n;s++){l.push(i[s]);let o=r[s].end,u=s<n-1?r[s+1].start:a;l.push(e.substring(o,u))}return l.join("")}function tl(e){let t=e.indexOf(","),r,n;return t>=0?(r=e.substring(4,t).trim(),n=e.substring(t+1,e.length-1).trim()):(r=e.substring(4,e.length-1).trim(),n=""),{name:r,fallback:n}}function ts(e,t,r){let n=e=>{let{name:n,fallback:a}=tl(e),i=t(n);if(!a)return`var(${i})`;let l;return`var(${i}, ${l=t$(a)?ts(a,t,r):r?r(a):a})`};return ti(e,n)}function to(e){return`--darkreader-bg${e}`}function tu(e){return`--darkreader-text${e}`}function td(e){return`--darkreader-border${e}`}function tc(e){return`--darkreader-bgimg${e}`}function tf(e){return e.startsWith("--")}function t$(e){return e.includes("var(")}function th(e){return e.match(/^\s*(rgb|hsl)a?\(/)}function tp(e,t){let r=eK(e);return r?e9(r,t):e}function tg(e,t){let r=eK(e);return r?eV(r,t):e}function tm(e,t){let r=eK(e);return r?eA(r,t):e}function tb(e,t,r=new Set){let n=!1,a=e=>{let{name:a,fallback:i}=tl(e);if(r.has(a))return n=!0,null;r.add(a);let l=t.get(a)||i,s=null;return(l&&(s=t$(l)?tb(l,t,r):l),s)?s:(n=!0,null)},i=ti(e,a);return n?null:i}let ty={"background-color":{customProp:"--darkreader-inline-bgcolor",cssProp:"background-color",dataAttr:"data-darkreader-inline-bgcolor"},"background-image":{customProp:"--darkreader-inline-bgimage",cssProp:"background-image",dataAttr:"data-darkreader-inline-bgimage"},"border-color":{customProp:"--darkreader-inline-border",cssProp:"border-color",dataAttr:"data-darkreader-inline-border"},"border-bottom-color":{customProp:"--darkreader-inline-border-bottom",cssProp:"border-bottom-color",dataAttr:"data-darkreader-inline-border-bottom"},"border-left-color":{customProp:"--darkreader-inline-border-left",cssProp:"border-left-color",dataAttr:"data-darkreader-inline-border-left"},"border-right-color":{customProp:"--darkreader-inline-border-right",cssProp:"border-right-color",dataAttr:"data-darkreader-inline-border-right"},"border-top-color":{customProp:"--darkreader-inline-border-top",cssProp:"border-top-color",dataAttr:"data-darkreader-inline-border-top"},"box-shadow":{customProp:"--darkreader-inline-boxshadow",cssProp:"box-shadow",dataAttr:"data-darkreader-inline-boxshadow"},color:{customProp:"--darkreader-inline-color",cssProp:"color",dataAttr:"data-darkreader-inline-color"},fill:{customProp:"--darkreader-inline-fill",cssProp:"fill",dataAttr:"data-darkreader-inline-fill"},stroke:{customProp:"--darkreader-inline-stroke",cssProp:"stroke",dataAttr:"data-darkreader-inline-stroke"},"outline-color":{customProp:"--darkreader-inline-outline",cssProp:"outline-color",dataAttr:"data-darkreader-inline-outline"},"stop-color":{customProp:"--darkreader-inline-stopcolor",cssProp:"stop-color",dataAttr:"data-darkreader-inline-stopcolor"}},t8=Object.values(ty),tx={};t8.forEach(({cssProp:e,customProp:t})=>tx[t]=e);let tk=["style","fill","stop-color","stroke","bgcolor","color"],tw=tk.map(e=>`[${e}]`).join(", ");function t_(){return t8.map(({dataAttr:e,customProp:t,cssProp:r})=>`[${e}] {
  ${r}: var(${t}) !important;
}`).join("\n")}let tE=new Map,tv=new Map;function tS(e,t,r){tE.has(e)&&(tE.get(e).disconnect(),tv.get(e).disconnect());let n=new WeakSet;function a(e){(function e(t){let r=[];return t instanceof Element&&t.matches(tw)&&r.push(t),(t instanceof Element||D&&t instanceof ShadowRoot||t instanceof Document)&&s(r,t.querySelectorAll(tw)),r})(e).forEach(e=>{!n.has(e)&&(n.add(e),t(e))}),f(e,a=>{!n.has(e)&&(n.add(e),r(a.shadowRoot),tS(a.shadowRoot,t,r))})}let l=_(e,{onMinorMutations({additions:e}){e.forEach(e=>a(e))},onHugeMutations(){a(e)}});tE.set(e,l);let u=0,d=null,c=o({seconds:10}),$=o({seconds:2}),h=[],p=null,g=i(e=>{e.forEach(e=>{tk.includes(e.attributeName)&&t(e.target)})}),m=new MutationObserver(e=>{if(p){h.push(...e);return}u++;let t=Date.now();if(null==d)d=t;else if(u>=50){if(t-d<c){p=setTimeout(()=>{d=null,u=0,p=null;let e=h;h=[],g(e)},$),h.push(...e);return}d=t,u=1}g(e)});m.observe(e,{attributes:!0,attributeFilter:tk.concat(t8.map(({dataAttr:e})=>e)),subtree:!0}),tv.set(e,m)}let t0=new WeakMap,tC=["brightness","contrast","grayscale","sepia","mode"];function t9(e,t){return tk.map(t=>`${t}="${e.getAttribute(t)}"`).concat(tC.map(e=>`${e}="${t[e]}"`)).join(" ")}function t3(e,t,r,n){let a=t9(e,t);if(a===t0.get(e))return;let i=new Set(Object.keys(ty));function s(r,a,l){let{customProp:s,dataAttr:o}=ty[r],u=eH(a,l,{},tn,n,null);if(!u)return;let d=u.value;"function"==typeof d&&(d=d(t)),e.style.setProperty(s,d),e.hasAttribute(o)||e.setAttribute(o,""),i.delete(r)}if(r.length>0&&function e(t,r){for(let n=0,a=r.length;n<a;n++){let i=r[n];if(t.matches(i))return!0}return!1}(e,r)){i.forEach(t=>{e.removeAttribute(ty[t].dataAttr)});return}if(e.hasAttribute("bgcolor")){let o=e.getAttribute("bgcolor");(o.match(/^[0-9a-f]{3}$/i)||o.match(/^[0-9a-f]{6}$/i))&&(o=`#${o}`),s("background-color","background-color",o)}if(e.hasAttribute("color")&&"mask-icon"!==e.rel){let u=e.getAttribute("color");(u.match(/^[0-9a-f]{3}$/i)||u.match(/^[0-9a-f]{6}$/i))&&(u=`#${u}`),s("color","color",u)}if(e instanceof SVGElement){if(e.hasAttribute("fill")){let d=e.getAttribute("fill");if("none"!==d){if(e instanceof SVGTextElement)s("fill","color",d);else{let c=()=>{let{width:t,height:r}=e.getBoundingClientRect();s("fill",t>32||r>32?"background-color":"color",d)};m()?c():y(c)}}}e.hasAttribute("stop-color")&&s("stop-color","background-color",e.getAttribute("stop-color"))}if(e.hasAttribute("stroke")){let f=e.getAttribute("stroke");s("stroke",e instanceof SVGLineElement||e instanceof SVGTextElement?"border-color":"color",f)}e.style&&W(e.style,(t,r)=>{if(!("background-image"===t&&r.includes("url"))){if(ty.hasOwnProperty(t))s(t,t,r);else{let n=tx[t];!n||e.style.getPropertyValue(n)||e.hasAttribute(n)||e.style.setProperty(t,"")}}}),e.style&&e instanceof SVGTextElement&&e.style.fill&&s("fill","color",e.style.getPropertyValue("fill")),l(i,t=>{e.removeAttribute(ty[t].dataAttr)}),t0.set(e,t9(e,t))}let tT="theme-color",tV=`meta[name="${tT}"]`,tR=null,tA=null;function t4(e,t){tR=tR||e.content;try{let r=ea(tR);e.content=e9(r,t)}catch(n){}}let t6=["mode","brightness","contrast","grayscale","sepia","darkSchemeBackgroundColor","darkSchemeTextColor","lightSchemeBackgroundColor","lightSchemeTextColor"],tL=function e(){let t=[],r=null;function n(){let e;for(;e=t.shift();)e();r=null}return{add:function e(a){t.push(a),r||(r=requestAnimationFrame(n))},cancel:function e(){t.splice(0),cancelAnimationFrame(r),r=null}}}();function t7(){let e=0,t=new Set,r=new Map,n=new Set,a=null,i=!1,l=!1;return{modifySheet:function s(o){var u;let d=o.sourceCSSRules,{theme:c,ignoreImageAnalysis:f,force:$,prepareSheet:h,isAsyncCancelled:p}=o,g=0===r.size,m=new Set(r.keys()),b=(u=c,t6.map(e=>`${e}:${u[e]}`).join(";")),y=b!==a;i&&(l=!0);let x=[];if(U(d,e=>{let n=e.cssText,a=!1;if(m.delete(n),e.parentRule instanceof CSSMediaRule&&(n+=`;${e.parentRule.media.mediaText}`),t.has(n)||(t.add(n),a=!0),a)g=!0;else{x.push(r.get(n));return}let i=[];e.style&&W(e.style,(t,r)=>{let n=eH(t,r,e,tn,f,p);n&&i.push(n)});let l=null;if(i.length>0){let s=e.parentRule;l={selector:e.selectorText,declarations:i,parentRule:s},x.push(l)}r.set(n,l)},()=>{i=!0}),m.forEach(e=>{t.delete(e),r.delete(e)}),a=b,!$&&!g&&!y)return;function k(e,t,r){let{selector:n,declarations:a}=r,i=e=>{let{property:t,value:r,important:n,sourceValue:a}=e;return`${t}: ${null==r?a:r}${n?" !important":""};`},l="";a.forEach(e=>{l+=`${i(e)} `});let s=`${n} { ${l} }`;e.insertRule(s,t)}e++;let w=new Map,_=new Map,E=0,v=0,S={rule:null,rules:[],isGroup:!0},C=new WeakMap;n.forEach(e=>e()),n.clear(),x.filter(e=>e).forEach(({selector:t,declarations:r,parentRule:a})=>{let i=function e(t){if(null==t)return S;if(C.has(t))return C.get(t);let r={rule:t,rules:[],isGroup:!0};C.set(t,r);let n=e(t.parentRule);return n.rules.push(r),r}(a),l={selector:t,declarations:[],isGroup:!1},s=l.declarations;function o(t,r,n,a){let i=++E,l={property:t,value:null,important:n,asyncKey:i,sourceValue:a};s.push(l);let o=e;r.then(t=>{!(!t||p())&&o===e&&(l.value=t,tL.add(()=>{!p()&&o===e&&function e(t){let{rule:r,target:n,index:a}=w.get(t);n.deleteRule(a),k(n,a,r),w.delete(t)}(i)}))})}i.rules.push(l),r.forEach(({property:t,value:r,important:a,sourceValue:i})=>{if("function"==typeof r){let l=r(c);l instanceof Promise?o(t,l,a,i):t.startsWith("--")?function t(r,a,i,l){let{declarations:u,onTypeChange:d}=a,c=++v,f=e,$=s.length,h=[];if(0===u.length){let g={property:r,value:l,important:i,sourceValue:l,varKey:c};s.push(g),h=[g]}u.forEach(e=>{if(e.value instanceof Promise)o(e.property,e.value,i,l);else{let t={property:e.property,value:e.value,important:i,sourceValue:l,varKey:c};s.push(t),h.push(t)}}),d.addListener(t=>{if(p()||f!==e)return;let r=t.map(e=>({property:e.property,value:e.value,important:i,sourceValue:l,varKey:c})),n=s.indexOf(h[0],$);s.splice(n,h.length,...r),h=r,function e(t){let{rule:r,target:n,index:a}=_.get(t);n.deleteRule(a),k(n,a,r)}(c)}),n.add(()=>d.removeListeners())}(t,l,a,i):s.push({property:t,value:l,important:a,sourceValue:i})}else s.push({property:t,value:r,important:a,sourceValue:i})})});let T=h();!function e(t,r,n){t.rules.forEach(t=>{if(t.isGroup){let a=function e(t,r){let{rule:n}=t;if(n instanceof CSSMediaRule){let{media:a}=n,i=r.cssRules.length;return r.insertRule(`@media ${a.mediaText} {}`,i),r.cssRules[i]}return r}(t,r);e(t,a,n)}else n(t,r)})}(S,T,(e,t)=>{let r=t.cssRules.length;e.declarations.forEach(({asyncKey:n,varKey:a})=>{null!=n&&w.set(n,{rule:e,target:t,index:r}),null!=a&&_.set(a,{rule:e,target:t,index:r})}),k(t,r,e)})},shouldRebuildStyle:function e(){return i&&!l}}}function tD(e){return(e instanceof HTMLStyleElement||e instanceof SVGStyleElement||e instanceof HTMLLinkElement&&e.rel&&e.rel.toLowerCase().includes("stylesheet")&&!e.disabled)&&!e.classList.contains("darkreader")&&"print"!==e.media.toLowerCase()&&!e.classList.contains("stylus")}function tN(e,t=[],r=!0){return tD(e)?t.push(e):(e instanceof Element||D&&e instanceof ShadowRoot||e===document)&&(l(e.querySelectorAll('style, link[rel*="stylesheet" i]:not([disabled])'),e=>tN(e,t,!1)),r&&f(e,e=>tN(e.shadowRoot,t,!1))),t}let tI=new WeakSet,t2=new WeakSet,tP=!1;document.addEventListener("__darkreader__inlineScriptsAllowed",()=>{tP=!0});let tB=0,t5=new Map;async function t1(e,t){return new Promise((r,n)=>{let a=()=>{e.removeEventListener("load",i),e.removeEventListener("error",l),t5.delete(t)},i=()=>{a(),r()},l=()=>{a(),n(`Linkelement ${t} couldn't be loaded. ${e.href}`)};t5.set(t,()=>{a(),n()}),e.addEventListener("load",i),e.addEventListener("error",l),e.href||l()})}function tM(e){return z(e.substring(7).trim().replace(/;$/,""))}async function tF(e){return e.startsWith("data:")?await (await fetch(e)).text():await e2({url:e,responseType:"text",mimeType:"text/css",origin:window.location.origin})}async function tO(e,t,r=new Map){var n,a,i,l;e=(i=e=(a=e=(n=e).replace(X,"")).replace(Y,""),l=t,i.replace(H,e=>{let t=z(e);return`url("${O(l,t)}")`}));let s=ep(j,e);for(let o of s){let u=tM(o),d=O(t,u),c;if(r.has(d))c=r.get(d);else try{c=await tF(d),r.set(d,c),c=await tO(c,Q(d),r)}catch(f){c=""}e=e.split(o).join(c)}return e=e.trim()}let tU=[],tq,tG=new Map,tW;function tH(e){I&&l(e.querySelectorAll(":not(:defined)"),e=>{let t=e.tagName.toLowerCase();if(!t.includes("-")){let r=e.getAttribute("is");if(!r)return;t=r}tG.has(t)||(tG.set(t,new Set),tX(t).then(()=>{if(tW){let e=tG.get(t);tG.delete(t),tW(Array.from(e))}})),tG.get(t).add(e)})}let tj=!1;document.addEventListener("__darkreader__inlineScriptsAllowed",()=>{tj=!0});let tz=new Map;function tQ(e){if(tj=!0,tz.has(e.detail.tag)){let t=tz.get(e.detail.tag);t()}}async function tX(e){return new Promise(t=>{if(window.customElements&&"function"==typeof customElements.whenDefined)customElements.whenDefined(e).then(t);else if(tj)tz.set(e,t),document.dispatchEvent(new CustomEvent("__darkreader__addUndefinedResolver",{detail:{tag:e}}));else{let r=()=>{let n=tG.get(e);n&&n.size>0&&(n.values().next().value.matches(":defined")?t():requestAnimationFrame(r))};requestAnimationFrame(r)}})}function tY(e){tW=e}function tK(){tU.forEach(e=>e.disconnect()),tU.splice(0,tU.length),tq=new WeakSet,tW=null,tG.clear(),document.removeEventListener("__darkreader__isDefined",tQ)}let tZ=new WeakMap,tJ=new WeakSet;function re(){document.dispatchEvent(new CustomEvent("__darkreader__inlineScriptsAllowed"));let e=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"addRule"),t=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"insertRule"),r=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"deleteRule"),n=Object.getOwnPropertyDescriptor(CSSStyleSheet.prototype,"removeRule"),a=Object.getOwnPropertyDescriptor(Document.prototype,"styleSheets"),i=location.hostname.endsWith("baidu.com"),l=i?Object.getOwnPropertyDescriptor(Element.prototype,"getElementsByTagName"):null,s=()=>{Object.defineProperty(CSSStyleSheet.prototype,"addRule",e),Object.defineProperty(CSSStyleSheet.prototype,"insertRule",t),Object.defineProperty(CSSStyleSheet.prototype,"deleteRule",r),Object.defineProperty(CSSStyleSheet.prototype,"removeRule",n),document.removeEventListener("__darkreader__cleanUp",s),document.removeEventListener("__darkreader__addUndefinedResolver",o),Object.defineProperty(Document.prototype,"styleSheets",a),i&&Object.defineProperty(Element.prototype,"getElementsByTagName",l)},o=e=>{customElements.whenDefined(e.detail.tag).then(()=>{document.dispatchEvent(new CustomEvent("__darkreader__isDefined",{detail:{tag:e.detail.tag}}))})};document.addEventListener("__darkreader__cleanUp",s),document.addEventListener("__darkreader__addUndefinedResolver",o);let u=new Event("__darkreader__updateSheet");Object.defineProperty(CSSStyleSheet.prototype,"addRule",Object.assign({},e,{value:function t(r,n,a){return e.value.call(this,r,n,a),this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(u),-1}})),Object.defineProperty(CSSStyleSheet.prototype,"insertRule",Object.assign({},t,{value:function e(r,n){let a=t.value.call(this,r,n);return this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(u),a}})),Object.defineProperty(CSSStyleSheet.prototype,"deleteRule",Object.assign({},r,{value:function e(t){r.value.call(this,t),this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(u)}})),Object.defineProperty(CSSStyleSheet.prototype,"removeRule",Object.assign({},n,{value:function e(t){n.value.call(this,t),this.ownerNode&&!this.ownerNode.classList.contains("darkreader")&&this.ownerNode.dispatchEvent(u)}})),Object.defineProperty(Document.prototype,"styleSheets",Object.assign({},a,{get:function e(){let t=a.get.call(this),r=[...t].filter(e=>!e.ownerNode.classList.contains("darkreader"));return Object.setPrototypeOf(r,StyleSheetList.prototype)}})),i&&Object.defineProperty(Element.prototype,"getElementsByTagName",Object.assign({},l,{value:function e(t){let r=()=>{let e=l.value.call(this,t);return"style"===t&&(e=Object.setPrototypeOf([...e].filter(e=>!e.classList.contains("darkreader")),NodeList.prototype)),e},n=r(),a={get:function(e,t){return r()[t]}};return new Proxy(n,a)}}))}let rt=function e(){if("randomUUID"in crypto){let t=crypto.randomUUID();return t.substring(0,8)+t.substring(9,13)+t.substring(14,18)+t.substring(19,23)+t.substring(24)}return Array.from(crypto.getRandomValues(new Uint8Array(16))).map(e=>{var t;return((t=e)<16?"0":"")+t.toString(16)}).join("")}(),rr=new Map,rn=[],ra=null,ri=null,rl=null,rs=null,ro=null;function ru(e,t=document.head||document){let r=t.querySelector(`.${e}`);return r||((r=document.createElement("style")).classList.add("darkreader"),r.classList.add(e),r.media="screen",r.textContent=""),r}let rd=new Map;function rc(e,t){rd.has(t)&&rd.get(t).stop(),rd.set(t,c(e,"parent"))}let rf=new Set;function r$(e){let t=ru("darkreader--inline",e);t.textContent=t_(),e.insertBefore(t,e.firstChild);let r=ru("darkreader--override",e);r.textContent=ri&&ri.css?rh(ri.css):"",e.insertBefore(r,t.nextSibling);let n=ru("darkreader--invert",e);ri&&Array.isArray(ri.invert)&&ri.invert.length>0?n.textContent=`${ri.invert.join(", ")} {
    filter: ${eL({...ra,contrast:0===ra.mode?ra.contrast:e$(ra.contrast-10,0,100)})} !important;
}`:n.textContent="",e.insertBefore(n,r.nextSibling),rf.add(e)}function rh(e){return e.replace(/\${(.+?)}/g,(e,t)=>{let r=eK(t);if(r){var n,a;return n=r,a=ra,eE(n,a,ev)}return t})}function rp(){let e=document.querySelector(".darkreader--fallback");e&&(e.textContent="")}let rg=0,rm=new Set;function rb(e){let t=++rg,r=function e(t,{update:r,loadingStart:a,loadingEnd:i}){let l=[],s=t;for(;(s=s.nextElementSibling)&&s.matches(".darkreader");)l.push(s);let o=l.find(e=>e.matches(".darkreader--cors")&&!t2.has(e))||null,u=l.find(e=>e.matches(".darkreader--sync")&&!tI.has(e))||null,f=null,$=null,h=!1,p=!0,g=t7(),m=new MutationObserver(()=>{r()}),b={attributes:!0,childList:!0,subtree:!0,characterData:!0};function x(){return t instanceof HTMLStyleElement&&t.textContent.trim().match(j)}function k(e){let t=!1;if(e){let r;cssRulesLoop:for(let n=0,a=e.length;n<a;n++)if((r=e[n]).href&&r.href.startsWith("http")&&!r.href.startsWith(location.origin)){t=!0;break cssRulesLoop}}return t}function w(){if(o)return o.sheet.cssRules;if(x())return null;let e=D();return k(e)?null:e}function _(){o?(t.nextSibling!==o&&t.parentNode.insertBefore(o,t.nextSibling),o.nextSibling!==u&&t.parentNode.insertBefore(u,o.nextSibling)):t.nextSibling!==u&&t.parentNode.insertBefore(u,t.nextSibling)}let E=!1,v=!1,S=++tB;async function C(){let e,r;if(t instanceof HTMLLinkElement){var a;let[i,l]=A();if(!i&&!l&&!L||L&&!t.sheet||(a=l,a&&a.message&&a.message.includes("loading"))){try{n(`Linkelement ${S} is not loaded yet and thus will be await for`,t),await t1(t,S)}catch(s){v=!0}if(h)return null;[i,l]=A()}let u=k(i);if(null!=i&&!u)return i;if(e=await tF(t.href),r=Q(t.href),h)return null}else{if(!x())return null;e=t.textContent.trim(),r=Q(location.href)}if(e){try{let d=await tO(e,r);o=function e(t,r){if(!r)return null;let n=document.createElement("style");return n.classList.add("darkreader"),n.classList.add("darkreader--cors"),n.media="screen",n.textContent=r,t.parentNode.insertBefore(n,t.nextSibling),n.sheet.disabled=!0,t2.add(n),n}(t,d)}catch($){}if(o)return f=c(o,"prev-sibling"),o.sheet.cssRules}return null}let T=!1;function A(){try{if(null==t.sheet)return[null,null];return[t.sheet.cssRules,null]}catch(e){return[null,e]}}function D(){let[e,t]=A();return t?null:e}let N=null,I=null;function P(){let e=D();return e?e.length:null}function B(){cancelAnimationFrame(I)}let M=!1;function F(){tP=!0,B(),!M&&(M=!0,"function"==typeof queueMicrotask?queueMicrotask(e):requestAnimationFrame(e));function e(){M=!1,!h&&r()}}function O(){m.disconnect(),h=!0,f&&f.stop(),$&&$.stop(),t.removeEventListener("__darkreader__updateSheet",F),B()}let U=0;return{details:function e(){let t=w();return t?{rules:t}:(E||v||(E=!0,a(),C().then(e=>{E=!1,i(),e&&r()}).catch(e=>{E=!1,i()})),null)},render:function e(n,a){let i=w();function l(){u||((u=t instanceof SVGStyleElement?document.createElementNS("http://www.w3.org/2000/svg","style"):document.createElement("style")).classList.add("darkreader"),u.classList.add("darkreader--sync"),u.media="screen",!V&&t.title&&(u.title=t.title),tI.add(u)),$&&$.stop(),_(),null==u.sheet&&(u.textContent="");let e=u.sheet;for(let r=e.cssRules.length-1;r>=0;r--)e.deleteRule(r);return $?$.run():$=c(u,"prev-sibling",()=>{T=!0,s()}),u.sheet}function s(){let e=T;T=!1,g.modifySheet({prepareSheet:l,sourceCSSRules:i,theme:n,ignoreImageAnalysis:a,force:e,isAsyncCancelled:()=>h}),p=0===u.sheet.cssRules.length,g.shouldRebuildStyle()&&y(()=>r())}i&&(h=!1,s())},pause:O,destroy:function e(){if(O(),d(o),d(u),i(),t5.has(S)){let t=t5.get(S);t5.delete(S),t&&t()}},watch:function e(){m.observe(t,b),t instanceof HTMLStyleElement&&(t.addEventListener("__darkreader__updateSheet",F),R||tP&&t.sheet||function e(){N=P(),B();let n=()=>{if(P()!==N&&(N=P(),r()),tP&&t.sheet){B();return}I=requestAnimationFrame(n)};n()}())},restore:function e(){!(!u||++U>10)&&(_(),f&&f.skip(),$&&$.skip(),p||(T=!0,r()))}}}(e,{update:function e(){let t=r.details();t&&(tn.addRulesForMatching(t.rules),tn.matchVariablesAndDependants(),r.render(ra,rs))},loadingStart:function e(){if(!$()||!r_){rm.add(t),n(`Current amount of styles loading: ${rm.size}`);let r=document.querySelector(".darkreader--fallback");r.textContent||(r.textContent=ez(ra,{strict:!1}))}},loadingEnd:function e(){rm.delete(t),n(`Removed loadingStyle ${t}, now awaiting: ${rm.size}`),0===rm.size&&$()&&rp()}});return rr.set(e,r),r}function ry(e){let t=rr.get(e);t&&(t.destroy(),rr.delete(e))}let r8=i(e=>{rr.forEach(e=>e.render(ra,rs)),rn.forEach(e=>e.render(ra,rs)),e&&e()}),rx=function(){r8.cancel()};function rk(){if(0===rm.size){rp();return}}let rw=null,r_=!document.hidden;function rE(){document.removeEventListener("visibilitychange",rw),rw=null}function rv(){function e(){(function e(){rx();let t=tN(document),r=t.filter(e=>!rr.has(e)).map(e=>rb(e));r.map(e=>e.details()).filter(e=>e&&e.rules.length>0).forEach(e=>{tn.addRulesForMatching(e.rules)}),tn.matchVariablesAndDependants(),tn.setOnRootVariableChange(()=>{tn.putRootVars(document.head.querySelector(".darkreader--root-vars"),ra)}),tn.putRootVars(document.head.querySelector(".darkreader--root-vars"),ra),rr.forEach(e=>e.render(ra,rs)),0===rm.size&&rp(),r.forEach(e=>e.watch());let n=function e(t){let r=[];for(let n=0,a=t.length;n<a;n++)r.push(t[n]);return r}(document.querySelectorAll(tw));f(document.documentElement,e=>{r$(e.shadowRoot);let t=e.shadowRoot.querySelectorAll(tw);t.length>0&&s(n,t)}),n.forEach(e=>t3(e,ra,ro,rs)),rS(document),setTimeout(rP,100)})(),function e(){var t,r;let n=Array.from(rr.keys());(function e(t,r,n){var a;tK();let i=new Set(t),l=new WeakMap,o=new WeakMap;function u(e){l.set(e,e.previousElementSibling),o.set(e,e.nextElementSibling)}function d(e){let{createdStyles:t,removedStyles:n,movedStyles:a}=e;t.forEach(e=>u(e)),a.forEach(e=>u(e)),n.forEach(e=>{var t;return t=e,void(l.delete(t),o.delete(t))}),t.forEach(e=>i.add(e)),n.forEach(e=>i.delete(e)),t.size+n.size+a.size>0&&r({created:Array.from(t),removed:Array.from(n),moved:Array.from(a),updated:[]})}function c({additions:e,moves:t,deletions:r}){let n=new Set,a=new Set,i=new Set;e.forEach(e=>tN(e).forEach(e=>n.add(e))),r.forEach(e=>tN(e).forEach(e=>a.add(e))),t.forEach(e=>tN(e).forEach(e=>i.add(e))),d({createdStyles:n,removedStyles:a,movedStyles:i}),e.forEach(e=>{f(e,g),tH(e)})}function $(e){let t=new Set(tN(e)),r=new Set,n=new Set,a=new Set;t.forEach(e=>{i.has(e)||r.add(e)}),i.forEach(e=>{t.has(e)||n.add(e)}),t.forEach(e=>{var t;!r.has(e)&&!n.has(e)&&((t=e).previousElementSibling!==l.get(t)||t.nextElementSibling!==o.get(t))&&a.add(e)}),d({createdStyles:r,removedStyles:n,movedStyles:a}),f(e,g),tH(e)}function h(e){let t=new Set,n=new Set;e.forEach(e=>{let{target:r}=e;r.isConnected&&(tD(r)?t.add(r):r instanceof HTMLLinkElement&&r.disabled&&n.add(r))}),t.size+n.size>0&&r({updated:Array.from(t),created:[],removed:Array.from(n),moved:[]})}function p(e){let t=_(e,{onMinorMutations:c,onHugeMutations:$}),r=new MutationObserver(h);r.observe(e,{attributes:!0,attributeFilter:["rel","disabled","media"],subtree:!0}),tU.push(t,r),tq.add(e)}function g(e){let{shadowRoot:t}=e;!(null==t||tq.has(t))&&(p(t),n(t))}t.forEach(u),p(document),f(document.documentElement,g),tW=a=e=>{let t=[];e.forEach(e=>s(t,tN(e.shadowRoot))),r({created:t,updated:[],removed:[],moved:[]}),e.forEach(e=>{let{shadowRoot:t}=e;null!=t&&(g(e),f(t,g),tH(t))})},document.addEventListener("__darkreader__isDefined",tQ),tH(document)})(n,({created:e,updated:t,removed:r,moved:n})=>{let a=e.concat(t).concat(n).filter(e=>!rr.has(e)),i=n.filter(e=>rr.has(e));r.forEach(e=>ry(e));let l=a.map(e=>rb(e));l.map(e=>e.details()).filter(e=>e&&e.rules.length>0).forEach(e=>{tn.addRulesForMatching(e.rules)}),tn.matchVariablesAndDependants(),l.forEach(e=>e.render(ra,rs)),l.forEach(e=>e.watch()),i.forEach(e=>rr.get(e).restore())},e=>{r$(e),rS(e)}),t=e=>{if(t3(e,ra,ro,rs),e===document.documentElement){let t=e.getAttribute("style");t.includes("--")&&(tn.matchVariablesAndDependants(),tn.putRootVars(document.head.querySelector(".darkreader--root-vars"),ra))}},tS(document,t,r=e=>{r$(e);let t=e.querySelectorAll(tw);t.length>0&&l(t,e=>t3(e,ra,ro,rs))}),f(document.documentElement,e=>{tS(e.shadowRoot,t,r)}),p(rk)}()}(function e(){let t=ru("darkreader--fallback",document);t.textContent=ez(ra,{strict:!0}),document.head.insertBefore(t,document.head.firstChild),rc(t,"fallback");let r=ru("darkreader--user-agent");r.textContent=function e(t,r,n){let a=[];if(r||(a.push("html {"),a.push(`    background-color: ${e9({r:255,g:255,b:255},t)} !important;`),a.push("}")),a.push(`${r?"":"html, body, "}${n?"input, textarea, select, button":""} {`),a.push(`    background-color: ${e9({r:255,g:255,b:255},t)};`),a.push("}"),a.push(`html, body, ${n?"input, textarea, select, button":""} {`),a.push(`    border-color: ${eA({r:76,g:76,b:76},t)};`),a.push(`    color: ${eV({r:0,g:0,b:0},t)};`),a.push("}"),a.push("a {"),a.push(`    color: ${eV({r:0,g:64,b:255},t)};`),a.push("}"),a.push("table {"),a.push(`    border-color: ${eA({r:128,g:128,b:128},t)};`),a.push("}"),a.push("::placeholder {"),a.push(`    color: ${eV({r:169,g:169,b:169},t)};`),a.push("}"),a.push("input:-webkit-autofill,"),a.push("textarea:-webkit-autofill,"),a.push("select:-webkit-autofill {"),a.push(`    background-color: ${e9({r:250,g:255,b:189},t)} !important;`),a.push(`    color: ${eV({r:0,g:0,b:0},t)} !important;`),a.push("}"),t.scrollbarColor&&a.push(function e(t){let r=[],n,a,i,l,s,o;if("auto"===t.scrollbarColor)n=e9({r:241,g:241,b:241},t),a=eV({r:96,g:96,b:96},t),i=e9({r:176,g:176,b:176},t),l=e9({r:144,g:144,b:144},t),s=e9({r:96,g:96,b:96},t),o=e9({r:255,g:255,b:255},t);else{let u=ea(t.scrollbarColor),d=Z(u),c=d.l>.5,f=e=>({...d,l:e$(d.l+e,0,1)}),$=e=>({...d,l:e$(d.l-e,0,1)});n=ee($(.4)),a=ee(c?$(.4):f(.4)),i=ee(d),l=ee(f(.1)),s=ee(f(.2))}return r.push("::-webkit-scrollbar {"),r.push(`    background-color: ${n};`),r.push(`    color: ${a};`),r.push("}"),r.push("::-webkit-scrollbar-thumb {"),r.push(`    background-color: ${i};`),r.push("}"),r.push("::-webkit-scrollbar-thumb:hover {"),r.push(`    background-color: ${l};`),r.push("}"),r.push("::-webkit-scrollbar-thumb:active {"),r.push(`    background-color: ${s};`),r.push("}"),r.push("::-webkit-scrollbar-corner {"),r.push(`    background-color: ${o};`),r.push("}"),A&&(r.push("* {"),r.push(`    scrollbar-color: ${i} ${n};`),r.push("}")),r.join("\n")}(t)),t.selectionColor&&!r2&&a.push(function e(t){let r=[],n=ej(t),a=n.backgroundColorSelection,i=n.foregroundColorSelection;return["::selection","::-moz-selection"].forEach(e=>{r.push(`${e} {`),r.push(`    background-color: ${a} !important;`),r.push(`    color: ${i} !important;`),r.push("}")}),r.join("\n")}(t)),null!=t.imageBrightness&&t.imageBrightness<97){let i=t.imageBrightness/100;a.push("img {"),a.push(`    filter: brightness(${i}) !important;`),a.push("}")}return a.join("\n")}(ra,rl,ra.styleSystemControls),document.head.insertBefore(r,t.nextSibling),rc(r,"user-agent");let n=ru("darkreader--text");ra.useFont||ra.textStroke>0?n.textContent=function e(t){let r=[];return r.push('*:not(pre, pre *, code, .far, .fa, .glyphicon, [class*="vjs-"], .fab, .fa-github, .fas, .material-icons, .icofont, .typcn, mu, [class*="mu-"], .glyphicon, .icon) {'),t.useFont&&t.fontFamily&&r.push(`  font-family: ${t.fontFamily} !important;`),t.textStroke>0&&(r.push(`  -webkit-text-stroke: ${t.textStroke}px !important;`),r.push(`  text-stroke: ${t.textStroke}px !important;`)),r.push("}"),r.join("\n")}(ra):n.textContent="",document.head.insertBefore(n,t.nextSibling),rc(n,"text");let a=ru("darkreader--invert");ri&&Array.isArray(ri.invert)&&ri.invert.length>0?a.textContent=`${ri.invert.join(", ")} {
    filter: ${eL({...ra,contrast:0===ra.mode?ra.contrast:e$(ra.contrast-10,0,100)})} !important;
}`:a.textContent="",document.head.insertBefore(a,n.nextSibling),rc(a,"invert");let i=ru("darkreader--inline");i.textContent=t_(),document.head.insertBefore(i,a.nextSibling),rc(i,"inline");let l=ru("darkreader--override");l.textContent=ri&&ri.css?rh(ri.css):"",document.head.appendChild(l),rc(l,"override");let s=ru("darkreader--variables"),o=ej(ra),{darkSchemeBackgroundColor:u,darkSchemeTextColor:d,lightSchemeBackgroundColor:c,lightSchemeTextColor:f,mode:$}=ra,h=0===$?c:u,p=0===$?f:d;h=e9(ea(h),ra),p=eV(ea(p),ra),s.textContent=`:root {
   --darkreader-neutral-background: ${h};
   --darkreader-neutral-text: ${p};
   --darkreader-selection-background: ${o.backgroundColorSelection};
   --darkreader-selection-text: ${o.foregroundColorSelection};
}`,document.head.insertBefore(s,i.nextSibling),rc(s,"variables");let g=ru("darkreader--root-vars");document.head.insertBefore(g,s.nextSibling);let m=function e(t,r=document.head||document){let n=r.querySelector(`.${t}`);return n||((n=document.createElement("script")).classList.add("darkreader"),n.classList.add(t)),n}("darkreader--proxy");m.append(`(${re})()`),document.head.insertBefore(m,g.nextSibling),m.remove()})(),document.hidden?function e(t){let r=Boolean(rw);rw=()=>{document.hidden||(rE(),t(),r_=!0)},r||document.addEventListener("visibilitychange",rw)}(e):e(),function e(t){let r=document.querySelector(tV);r?t4(r,t):(tA&&tA.disconnect(),(tA=new MutationObserver(e=>{loop:for(let r=0;r<e.length;r++){let{addedNodes:n}=e[r];for(let a=0;a<n.length;a++){let i=n[a];if(i instanceof HTMLMetaElement&&i.name===tT){tA.disconnect(),tA=null,t4(i,t);break loop}}}})).observe(document.head,{childList:!0}))}(ra)}function rS(e){if(Array.isArray(e.adoptedStyleSheets)&&e.adoptedStyleSheets.length>0){var t;let r,n=(t=e,r=!1,{render:function e(n,a){t.adoptedStyleSheets.forEach(e=>{if(tJ.has(e))return;let i=e.rules,l=new CSSStyleSheet,s=t7();s.modifySheet({prepareSheet:function r(){for(let n=l.cssRules.length-1;n>=0;n--)l.deleteRule(n);return function e(r,n){let a=[...t.adoptedStyleSheets],i=a.indexOf(r),l=a.indexOf(n);i!==l-1&&(l>=0&&a.splice(l,1),a.splice(i+1,0,n),t.adoptedStyleSheets=a)}(e,l),tZ.set(e,l),tJ.add(l),l},sourceCSSRules:i,theme:n,ignoreImageAnalysis:a,force:!1,isAsyncCancelled:()=>r})})},destroy:function e(){r=!0;let n=[...t.adoptedStyleSheets];t.adoptedStyleSheets.forEach(e=>{if(tJ.has(e)){let t=n.indexOf(e);t>=0&&n.splice(t,1),tZ.delete(e),tJ.delete(e)}}),t.adoptedStyleSheets=n}});rn.push(n),n.render(ra,rs)}}function r0(){let e=document.querySelector('meta[name="neonoir"]');return e?e.content!==rt:(function e(){let t=document.createElement("meta");t.name="neonoir",t.content=rt,document.head.appendChild(t)}(),!1)}function rC(){document.documentElement.removeAttribute("data-darkreader-scheme"),r9(),d(document.querySelector(".darkreader--fallback")),document.head&&(function e(){tA&&(tA.disconnect(),tA=null);let t=document.querySelector(tV);t&&tR&&(t.content=tR)}(),d(document.head.querySelector(".darkreader--user-agent")),d(document.head.querySelector(".darkreader--text")),d(document.head.querySelector(".darkreader--invert")),d(document.head.querySelector(".darkreader--inline")),d(document.head.querySelector(".darkreader--override")),d(document.head.querySelector(".darkreader--variables")),d(document.head.querySelector(".darkreader--root-vars")),d(document.head.querySelector('meta[name="darkreader"]')),document.dispatchEvent(new CustomEvent("__darkreader__cleanUp")),d(document.head.querySelector(".darkreader--proxy"))),rf.forEach(e=>{d(e.querySelector(".darkreader--inline")),d(e.querySelector(".darkreader--override"))}),rf.clear(),l(rr.keys(),e=>ry(e)),rm.clear(),t5.clear(),l(document.querySelectorAll(".darkreader"),d),rn.forEach(e=>{e.destroy()}),rn.splice(0)}function r9(){tn.clear(),B.clear(),rE(),rx(),rr.forEach(e=>e.pause()),l(rd.values(),e=>e.stop()),rd.clear(),tK(),tE.forEach(e=>e.disconnect()),tv.forEach(e=>e.disconnect()),tE.clear(),tv.clear(),g(rk),b.clear(),eX.clear(),e8.clear(),ex.clear(),eJ.clear(),e1&&e1.stopQueue(),eU=null,eq=null,te.clear()}let r3=/url\(\"(blob\:.*?)\"\)/g;async function rT(e){let t=[];ep(r3,e,1).forEach(e=>{let r=eB(e);t.push(r)});let r=await Promise.all(t);return e.replace(r3,()=>`url("${r.shift()}")`)}let rV=`/*
                       _______
                      /       \\
                     .==.    .==.
                    ((  ))==((  ))
                   / "=="    "=="\\
                  /____|| || ||___\\
      ________     ____    ________  ___    ___
      |  ___  \\   /    \\   |  ___  \\ |  |  /  /
      |  |  \\  \\ /  /\\  \\  |  |  \\  \\|  |_/  /
      |  |   )  /  /__\\  \\ |  |__/  /|  ___  \\
      |  |__/  /  ______  \\|  ____  \\|  |  \\  \\
_______|_______/__/ ____ \\__\\__|___\\__\\__|___\\__\\____
|  ___  \\ |  ____/ /    \\   |  ___  \\ |  ____|  ___  \\
|  |  \\  \\|  |___ /  /\\  \\  |  |  \\  \\|  |___|  |  \\  \\
|  |__/  /|  ____/  /__\\  \\ |  |   )  |  ____|  |__/  /
|  ____  \\|  |__/  ______  \\|  |__/  /|  |___|  ____  \\
|__|   \\__\\____/__/      \\__\\_______/ |______|__|   \\__\\
               https://darkreader.org
*/

/*! Dark reader generated CSS | Licensed under MIT https://github.com/darkreader/darkreader/blob/master/LICENSE */
`;async function rR(){let e=[rV];function t(t,r){let n=document.querySelector(t);n&&n.textContent&&(e.push(`/* ${r} */`),e.push(n.textContent),e.push(""))}t(".darkreader--fallback","Fallback Style"),t(".darkreader--user-agent","User-Agent Style"),t(".darkreader--text","Text Style"),t(".darkreader--invert","Invert Style"),t(".darkreader--variables","Variables Style");let r=[];if(document.querySelectorAll(".darkreader--sync").forEach(e=>{l(e.sheet.cssRules,e=>{e&&e.cssText&&r.push(e.cssText)})}),r.length){let n=function e(t){function r(e){return e.replace(/^\s+/,"")}function n(e){return 0===e?"":" ".repeat(4*e)}if(t.length<5e4){let a=/[^{}]+{\s*}/;for(;a.test(t);)t=t.replace(a,"")}let i=t.replace(/\s{2,}/g," ").replace(/\{/g,"{\n").replace(/\}/g,"\n}\n").replace(/\;(?![^\(|\"]*(\)|\"))/g,";\n").replace(/\,(?![^\(|\"]*(\)|\"))/g,",\n").replace(/\n\s*\n/g,"\n").split("\n"),l=0,s=[];for(let o=0,u=i.length;o<u;o++){let d=`${i[o]}
`;d.includes("{")?s.push(n(l++)+r(d)):d.includes("}")?s.push(n(--l)+r(d)):s.push(n(l)+r(d))}return s.join("").trim()}(r.join("\n"));e.push("/* Modified CSS */"),e.push(await rT(n)),e.push("")}return t(".darkreader--override","Override Style"),e.join("\n")}let rA=!1,r4;function r6(){rA=!0,removeEventListener("pagehide",rD),removeEventListener("freeze",rN),removeEventListener("resume",rI),r9(),r4&&(r4.disconnect(),r4=null)}function rL(e,t){if(!rA){console.log("message waiting for response:",e.type,e.data);try{chrome.runtime.sendMessage(e,e=>{"unsupportedSender"===e&&(v(),S(),rC(),r6()),t?.(e)})}catch(r){r6()}}}function r7({type:e,data:t}){switch(e){case e7.BG_ADD_CSS_FILTER:case e7.BG_ADD_STATIC_THEME:rC(),E(t,e===e7.BG_ADD_STATIC_THEME?"static":"filter");break;case e7.BG_ADD_SVG_FILTER:{var r,n;let{css:a,svgMatrix:i,svgReverseMatrix:l}=t;rC(),r=i,n=l,u({selectNode:()=>document.getElementById("dark-reader-svg"),createNode(e){let t="http://www.w3.org/2000/svg",a=(e,r)=>{let n=document.createElementNS(t,"filter");return n.id=e,n.style.colorInterpolationFilters="sRGB",n.setAttribute("x","0"),n.setAttribute("y","0"),n.setAttribute("width","99999"),n.setAttribute("height","99999"),n.appendChild(i(r)),n},i=e=>{let r=document.createElementNS(t,"feColorMatrix");return r.setAttribute("type","matrix"),r.setAttribute("values",e),r},l=document.createElementNS(t,"svg");l.id="dark-reader-svg",l.style.height="0",l.style.width="0",l.appendChild(a("dark-reader-filter",r)),l.appendChild(a("dark-reader-reverse-filter",n)),e.appendChild(l)},updateNode(e){let t=e.firstChild.firstChild;if(t.getAttribute("values")!==r){t.setAttribute("values",r);let n=document.getElementById("dark-reader-style"),a=n.textContent;n.textContent="",n.textContent=a}},selectTarget:()=>document.head,createTarget(){let e=document.createElement("head");return document.documentElement.insertBefore(e,document.documentElement.firstElementChild),e},isTargetMutation:e=>"head"===e.target.nodeName.toLowerCase()}),E(a,"filter");break}case e7.BG_ADD_DYNAMIC_THEME:{let{filter:s,fixes:o,isIFrame:d}=t;v(),function e(t,r,n){if(ra=t,(ri=r)?(rs=Array.isArray(ri.ignoreImageAnalysis)?ri.ignoreImageAnalysis:[],ro=Array.isArray(ri.ignoreInlineStyle)?ri.ignoreInlineStyle:[]):(rs=[],ro=[]),rl=n,!document.head&&document.body){let a=document.createElement("head");document.documentElement.prepend(a)}if(document.head){if(r0())return;document.documentElement.setAttribute("data-darkreader-mode","dynamic"),document.documentElement.setAttribute("data-darkreader-scheme",ra.mode?"dark":"dimmed"),rv()}else{if(!A){let i=ru("darkreader--fallback");document.documentElement.appendChild(i),i.textContent=ez(ra,{strict:!0})}let l=new MutationObserver(()=>{if(document.head){if(l.disconnect(),r0()){rC();return}rv()}});l.observe(document,{childList:!0,subtree:!0})}}(s,o,d);break}case e7.BG_EXPORT_CSS:rR().then(e=>rL({type:e7.CS_EXPORT_CSS_RESPONSE,data:e}));break;case e7.BG_UNSUPPORTED_SENDER:case e7.BG_CLEAN_UP:v(),S(),rC(),rP();break;case e7.BG_RELOAD:r6()}}function rD(e){!1===e.persisted&&rL({type:e7.CS_FRAME_FORGET})}function rN(){rL({type:e7.CS_FRAME_FREEZE})}function rI(){rL({type:e7.CS_FRAME_RESUME})}window==top&&(r4=function e(t){let r=matchMedia("(prefers-color-scheme: dark)"),n=()=>t({isDark:r.matches});return N?r.addEventListener("change",n):r.addListener(n),{disconnect(){N?r.removeEventListener("change",n):r.removeListener(n)}}}(({isDark:e})=>{console.log("colorSchemeWatcher isDark",e),rL({type:e7.CS_COLOR_SCHEME_CHANGE,data:{isDark:e}})})),blz_chrome_runtime_onMessage_addListener(r7),/safari/i.test(navigator.userAgent);let r2=/mobile/i.test(navigator.userAgent);function rP(){document.querySelectorAll(".darkreader--whiteflashfix").forEach(e=>e.remove()),document.documentElement&&(document.documentElement.dataset.darkreaderWhiteFlashSuppressor="active")}rL({type:e7.CS_FRAME_CONNECT},r7),R||(addEventListener("pagehide",rD),addEventListener("freeze",rN),addEventListener("resume",rI))}();
