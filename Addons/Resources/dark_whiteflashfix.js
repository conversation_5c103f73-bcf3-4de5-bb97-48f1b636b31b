 (()=>{let t=!1;async function r(t){return new Promise(r=>{chrome.storage.local.get(t,e=>{if(chrome.runtime.lastError){console.warn(chrome.runtime.lastError.message),r(t);return}r(e)})})}async function e(r){return new Promise(e=>{t=!0,chrome.storage.local.set(r,()=>{e(),setTimeout(()=>t=!1)})})}function n(r){chrome.storage.onChanged.addListener(()=>{t||r()})}var a={read:r,write:e,onChange:n};let s={id:"whiteflashfix-js",js:["whiteflashfix.js"],matches:["<all_urls>"],runAt:"document_start",allFrames:!0},i={id:"whiteflashfix-css",css:["whiteflashfix.css"],matches:["<all_urls>"],runAt:"document_start",allFrames:!0},l=[s,i],c=l.map(t=>t.id);async function o(){try{await chrome.scripting.registerContentScripts(l)}catch(t){console.log("error = "+t)}}let u;async function d(t){try{let r=await window.loadDarkConfig();if(1===r.enableValue){if(null==u)return!1;return u}if(2===r.enableValue)return!0;return!1}catch(e){return console.error(e),!1}}async function f(){try{await chrome.scripting.unregisterContentScripts({ids:c})}catch(t){console.log("error = "+t)}}async function g(){try{let t=await chrome.scripting.getRegisteredContentScripts(),r=t=>c.includes(t.id);return t.some?.(r)}catch(e){return console.log("error = "+e),!0}}async function h(t){let r=await d(t),e=await g();try{r?e||await o():e&&await f()}catch(n){console.log("error = "+n)}}chrome.runtime.onMessage.addListener(({type:t,data:r})=>{"cs-color-scheme-change"===t&&(r?.isDark!=null&&(u=r?.isDark),h(null))}),chrome.runtime.onInstalled.addListener(()=>{h()}),chrome.runtime.onStartup.addListener(()=>{h()}),a.onChange(()=>{h()}),window.whiteFlashFixIds=c})();
