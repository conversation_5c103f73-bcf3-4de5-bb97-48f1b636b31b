//
//  ExtensionHelper.h
//  Saber Extension
//
//  Created by q<PERSON><PERSON> on 2023/3/5.
//

#import <Foundation/Foundation.h>
#import "ExtensionModel.h"
#import "TagitModel.h"

#import "PPEnums.h"

@interface ExtensionHelper : NSObject

+ (instancetype)shareInstance;
//加载所有数据
- (void)asyncLoadAllDatas:(ExtensionReloadStatus)status
        completionHandler:(void (^)(ExtensionModel*))completionHandler;
//更新暗黑模式
- (void)updateDarkModeWithOption:(int)option;
//记录选择的模块
//+ (void)saveModuleIndex:(int)selectIndex;
//获取选择的模块
//+ (int)getLastModuleIndex;
//屏蔽元素
+ (void)addTagitWithItem:(TagitModel *)item;
//撤回元素(返回当前网站最新的xpath，可能没有)
+ (void)removeTagitWithHost:(NSString *)host
                 completion:(void(^)(NSString *xpath))completion;
//popup,删除一项tagit
+ (void)removeTagitWithId:(NSString *)uuid;
//popup,激活或者暂停一项tagit
// 更新是否激活状态
+ (void)updateTagitWithId:(NSString*)uuid
                 isActive:(NSInteger)isActive;

@property (nonatomic, strong) TagitModel* currentTagitItem;

@end

