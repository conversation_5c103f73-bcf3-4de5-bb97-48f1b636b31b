//
//  ExUserScriptHelper.m
//  Saber Extension
//
//  Created by qing<PERSON> on 2023/1/13.
//

#import "ExUserScriptHelper.h"

#import "ResourceHelper.h"
#import "BrowserUtils.h"
#import "UserScript.h"
#import "DatabaseUnit+UserScript.h"

#import "NSFileManager+Helper.h"
#import "NSString+Helper.h"
#import "NSString+MKNetworkKitAdditions.h"

#import "CompressorHelper.h"

@interface ExUserScriptHelper()

@property (nonatomic, strong) dispatch_queue_t queue;

///由于字符串替换会占用非常大的内存空间，因此改成字符串拼接的方式
//Content Script
//@property (nonatomic, strong) NSString *source_code_generate_content;
@property (nonatomic, strong) NSString *script_content_segment1;
@property (nonatomic, strong) NSString *script_content_segment2;
@property (nonatomic, strong) NSString *script_content_segment3;

//Page Script
//@property (nonatomic, strong) NSString *source_code_generate_page;
@property (nonatomic, strong) NSString *script_page_segment1;
@property (nonatomic, strong) NSString *script_page_segment2;
@property (nonatomic, strong) NSString *script_page_segment3;

@end

@implementation ExUserScriptHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static ExUserScriptHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [ExUserScriptHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.userscript.serial", DISPATCH_QUEUE_SERIAL);
    
//        self.source_code_generate_page = [self loadJsFromBundleWithFileName:@"source_code_generate_page"];
//        self.source_code_generate_content = [self loadJsFromBundleWithFileName:@"source_code_generate_content"];
//        self.script_page_segment1 = [self loadJsFromBundleWithFileName:@"script_page_segment1"];
//        self.script_page_segment2 = [self loadJsFromBundleWithFileName:@"script_page_segment2"];
//        self.script_page_segment3 = [self loadJsFromBundleWithFileName:@"script_page_segment3"];
//
//        self.script_content_segment1 = [self loadJsFromBundleWithFileName:@"script_content_segment1"];
//        self.script_content_segment2 = [self loadJsFromBundleWithFileName:@"script_content_segment2"];
//        self.script_content_segment3 = [self loadJsFromBundleWithFileName:@"script_content_segment3"];
        
//        self.source_code_generate_content = [ResourceHelper addons].source_code_generate_content;
//        self.source_code_generate_page = [ResourceHelper addons].source_code_generate_page;
        
        self.script_page_segment1 = [ResourceHelper addons].script_page_segment1;
        self.script_page_segment2 = [ResourceHelper addons].script_page_segment2;
        self.script_page_segment3 = [ResourceHelper addons].script_page_segment3;
        
        self.script_content_segment1 = [ResourceHelper addons].script_content_segment1;
        self.script_content_segment2 = [ResourceHelper addons].script_content_segment2;
        self.script_content_segment3 = [ResourceHelper addons].script_content_segment3;
    }
    
    return self;
}

- (void)updateWithModel:(NSArray<UserScript*> *)models
                 status:(ExtensionReloadStatus)status
{
    for(UserScript* item in models) {
        @autoreleasepool {
            //v4.5, 处理黑白名单
            //将自定义的白名单和黑名单合并到matches和excludes中，这样代码逻辑统一
            NSMutableArray* matches = [NSMutableArray array];
            [matches addObjectsFromArray:item.matches];
            [matches addObjectsFromArray:[item getWhiteList]];
            
            NSMutableArray* excludes = [NSMutableArray array];
            [excludes addObjectsFromArray:item.excludes];
            [excludes addObjectsFromArray:[item getBlackList]];
            item.matches = matches;
            item.excludes = excludes;
            
            if(status == ExtensionReloadStatusBootstrap) {
                //bootstrap加载数据
                
                NSMutableString* executorJs = [[NSMutableString alloc]init];
                //第一部分，GM_Info
                if(item.injectMode == UserScriptInjectAuto || item.injectMode == UserScriptInjectContent) {
                    [executorJs appendString:self.script_content_segment1];
                } else {
                    [executorJs appendString:self.script_page_segment1];
                }
                
                NSString* GMScriptInfo = [item GM_Info];
                [executorJs appendString:GMScriptInfo];
                
                //第二部分，中间层
                if(item.injectMode == UserScriptInjectAuto || item.injectMode == UserScriptInjectContent) {
                    [executorJs appendString:self.script_content_segment2];
                } else {
                    [executorJs appendString:self.script_page_segment2];
                }
                
                //第三部分，@require + 脚本源码
                
                //处理@require, 在源码中替换requires
                //gear和stay都已经这样处理，我们也同步这种做法
                //主要是有些脚本会引用全局变量，如果不这样处理，那么会没法正常工作
                //加载@require
                NSMutableString* requireCode = [NSMutableString string];
                NSString* groupPath = [NSFileManager groupPath];
                NSString* dir = [NSString stringWithFormat:@"%@/%@/require", groupPath, item.uuid];
                NSFileManager* fm = [NSFileManager defaultManager];
                
                NSMutableArray* result = [NSMutableArray array];
                for(NSString *url in item.requireUrls) {
                    @autoreleasepool {
                        NSString* fileName = [url md5];
                        NSString* storageUrl = [NSString stringWithFormat:@"%@/%@",dir,fileName];
                        
                        //适配旧版本
                        if(![fm fileExistsAtPath:storageUrl]) {
                            fileName = [url lastPathComponent];
                            storageUrl = [NSString stringWithFormat:@"%@/%@",dir,fileName];
                        }
                        
                        if([fm fileExistsAtPath:storageUrl]) {
                            NSError* error = nil;
                            NSString* jsContent = [NSString stringWithContentsOfFile:storageUrl encoding:NSUTF8StringEncoding error:&error];
                            //v4.4 移除//# sourceMappingURL=xx
//                            jsContent = [NSString removeSourceMappingURL:jsContent];
                            
                            if(error) {
                                NSLog(@"error = %@", error.localizedDescription);
                            } else {
                                NSMutableDictionary* dictionary = [NSMutableDictionary dictionary];
                                dictionary[@"url"] = url;
                                dictionary[@"text"] = jsContent;
                                [result addObject:dictionary];
                                
                                if(jsContent.length > 0) {
                                    [requireCode appendFormat:@"%@ \n\n\n",jsContent];
                                }
                            }
                        }
                    }
                }
                
                item.jsRequireUrls = result;
                
                if(requireCode.length == 0) {
                    requireCode = [@"" copy];
                }
                
                [executorJs appendString:requireCode];
                [executorJs appendString:@"\n\n\n"];
                [executorJs appendString:item.content];
                
                if(item.injectMode == UserScriptInjectAuto || item.injectMode == UserScriptInjectContent) {
                    [executorJs appendString:self.script_content_segment3];
                } else {
                    [executorJs appendString:self.script_page_segment3];
                }
                
                //1、压缩executorJs
                item.executorJs = [CompressorHelper compressString:executorJs];
                
                //加载@resource的资源，结构是 @resource key url
                //两种情况，一种是getResourceText，其实就是根据key值，获取url对应的text文件
                //第二种则是getResourceUrl，根据key值，获取url
                //因此这里的@resource返回这样的结构: {key: {url, text}}
    //            item.resourceArray = [self _loadResourceUserScriptWithId:item.uuid];
                NSString* resourceJson = [self _loadResourceUserScriptWithId:item.uuid];
                //2、压缩resourceJson
                item.resourceJson = [CompressorHelper compressString:resourceJson];
                
                //3、压缩iconUrl
                item.iconUrl = [CompressorHelper compressString:item.iconUrl];
                
                //清空
                item.content = @"";
                //清理不必要的字段，防止触发80mb的崩溃
                [self _clearUserScript:item];
            } else if(status == ExtensionReloadStatusPopUp) {
                //popup加载数据
                //popup不需要那么多数据,只加载必要的数据即可,防止触发80mb的崩溃
                
                //清空
                item.content = @"";
                item.executorJs = @"";
                item.resourceJson = @"";
                //清理不必要的字段，防止触发80mb的崩溃
                [self _clearUserScript:item];
            }
        }
    }
}

#pragma mark -- 清理不必要的字段，防止触发80mb的崩溃
- (void)_clearUserScript:(UserScript *)userScript
{
    userScript.sql_includes = nil;
    userScript.sql_matches = nil;
    userScript.sql_excludes = nil;
    userScript.sql_grants = nil;
    userScript.sql_requireUrls = nil;
    userScript.sql_resourceUrls = nil;
    
    //限制长度
    int maxLength = 50;
    if([BrowserUtils isiPhone]) {
        maxLength = 50;
    } else {
        maxLength = 100;
    }
    
    if(userScript.desc.length > maxLength) {
        userScript.desc = [userScript.desc substringToIndex:maxLength];
    }
}

- (NSString *)_loadResourceUserScriptWithId:(NSString *)scriptId
{
    NSString* groupPath = [NSFileManager groupPath];
    NSString* dir = [NSString stringWithFormat:@"%@/%@/resource", groupPath, scriptId];
    NSFileManager* fm = [NSFileManager defaultManager];
    
    NSString* storageUrl = [NSString stringWithFormat:@"%@/resource.json",dir];
    if([fm fileExistsAtPath:storageUrl]) {
        NSError* error = nil;
        NSString* source = [NSString stringWithContentsOfFile:storageUrl encoding:NSUTF8StringEncoding error:&error];
        if(error) {
            NSLog(@"error = %@", error.localizedDescription);
        } else {
            return source;
        }
    }
    
    return @"";
}

#pragma mark -- 从bundle中加载js文件
- (NSString *)loadJsFromBundleWithFileName:(NSString *)scriptFileName
{
    NSString* path = [[NSBundle mainBundle]pathForResource:scriptFileName ofType:@"js"];
    NSError* error = nil;
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        NSLog(@"error = %@", error.localizedDescription);
    }
    
    return source;
}

#pragma mark -- 从bundle中加载css文件
- (NSString *)loadCssFromBundleWithFileName:(NSString *)cssFileName
{
    NSString* path = [[NSBundle mainBundle]pathForResource:cssFileName ofType:@"css"];
    NSError* error = nil;
    NSString* source = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:&error];
    if(error) {
        NSLog(@"error = %@", error.localizedDescription);
    }

    return source;
}

#pragma mark -- 更新是否激活状态
+ (void)updateUserScriptWithId:(NSString*)uuid
                      isActive:(NSInteger)isActive
{
    if(uuid.length == 0) return;
    
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithId:uuid isActive:isActive];
    DB_EXEC(unit);
}

@end
