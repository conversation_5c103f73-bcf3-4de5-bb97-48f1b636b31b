//
//  CompressorHelper.m
//  Addons
//
//  Created by qingbin on 2024/6/15.
//

#import "CompressorHelper.h"

#import <zlib.h>

@implementation CompressorHelper

+ (NSString *)compressString:(NSString *)string 
{
    if ([string length] == 0) {
        return string;
    }

    NSData *inputData = [string dataUsingEncoding:NSUTF8StringEncoding];
    NSUInteger inputLength = [inputData length];

    NSUInteger maxCompressedSize = compressBound((uLong)inputLength);
    NSMutableData *compressedData = [NSMutableData dataWithLength:maxCompressedSize];

    //
    int compressionLevel = Z_DEFAULT_COMPRESSION; // 最高压缩率
    z_stream zStream;
    zStream.next_in = (Bytef *)[inputData bytes];
    zStream.avail_in = (uInt)inputLength;
    zStream.total_out = 0;
    zStream.zalloc = Z_NULL;
    zStream.zfree = Z_NULL;
    zStream.opaque = Z_NULL;

    if (deflateInit(&zStream, compressionLevel) != Z_OK) {
        return string;
    }

    zStream.next_out = [compressedData mutableBytes];
    zStream.avail_out = (uInt)[compressedData length];

    int deflateStatus = deflate(&zStream, Z_FINISH);
    deflateEnd(&zStream);

    if (deflateStatus != Z_STREAM_END) {
        return string;
    }

    [compressedData setLength:zStream.total_out];
    NSString *base64CompressedString = [compressedData base64EncodedStringWithOptions:0];

    return base64CompressedString;
}

@end
