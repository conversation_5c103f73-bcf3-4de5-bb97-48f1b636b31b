//
//  GMKeyValueHelper.m
//  Saber Extension
//
//  Created by qing<PERSON> on 2023/1/17.
//

#import "GMKeyValueHelper.h"
#import "ReactiveCocoa.h"
#import "MaizyHeader.h"

@interface GMKeyValueHelper()

@property (nonatomic, strong) NSUserDefaults* userDefaults;

@end

@implementation GMKeyValueHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static GMKeyValueHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [GMKeyValueHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        //https://easeapi.com/blog/123-nsuserdefaults.html
        self.userDefaults = [[NSUserDefaults alloc]initWithSuiteName:kGroupPath];
    }
    
    return self;
}


/// 暗黑模式，保存基本的配置文件
- (void)saveDarkConfig:(id)value
{
    //保存的时候是string类型
    if(value) {
        NSString* key = [self keyforDarkConfig];
        
        [self.userDefaults setObject:value forKey:key];
        [self.userDefaults synchronize];
    }
}

- (DarkConfigModel *)valueForDarkConfig
{
    NSString* key = [self keyforDarkConfig];
    
    NSString* string = [self.userDefaults valueForKey:key];
    
    DarkConfigModel* config;
    if(string.length == 0) {
        config = [DarkConfigModel defaultConfig];
    } else {
        config = [[DarkConfigModel alloc]initWithString:string error:nil];
    }
        
    return config;
}

- (NSString *)keyforDarkConfig
{
    return @"com.addons.darkconfig.v1";
}

@end
