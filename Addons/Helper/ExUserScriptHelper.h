//
//  ExUserScriptHelper.h
//  Saber Extension
//
//  Created by qing<PERSON> on 2023/1/13.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"

@class UserScript;

@interface ExUserScriptHelper : NSObject

+ (instancetype)shareInstance;

//数据通过ExtensionHelper传递过来
- (void)updateWithModel:(NSArray<UserScript*> *)models
                 status:(ExtensionReloadStatus)status;

// 更新是否激活状态
+ (void)updateUserScriptWithId:(NSString*)uuid
                      isActive:(NSInteger)isActive;

// 从bundle中加载js文件
- (NSString *)loadJsFromBundleWithFileName:(NSString *)scriptFileName;

// 从bundle中加载css文件
- (NSString *)loadCssFromBundleWithFileName:(NSString *)cssFileName;

@end

/**
 Focus的网络请求，需要同步addons的逻辑
 window.GM_openInTab = GM_openInTab; 需要同步到Focus
 
 
 由于扩展只能限制80MB的最大运行内存，因此字符串替换等耗费空间的操作放在主APP或者bootstrap.js中进行？
 或者直接进行字符串的拼接操作。
 
 关于GM_addStyle,
 Adds the given style to the document and returns the injected style element.
 https://stackoverflow.com/questions/23683439/gm-addstyle-equivalent-in-tampermonkey
 
 
 */
