//
//  ExtensionHelper.m
//  Saber Extension
//
//  Created by q<PERSON><PERSON> on 2023/3/5.
//

#import "ExtensionHelper.h"

#import "DatabaseUnit+ExtConfig.h"
#import "DatabaseUnit+Tagit.h"
#import "DatabaseUnit+SaberExtension.h"

//本地的
#import "ExUserScriptHelper.h"
#import "NSFileManager+Helper.h"

@interface ExtensionHelper ()

@property (nonatomic, strong) dispatch_queue_t queue;

@property (nonatomic, strong) ExtensionModel* model;

@end

@implementation ExtensionHelper

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static ExtensionHelper* obj;
    dispatch_once(&onceToken, ^{
        obj = [ExtensionHelper new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
        self.queue = dispatch_queue_create("com.extension.serial", DISPATCH_QUEUE_SERIAL);

    }
    
    return self;
}

- (void)asyncLoadAllDatas:(ExtensionReloadStatus)status
        completionHandler:(void (^)(ExtensionModel*))completionHandler;
{
    dispatch_async(self.queue, ^{
        [self _asyncLoadAllDatas:status completionHandler:completionHandler];
    });
}

- (void)_asyncLoadAllDatas:(ExtensionReloadStatus)status
         completionHandler:(void (^)(ExtensionModel*))completionHandler;
{
    DatabaseUnit* unit = [DatabaseUnit queryAllDataForExtension];
    [unit setCompleteBlock:^(ExtensionModel* model, BOOL success) {
        if(success) {
//            NSNumber* unlockLimit = [NSFileManager valueForUnlockLimit];
//            if(unlockLimit) {
//                model.isUnlockLimit = [unlockLimit boolValue];
//            } else {
//                model.isUnlockLimit = NO;
//            }
            
            self.model = model;
            //更新脚本数据
            [[ExUserScriptHelper shareInstance] updateWithModel:model.userScripts status:status];
            
            if(completionHandler) {
                completionHandler(model);
            }
        } else {
            if(completionHandler) {
                completionHandler(nil);
            }
        }
    }];
    DB_EXEC(unit);
}

//更新暗黑模式
- (void)updateDarkModeWithOption:(int)option
{
    DatabaseUnit* unit = [DatabaseUnit updateExtConfigWithId:ExtConfigOptionDarkMode option:option];
    DB_EXEC(unit);
}

//屏蔽元素
+ (void)addTagitWithItem:(TagitModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit addTagitWithItem:item];
    DB_EXEC(unit);
}

//撤回元素
+ (void)removeTagitWithHost:(NSString *)host
                 completion:(void(^)(NSString *xpath))completion
{
    DatabaseUnit* unit = [DatabaseUnit queryTagitWithHost:host];
    [unit setCompleteBlock:^(NSArray* result, BOOL success) {
        result = [result sortedArrayUsingComparator:^NSComparisonResult(TagitModel*  _Nonnull obj1, TagitModel*  _Nonnull obj2) {
            return obj1.ctime.integerValue < obj2.ctime.integerValue;
        }];
        
        TagitModel* item = result.firstObject;
        if(completion) {
            completion(item.xpath);
        }
        
        DatabaseUnit* unit = [DatabaseUnit removeTagitWithId:item.uuid];
        DB_EXEC(unit);
    }];
    
    DB_EXEC(unit);
}

//popup,删除一项tagit
+ (void)removeTagitWithId:(NSString *)uuid
{
    DatabaseUnit* unit = [DatabaseUnit removeTagitWithId:uuid];
    DB_EXEC(unit);
}

// 更新是否激活状态
+ (void)updateTagitWithId:(NSString*)uuid
                 isActive:(NSInteger)isActive
{
    DatabaseUnit* unit = [DatabaseUnit updateTagitWithId:uuid isActive:isActive];
    DB_EXEC(unit);
}

@end
