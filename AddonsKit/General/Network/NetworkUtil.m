//
//  NetworkUtil.m
//  Saber
//
//  Created by qing<PERSON> on 2022/12/27.
//

#import "NetworkUtil.h"

@implementation NetworkUtil

// 异步请求封装
+ (void)requestGet:(NSString *)url
        completion:(void(^)(BOOL succ, id responseObject))completion
{
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    //超时时间太短，会容易导致更新失败，例如“百度系优化”
    [manager.requestSerializer setTimeoutInterval:30];
    
    //指定响应类型
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    [manager GET:url parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        //responseObject返回NSZeroData,也是空值,例如抖音
        //https://www.anycodings.com/1questions/2566953/how-to-check-if-id-object-is-null-in-ios
        
        NSData* data = (NSData*)responseObject;
        NSLog(@"data = %@, length = %lu",data, (unsigned long)data.length);
        
        BOOL isValid = YES;
        if(data.length == 0) {
            isValid = NO;
        }
        
        if(isValid) {
            if(responseObject == (id)[NSNull null]) {
                isValid = NO;
            }
        }
        
        if(isValid) {
            if(completion) {
                completion(YES, responseObject);
            }
        } else {
            if(completion) {
                completion(NO, nil);
            }
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if(completion) {
            completion(NO, nil);
        }
    }];
}

// 同步请求封装, 返回值(BOOL, id)
+ (RACTuple *)requestGet:(NSString *)url
{
    __block RACTuple *result = [RACTuple tupleWithObjects:@(NO), [RACTupleNil tupleNil], nil];
    NSCondition* lock = [NSCondition new];
    
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    [manager.requestSerializer setTimeoutInterval:10];
    
    //指定响应类型
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    [manager GET:url parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        //responseObject返回NSZeroData,也是空值,例如抖音
        //https://www.anycodings.com/1questions/2566953/how-to-check-if-id-object-is-null-in-ios
        
        NSData* data = (NSData*)responseObject;
        NSLog(@"data = %@, length = %lu",data, (unsigned long)data.length);
        
        BOOL isValid = YES;
        if(data.length == 0) {
            isValid = NO;
        }
        
        if(isValid) {
            if(responseObject == (id)[NSNull null]) {
                isValid = NO;
            }
        }
        
        if(isValid) {
            result = [RACTuple tupleWithObjects:@(YES), responseObject, nil];
        } else {
            result = [RACTuple tupleWithObjects:@(NO), [RACTupleNil tupleNil], nil];
        }
        [lock broadcast];
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        result = [RACTuple tupleWithObjects:@(NO), [RACTupleNil tupleNil], nil];
        [lock broadcast];
    }];
    
    [lock wait];
    
    return result;
}


@end
