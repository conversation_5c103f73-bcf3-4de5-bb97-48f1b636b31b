//
//  NetworkUtil.h
//  Saber
//
//  Created by qingbin on 2022/12/27.
//

#import <Foundation/Foundation.h>

#import "ReactiveCocoa.h"
#import "AFNetworking.h"
#import "AFNetworkReachabilityManager.h"

@interface NetworkUtil : NSObject

// 异步请求封装
+ (void)requestGet:(NSString *)url
        completion:(void(^)(BOOL succ, id responseObject))completion;

// 同步请求封装, 返回值(BOOL, id);
// 因此运行在异步线程, 不能运行在主线程
+ (RACTuple *)requestGet:(NSString *)url;

@end
