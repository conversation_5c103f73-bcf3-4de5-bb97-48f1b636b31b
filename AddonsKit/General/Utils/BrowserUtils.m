//
//  BrowserUtils.m
//  PPBrowser
//
//  Created by qingbin on 2022/9/26.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "BrowserUtils.h"

@interface BrowserUtils ()

@end

@implementation BrowserUtils

+ (BOOL)isiPhone
{
    return [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPhone;
}

+ (BOOL)isiPad
{
    return [UIDevice currentDevice].userInterfaceIdiom == UIUserInterfaceIdiomPad;
}

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static BrowserUtils* obj;
    dispatch_once(&onceToken, ^{
        obj = [BrowserUtils new];
    });
    
    return obj;
}

- (CGSize)transitionToSize
{
    if(CGSizeEqualToSize(_transitionToSize, CGSizeZero)) {
        // 主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
        float screenWidth = [BrowserUtils screenWidth];
        float screenHeight = [BrowserUtils screenHeight];
        
        _transitionToSize = CGSizeMake(screenWidth, screenHeight);
    }
    
    return _transitionToSize;
}

- (CGSize)sizeForNewTabPage
{
    if(CGSizeEqualToSize(_sizeForNewTabPage, CGSizeZero)) {
        // 主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
        float screenWidth = [BrowserUtils screenWidth];
        float screenHeight = [BrowserUtils screenHeight];
        
        _sizeForNewTabPage = CGSizeMake(screenWidth, screenHeight);
    }
    
    return _sizeForNewTabPage;
}

+ (CGFloat)valueForiPad:(CGFloat)ipad
               standard:(CGFloat)standard
{
    NSNumber *result = [self _objectForiPad:@(ipad) standard:@(standard)];
    return result.floatValue;
}

+ (CGSize)sizeForiPad:(CGSize)ipad
             standard:(CGSize)standard
{
    NSNumber *result = [self _objectForiPad:@(ipad) standard:@(standard)];
    return result.CGSizeValue;
}

+ (NSString*)stringForiPad:(NSString*)ipad
                  standard:(NSString*)standard
{
    NSString *result = [self _objectForiPad:ipad standard:standard];
    return result;
}

+ (id)_objectForiPad:(id)ipad standard:(id)standard
{
    if ([self isiPad]) {
        return ipad;
    } else {
        return standard;
    }
}

+ (LocalizableOption)localizableOption
{
    LocalizableOption option = LocalizableOption_en;
    NSString* val = NSLocalizedString(@"opensearch.value", nil);
    if(val.length > 0) {
        option = [val intValue];
    }
    
    return option;
}

// 主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
+ (float)screenHeight
{
    //iPad不用判断
    if([self isiPad]) return kScreenHeight;
    
    //iPhone默认竖屏
    return MAX(kScreenWidth, kScreenHeight);
}

// 主要是为了适配iPhone横屏启动, 导致宽高错乱的问题
+ (float)screenWidth
{
    //iPad不用判断
    if([self isiPad]) return kScreenWidth;
    
    //iPhone默认竖屏
    return MIN(kScreenWidth, kScreenHeight);
}

@end
