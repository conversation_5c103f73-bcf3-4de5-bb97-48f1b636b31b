//
//  DatabaseUnit+CreateTable.m
//  Saber (iOS)
//
//  Created by q<PERSON><PERSON> on 2022/12/27.
//

#import "DatabaseUnit+CreateTable.h"

#import "ReactiveCocoa.h"
#import "PreferenceManager.h"

#import "AiSearchModel.h"
#import "ExtConfigItem.h"
#import "AdBlockModel.h"

@implementation DatabaseUnit (CreateTable)

+ (DatabaseUnit*)createTabTable
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //#用户脚本
        //noframes-0,运行在MainFrame, frame-1,运行在allFrame
        //带下划线_的表示字符串数组
        NSString* command = @"CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, kNamespace TEXT, version TEXT,  desc TEXT, iconUrl TEXT, runAt TEXT, noframes INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, injectMode INTEGER, isAutoUpdate INTEGER, ppOrder INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, sql_resourceUrls TEXT, whiteList TEXT, blackList TEXT, updateTime TEXT DEFAULT '1', ctime TEXT)";
        BOOL result = [db executeUpdate:command];
        //增加注入模式字段
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_userscript'";
        FMResultSet *reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
                if ([data rangeOfString:@"injectMode"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD injectMode INTEGER";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"ppOrder"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD ppOrder INTEGER";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"kNamespace"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD kNamespace TEXT";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD updateTime TEXT DEFAULT '1'";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"whiteList"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD whiteList TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
                
                if ([data rangeOfString:@"blackList"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_userscript ADD blackList TEXT DEFAULT ''";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
        }
        
        //#标记模式
        command = @"CREATE TABLE IF NOT EXISTS t_tagit(uuid TEXT PRIMARY KEY, host TEXT, xpath TEXT, originUrl TEXT, isActive INTEGER, updateTime TEXT DEFAULT '1', ctime TEXT)";
        result = [db executeUpdate:command];
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_tagit'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
                if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_tagit ADD updateTime TEXT DEFAULT '1'";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
        }
        
        //广告过滤
        command = @"CREATE TABLE IF NOT EXISTS t_adblock(uuid TEXT PRIMARY KEY, title TEXT, url TEXT, type INTEGER, isActive INTEGER, updateTime TEXT DEFAULT '1', ctime TEXT)";
        result = [db executeUpdate:command];
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_adblock'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
                if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_adblock ADD updateTime TEXT DEFAULT '1'";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
        }
        //插入内置类型
        //内置广告类型
//        NSUserDefaults* userDefault = [NSUserDefaults standardUserDefaults];
//        NSNumber* val = [userDefault objectForKey:@"t_adblock"];
//        if(!val) {
//            //只插入一次, 后面即使删除了也不再添加回来
//            [userDefault setObject:@(true) forKey:@"t_adblock"];
//            [userDefault synchronize];
//
//            NSArray* array = @[
//                [AdBlockModel easylistChinaModel],
//            ];
//
//            command = @"INSERT INTO t_adblock(uuid, title, url, type, isActive, updateTime, ctime) VALUES (?,?,?,?,?,?,?);";
//            for(int i=0;i<array.count;i++) {
//                AdBlockModel* item = array[i];
//                result = [db executeUpdate:command,
//                               item.uuid,
//                               item.title?:@"",
//                               item.url?:@"",
//                               @(item.type),
//                               @(item.isActive),
//                               item.updateTime,
//                               item.ctime];
//            }
//        }
        
        //广告过滤白名单
        command = @"CREATE TABLE IF NOT EXISTS t_adblock_whitelist(uuid TEXT PRIMARY KEY, url TEXT, ctime TEXT)";
        result = [db executeUpdate:command];
        
        //#任务表
        //originUrl音视频所在网页url
        command = @"CREATE TABLE IF NOT EXISTS t_download(uuid TEXT PRIMARY KEY, parentId TEXT, title TEXT, url TEXT, originalUrl TEXT, fileName TEXT, UTIS INTEGER, status INTEGER, transformStatus INTEGER, totalUnitCount INTEGER, seekTime REAL, ctime TEXT)";
        result = [db executeUpdate:command];
        
        //#任务分组表
        command = @"CREATE TABLE IF NOT EXISTS t_download_section(uuid TEXT PRIMARY KEY, title TEXT, isOpen INTEGER, ctime TEXT)";
        result = [db executeUpdate:command];
        
        //插入一条根节点
        command = @"INSERT INTO t_download_section(uuid, title, isOpen, ctime) VALUES (?,?,?,?)\
        on CONFLICT(uuid) DO UPDATE SET uuid=excluded.uuid WHERE excluded.uuid=t_download_section.uuid;\
        ";
        //#我的任务
        result = [db executeUpdate:command, @"root", NSLocalizedString(@"video.myTask", nil), @(YES), @(0)];
        
        //#智能搜索
        command = @"CREATE TABLE IF NOT EXISTS t_aisearch(uuid TEXT PRIMARY KEY, name TEXT, sql_rule_keys TEXT, url TEXT, host TEXT, query TEXT, isActive INTEGER, iconName TEXT, updateTime TEXT DEFAULT '1')";
        result = [db executeUpdate:command];
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_aisearch'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
                if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_aisearch ADD updateTime TEXT DEFAULT '1'";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
        }
        
        //插入数据
        NSArray* items = @[
            [AiSearchModel baidu],
            [AiSearchModel google],
            [AiSearchModel bing],
            [AiSearchModel toutiao],
            [AiSearchModel sougou],
            [AiSearchModel _360],
            [AiSearchModel duckduckgo],
            [AiSearchModel ecosia],
            [AiSearchModel yahoo],
            [AiSearchModel yandex],
            [AiSearchModel weibo],
            [AiSearchModel taobao],
            [AiSearchModel greasyfork],
            [AiSearchModel xiaohongshu],
            [AiSearchModel zhihu],
        ];
        for(AiSearchModel* item in items) {
            command = @"INSERT INTO t_aisearch(uuid, name, sql_rule_keys, url, host, query, isActive, iconName, updateTime) VALUES (?,?,?,?,?,?,?,?,?)\
            on CONFLICT(uuid) DO UPDATE SET name=excluded.name WHERE excluded.uuid=t_aisearch.uuid;\
            ";
            NSString* sql_rule_keys = [item.rule_keys componentsJoinedByString:@","];
            result = [db executeUpdate:command, item.uuid, item.name, sql_rule_keys, item.url, item.host, item.query, @(item.isActive), item.iconName, @"1"];
        }
        
        //#配置表，主要用来和扩展共享数据
        //包括是否启用油猴功能，是否启动视频嗅探，是否启用标记模式，是否启用智能搜索，是否启用屏蔽牛皮癣广告
        //是否启用暗黑模式及其选择，是否启用长按探测元素
        command = @"CREATE TABLE IF NOT EXISTS t_ext_config(uuid INTEGER PRIMARY KEY, isActive INTEGER, option INTEGER default 0, updateTime TEXT DEFAULT '1')";
        result = [db executeUpdate:command];
        command = @"SELECT sql FROM sqlite_master WHERE tbl_name='t_ext_config'";
        reader = [db executeQuery:command];
        if ([reader next]) {
            id data = [reader objectForColumnIndex:0];
            if ([data isKindOfClass:[NSString class]]) {
                if ([data rangeOfString:@"updateTime"].location == NSNotFound) {
                    @try {
                        command = @"ALTER TABLE t_ext_config ADD updateTime TEXT DEFAULT '1'";
                        [db executeUpdate:command];
                    }
                    @catch (NSException *exception){}
                    @finally {}
                }
            }
        }
        
        //插入数据
        items = @[
            [ExtConfigItem tampermonkey],
            [ExtConfigItem sniffer],
            [ExtConfigItem tagit],
            [ExtConfigItem adblock],
            [ExtConfigItem aiSearch],
            [ExtConfigItem blockTheFixedAds],
            [ExtConfigItem darkMode],
            [ExtConfigItem longPressDetect],
            [ExtConfigItem showSnifferButton],
        ];
        for(ExtConfigItem* item in items) {
            command = @"INSERT INTO t_ext_config(uuid, isActive, option, updateTime) VALUES (?,?,?,?)\
            on CONFLICT(uuid) DO UPDATE SET uuid=excluded.uuid WHERE excluded.uuid=t_ext_config.uuid;\
            ";
            result = [db executeUpdate:command, @(item.uuid), @(item.isActive), @(item.option), @"1"];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
