//
//  DatabaseUtil.m
//  Line
//
//  Created by qingbin on 2018/6/21.
//  Copyright © 2018年 qingbin. All rights reserved.
//

#import "DatabaseUtil.h"
#import "FMDatabaseQueue.h"
#import "FMDatabase.h"
#import "DatabaseUnit.h"
#import "NSFileManager+Helper.h"

static NSString* gbl_dbPath = @"addons";
static NSString *gbl_dbName = @"db.sqlite3";

@interface DatabaseUtil()
@property(nonatomic,strong) FMDatabaseQueue* queue;
@property(nonatomic,copy) NSString* dbPath;
@property(nonatomic,copy) NSString* dbName;

@property(nonatomic,strong) dispatch_queue_t taskQueue; //数据库执行操作在串行队列中
@end

@implementation DatabaseUtil

+ (instancetype) shareInstance
{
	static DatabaseUtil* instance = nil;
	static dispatch_once_t onceToken;
	dispatch_once(&onceToken, ^{
		instance = [[self alloc]init];
	});

	return instance;
}

- (instancetype)init
{
	self = [super init];

	if(self) {
		self.dbName = gbl_dbName;

//		NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
        NSString *directory = [NSFileManager groupPath];
		self.dbPath = [directory stringByAppendingPathComponent:gbl_dbPath];
        
		NSString* filePath = [self.dbPath stringByAppendingPathComponent:self.dbName];
		if(![[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
			[[NSFileManager defaultManager] createDirectoryAtPath:self.dbPath
									  withIntermediateDirectories:YES
													   attributes:nil
															error:nil];
			[self createFileAtPath:filePath];
		}
        
        NSLog(@"当前数据库地址: %@",filePath);
        
		self.queue = [FMDatabaseQueue databaseQueueWithPath:filePath];
        self.taskQueue = dispatch_queue_create("com.database.serial.queue", NULL);
	}

	return self;
}

- (void)executeUnit:(DatabaseUnit*)unit
{
	if(!unit) return;

    dispatch_async(self.taskQueue, ^{
        [self.queue inDatabase:^(FMDatabase *db){
            if(![db open]) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    if(unit.completeBlock) {
                        unit.completeBlock(nil, NO);
                    }
                });
                
                return;
            }

            BOOL rollback = NO;
            @try {
                [db beginTransaction];
                if(unit.executeBlock) {
                    unit.executeBlock(db);
                }
            } @catch (NSException *exception){
                rollback = YES;
            } @finally {
                if(rollback) {
                    [db rollback];
                    
                    dispatch_async(dispatch_get_main_queue(), ^{
                        if(unit.completeBlock) {
                            unit.completeBlock(nil, NO);
                        }
                    });
                }
                else {
                    [db commit];
                }
            }
            [db close];
        }];
    });
}

- (void)asyncOnQueue:(void(^)(void))executeBlock
{
    dispatch_async(self.taskQueue, ^{
        if(executeBlock) {
            executeBlock();
        }
    });
}

- (void)createFileAtPath:(NSString *)filePath
{
    NSFileManager* fileManager = [NSFileManager defaultManager];

    if([fileManager fileExistsAtPath:filePath]) {
        [fileManager removeItemAtPath:filePath error:nil];
    }

    [fileManager createFileAtPath:filePath contents:nil attributes:nil];
}

@end
