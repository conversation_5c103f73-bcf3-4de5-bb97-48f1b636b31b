//
//  NSURL+Extension.m
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "NSURL+Extension.h"

@implementation NSURL (Extension)

// Obtain a schemeless absolute string
- (NSString*)schemelessAbsoluteString
{
    NSString* scheme = self.scheme;
    if(scheme.length > 0) {
        return [[self absoluteString] stringByReplacingOccurrencesOfString:[NSString stringWithFormat:@"%@://",scheme] withString:@""];
    } else {
        return [self absoluteString];
    }
}

- (BOOL)schemeIsValid
{
    // The list of permanent URI schemes has been taken from http://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml
    if(self.scheme.length == 0) return NO;
    
    NSArray* permanentURISchemes = @[@"aaa", @"aaas", @"about", @"acap", @"acct", @"cap", @"cid", @"coap", @"coaps", @"crid", @"data", @"dav", @"dict", @"dns", @"example", @"file", @"ftp", @"geo", @"go", @"gopher", @"h323", @"http", @"https", @"iax", @"icap", @"im", @"imap", @"info", @"ipp", @"ipps", @"iris", @"iris.beep", @"iris.lwz", @"iris.xpc", @"iris.xpcs", @"jabber", @"javascript", @"ldap", @"mailto", @"mid", @"msrp", @"msrps", @"mtqp", @"mupdate", @"news", @"nfs", @"ni", @"nih", @"nntp", @"opaquelocktoken", @"pkcs11", @"pop", @"pres", @"reload", @"rtsp", @"rtsps", @"rtspu", @"service", @"session", @"shttp", @"sieve", @"sip", @"sips", @"sms", @"snmp", @"soap.beep", @"soap.beeps", @"stun", @"stuns", @"tag", @"tel", @"telnet", @"tftp", @"thismessage", @"tip", @"tn3270", @"turn", @"turns", @"tv", @"urn", @"vemmi", @"vnc", @"ws", @"wss", @"xcon", @"xcon-userid", @"xmlrpc.beep", @"xmlrpc.beeps", @"xmpp", @"z39.50r", @"z39.50s"];
    
    NSString* scheme = [self.scheme lowercaseString];
    return [permanentURISchemes containsObject:scheme];
}

- (BOOL)isIPv6
{
    return [self.host containsString:@":"];
}

/**
 * Returns just the domain, but with the same scheme.
 *
 * E.g., https://m.foo.com/bar/baz?noo=abc#123  => https://foo.com
 *
 * Any failure? Return this URL.
 */
- (NSURL*)domainURL
{
    NSString* normalized = [self normalizedHost:NO];
    if(normalized.length > 0) {
        NSURLComponents* components = [NSURLComponents new];
        components.scheme = self.scheme;
        components.port = self.port;
        components.host = normalized;
        if(components.URL) return components.URL;
    }
    
    return self;
}

- (NSURL*)withoutWWW
{
    NSString* normalized = [self normalizedHost:YES];
    if(normalized.length > 0) {
        NSURLComponents* components = [NSURLComponents componentsWithURL:self resolvingAgainstBaseURL:NO];
        components.scheme = self.scheme;
        components.port = self.port;
        components.host = normalized;
        if(components.URL) return components.URL;
    }
    
    return self;
}

- (NSString*)normalizedHost:(BOOL)stripWWWSubdomainOnly
{
    // Use components.host instead of self.host since the former correctly preserves
    // brackets for IPv6 hosts, whereas the latter strips them.
    
    NSURLComponents* components = [NSURLComponents componentsWithURL:self resolvingAgainstBaseURL:false];
    NSString* host = components.host;
    if(host.length == 0) return nil;
    
    NSString* textToReplace;
    if(stripWWWSubdomainOnly) {
        textToReplace = @"^(www)\\.";
    } else {
        textToReplace = @"^(www|mobile|m|h5)\\.";
    }

    NSRange range = [host rangeOfString:textToReplace options:NSRegularExpressionSearch];
    if(range.location != NSNotFound) {
        host = [host stringByReplacingCharactersInRange:range withString:@""];
    }
    
    return host;
}


/**
 * Returns just the domain, and without the same scheme.
 *
 * E.g., https://m.foo.com/bar/baz?noo=abc#123  => foo.com
 *
 * Any failure? Return this URL.
 */
- (NSString*)normalizedHost
{
    return [self normalizedHost:NO];
}

//- (NSString*)hostSLD
//{
//    
//}

@end
