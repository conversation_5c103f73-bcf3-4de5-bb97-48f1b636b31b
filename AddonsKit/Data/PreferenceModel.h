//
//  PreferenceModel.h
//  PPBrowser
//
//  Created by qingbin on 2022/6/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "PPEnums.h"
#import "MJExtension.h"

@interface PreferenceModel : NSObject

@property (nonatomic, strong) NSNumber* enabledPlayer;

@property (nonatomic, strong) NSNumber* allowsCellularAccess;
///这个线程数非常重要，5/6是比较合适的数值，如果设置的比较大，例如10，那么ikandy.fun进入后台之后，就会下载暂停
///又或者ksksl.com进入后台之后，那么下载速度时高时低(例如会变成0，再变大)
///参考Tiercel最大并发数相关：
///https://github.com/Danie1s/Tiercel/wiki/iOS-%E5%8E%9F%E7%94%9F%E7%BA%A7%E5%88%AB%E5%90%8E%E5%8F%B0%E4%B8%8B%E8%BD%BD%E8%AF%A6%E8%A7%A3#%E6%9C%80%E5%A4%A7%E5%B9%B6%E5%8F%91%E6%95%B0
@property (nonatomic, strong) NSNumber* maxDownloadNumber;
//缓存文件排序,0-时间升序，时间降序，文件名称升序，文件名称降序
@property (nonatomic, strong) NSNumber* downloadSortType;

@property (nonatomic, strong) NSNumber* isAgreedUMengPermission;
//是否是会员
//@property (nonatomic, strong) NSNumber* isVip;
//暗黑模式, 0-跟随系统, 1-浅色, 2-深色
@property (nonatomic, strong) NSNumber* darkModeStatus;
//暗黑模式, 表示当前的暗黑模式
@property (nonatomic, strong) NSNumber* isDarkTheme;
//开启iCloud
@property (nonatomic, strong) NSNumber* enablediCloud2;
//iCloud同步时间戳
@property (nonatomic, strong) NSNumber *iCloudSyncTimestamp;
//第2次打开则弹评分弹窗
@property (nonatomic, strong) NSNumber *reviewCount;
//是否开启面容识别
@property (nonatomic, strong) NSNumber *isOpenBiometricVerify;
//首页开启代码编辑器
@property (nonatomic, strong) NSNumber *isEnabledCodeEditor;

//
@property (nonatomic, strong) NSNumber* downloadCount;
//
@property (nonatomic, strong) NSDate* today;

//4.0切换了去广告解析引擎，需要重新编译一次广告规则
@property (nonatomic, strong) NSNumber *adblockVersion;

//播放方式(顺序播放/列表循环/单曲循环)
@property (nonatomic, strong) NSNumber* playStatus;

//是否已经添加内置广告类型
@property (nonatomic, strong) NSNumber *isAdblockBuiltIn;

@end

