//
//  PreferenceManager.h
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "PPEnums.h"

#import "MJExtension.h"

#import "PreferenceModel.h"

@class ReadBaseConfigModel;

@interface PreferenceManager : NSObject

// 分两块, 其它自定义和阅读模式自定义
+ (instancetype)shareInstance;

// 编码保存
- (void)encode;

// 解码读数据
- (void)decode;

//其它自定义
@property (nonatomic, strong) PreferenceModel* items;

@end


