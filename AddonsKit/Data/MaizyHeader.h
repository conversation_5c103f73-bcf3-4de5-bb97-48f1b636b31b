//
//  MaizyHeader.h
//  Maizy<PERSON>lock
//
//  Created by qing<PERSON> on 2022/1/24.
//

#ifndef MaizyHeader_h
#define MaizyHeader_h

//为了解决在模拟器上能运行的问题
//正式服=0 测试=1
#define ADDONS_DEBUG 0
//是否开启友盟
#define UMENG_ENABLED 1

#define kUMengAppKey @"641a635fba6a5259c4233c1f"
#define sChannelId @"appStore"

//App id
#define kAppId @"6446811843"
//永久会员
#define kVipProductId @"com.qingbin.addons.vipmember"
//App 专用共享密钥
#define kSharedSecret @"43fb9319e9d44fcc93e4f69fd0926989"

//group id
#define kGroupPath @"group.com.qingbin.addons"

//广告过滤
#define kAdBlockKey @"Key_Addons_ContentBlocker_Enabled"

//参考http://colllor.com/0080fe
#define kMainColor @"#0080fe"
#define kSelectedColor @"#aa1bfe"

#define kScreenWidth ([UIScreen mainScreen].bounds.size.width)
#define kScreenHeight ([UIScreen mainScreen].bounds.size.height)

#ifdef DEBUG
#define bCatchCrash NO
#define bLogSwitch YES
#define kSendLog REALTIME
#else
#define bCatchCrash YES
#define bLogSwitch NO
#define kSendLog BATCH
#define NSLog(...) {}
#endif

#if DEBUG
#define __FILENAME__ (strrchr(__FILE__,'/')+1)
#define LOG_ERROR(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Error:", format, ##__VA_ARGS__)
#define LOG_WARNING(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Warning:", format, ##__VA_ARGS__)
#define LOG_INFO(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Info:", format, ##__VA_ARGS__)
#define LOG_DEBUG(format, ...) FFLog_(__FILENAME__, __LINE__, __FUNCTION__, @"Debug:", format, ##__VA_ARGS__)

#define FFLog_(file, line, func, prefix, format, ...) {    \
NSString *aMessage = [NSString stringWithFormat:@"%@ %@",prefix, [NSString stringWithFormat:format, ##__VA_ARGS__, nil]]; \
NSLog(@"%@",aMessage);    \
}
#else
#define LOG_ERROR(format, ...) {}
#define LOG_WARNING(format, ...) {}
#define LOG_INFO(format, ...) {}
#define LOG_DEBUG(format, ...) {}
#endif

#define kWebViewEstimatedProgress @"estimatedProgress"
#define kWebViewURL @"URL"
#define kWebViewTitle @"title"
#define kWebViewCanGoBack @"canGoBack"
#define kWebViewCanGoForward @"canGoForward"
#define kWebViewLoading @"loading"
#define kWebViewHasOnlySecureContent @"hasOnlySecureContent"
#define kWebViewServerTrust @"serverTrust"

#define kDownloadIdentifier @"com.addons.downloadmodule"

//必须是16个字母
#define kPassword @"_qingbin_&*^1024"

// 等屏幕宽的广告，按比例放大，a是750屏幕下的高度point
#define kAdvertViewHeight(a) (a*kScreenWidth/375)

#endif /* MaizyHeader_h */

