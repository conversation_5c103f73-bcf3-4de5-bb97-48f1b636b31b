//
//  PreferenceManager.m
//  PPBrowser
//
//  Created by qingbin on 2022/3/20.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "PreferenceManager.h"

#import "BrowserUtils.h"

@interface PreferenceManager ()

@end

@implementation PreferenceManager

+ (instancetype)shareInstance
{
    static dispatch_once_t onceToken;
    static PreferenceManager* obj;
    dispatch_once(&onceToken, ^{
        obj = [PreferenceManager new];
    });
    
    return obj;
}

- (instancetype)init
{
    self = [super init];
    if(self) {
//        [PreferenceModel mj_setupIgnoredCodingPropertyNames:^NSArray *{
//            return @[@"isNoImage"];
//        }];
    }
    
    return self;
}

// 编码保存
- (void)encode
{
    if(self.items) {
        NSData* data = [NSKeyedArchiver archivedDataWithRootObject:self.items];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:@"key_preferencemanager_commonconfig"];
    }
}

// 解码读数据
- (void)decode
{
    NSData* data = [[NSUserDefaults standardUserDefaults] objectForKey:@"key_preferencemanager_commonconfig"];
    self.items = [NSKeyedUnarchiver unarchiveObjectWithData:data];
        
    if(!self.items) {
        self.items = [PreferenceModel new];
    }
    
    if(!self.items.enabledPlayer) {
        self.items.enabledPlayer = @(YES);
    }
    
    if(!self.items.allowsCellularAccess) {
        self.items.allowsCellularAccess = @(YES);
    }
    
    if(!self.items.maxDownloadNumber) {
        self.items.maxDownloadNumber = @(6);
    }

    if(!self.items.downloadSortType) {
        self.items.downloadSortType = @(DownloadFileSortTypeTimeAscend);
    }
    
    if(!self.items.isAgreedUMengPermission) {
        self.items.isAgreedUMengPermission = @(NO);
    }
    
    if(!self.items.reviewCount) {
        self.items.reviewCount = @(0);
    }
    
    if(!self.items.darkModeStatus) {
        self.items.darkModeStatus = @(DarkModeStatusAuto);
    }
    
    if(!self.items.isDarkTheme) {
        self.items.isDarkTheme = @(NO);
    }
    
    if(!self.items.enablediCloud2) {
        self.items.enablediCloud2 = @(NO);
    }
    
    if(!self.items.isOpenBiometricVerify) {
        //默认关闭
        self.items.isOpenBiometricVerify = @(NO);
    }
    
    if(!self.items.downloadCount) {
        self.items.downloadCount = @(0);
    }
    
    if(!self.items.today) {
        self.items.today = [NSDate date];
    }
    
    if(!self.items.isEnabledCodeEditor) {
        //默认关闭
        self.items.isEnabledCodeEditor = @(NO);
    }
    
    //播放设置
    if(!self.items.playStatus) {
        self.items.playStatus = @(PlayModeStatusDefautl);
    }
    
    if(!self.items.adblockVersion) {
        self.items.adblockVersion = @(0);
    }
    
    if(!self.items.isAdblockBuiltIn) {
        self.items.isAdblockBuiltIn = @(NO);
    }
}

@end
