//
//  PPNotifications.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/4/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#ifndef PPNotifications_h
#define PPNotifications_h

#define kReadabilityMessageHelperNotification @"kReadabilityMessageHelperNotification"

#define kPlaylistDetectorNotification @"kPlaylistDetectorNotification"

#define kDownloadProgressNotification @"kDownloadProgressNotification"

#define kDownloadSuccessNotification @"kDownloadSuccessNotification"

#define kDownloadFailureNotification @"kDownloadFailureNotification"

#define kDownloadReloadNotification @"kDownloadReloadNotification"

#define kDownloadNewTaskNotification @"kDownloadNewTaskNotification"

// 添加一个脚本
#define kAddUserScriptNotification @"kAddUserScriptNotification"

// 编辑了脚本信息,重新加载脚本
#define kReloadUserScriptNotification @"kReloadUserScriptNotification"

// 更新了脚本
#define kUpdateUserScriptNotification @"kUpdateUserScriptNotification"

// 切换夜间模式
#define kDarkThemeDidChangeNotification @"kDarkThemeDidChangeNotification"
 
// 长按事件
#define kContextMenuNotification @"kContextMenuNotification"

// iCloud更新
#define kBookMarkDidChangeNotification @"kBookMarkDidChangeNotification"

// 屏幕旋转
#define kOrientationDidChangeNotification @"kOrientationDidChangeNotification"

// 记录seekTime
#define kUpdateSeekTimeNotification @"kUpdateSeekTimeNotification"

// 开启获取关闭代码编辑器通知
#define kReloadCodeEditorNotification @"kReloadCodeEditorNotification"

// 成为会员/恢复会员
#define kReloadVipNotification @"kReloadVipNotification"

// 切换了播放循环方式
#define kChangePlayStatusNotification @"kChangePlayStatusNotification"

// CloudKit接收到订阅信息
#define kCloudKitDataSubscriptionNotification @"kCloudKitDataSubscriptionNotification"

// CloudKit接收到Record更新的通知
#define kCloudKitDataDidChangeNotification @"kCloudKitDataDidChangeNotification"


#endif /* PPNotifications_h */
