//
//  PPEnums.h
//  PPBrowser
//
//  Created by qingbin on 2022/4/6.
//  Copyright © 2022 qingbin. All rights reserved.
//

#ifndef PPEnums_h
#define PPEnums_h

typedef NS_ENUM(NSInteger,HistorySearchType) {
    HistorySearchTypeUnknown = 0,
    HistorySearchTypeKeyword = 1,           //搜索引擎
    HistorySearchTypeUrl = 2,               //网址url
    HistorySearchTypePasteboard = 3,        //剪贴板,辅助
};

typedef NS_ENUM(NSInteger,SystemUTIs) {
    SystemUTIsUnknown = 0,
    SystemUTIsVideo ,          //视频
    SystemUTIsAudio ,          //音频
    SystemUTIsHLS,             //m3u8
};

typedef NS_ENUM(NSInteger,M3U8TransformStatus) {
    M3U8TransformStatusInit = 0,
    M3U8TransformStatusTransforming ,  //转换中
    M3U8TransformStatusSucceed ,       //转换成功
    M3U8TransformStatusFailed ,        //转换失败
};

typedef NS_ENUM(NSInteger,MimeType) {
    MimeTypeUnknown = 0,     //其它都归类为此类
    MimeTypeVideo ,          //视频
    MimeTypeAudio ,          //音频
    MimeTypePhoto,           //图片
    MimeTypeZip,             //压缩文件
    MimeTypePDF,             //pdf文件
    MimeTypeFile,            //text开头
    MimeTypeIpa,             //ipa文件
};

typedef NS_ENUM(NSInteger,PlayModelItemStatus) {
    PlayModelItemStatusFile = 0,  //文件url
    PlayModelItemStatusPlaylistDetector ,    //PlaylistDetector检测出来的链接
    PlayModelItemStatusContextMenuDetector , //ContextMenuHelper检测出来的链接
};

typedef enum SettingType {
    SettingTypeVIP,         //vip
    SettingTypeGeneralSetting, //通用设置
    SettingTypeDataClear,      //清理数据
    SettingTypeHomeSetting, //主页设置
    SettingTypeWebSetting,  //网页设置
//    SettingTypePlaySetting, //播放设置
    SettingTypeSearchSetting, //搜索设置
    SettingTypeTaskSetting, //任务设置
    SettingTypeRate ,       //评价应用
    SettingTypeFeedback ,   //意见反馈
    SettingTypeShare ,      //分享给朋友
    SettingTypeAboutUs ,    //关于我们
    SettingTypeProtocol ,   //隐私协议
    SettingTypeGuideline, //用户指南
    SettingTypeTagit,       //标记模式
    SettingTypeiCloud,      //iCloud同步
} SettingType;

typedef enum LocalizableOption {
    LocalizableOptionZh_Hans = 0,       //简体
    LocalizableOptionZh_Hant = 1,       //繁体
    LocalizableOption_en   = 2,       //英语
} LocalizableOption;

typedef enum ExportOption {
    ExportOptionDefault = 0,   //默认
    ExportOptionAlbum   = 1,   //相册
    ExportOptionFiles   = 2,   //文件-Focus-Downloads
} ExportOption;

typedef enum ExtConfigOption {
    ExtConfigOptionDefault = 0,
    ExtConfigOptionTampermonkey = 1,
    ExtConfigOptionSniffer   = 2,
    ExtConfigOptionTagit     = 3,
    ExtConfigOptionAiSearch  = 4,
    ExtConfigOptionBlockTheFixedAds  = 5,
    ExtConfigOptionDarkMode  = 6,
    ExtConfigOptionLongPressDetect = 7,
//    ExtConfigOptionNoImage = 8,
    ExtConfigOptionAdblock = 9,
    ExtConfigOptionShowSnifferButton = 10,
} ExtConfigOption;

typedef enum HomeItemOption {
    HomeItemOptionHelp = 0,   //查看帮助
} HomeItemOption;

typedef enum DarkModeStatus {
    DarkModeStatusAuto = 0,   //自动
    DarkModeStatusLight = 1,  //浅色
    DarkModeStatusDark = 2,   //深色
} DarkModeStatus;

typedef NS_ENUM(NSInteger, DataClearType) {
    DataClearTypeTmp = 0,
    DataClearTypeInbox = 1,
    DataClearTypeWebView = 2,
    DataClearTypeDownloadSessions = 3,  //m3u8缓存
    DataClearTypeImages = 4, //sdwebimage
};

typedef NS_ENUM(NSInteger, SnifferPolicy) {
    SnifferPolicy0 = 0,     /// referer: originUrl的domain
    SnifferPolicy1,         /// referer不传
    SnifferPolicy2,         /// referer: originUrl的全部
    SnifferPolicy3,         /// referer: http(s)://domain, 即origin， 靠,missav会检测这个https请求协议
    SnifferPolicy4,         /// 什么都不传
};

typedef NS_ENUM(NSInteger, FaceIDStatus) {
    FaceIDStatusDefault = 0,  //默认
    FaceIDStatusHandling = 1,  //验证中
    FaceIDStatusFinish = 2,   //验证成功
};

typedef NS_ENUM(NSInteger, AdBlockType) {
    AdBlockTypeDefault = 0, //默认
    AdBlockTypeText = 1,    //直接编写
    AdBlockTypeURL = 2,     //从链接导入
    AdBlockTypeFile = 3,    //从文件导入
};

//https://violentmonkey.github.io/posts/inject-into-context/
typedef NS_ENUM(NSInteger, UserScriptInjectMode) {
    UserScriptInjectAuto = 0,  //默认, content
    UserScriptInjectPage = 1,  //Page
    UserScriptInjectContent = 2, //content
};

//缓存的文件排序
typedef NS_ENUM(NSInteger,DownloadFileSortType) {
    DownloadFileSortTypeTimeAscend = 0,
    DownloadFileSortTypeTimeDescend = 1,
    DownloadFileSortTypeNameAscend = 2,
    DownloadFileSortTypeNameDescend = 3
};

//更新相关状态
typedef NS_ENUM(NSInteger, UpdateStatus) {
    UpdateStatusDefault = 0, //默认,初始化
    UpdateStatusLoading, //更新中
    UpdateStatusFinishRequest,  //更新请求完毕
    UpdateStatusFailed, //更新失败
};

typedef enum ContextMenuType {
    ContextMenuTypeTitle = 0,   //标题
    ContextMenuTypeItem = 1,  //选项
} ContextMenuType;

typedef enum PlayModeStatus {
    PlayModeStatusDefautl = 0,      //默认(只播放当前视频)
    PlayModeStatusRepeatList = 1,   //列表循环
    PlayModeStatusRepeatOne = 2,    //单曲循环
} PlayModeStatus;

typedef enum CloudModelType {
    CloudModelTypeDefault = 0,   //默认
    CloudModelTypeUserScript = 1,  //脚本
    CloudModelTypeAdBlock = 2, //广告过滤规则
    CloudModelTypeTagit = 3, //标记模式
} CloudModelType;

// m3u8格式
typedef NS_ENUM(NSInteger,M3U8FormatType) {
    M3U8FormatTypeDefault = 0,  //M3U8默认类型
    M3U8FormatTypeMaster = 1,   //master hls,多码率
    M3U8FormatTypeMedia = 2,    //EXT-X-MEDIA, 音频和视频分离
};

typedef NS_ENUM(NSInteger, ExtensionReloadStatus) {
    //bootstrap,reload数据
    ExtensionReloadStatusBootstrap = 0,
    //PopUp初始化
    ExtensionReloadStatusPopUp = 1
};

#endif /* PPEnums_h */
