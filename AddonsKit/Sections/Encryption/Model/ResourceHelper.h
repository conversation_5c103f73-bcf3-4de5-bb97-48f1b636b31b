//
//  ResourceHelper.h
//  PPBrowser
//
//  Created by qingbin on 2023/3/23.
//  Copyright © 2023 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface ResourceHelper : NSObject

//分开初始化,优化加载时间
//主APP
+ (instancetype)saber;

//扩展
+ (instancetype)addons;

/* Saber相关脚本, Start */
@property (readonly) NSString *__firefox__;

@property (readonly) NSString *installHelper;

@property (readonly) NSString *supported_apis;

@property (readonly) NSString *convert2RegExp;

@property (readonly) NSString *MatchPattern;

@property (readonly) NSString *parse_meta_line;

@property (readonly) NSString *parse_user_script;

@property (readonly) NSString *jSConverter;

@property (readonly) NSString *jsAdBlockConvert;

/* Saber相关脚本, End */


/* Addons相关脚本, Start */

@property (readonly) NSString* blockTheFixedAds;

@property (readonly) NSString* addons_installHelper;

@property (readonly) NSString* contextMenuHelper;

@property (readonly) NSString* noImageModeHelper;

//Content Script
@property (readonly) NSString *script_content_segment1;
@property (readonly) NSString *script_content_segment2;
@property (readonly) NSString *script_content_segment3;

//Page Script
@property (readonly) NSString *script_page_segment1;
@property (readonly) NSString *script_page_segment2;
@property (readonly) NSString *script_page_segment3;

/* Addons相关脚本, End */

@end

