//
//  TagitModel.h
//  SaberKit
//
//  Created by q<PERSON><PERSON> on 2023/3/12.
//

#import "BaseModel.h"

#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

@interface TagitModel : BaseModel<SyncProtocol>
// id
@property (nonatomic, strong) NSString* uuid;
// 域名
@property (nonatomic, strong) NSString* host;
// css xpath
@property (nonatomic, strong) NSString* xpath;
// 创建时间
@property (nonatomic, strong) NSString* ctime;
// 源网址
@property (nonatomic, strong) NSString* originUrl;
// 是否正在运行
@property (nonatomic, assign) BOOL isActive;
// 更新时间
@property (nonatomic, strong) NSString *updateTime;

- (instancetype)initWithUrl:(NSString *)url
                      xpath:(NSString *)xpath;

// 辅助
@property (nonatomic, assign) BOOL isLastInSection;


//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为UserScript
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个广告过滤规则是否一致，1、根据uuid,2、判断内容是否相等，如果是URL，那么判断它们的url是否一致
- (BOOL)objectIsEqualTo:(id)obj;

//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

@end


@protocol TagitModel <NSObject>
@end

