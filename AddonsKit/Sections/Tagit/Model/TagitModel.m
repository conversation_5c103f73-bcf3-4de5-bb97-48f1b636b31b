//
//  TagitModel.m
//  SaberKit
//
//  Created by q<PERSON><PERSON> on 2023/3/12.
//

#import "TagitModel.h"
#import "NSURL+Extension.h"

#import "OpenUDID.h"
#import "PPEnums.h"
#import "CloudKitHelper.h"

@implementation TagitModel

- (instancetype)initWithUrl:(NSString *)url
                      xpath:(NSString *)xpath
{
    self = [super init];
    if(self) {
        self.uuid = [[NSUUID UUID] UUIDString];
        self.originUrl = url;
        self.xpath = xpath;
        self.host = [[NSURL URLWithString:url] normalizedHost];
        self.isActive = YES;
        
        self.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    }
    
    return self;
}

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.uuid zoneID:[CloudKitHelper addonsZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(TagitModel.class) recordID:recordID];
    
    return record;
}

//转换成CKRecord
- (CKRecord *)toCKRecord
{
    //CREATE TABLE IF NOT EXISTS t_tagit(uuid TEXT PRIMARY KEY, host TEXT, xpath TEXT, originUrl TEXT, isActive INTEGER, ctime TEXT)
    
    CKRecord* record = [self toDefaultCKRecord];
    
    record[@"uuid"] = self.uuid;
    record[@"host"] = self.host?:@"";
    record[@"xpath"] = self.xpath?:@"";
    record[@"originUrl"] = self.originUrl?:@"";
    record[@"isActive"] = @(self.isActive);
    record[@"ctime"] = self.ctime;
    record[@"updateTime"] = self.updateTime?:@"1";
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeTagit);
    
    return record;
}
//从CKRecord转换为AdBlockModel
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.uuid = record[@"uuid"];
        self.host = record[@"host"];
        self.xpath = record[@"xpath"];
        self.originUrl = record[@"originUrl"];
        self.isActive = [[record objectForKey:@"isActive"] intValue];
        self.ctime = record[@"ctime"];

        NSString* updateTimeText = record[@"updateTime"];
        NSInteger updateTime = [updateTimeText integerValue];
        if(updateTime == 0 || updateTime == 1) {
            //初始化
            updateTime = [record.modificationDate timeIntervalSince1970];
        }
        self.updateTime = [NSString stringWithFormat:@"%ld", updateTime];
    }
    
    return self;
}

//判断两个标记模式是否一致，1、根据uuid,2、判断内容是否相等，判断它们的originUrl和xpath是否一致
- (BOOL)objectIsEqualTo:(id)obj
{
    TagitModel *item = obj;
    if([self.uuid isEqualToString:item.uuid]) return YES;
    
    return [self.originUrl isEqualToString:item.originUrl] && [self.xpath isEqualToString:item.xpath];
}

//返回uuid
- (NSString*)getUuid
{
    return self.uuid;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

@end
