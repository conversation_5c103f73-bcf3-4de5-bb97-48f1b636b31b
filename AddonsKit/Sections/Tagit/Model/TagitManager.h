//
//  TagitManager.h
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BaseModel.h"
#import "TagitModel.h"

@interface TagitManager : NSObject

//提前缓存
- (void)reloadData:(void(^)(void))completion;

- (void)pushElement:(TagitModel *)item;

- (NSArray<TagitModel*>*)getElementsWithHost:(NSString *)host;

- (NSArray *)getAllElementsOfHost;

- (void)removeElement:(TagitModel *)item;

- (void)removeElementWithHost:(NSString *)host
                   completion:(void(^)(void))completion;

@end
