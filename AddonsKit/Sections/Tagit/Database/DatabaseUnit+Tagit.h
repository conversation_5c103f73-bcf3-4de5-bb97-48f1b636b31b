//
//  DatabaseUnit+Tagit.h
//  PPBrowser
//
//  Created by qing<PERSON> on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit.h"
#import "TagitManager.h"

@interface DatabaseUnit (Tagit)

+ (DatabaseUnit *)addTagitWithItem:(TagitModel *)item;

//根据tagitId删除
+ (DatabaseUnit *)removeTagitWithId:(NSString *)uuid;
//根据一组tagitId删除
+ (DatabaseUnit *)removeTagitsWithIds:(NSArray<NSString *> *)ids;

//+ (DatabaseUnit *)removeAllTagit;

+ (DatabaseUnit *)queryTagitWithHost:(NSString *)host;

+ (DatabaseUnit *)queryAllTagit;

// 更新是否激活状态
+ (DatabaseUnit*)updateTagitWithId:(NSString*)uuid
                          isActive:(NSInteger)isActive;

// 编辑
+ (DatabaseUnit*)updateTagitWithId:(NSString*)uuid
                              host:(NSString *)host
                             xpath:(NSString *)xpath;

// 编辑一组的host
+ (DatabaseUnit *)updateTagitSectionWithHost:(NSString *)oldHost
                                     newHost:(NSString *)newHost;

// CloudKit, 添加多个标记模式
+ (DatabaseUnit*)addTagitArray:(NSArray<TagitModel*>*)items;

// CloudKit, 批量更新多个标记模式
+ (DatabaseUnit*)updateTagitArray:(NSArray<TagitModel*>*)array;

// CloudKit, 批量删除多个标记模式
+ (DatabaseUnit*)removeTagitArray:(NSArray*)tagitIds;

@end

