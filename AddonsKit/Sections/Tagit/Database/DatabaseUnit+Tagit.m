//
//  DatabaseUnit+Tagit.m
//  PPBrowser
//
//  Created by qingbin on 2022/12/17.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "DatabaseUnit+Tagit.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "SyncEngine.h"
#import "CloudKitHelper.h"

@implementation DatabaseUnit (Tagit)

+ (DatabaseUnit *)addTagitWithItem:(TagitModel *)item
{
    //https://stackoverflow.com/questions/3634984/insert-if-not-exists-else-update
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //必须有id
    if(item.uuid.length == 0) {
        item.uuid = [[NSUUID UUID] UUIDString];
    }
    
    if(item.host.length == 0) item.host = @"";
    if(item.xpath.length == 0) item.xpath = @"";
    if(item.originUrl.length == 0) item.originUrl = @"";
    if(item.ctime.length == 0) {
        item.ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    }
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.updateTime = updateTime;
        
        NSString* command = @"INSERT INTO t_tagit(uuid, host, xpath, originUrl, isActive, updateTime, ctime) VALUES (?,?,?,?,?,?,?)\
        on CONFLICT(uuid) DO UPDATE SET host=excluded.host, xpath=excluded.xpath\
        WHERE excluded.uuid=t_tagit.uuid;\
        ";

        BOOL result = [db executeUpdate:command, item.uuid, item.host?:@"", item.xpath?:@"", item.originUrl?:@"", @(item.isActive), item.updateTime, item.ctime];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)removeTagitWithId:(NSString *)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"DELETE FROM t_tagit WHERE uuid=?;";
        BOOL result = [db executeUpdate:command, uuid];

        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:uuid zoneID:[CloudKitHelper addonsZoneID]];
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:@[recordID] completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

//根据一组uuid删除
+ (DatabaseUnit *)removeTagitsWithIds:(NSArray<NSString *> *)uuids
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSMutableArray* recordIDs = [NSMutableArray array];
        NSMutableString* ids = [NSMutableString new];
        for(int i=0;i<uuids.count;i++) {
            NSString* str = uuids[i];
            NSString* option = [NSString stringWithFormat:@"\"%@\"",str];
            [ids appendString:option];
            if(i < uuids.count-1) {
                [ids appendString:@","];
            }
            
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:str zoneID:[CloudKitHelper addonsZoneID]];
            [recordIDs addObject:recordID];
        }
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_tagit WHERE uuid in (%@);",ids];
        BOOL result = [db executeUpdate:command];

        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:recordIDs completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

//+ (DatabaseUnit *)removeAllTagit
//{
//    DatabaseUnit* unit = [DatabaseUnit new];
//
//    @weakify(unit)
//    unit.executeBlock = ^(FMDatabase *db) {
//        @strongify(unit)
//        NSString* command = @"DELETE FROM t_tagit;";
//
//        BOOL result = [db executeUpdate:command];
//
//        dispatch_async(dispatch_get_main_queue(), ^{
//            if(unit.completeBlock) {
//                unit.completeBlock(nil, result);
//            }
//        });
//    };
//
//    return unit;
//}

+ (DatabaseUnit *)queryTagitWithHost:(NSString *)host
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_tagit WHERE host like '%%%@%%' ORDER BY ctime ASC", host];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)queryAllTagit
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_tagit ORDER BY ctime ASC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateTagitWithId:(NSString*)uuid
                          isActive:(NSInteger)isActive
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_tagit SET isActive = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_tagit WHERE uuid = ?"];
            FMResultSet* set = [db executeQuery:command, uuid];
            
            if([set next]) {
                TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
                
                CKRecord* record = [item toDefaultCKRecord];
                record[@"isActive"] = @(isActive);
                record[@"updateTime"] = updateTime;
                [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
            }
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 编辑
+ (DatabaseUnit*)updateTagitWithId:(NSString*)uuid
                              host:(NSString *)host
                             xpath:(NSString *)xpath
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_tagit SET host = ?, xpath = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, host?:@"", xpath?:@"", updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_tagit WHERE uuid = ?"];
            FMResultSet* set = [db executeQuery:command, uuid];
            
            if([set next]) {
                TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
                
                CKRecord* record = [item toDefaultCKRecord];
                record[@"host"] = host;
                record[@"xpath"] = xpath;
                record[@"updateTime"] = updateTime;
                
                [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
            }
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 编辑一组的host
+ (DatabaseUnit *)updateTagitSectionWithHost:(NSString *)oldHost
                                     newHost:(NSString *)newHost
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_tagit WHERE host = ?"];
        FMResultSet* set = [db executeQuery:command, oldHost];
        NSMutableArray* items = [NSMutableArray array];
        while ([set next]) {
            TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [items addObject:item];
        }
        
        command = [NSString stringWithFormat:@"UPDATE t_tagit SET host = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = YES;
        NSMutableArray* recordToStores = [NSMutableArray array];
        for(TagitModel* item in items) {
            item.host = newHost?:@"";
            result = [db executeUpdate:command, newHost, updateTime, item.uuid] && result;
            
            CKRecord* record = [item toDefaultCKRecord];
            record[@"host"] = newHost?:@"";
            record[@"updateTime"] = updateTime;
            [recordToStores addObject:record];
        }
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:recordToStores recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 添加多个标记模式
+ (DatabaseUnit*)addTagitArray:(NSArray<TagitModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_tagit(uuid, host, xpath, originUrl, isActive, updateTime, ctime) VALUES (?,?,?,?,?,?,?)";
        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            TagitModel* item = items[i];
            result = [db executeUpdate:command, item.uuid, item.host?:@"", item.xpath?:@"", item.originUrl?:@"", @(item.isActive), item.updateTime, item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个标记模式
+ (DatabaseUnit*)updateTagitArray:(NSArray<TagitModel*>*)array
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        BOOL result = NO;
        for(TagitModel* item in array) {
            NSString* command = @"UPDATE t_tagit SET host=?, xpath=?, originUrl=?, isActive=?, updateTime=?, ctime=? WHERE uuid=?;";
            result = [db executeUpdate:command, item.host?:@"", item.xpath?:@"", item.originUrl?:@"",  @(item.isActive), updateTime, item.ctime, item.uuid];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个标记模式
+ (DatabaseUnit*)removeTagitArray:(NSArray*)tagitIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_tagit WHERE uuid=?;"];

        BOOL result = YES;
        for(int i=0;i<tagitIds.count;i++) {
            NSString* tagitId = tagitIds[i];
            result = [db executeUpdate:command, tagitId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
