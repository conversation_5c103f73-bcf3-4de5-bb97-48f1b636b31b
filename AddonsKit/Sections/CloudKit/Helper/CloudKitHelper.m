//
//  CloudKitHelper.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/12/28.
//

#import "CloudKitHelper.h"

#import "PreferenceManager.h"

@implementation CloudKitHelper

+ (CKRecordZoneID *)addonsZoneID
{
    CKRecordZoneID* zoneID = [[CKRecordZoneID alloc]initWithZoneName:@"AddonsZone" ownerName:CKCurrentUserDefaultName];
    return zoneID;
}

#pragma mark -- 更新iCloud同步时间
+ (void)updateSyncTimestamp
{
    [PreferenceManager shareInstance].items.iCloudSyncTimestamp = @([[NSDate date] timeIntervalSince1970]);
    [[PreferenceManager shareInstance] encode];
}

#pragma mark -- 获取iCloud同步时间
+ (NSString *)iCloudSyncTimestamp
{
    NSNumber* iCloudSyncTimestamp = [PreferenceManager shareInstance].items.iCloudSyncTimestamp;
    NSString* time = @"";
    if(iCloudSyncTimestamp) {
        time = formatTimestamp([iCloudSyncTimestamp integerValue]);
    }
    
    return time;
}

NSString *formatTimestamp(NSTimeInterval timestamp) {
    NSDate *date = [NSDate dateWithTimeIntervalSince1970:timestamp];
    
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"MM/dd HH:mm"];
    
    NSString *formattedDateString = [dateFormatter stringFromDate:date];
    
    return formattedDateString;
}

@end
