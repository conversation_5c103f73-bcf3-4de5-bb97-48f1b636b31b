//
//  UserScript.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "UserScript.h"

#import "NSString+Helper.h"
#import "OpenUDID.h"
#import "PPEnums.h"

#import "NSFileManager+Helper.h"
#import "CloudKitHelper.h"
#import "ReactiveCocoa.h"
#import "DatabaseUnit+UserScript.h"

@implementation UserScript

- (void)setUuid:(NSString *)uuid
{
    if(uuid.length > 0) {
        _uuid = [uuid stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    }
}

- (NSArray<NSString *> *)matches
{
    if(_matches.count == 0 && _includes.count == 0) {
        _matches = @[@"*"];
    }
    
    return _matches;
}

- (NSString *)runAt
{
    if(_runAt.length == 0) {
        _runAt = @"document_end";
    }
    
    return _runAt;
}

#pragma mark -- 重写
- (NSDictionary *)toDictionary
{
    NSDictionary* dict = [super toDictionary];
    NSMutableDictionary* mutableDict = [[NSMutableDictionary alloc]initWithDictionary:dict];

    //排除序列化
    mutableDict[@"isFirstInSection"] = nil;
    mutableDict[@"isLastInSection"] = nil;
    
    //
    mutableDict[@"namespace"] = self.kNamespace?:@"";
    mutableDict[@"kNamespace"] = nil;
    
    return [mutableDict copy];
}

- (NSString*)GM_Info
{
    GMInfoModel* info = [GMInfoModel new];
    info.script = [self _generateGM_Info];
    //有脚本会验证这个，通过GM_info.scriptHandler来获取
    //在Tampermonkey中，通过 alert(JSON.stringify(GM_info, null, 2)); 打印所有属性
    info.scriptHandler = @"Tampermonkey";
    info.version = @"5.3.3";
    
    return [info toJSONString];
}

#pragma mark -- 生成需要的GM_Info
- (UserScript*)_generateGM_Info
{
    UserScript* result = [UserScript new];
    result.uuid = self.uuid;
    //safari脚本的runAt是document_start这种格式
    result.runAt = [self.runAt stringByReplacingOccurrencesOfString:@"-" withString:@"_"];
    result.name = self.name;
    result.isActive = self.isActive;
    result.noframes = self.noframes;
    
    //v4.5
    //将自定义的白名单和黑名单合并到matches和excludes中，这样代码逻辑统一
    NSMutableArray* matches = [NSMutableArray array];
    [matches addObjectsFromArray:self.matches];
    [matches addObjectsFromArray:[self getWhiteList]];
    
    NSMutableArray* excludes = [NSMutableArray array];
    [excludes addObjectsFromArray:self.excludes];
    [excludes addObjectsFromArray:[self getBlackList]];
    
    result.matches = matches;
    result.includes = self.includes?self.includes:@[];
    result.excludes = excludes;
    
    result.requireUrls = self.requireUrls;
    result.resourceUrls = self.resourceUrls;
    //沉浸式翻译取的version是这个字段，要不然它会报错
    result.version = self.version?:@"0.0";
    //自动无缝翻页脚本
    result.scriptHandler = @"Greasemonkey";
    //GM_info.script.namespace
    //适配https://greasyfork.org/zh-CN/scripts/462804-keepchatgpt/code
    result.kNamespace = self.kNamespace?:@"";
    
    return result;
}

//获取白名单
- (NSArray<NSString *> *)getWhiteList
{
    NSMutableArray* list = [NSMutableArray array];
    if(self.whiteList.length == 0) {
        return @[];
    }
    
    list = [[self.whiteList componentsSeparatedByString:@","] mutableCopy];
    
    return list;
}

//添加白名单
- (void)addWhiteListItem:(NSString *)whiteListItem
{
    if(whiteListItem.length == 0) return;
    
    NSMutableArray<NSString *> *whiteList = [[self getWhiteList] mutableCopy];
    if(![whiteList containsObject:whiteListItem]) {
        [whiteList addObject:whiteListItem];
    }
    
    self.whiteList = [whiteList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
//    [self reloadUserScript];
}

//删除白名单
- (void)removeWhiteLisItem:(NSString *)whiteListItem
{
    if(whiteListItem.length == 0) return;
    
    NSMutableArray<NSString *> *whiteList = [[self getWhiteList] mutableCopy];
    [whiteList removeObject:whiteListItem];
    
    self.whiteList = [whiteList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
//    [self reloadUserScript];
}

//获取黑名单
- (NSArray<NSString *> *)getBlackList
{
    NSMutableArray* list = [NSMutableArray array];
    if(self.blackList.length == 0) {
        return @[];
    }
    
    list = [[self.blackList componentsSeparatedByString:@","] mutableCopy];
    
    return list;
}

//添加黑名单
- (void)addBlackListItem:(NSString *)blackListItem
{
    if(blackListItem.length == 0) return;
    
    NSMutableArray<NSString *> *blackList = [[self getBlackList] mutableCopy];
    if(![blackList containsObject:blackListItem]) {
        [blackList addObject:blackListItem];
    }
    
    self.blackList = [blackList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
//    [self reloadUserScript];
}

//删除黑名单
- (void)removeBlackLisItem:(NSString *)blackListItem
{
    if(blackListItem.length == 0) return;
    
    NSMutableArray<NSString *> *blackList = [[self getBlackList] mutableCopy];
    [blackList removeObject:blackListItem];
    
    self.blackList = [blackList componentsJoinedByString:@","];
    
    //保存到数据库和iCloud中
    DatabaseUnit* unit = [DatabaseUnit updateUserScriptWithItem:self];
    DB_EXEC(unit);
    
    //更新执行代码
//    [self reloadUserScript];
}


#pragma mark -- 获取油猴注释的头部
- (NSString*)_scriptMetaStringWithSourceCode:(NSString *)sourceCode
{
    NSString *process = [sourceCode copy];

    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"^\\/\\/.*" options:NSRegularExpressionAnchorsMatchLines error:NULL];

    NSArray<NSTextCheckingResult *> *matches = [regex matchesInString:process options:0 range:NSMakeRange(0, process.length)];

    NSMutableArray *array = [NSMutableArray array];
    for (NSTextCheckingResult *match in matches) {
        for (int i = 0; i < [match numberOfRanges]; i++) {
            NSString *component = [process substringWithRange:[match rangeAtIndex:i]];
            [array addObject:component];
        }
    }

    NSString* result = [array componentsJoinedByString:@"\n"];

    return result;
}

// 从model初始化到sql字段
- (void)jsonModelToSql
{
    if(self.name.length == 0) {
        self.name = @"";
    }
    
    if(self.author.length == 0) {
        self.author = @"";
    }
    
    if(self.version.length == 0) {
        self.version = @"1.0.0";
    }
    
    if(self.kNamespace.length == 0) {
        self.kNamespace = @"";
    }
    
    if(self.desc.length == 0) {
        self.desc = @"";
    }
    
    if(self.iconUrl.length == 0) {
        self.iconUrl = @"";
    }
    
    if(self.updateUrl.length == 0) {
        self.updateUrl = @"";
    }
    
    if(self.downloadUrl.length == 0) {
        self.downloadUrl = @"";
    }
    
    if(self.content.length == 0) {
        self.content = @"";
    }
    
    if(self.includes.count > 0) {
        self.sql_includes = [self.includes componentsJoinedByString:@","];
    } else {
        self.sql_includes = @"";
    }
    
    if(self.matches.count > 0) {
        self.sql_matches = [self.matches componentsJoinedByString:@","];
    } else {
        self.sql_matches = @"";
    }
    
    if(self.excludes.count > 0) {
        self.sql_excludes = [self.excludes componentsJoinedByString:@","];
    } else {
        self.sql_excludes = @"";
    }
    
    if(self.grants.count > 0) {
        self.sql_grants = [self.grants componentsJoinedByString:@","];
    } else {
        self.sql_grants = @"";
    }
    
    if(self.requireUrls.count > 0) {
        self.sql_requireUrls = [self.requireUrls componentsJoinedByString:@","];
    } else {
        self.sql_requireUrls = @"";
    }
    
    if(self.resourceUrls.count > 0) {
        self.sql_resourceUrls = [NSString convertToJsonString:self.resourceUrls options:NO];
    } else {
        self.sql_resourceUrls = @"";
    }
}

// 从sql初始化到model
- (void)jsonModelFromSql
{
    if(self.sql_includes.length > 0) {
        self.includes = [self.sql_includes componentsSeparatedByString:@","];
    } else {
        self.includes = nil;
    }
    
    if(self.sql_matches.length > 0) {
        self.matches = [self.sql_matches componentsSeparatedByString:@","];
    } else {
        self.matches = nil;
    }
    
    if(self.sql_excludes.length > 0) {
        self.excludes = [self.sql_excludes componentsSeparatedByString:@","];
    } else {
        self.excludes = nil;
    }
    
    if(self.sql_grants.length > 0) {
        self.grants = [self.sql_grants componentsSeparatedByString:@","];
    } else {
        self.grants = nil;
    }
    
    if(self.sql_requireUrls.length > 0) {
        self.requireUrls = [self.sql_requireUrls componentsSeparatedByString:@","];
    } else {
        self.requireUrls = nil;
    }
    
    if(self.sql_resourceUrls.length > 0) {
        self.resourceUrls = [NSString jsonConvertToObject:self.sql_resourceUrls];
    } else {
        self.resourceUrls = nil;
    }
}

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.uuid zoneID:[CloudKitHelper addonsZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(UserScript.class) recordID:recordID];
    
    return record;
}

//CloudKit同步
//转换成CKRecord
- (CKRecord *)toCKRecord
{
    //@"CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, kNamespace TEXT, version TEXT,  desc TEXT, iconUrl TEXT, runAt TEXT, noframes INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, injectMode INTEGER, isAutoUpdate INTEGER, ppOrder INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, sql_resourceUrls TEXT, ctime TEXT)"
//    NSString* recordIDString = [self.uuid stringByReplacingOccurrencesOfString:@"-" withString:@""];
//    recordIDString = [recordIDString stringByReplacingOccurrencesOfString:@"_" withString:@""];
    
    CKRecord* record = [self toDefaultCKRecord];
    
    [self jsonModelToSql];
    
    record[@"uuid"] = self.uuid;
    record[@"name"] = self.name?:@"";
    record[@"author"] = self.author?:@"";
    record[@"kNamespace"] = self.kNamespace?:@"";
    record[@"version"] = self.version?:@"";
    record[@"desc"] = self.desc?:@"";
//    record[@"iconUrl"] = self.iconUrl;
    record[@"runAt"] = self.runAt;
    record[@"noframes"] = @(self.noframes);
    record[@"updateUrl"] = self.updateUrl?:@"";
    record[@"downloadUrl"] = self.downloadUrl?:@"";
//    record[@"content"] = self.content;
    record[@"isActive"] = @(self.isActive);
    record[@"injectMode"] = @(self.injectMode);
    record[@"isAutoUpdate"] = @(self.isAutoUpdate);
    record[@"ppOrder"] = @(self.ppOrder);
    record[@"sql_includes"] = self.sql_includes?:@"";
    record[@"sql_matches"] = self.sql_matches?:@"";
    record[@"sql_excludes"] = self.sql_excludes?:@"";
    record[@"sql_grants"] = self.sql_grants?:@"";
    record[@"sql_requireUrls"] = self.sql_requireUrls?:@"";
    record[@"sql_resourceUrls"] = self.sql_resourceUrls?:@"";
    record[@"ctime"] = self.ctime;
    record[@"updateTime"] = self.updateTime?:@"1";
    record[@"whiteList"] = self.whiteList?:@"";
    record[@"blackList"] = self.blackList?:@"";
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeUserScript);
    
    //将iconUrl和content转换为CKAsset
    NSFileManager* fileManager = [NSFileManager defaultManager];
    if(self.iconUrl.length > 0) {
        NSString* fileName = [NSString stringWithFormat:@"%@_iconUrl", self.uuid];
        NSURL* filePath = [[NSFileManager cloudKitPath] URLByAppendingPathComponent:fileName];
        
        //保存到Documents/CloudKit目录
        if([fileManager fileExistsAtPath:filePath.path]) {
            [fileManager removeItemAtURL:filePath error:nil];
        }
        [fileManager createFileAtPath:filePath.path contents:[self.iconUrl dataUsingEncoding:NSUTF8StringEncoding] attributes:nil];
    
        CKAsset* asset = [[CKAsset alloc]initWithFileURL:filePath];
        record[@"iconUrl"] = asset;
    }
    
    if(self.content.length > 0) {
        NSString* fileName = [NSString stringWithFormat:@"%@_content", self.uuid];
        NSURL* filePath = [[NSFileManager cloudKitPath] URLByAppendingPathComponent:fileName];
        
        //保存到Documents/CloudKit目录
        if([fileManager fileExistsAtPath:filePath.path]) {
            [fileManager removeItemAtURL:filePath error:nil];
        }
        [fileManager createFileAtPath:filePath.path contents:[self.content dataUsingEncoding:NSUTF8StringEncoding] attributes:nil];
    
        CKAsset* asset = [[CKAsset alloc]initWithFileURL:filePath];
        record[@"content"] = asset;
    }
    
    return record;
}
//从CKRecord转换为UserScript
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.uuid = record[@"uuid"];
        self.name = record[@"name"];
        self.author = record[@"author"];
        
        self.kNamespace = record[@"kNamespace"];
        self.version = record[@"version"];
        self.desc = record[@"desc"];
//        self.iconUrl = record[@"iconUrl"];
        self.runAt = record[@"runAt"];
        self.noframes = [[record objectForKey:@"noframes"] intValue];
        
        self.updateUrl = record[@"updateUrl"];
        self.downloadUrl = record[@"downloadUrl"];
//        self.content = record[@"content"];
        self.isActive = [[record objectForKey:@"isActive"] intValue];
        self.injectMode = [[record objectForKey:@"injectMode"] intValue];
        self.isAutoUpdate = [[record objectForKey:@"isAutoUpdate"] intValue];
        self.ppOrder = [[record objectForKey:@"ppOrder"] intValue];
        
        self.sql_includes = record[@"sql_includes"];
        self.sql_matches = record[@"sql_matches"];
        self.sql_excludes = record[@"sql_excludes"];
        self.sql_grants = record[@"sql_grants"];
        self.sql_requireUrls = record[@"sql_requireUrls"];
        self.sql_resourceUrls = record[@"sql_resourceUrls"];
        self.ctime = record[@"ctime"];
    
        self.whiteList = record[@"whiteList"];
        self.blackList = record[@"blackList"];
        
        NSString* updateTimeText = record[@"updateTime"];
        NSInteger updateTime = [updateTimeText integerValue];
        if(updateTime == 0 || updateTime == 1) {
            //初始化
            updateTime = [record.modificationDate timeIntervalSince1970];
        }
        self.updateTime = [NSString stringWithFormat:@"%ld", updateTime];
        
        [self jsonModelFromSql];
        
        //把CKAsset转换为iconUrl,content
        NSFileManager* fileManager = [NSFileManager defaultManager];
        
        CKAsset* iconUrlAsset = record[@"iconUrl"];
        if(iconUrlAsset) {
            self.iconUrl = [[NSString alloc]initWithData:[fileManager contentsAtPath:iconUrlAsset.fileURL.path] encoding:NSUTF8StringEncoding];
        }
        
        CKAsset* contentAsset = record[@"content"];
        if(contentAsset) {
            self.content = [[NSString alloc]initWithData:[fileManager contentsAtPath:contentAsset.fileURL.path] encoding:NSUTF8StringEncoding];
        }
        
//        if(self.content.length == 0) {
//            CKFetchRecordsOperation *operation = [[CKFetchRecordsOperation alloc] initWithRecordIDs:@[record.recordID]];
//            operation.desiredKeys = @[@"content"];
//            @weakify(self)
//            operation.perRecordCompletionBlock = ^(CKRecord * _Nullable record, CKRecordID * _Nullable recordID, NSError * _Nullable error) {
//                @strongify(self)
//                if (error) {
//                    return;
//                }
//                
//                CKAsset *asset = record[@"content"];
//                if (!asset || !asset.fileURL) {
//                    return;
//                }
//                
//                NSData *data = [NSData dataWithContentsOfURL:asset.fileURL];
//                NSString *content = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
//                self.content = content;
//            };
//            
//            [operation start];
//        }
    }
    
    return self;
}

//判断两个脚本是否一致，1、根据uuid,2、判断内容是否相等，即updateUrl/downloadUrl是否相等
- (BOOL)objectIsEqualTo:(id)obj
{
    UserScript* script = obj;
    if([self.uuid isEqualToString:script.uuid]) return YES;
    
    NSString* url = self.updateUrl;
    if(url.length == 0) {
        url = self.downloadUrl;
    }
    
    NSString* scriptUrl = script.updateUrl;
    if(scriptUrl.length == 0) {
        scriptUrl = script.downloadUrl;
    }
    
    return [url isEqualToString:scriptUrl];
}

//返回uuid
- (NSString*)getUuid
{
    return self.uuid;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

@end


@implementation GMInfoModel
@end


@implementation GMResourceModel
@end

@implementation GMRequireModel
@end
