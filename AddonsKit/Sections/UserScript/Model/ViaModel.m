//
//  ViaModel.m
//  PPBrowser
//
//  Created by qingbin on 2022/5/28.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import "ViaModel.h"

@implementation ViaModel

+ (ViaModel*)parseSourceCodeFromCommand:(NSString*)commandValue
{
    //via插件管理, 不和油猴脚本混在一起处理, 增加代码的维护难度
    //via中url、originalUrl和code是必有的
    //name和autor可能没有, 而且via插件有可能是油猴的格式
    
    //via插件需要特殊处理
    ViaModel* via;
    //base64解码
    NSData* data = [commandValue dataUsingEncoding:NSUTF8StringEncoding];
    data = [[NSData alloc]initWithBase64EncodedData:data options:0];
    NSString* value = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    
    //code字段再次用base64编码了一次
    via = [[ViaModel alloc]initWithString:value error:nil];
    data = [via.code dataUsingEncoding:NSUTF8StringEncoding];
    data = [[NSData alloc]initWithBase64EncodedData:data options:0];
    via.code = [[NSString alloc]initWithData:data encoding:NSUTF8StringEncoding];
    
    return via;
}

/*
* @name: 网址二维码
* @Author: Sky
* @version: 1.2.1
* @description: 将网址转为二维码图片
* @include: *
* @createTime: 2020-7-8 13:30
* @updateTime: 2020-11-3 23:10
*/

/*
 var patt1 = /@description:?\s*(.*)/i
 var _test = "/* \
 * @name: 网址二维码 \
 * @Author: Sky \n \
 * @description: 将网址转为二维码图片";

 var val = _test.match(patt1);

 console.log(val[1]);
 */

@end
