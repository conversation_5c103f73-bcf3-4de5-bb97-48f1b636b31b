//
//  UserScript.h
//  PPBrowser
//
//  Created by qingbin on 2022/5/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BaseModel.h"
#import "PPEnums.h"

#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

//https://www.tampermonkey.net/documentation.php

@interface GMResourceModel : BaseModel

@property (nonatomic, copy) NSString *key;
@property (nonatomic, copy) NSString *url;
@property (nonatomic, copy) NSString *text;

@end

@protocol GMResourceModel <NSObject>
@end

@interface GMRequireModel : BaseModel

@property (nonatomic, copy) NSString *url;
@property (nonatomic, copy) NSString *text;

@end

@protocol GMRequireModel <NSObject>
@end

@interface UserScript : BaseModel<SyncProtocol>

@property (nonatomic, copy) NSString *uuid;
@property (nonatomic, copy) NSString *name;
//namespace关键字报错,改名字
@property (nonatomic, copy) NSString *kNamespace;
@property (nonatomic, copy) NSString *author;
@property (nonatomic, copy) NSString *version;
@property (nonatomic, copy) NSString *desc;
@property (nonatomic, copy) NSString *iconUrl;
@property (nonatomic, copy) NSString *runAt;
@property (nonatomic, copy) NSString *ctime;
//mainframe
@property (nonatomic, assign) BOOL noframes;

@property (nonatomic, copy) NSString *updateUrl;
@property (nonatomic, copy) NSString *downloadUrl;
/// 通过source-code-generate生成的js源码,译为可执行源码
@property (nonatomic, copy) NSString *executorJs;
///  是否激活
@property (nonatomic, assign) BOOL isActive;
/// 是否自动更新
@property (nonatomic, assign) BOOL isAutoUpdate;
//注入模式
@property (nonatomic, assign) UserScriptInjectMode injectMode;
//
@property (nonatomic, copy) NSString *scriptHandler;
//顺序
@property (nonatomic, assign) int ppOrder;
//更新时间
@property (nonatomic, strong) NSString *updateTime;

@property (nonatomic, copy) NSArray<NSString *> *includes;
@property (nonatomic, copy) NSArray<NSString *> *matches;
@property (nonatomic, copy) NSArray<NSString *> *excludes;
@property (nonatomic, copy) NSArray<NSString *> *grants;
@property (nonatomic, copy) NSArray<NSString *> *requireUrls;
@property (nonatomic, copy) NSDictionary *resourceUrls;

//直接通过扩展注入@require代码
//之前的做法是插入到document.head/document.body中，但是这种做法没有适配很多脚本
//这里应该是@inject-into的语法
@property (nonatomic, strong) NSArray* jsRequireUrls;

//数据库使用相关字段
@property (nonatomic, copy) NSString *sql_includes;
@property (nonatomic, copy) NSString *sql_matches;
@property (nonatomic, copy) NSString *sql_excludes;
@property (nonatomic, copy) NSString *sql_grants;
@property (nonatomic, copy) NSString *sql_requireUrls;
@property (nonatomic, copy) NSString *sql_resourceUrls;

//v4.5
//白名单(用","拼接的数组)
@property (nonatomic, strong) NSString *whiteList;
//黑名单(用","拼接的数组)
@property (nonatomic, strong) NSString *blackList;

// 获取脚本信息
- (NSString*)GM_Info;

// 从model初始化到sql字段
- (void)jsonModelToSql;
// 从sql初始化到model
- (void)jsonModelFromSql;

// 辅助,用于GM_info, 组合是key+url
//@property (nonatomic, copy) NSArray<NSDictionary *> *allResources;
//@property (nonatomic, copy) NSArray<NSDictionary *> *allRequires;

/// js源码
@property (nonatomic, copy) NSString *content;
/// 去掉头部注释的js源码
//@property (nonatomic, copy) NSString *parsedContent;
/// 本地化
@property (nonatomic, copy) NSDictionary<NSString *,NSDictionary *> *locales;

@property (nonatomic, copy) NSString *errorMessage;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//在content.js中使用的辅助字段，@resource的数据集合
//由于80MB的限制，如果resource太多，那么在json转换的时候，会导致超出最大内存崩溃，例如：
//https://update.greasyfork.org/scripts/475228/%E7%BD%91%E9%A1%B5%E8%B0%83%E8%AF%95.user.js
//所以不再进行json转换，而是直接返回字符串
//@property (nonatomic, strong) NSArray* resourceArray;
@property (nonatomic, strong) NSString* resourceJson;

//当前更新状态
//运行标志
@property (nonatomic, assign) UpdateStatus updateStatus;

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为UserScript
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个脚本是否一致，1、根据uuid,2、判断内容是否相等，即updateUrl/downloadUrl是否相等
- (BOOL)objectIsEqualTo:(id)script;

//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

//获取白名单
- (NSArray<NSString *> *)getWhiteList;
//添加白名单
- (void)addWhiteListItem:(NSString *)whiteListItem;
//删除白名单
- (void)removeWhiteLisItem:(NSString *)whiteListItem;
//获取黑名单
- (NSArray<NSString *> *)getBlackList;
//添加黑名单
- (void)addBlackListItem:(NSString *)blackListItem;
//删除黑名单
- (void)removeBlackLisItem:(NSString *)blackListItem;

@end

@protocol UserScript <NSObject>
@end

//https://wiki.greasespot.net/GM.info
@interface GMInfoModel : BaseModel

@property (nonatomic, strong) UserScript* script;
//脚本头部所有的信息(去掉源码)
@property (nonatomic, copy) NSString* scriptMetaStr;
//脚本引擎的名称, 默认Greasemonkey
@property (nonatomic, copy) NSString* scriptHandler;
//脚本引擎的版本号, 默认4.0
@property (nonatomic, copy) NSString* version;

@end
