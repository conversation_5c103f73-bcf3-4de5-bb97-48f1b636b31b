//
//  DatabaseUnit+UserScript.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2022/12/27.
//

#import "DatabaseUnit.h"
#import "UserScript.h"

@interface DatabaseUnit (UserScript)

// 添加一个脚本
+ (DatabaseUnit*)addUserScriptWithItem:(UserScript*)item;

// 更新一个脚本
+ (DatabaseUnit*)updateUserScriptWithItem:(UserScript*)item;

// 删除一个脚本
+ (DatabaseUnit*)removeUserScriptWithId:(NSString*)uuid;

// 更新是否激活状态
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                               isActive:(NSInteger)isActive
                                 script:(UserScript *)script;

// 更新是否激活状态(扩展中的处理方式，为了节省内存)
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                               isActive:(NSInteger)isActive;

// 更新是否自动更新
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid 
                           isAutoUpdate:(NSInteger)isAutoUpdate 
                                 script:(UserScript *)script;

// 更新脚本代码
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                content:(NSString*)content
                                 script:(UserScript *)script;

// 更新注入模式
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                             injectMode:(UserScriptInjectMode)injectMode
                                 script:(UserScript *)script;

// 更新顺序
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                ppOrder:(NSInteger)ppOrder
                                 script:(UserScript *)script;

// 查询所有脚本
+ (DatabaseUnit*)queryAllUserScripts;

// iCloud, 删除所有脚本
+ (DatabaseUnit*)removeAllUserScripts;

// CloudKit, 添加多个脚本, 同时插入多个脚本
+ (DatabaseUnit*)addUserScriptArray:(NSArray<UserScript*>*)items;

// CloudKit, 批量更新多个脚本
+ (DatabaseUnit*)updateUserScriptArray:(NSArray<UserScript*>*)array;

// CloudKit, 批量删除多个脚本
+ (DatabaseUnit*)removeUserScriptArray:(NSArray*)scriptIds;

//批量更新顺序
+ (DatabaseUnit*)updateUserScriptAllOrder:(NSArray*)allItems;

@end
