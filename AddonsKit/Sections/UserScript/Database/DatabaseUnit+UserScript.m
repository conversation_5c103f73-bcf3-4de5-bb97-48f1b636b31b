//
//  DatabaseUnit+UserScript.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2022/12/27.
//

#import "DatabaseUnit+UserScript.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "SyncEngine.h"
#import "CloudKitHelper.h"

@implementation DatabaseUnit (UserScript)

// 添加一个脚本
+ (DatabaseUnit*)addUserScriptWithItem:(UserScript*)item
{
    //CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, version TEXT,  desc TEXT, icon TEXT, runAt TEXT, noframes INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, resourceUrls TEXT, updateTime TEXT, ctime TEXT)
    
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];
    
    [item jsonModelToSql];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        NSString* updateTime = ctime;
        item.ctime = ctime;
        item.updateTime = updateTime;
        
        NSString* command = @"INSERT INTO t_userscript(uuid, name, author, version, kNamespace, desc, iconUrl, runAt, noframes, injectMode, updateUrl, downloadUrl, content, isActive, isAutoUpdate, ppOrder, sql_includes, sql_matches, sql_excludes, sql_grants, sql_requireUrls, sql_resourceUrls, whiteList, blackList, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        BOOL result = [db executeUpdate:command, item.uuid, item.name?:@"", item.author?:@"", item.version?:@"", item.kNamespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt?:@"", @(item.noframes), @(item.injectMode), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive), @(item.isAutoUpdate),@(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"",item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", updateTime, ctime];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 更新一个脚本
+ (DatabaseUnit*)updateUserScriptWithItem:(UserScript*)item
{
    //CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, version TEXT,  desc TEXT, icon TEXT, runAt TEXT, noframes INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, resourceUrls TEXT, updateTime TEXT, ctime TEXT)
    
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];
    
    [item jsonModelToSql];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];;
        item.updateTime = updateTime;
        
        NSString* command = @"UPDATE t_userscript SET name=?, author=?, version=?, kNamespace=?, desc=?, iconUrl=?, runAt=?, noframes=?, injectMode=?, updateUrl=?, downloadUrl=?, content=?, isActive=?, isAutoUpdate=?, ppOrder=?, sql_includes=?, sql_matches=?, sql_excludes=?, sql_grants=?, sql_requireUrls=?, sql_resourceUrls=?, whiteList=?, blackList=?, updateTime=? WHERE uuid=?;";

        BOOL result = [db executeUpdate:command, item.name?:@"", item.author?:@"", item.version?:@"", item.kNamespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt?:@"", @(item.noframes), @(item.injectMode), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive), @(item.isAutoUpdate), @(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"",item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", updateTime, item.uuid];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 删除一个脚本
+ (DatabaseUnit*)removeUserScriptWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //删除数据库记录
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_userscript WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, uuid];

        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:uuid zoneID:[CloudKitHelper addonsZoneID]];
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:@[recordID] completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 查询所有脚本
+ (DatabaseUnit*)queryAllUserScripts
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_userscript ORDER BY ppOrder ASC, ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            UserScript* item = [[UserScript alloc]initWithDictionary:[set resultDictionary] error:nil];
            //从sql初始化
            [item jsonModelFromSql];
            
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                               isActive:(NSInteger)isActive
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        script.updateTime = updateTime;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET isActive = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"isActive"] = @(isActive);
            record[@"updateTime"] = updateTime;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态(扩展中的处理方式，为了节省内存)
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                               isActive:(NSInteger)isActive
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET isActive = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:uuid zoneID:[CloudKitHelper addonsZoneID]];
            CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(UserScript.class) recordID:recordID];
            record[@"isActive"] = @(isActive);
            record[@"updateTime"] = updateTime;
            
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新是否自动更新
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                           isAutoUpdate:(NSInteger)isAutoUpdate
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        script.updateTime = updateTime;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET isAutoUpdate = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isAutoUpdate),  updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"isAutoUpdate"] = @(isAutoUpdate);
            record[@"updateTime"] = updateTime;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新注入模式
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                             injectMode:(UserScriptInjectMode)injectMode
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        script.updateTime = updateTime;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET injectMode = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(injectMode), updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"injectMode"] = @(injectMode);
            record[@"updateTime"] = updateTime;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新顺序
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                ppOrder:(NSInteger)ppOrder
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        script.updateTime = updateTime;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET ppOrder = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(ppOrder), updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"ppOrder"] = @(ppOrder);
            record[@"updateTime"] = updateTime;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新脚本代码
+ (DatabaseUnit*)updateUserScriptWithId:(NSString*)uuid
                                content:(NSString*)content
                                 script:(UserScript *)script
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        script.updateTime = updateTime;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_userscript SET content = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, content?:@"", updateTime, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [script toDefaultCKRecord];
            record[@"content"] = content;
            record[@"updateTime"] = updateTime;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// iCloud, 删除所有脚本
+ (DatabaseUnit*)removeAllUserScripts
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_userscript"];
        BOOL result = [db executeUpdate:command];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 添加多个节点, 同时插入多个节点
+ (DatabaseUnit*)addUserScriptArray:(NSArray<UserScript*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)        
        NSString* command = @"INSERT INTO t_userscript(uuid, name, author, version, kNamespace, desc, iconUrl, runAt, noframes, injectMode, updateUrl, downloadUrl, content, isActive, isAutoUpdate, ppOrder, sql_includes, sql_matches, sql_excludes, sql_grants, sql_requireUrls, sql_resourceUrls,  whiteList, blackList, updateTime, ctime) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            UserScript* item = items[i];
            
            result = [db executeUpdate:command, item.uuid, item.name?:@"", item.author?:@"", item.version?:@"", item.kNamespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt?:@"", @(item.noframes), @(item.injectMode), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive), @(item.isAutoUpdate), @(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"",item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", item.updateTime, item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个脚本
+ (DatabaseUnit*)updateUserScriptArray:(NSArray<UserScript*>*)array
{
    //CREATE TABLE IF NOT EXISTS t_userscript(uuid TEXT PRIMARY KEY, name TEXT, author TEXT, version TEXT,  desc TEXT, icon TEXT, runAt TEXT, noframes INTEGER, updateUrl TEXT, downloadUrl TEXT, content TEXT, isActive INTEGER, sql_includes TEXT, sql_matches TEXT, sql_excludes TEXT, sql_grants TEXT, sql_requireUrls TEXT, resourceUrls TEXT, ctime TEXT)
    
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = NO;
        for(UserScript* item in array) {
            [item jsonModelToSql];
            
            NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
            
            NSString* command = @"UPDATE t_userscript SET name=?, author=?, version=?, kNamespace=?, desc=?, iconUrl=?, runAt=?, noframes=?, injectMode=?, updateUrl=?, downloadUrl=?, content=?, isActive=?, isAutoUpdate=?, ppOrder=?, sql_includes=?, sql_matches=?, sql_excludes=?, sql_grants=?, sql_requireUrls=?, sql_resourceUrls=?, whiteList=?, blackList=?, updateTime=?, ctime=? WHERE uuid=?;";

            result = [db executeUpdate:command, item.name?:@"", item.author?:@"", item.version?:@"", item.kNamespace?:@"", item.desc?:@"", item.iconUrl?:@"", item.runAt?:@"", @(item.noframes), @(item.injectMode), item.updateUrl?:@"", item.downloadUrl?:@"", item.content?:@"", @(item.isActive), @(item.isAutoUpdate), @(item.ppOrder), item.sql_includes?:@"", item.sql_matches?:@"", item.sql_excludes?:@"",item.sql_grants?:@"", item.sql_requireUrls?:@"", item.sql_resourceUrls?:@"", item.whiteList?:@"", item.blackList?:@"", updateTime, item.ctime, item.uuid];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个脚本
+ (DatabaseUnit*)removeUserScriptArray:(NSArray*)scriptIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_userscript WHERE uuid=?;"];

        BOOL result = YES;
        for(int i=0;i<scriptIds.count;i++) {
            NSString* scriptId = scriptIds[i];
            result = [db executeUpdate:command, scriptId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 批量更新顺序
+ (DatabaseUnit*)updateUserScriptAllOrder:(NSArray*)allItems
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        BOOL result = YES;
        for(int i=0;i<allItems.count;i++) {
            UserScript* item = allItems[i];
            item.updateTime = updateTime;
            item.ppOrder = i;
            NSString* command = @"UPDATE t_userscript SET ppOrder=?, updateTime=? WHERE uuid=?";
            result = [db executeUpdate:command, @(item.ppOrder), updateTime, item.uuid] && result;
        }

        if(result) {
            //同步到CloudKit中
            NSMutableArray* records = [NSMutableArray array];
            for(UserScript* item in allItems) {
                CKRecord* record = [item toDefaultCKRecord];
                record[@"ppOrder"] = @(item.ppOrder);
                record[@"updateTime"] = updateTime;
                
                [records addObject:record];
            }
            
            [[SyncEngine shareInstance] syncRecordsToCloudKit:records recordIDsToDelete:nil completion:nil];
        }
        
        if(unit.completeBlock) {
            unit.completeBlock(nil, result);
        }
    };
    
    return unit;
}


@end
