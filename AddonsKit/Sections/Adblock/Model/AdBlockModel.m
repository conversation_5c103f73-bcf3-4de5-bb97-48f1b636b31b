//
//  AdBlockModel.m
//  Saber
//
//  Created by qing<PERSON> on 2023/4/7.
//

#import "AdBlockModel.h"
#import "DateHelper.h"

#import "NSFileManager+Helper.h"
#import "CloudKitHelper.h"
#import "OpenUDID.h"

@implementation AdBlockModel

//手动编写
- (instancetype)initWithContent:(NSString *)content
{
    self = [super init];
    if(self) {
        self.uuid = [[NSUUID UUID] UUIDString];
        
        NSDateFormatter *dateFormatter = [DateHelper dateFormatterWithFormatString:@"yyyyMMdd_HHmmss"];
        self.title = [dateFormatter stringFromDate:[NSDate date]];
        
        self.text = content;
        self.type = AdBlockTypeText;
        //新建的时候默认激活
        self.isActive = YES;
        self.url = @"";
    }
    
    return self;
}

//文件导入
- (instancetype)initWithTitle:(NSString *)title content:(NSString *)content
{
    self = [super init];
    if(self) {
        self.uuid = [[NSUUID UUID] UUIDString];
        
        self.title = title;
        
        self.text = content;
        self.type = AdBlockTypeFile;
        self.isActive = YES;
        self.url = @"";
    }
    
    return self;
}

//从链接导入
- (instancetype)initWithUrl:(NSString *)url content:(NSString *)content
{
    self = [super init];
    if(self) {
        self.uuid = [[NSUUID UUID] UUIDString];
        
        NSURL* URL = [NSURL URLWithString:url];
        self.title = [URL lastPathComponent];
        
        self.text = content;
        self.type = AdBlockTypeURL;
        self.isActive = YES;
        self.url = url;
    }
    
    return self;
}

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord
{
    CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:self.uuid zoneID:[CloudKitHelper addonsZoneID]];
    CKRecord* record = [[CKRecord alloc]initWithRecordType:NSStringFromClass(AdBlockModel.class) recordID:recordID];
    
    return record;
}

+ (NSString *)adblockPath
{
    NSString *library = [NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES) lastObject];
    NSString *adblock = [library stringByAppendingPathComponent:@"Adblock"];
    
    return adblock;
}

//CloudKit同步
//转换成CKRecord
- (CKRecord *)toCKRecord
{
    //CREATE TABLE IF NOT EXISTS t_adblock(uuid TEXT PRIMARY KEY, title TEXT, text TEXT, url TEXT, type INTEGER, isActive INTEGER, ctime TEXT)
    
    CKRecord* record = [self toDefaultCKRecord];
    
    record[@"uuid"] = self.uuid;
    record[@"title"] = self.title?:@"";
    record[@"type"] = @(self.type);
    record[@"url"] = self.url?:@"";
//    record[@"text"] = self.text;
    record[@"isActive"] = @(self.isActive);
    record[@"ctime"] = self.ctime;
    record[@"updateTime"] = self.updateTime?:@"1";
    
    //额外添加的字段，用于CloudKit
    //app版本号
    NSString* version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    record[@"appVersion"] = version?:@"";
    //上传到CloudKit创建时间
    record[@"appCtime"] = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
    //用户的uuid
    record[@"appUserID"] = [OpenUDID value]?:@"";
    //添加类型区分
    record[@"appType"] = @(CloudModelTypeAdBlock);
    
    //将text转换为CKAsset
    NSFileManager* fileManager = [NSFileManager defaultManager];
    NSString *filePath = [[AdBlockModel adblockPath] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.txt",self.title]];
    if([fileManager fileExistsAtPath:filePath]) {
        //存在过滤规则
        CKAsset* asset = [[CKAsset alloc]initWithFileURL:[NSURL fileURLWithPath:filePath]];
        record[@"text"] = asset;
    }
    
    return record;
}
//从CKRecord转换为AdBlockModel
- (instancetype)initWithCKRecord:(CKRecord *)record
{
    self = [super init];
    if(self) {
        self.uuid = record[@"uuid"];
        self.title = record[@"title"];
        self.type = [[record objectForKey:@"type"] intValue];
        self.url = record[@"url"];
        self.isActive = [[record objectForKey:@"isActive"] intValue];
//        self.text = record[@"text"];
        self.ctime = record[@"ctime"];

        NSString* updateTimeText = record[@"updateTime"];
        NSInteger updateTime = [updateTimeText integerValue];
        if(updateTime == 0 || updateTime == 1) {
            //初始化
            updateTime = [record.modificationDate timeIntervalSince1970];
        }
        self.updateTime = [NSString stringWithFormat:@"%ld", updateTime];
        
        //把CKAsset转换为iconUrl,content
        NSFileManager* fileManager = [NSFileManager defaultManager];
        
        CKAsset* textAsset = record[@"text"];
        if(textAsset) {
            self.text = [[NSString alloc]initWithData:[fileManager contentsAtPath:textAsset.fileURL.path] encoding:NSUTF8StringEncoding];
        }
    }
    
    return self;
}

//判断两个广告过滤规则是否一致，1、根据uuid,2、判断内容是否相等，如果是URL，那么判断它们的url是否一致
- (BOOL)objectIsEqualTo:(id)obj
{
    AdBlockModel *item = obj;
    if([self.uuid isEqualToString:item.uuid]) return YES;
    
    if(self.type == AdBlockTypeURL && item.type == AdBlockTypeURL) {
        return [self.url isEqualToString:item.url];
    }
    
    return NO;
}

//返回uuid
- (NSString*)getUuid
{
    return self.uuid;
}

//更新时间
- (NSString*)getUpdateTime
{
    return self.updateTime;
}

//保存文件,需要放在异步线程
//- (void)saveContent
//{
//    if(self.text.length == 0) {
//        return;
//    }
//    
//    NSString* groupPath;
//    NSString* dir;
//    NSFileManager* fm;
//    
//    groupPath = [NSFileManager groupPath];
//    dir = [NSString stringWithFormat:@"%@/Adblock", groupPath];
//    fm = [NSFileManager defaultManager];
//    if(![fm fileExistsAtPath:dir]) {
//        [fm createDirectoryAtPath:dir withIntermediateDirectories:YES attributes:nil error:nil];
//    }
//    
//    NSString* filePath = [NSString stringWithFormat:@"%@/%@",dir,self.title];
//    if([fm fileExistsAtPath:filePath]) {
//        [fm removeItemAtPath:filePath error:nil];
//    }
//    [self.text writeToFile:filePath atomically:YES encoding:NSUTF8StringEncoding error:nil];
//
//    NSLog(@"广告过滤规则的路径: %@", filePath);
//}

//内置类型
+ (instancetype)easylistChinaModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];

    NSString* url = @"https://easylist-downloads.adblockplus.org/easylistchina.txt";
//    NSURL* URL = [NSURL URLWithString:url];
    model.title = @"EasylistChina";
    model.url = url;
    model.type = AdBlockTypeURL;
    model.isActive = NO;

    NSString* ctime = @"0";
    model.ctime = ctime;
    model.updateTime = ctime;

    return model;
}

//内置类型
+ (instancetype)easylistModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    
    NSString* url = @"https://easylist-downloads.adblockplus.org/easylist.txt";
    model.title = @"Easylist";
    model.url = url;
    model.type = AdBlockTypeURL;
    model.isActive = NO;
    
    NSString* ctime = @"1";
    model.ctime = ctime;
    model.updateTime = ctime;
    
    return model;
}

+ (instancetype)easyPrivacyModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    
    NSString* url = @"https://easylist-downloads.adblockplus.org/easyprivacy.txt";
    model.title = @"EasyPrivacy";
    model.url = url;
    model.type = AdBlockTypeURL;
    model.isActive = NO;
    
    NSString* ctime = @"2";
    model.ctime = ctime;
    model.updateTime = ctime;
    
    return model;
}

+ (instancetype)antiadblockfiltersModel
{
    AdBlockModel* model = [AdBlockModel new];
    model.uuid = [[NSUUID UUID] UUIDString];
    
    NSString* url = @"https://easylist-downloads.adblockplus.org/antiadblockfilters.txt";
    model.title = @"Anti-Adblock Filters";
    model.url = url;
    model.type = AdBlockTypeURL;
    model.isActive = NO;
    
    NSString* ctime = @"3";
    model.ctime = ctime;
    model.updateTime = ctime;
    
    return model;
}

@end


@implementation AdBlockModelArray
@end
