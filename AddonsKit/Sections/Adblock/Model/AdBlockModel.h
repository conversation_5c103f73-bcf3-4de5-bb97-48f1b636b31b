//
//  AdBlockModel.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/4/7.
//

#import "BaseModel.h"
#import "PPEnums.h"

#import <CloudKit/CloudKit.h>
#import "SyncProtocol.h"

@interface AdBlockModel : BaseModel<SyncProtocol>

//手动编写
- (instancetype)initWithContent:(NSString *)content;

//文件导入
- (instancetype)initWithTitle:(NSString *)title content:(NSString *)content;

//从链接导入
- (instancetype)initWithUrl:(NSString *)url content:(NSString *)content;

//sqlite的TEXT类型最大可存储2^31-1个字符,英语字符约2G，中文字符约6G，
//因此将text字段保存到sqlite更加容易操作
//保存文件,需要放在异步线程
//- (void)saveContent;

@property (nonatomic, strong) NSString *uuid;

@property (nonatomic, strong) NSString *title;
//
@property (nonatomic, assign) AdBlockType type;
/// 仅当type=URL时有值，记录URL
@property (nonatomic, strong) NSString *url;
/// 是否正在运行
@property (nonatomic, assign) BOOL isActive;
/// 添加时间
@property (nonatomic, strong) NSString *ctime;
/// 更新时间
@property (nonatomic, strong) NSString *updateTime;

//辅助字段,主要是卡片化
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;
//辅助字段,没有转换前的规则
@property (nonatomic, strong) NSString *text;

//内置类型
//+ (instancetype)easylistChinaModel;

//CloudKit同步
//获取一个默认的CKRecord
- (CKRecord *)toDefaultCKRecord;
//转换成CKRecord
- (CKRecord *)toCKRecord;
//从CKRecord转换为UserScript
- (instancetype)initWithCKRecord:(CKRecord *)record;
//判断两个广告过滤规则是否一致，1、根据uuid,2、判断内容是否相等，如果是URL，那么判断它们的url是否一致
- (BOOL)objectIsEqualTo:(id)obj;

//返回uuid
- (NSString*)getUuid;
//更新时间
- (NSString*)getUpdateTime;

//内置类型
+ (instancetype)easylistChinaModel;
+ (instancetype)easylistModel;
+ (instancetype)easyPrivacyModel;
+ (instancetype)antiadblockfiltersModel;

@end


@protocol AdBlockModel <NSObject>
@end

///json转换
@interface AdBlockModelArray : BaseModel

@property (nonatomic, strong) NSArray<AdBlockModel> *array;

@end
