//
//  DatabaseUnit+AdBlockWhitelist.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/11/22.
//

#import "DatabaseUnit+AdBlockWhitelist.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"

@implementation DatabaseUnit (AdBlockWhitelist)

// 添加一个
+ (DatabaseUnit*)addAdblockWhitelistWithItem:(AdBlockWhitelistModel *)item
{
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        
        if(item.uuid.length == 0) {
            item.uuid = [[NSUUID UUID] UUIDString];
        }
        
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.ctime = ctime;
        
        NSString* command = @"INSERT INTO t_adblock_whitelist(uuid, url, ctime) VALUES (?,?,?)";

        BOOL result = [db executeUpdate:command, item.uuid, item.url?:@"", ctime];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 删除一个
+ (DatabaseUnit*)removeAdblockWhitelistWithId:(NSString *)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //删除数据库记录
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_adblock_whitelist WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, uuid];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 查询所有
+ (DatabaseUnit*)queryAllAdblockWhitelists
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_adblock_whitelist ORDER BY ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AdBlockWhitelistModel* item = [[AdBlockWhitelistModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}


@end
