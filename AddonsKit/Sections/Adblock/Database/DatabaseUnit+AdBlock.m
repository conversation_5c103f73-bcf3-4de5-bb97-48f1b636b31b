//
//  DatabaseUnit+AdBlock.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/4/8.
//

#import "DatabaseUnit+AdBlock.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "SyncEngine.h"
#import "CloudKitHelper.h"

//CREATE TABLE IF NOT EXISTS t_adblock(uuid TEXT PRIMARY KEY, title TEXT, text TEXT, url TEXT, type INTEGER, isActive INTEGER, ctime TEXT)

@implementation DatabaseUnit (AdBlock)

// 添加一个
+ (DatabaseUnit*)addAdblockWithItem:(AdBlockModel*)item
{
    //解析脚本的时候已经加入到内存缓存, 所以不需要再在这里添加
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* ctime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        NSString* updateTime = ctime;
        item.ctime = ctime;
        item.updateTime = updateTime;
        
        NSString* command = @"INSERT INTO t_adblock(uuid, title, url, type, isActive, updateTime, ctime) VALUES (?,?,?,?,?,?,?)";

        BOOL result = [db executeUpdate:command, item.uuid, item.title?:@"", item.url?:@"", @(item.type), @(item.isActive), updateTime, ctime];
        
        if(result) {
            //同步到CloudKit中
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[[item toCKRecord]] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 删除一个
+ (DatabaseUnit*)removeAdblockWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //删除数据库记录
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_adblock WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, uuid];

        if(result) {
            //同步到CloudKit中
            CKRecordID* recordID = [[CKRecordID alloc]initWithRecordName:uuid zoneID:[CloudKitHelper addonsZoneID]];
            [[SyncEngine shareInstance] syncRecordsToCloudKit:nil recordIDsToDelete:@[recordID] completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 查询所有
+ (DatabaseUnit*)queryAllAdblocks
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_adblock ORDER BY ctime DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AdBlockModel* item = [[AdBlockModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
                            isActive:(NSInteger)isActive
                                item:(AdBlockModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.updateTime = time;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_adblock SET isActive = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), time, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [item toDefaultCKRecord];
            record[@"isActive"] = @(isActive);
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//重命名标题
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
                               title:(NSString*)title
                                item:(AdBlockModel *)item
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* time = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.updateTime = time;
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_adblock SET title = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, title?:@"", time, uuid];
        
        if(result) {
            //同步到CloudKit中
            CKRecord* record = [item toDefaultCKRecord];
            record[@"title"] = title?:@"";
            record[@"updateTime"] = time;
            [[SyncEngine shareInstance] syncRecordsToCloudKit:@[record] recordIDsToDelete:nil completion:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//更新编辑/更新时间
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_adblock SET updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, updateTime, uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

//批量更新URL类型的时间
+ (DatabaseUnit*)updateAdblockWithIds:(NSArray*)uuids
{
    DatabaseUnit* unit = [DatabaseUnit new];
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = YES;
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        for(int i=0;i<uuids.count;i++) {
            NSString* uuid = uuids[i];
            NSString* command = @"UPDATE t_adblock SET updateTime=? WHERE uuid=?";
            result = [db executeUpdate:command, updateTime, uuid] & result;
        }

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 添加多个广告过滤规则, 同时插入多个广告过滤规则
+ (DatabaseUnit*)addAdBlockArray:(NSArray<AdBlockModel*>*)items
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = @"INSERT INTO t_adblock(uuid, title, url, type, isActive, updateTime, ctime) VALUES (?,?,?,?,?,?,?)";
        BOOL result = YES;
        for(int i=0;i<items.count;i++) {
            AdBlockModel* item = items[i];
            result = [db executeUpdate:command, item.uuid, item.title?:@"", item.url?:@"", @(item.type), @(item.isActive), item.updateTime?:@"1", item.ctime];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量更新多个广告过滤规则
+ (DatabaseUnit*)updateAdBlockArray:(NSArray<AdBlockModel*>*)array
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        BOOL result = NO;
        for(AdBlockModel* item in array) {
            NSString* command = @"UPDATE t_adblock SET title=?, url=?, type=?, isActive=?, updateTime=?, ctime=? WHERE uuid=?;";
            result = [db executeUpdate:command, item.title?:@"", item.url?:@"", @(item.type), @(item.isActive), item.updateTime?:@"1", item.ctime, item.uuid];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// CloudKit, 批量删除多个广告过滤规则
+ (DatabaseUnit*)removeAdBlockArray:(NSArray*)adblockIds
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_adblock WHERE uuid=?;"];

        BOOL result = YES;
        for(int i=0;i<adblockIds.count;i++) {
            NSString* adblockId = adblockIds[i];
            result = [db executeUpdate:command, adblockId];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
