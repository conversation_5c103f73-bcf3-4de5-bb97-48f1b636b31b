//
//  DatabaseUnit+AdBlock.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/4/8.
//

#import "DatabaseUnit.h"
#import "AdBlockModel.h"

@interface DatabaseUnit (AdBlock)
// 添加
+ (DatabaseUnit*)addAdblockWithItem:(AdBlockModel*)item;

// 删除
+ (DatabaseUnit*)removeAdblockWithId:(NSString*)uuid;

// 查询所有
+ (DatabaseUnit*)queryAllAdblocks;

// 更新是否激活状态
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
                            isActive:(NSInteger)isActive
                                item:(AdBlockModel *)item;

//重命名标题
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid
                               title:(NSString*)title
                                item:(AdBlockModel *)item;
                
//更新编辑/更新时间
+ (DatabaseUnit*)updateAdblockWithId:(NSString*)uuid;

//批量更新URL类型的时间
+ (DatabaseUnit*)updateAdblockWithIds:(NSArray*)uuids;


// CloudKit, 添加多个广告过滤规则, 同时插入多个广告过滤规则
+ (DatabaseUnit*)addAdBlockArray:(NSArray<AdBlockModel*>*)items;

// CloudKit, 批量更新多个广告过滤规则
+ (DatabaseUnit*)updateAdBlockArray:(NSArray<AdBlockModel*>*)array;

// CloudKit, 批量删除多个广告过滤规则
+ (DatabaseUnit*)removeAdBlockArray:(NSArray*)adblockIds;

@end

