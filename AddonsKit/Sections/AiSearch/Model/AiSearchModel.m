//
//  AiSearchModel.m
//  Saber
//
//  Created by qingbin on 2023/3/2.
//

#import "AiSearchModel.h"
#import "NSURL+Extension.h"

@implementation AiSearchModel

//是否是自定义的搜索引擎
- (BOOL)isCustomSearchModel
{
    int max = [self.uuid intValue];
    return max >= 50000;
}

//是否是自定义的搜索引擎
+ (BOOL)isCustomSearchModelWithUUID:(int)uuid
{
    return uuid >= 50000;
}

// 常用搜索引擎
+ (instancetype)baidu
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://m.baidu.com/s?word=%s";
    item.query = @"word";
    item.name = NSLocalizedString(@"aisearch.baidu", nil);
    item.uuid = @"10000";
    item.rule_keys = @[
        @"bd",
        @"baidu",
        @"百度"
    ];
    
    item.isActive = YES;
    item.iconName = @"baidu";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)google
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://www.google.com/search?q=%s";
    item.query = @"q";
    item.name = NSLocalizedString(@"aisearch.google", nil);
    item.uuid = @"9999";
    item.rule_keys = @[
        @"g",
        @"gg",
        @"谷歌"
    ];
    
    item.isActive = YES;
    item.iconName = @"google";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)bing
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://www.bing.com/search?q=%s";
    item.query = @"q";
    item.name = NSLocalizedString(@"aisearch.bing", nil);
    item.uuid = @"9998";
    item.rule_keys = @[
        @"b",
        @"bing",
        @"必应"
    ];
    
    item.isActive = YES;
    item.iconName = @"bing";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)toutiao
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://so.toutiao.com/search?source=input&keyword=%s";
    item.query = @"keyword";
    item.name = NSLocalizedString(@"aisearch.toutiao", nil);
    item.uuid = @"9997";
    item.rule_keys = @[
        @"tt",
        @"toutiao",
        @"头条"
    ];
    
    item.isActive = YES;
    item.iconName = @"toutiao";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)sougou
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://m.sogou.com/web/searchList.jsp?s_from=pcsearch&keyword=%s";
    item.query = @"keyword";
    item.name = NSLocalizedString(@"aisearch.sougou", nil);
    item.uuid = @"9996";
    item.rule_keys = @[
        @"sg",
        @"sougou",
        @"搜狗"
    ];
    
    item.isActive = YES;
    item.iconName = @"sougou";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)_360
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://m.so.com/s?ie=utf-8&fr=none&src=360sou_newhome&nlpv=basest&q=%s";
    item.query = @"q";
    item.name = @"360";
    item.uuid = @"9995";
    item.rule_keys = @[
        @"so",
        @"360",
    ];
    
    item.isActive = YES;
    item.iconName = @"360";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)duckduckgo
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://duckduckgo.com/?q=%s";
    item.query = @"q";
    item.name = @"DuckDuckgo";
    item.uuid = @"9994";
    item.rule_keys = @[
        @"d",
        @"ddg",
    ];
    
    item.isActive = YES;
    item.iconName = @"duckduckgo";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)ecosia
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://www.ecosia.org/search?q=%s";
    item.query = @"q";
    item.name = @"Ecosia";
    item.uuid = @"9993";
    item.rule_keys = @[
        @"es",
        @"ecosia",
    ];
    
    item.isActive = YES;
    item.iconName = @"ecosia";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)yahoo
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://search.yahoo.com/search?&ei=UTF-8&p=%s";
    item.query = @"p";
    item.name = @"Yahoo!";
    item.uuid = @"9992";
    item.rule_keys = @[
        @"yh",
        @"yahoo",
    ];
    
    item.isActive = YES;
    item.iconName = @"yahoo";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)yandex
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://yandex.com/search?text=%s";
    item.query = @"text";
    item.name = @"Yandex";
    item.uuid = @"9991";
    item.rule_keys = @[
        @"yd",
        @"yandex",
    ];
    
    item.isActive = YES;
    item.iconName = @"yandex";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

//APP内搜索
+ (instancetype)weibo
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://s.weibo.com/weibo/?q=%s";
    item.query = @"q";
    item.name = NSLocalizedString(@"aisearch.weibo", nil);
    item.uuid = @"8000";
    item.rule_keys = @[
        @"wb",
        @"weibo",
        @"微博"
    ];
    
    item.isActive = NO;
    item.iconName = @"weibo";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)taobao
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://uland.taobao.com/semm/tbsearch?keyword=%s";
    item.query = @"keyword";
    item.name = NSLocalizedString(@"aisearch.taobao", nil);
    item.uuid = @"7999";
    item.rule_keys = @[
        @"taobao",
        @"淘宝"
    ];
    
    item.isActive = NO;
    item.iconName = @"taobao";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)greasyfork
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://greasyfork.org/zh-CN/scripts?q=%s";
    item.query = @"q";
    item.name = NSLocalizedString(@"aisearch.greasyfork", nil);
    item.uuid = @"7998";
    item.rule_keys = @[
        @"gfork",
    ];
    
    item.isActive = NO;
    item.iconName = @"greasyfork";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)xiaohongshu
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://m.sogou.com/web/xiaohongshu?keyword=%s";
    item.query = @"keyword";
    item.name = NSLocalizedString(@"aisearch.xiaohongshu", nil);
    item.uuid = @"7997";
    item.rule_keys = @[
        @"xhs",
    ];
    
    item.isActive = NO;
    item.iconName = @"xiaohongshu";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

+ (instancetype)zhihu
{
    AiSearchModel* item = [AiSearchModel new];
    item.url = @"https://www.zhihu.com/search?type=content&q=%s";
    item.query = @"keyword";
    item.name = NSLocalizedString(@"aisearch.zhihu", nil);
    item.uuid = @"7996";
    item.rule_keys = @[
        @"zh",
        @"zhihu"
    ];
    
    item.isActive = NO;
    item.iconName = @"zhihu";
    
    //host
    NSString* url = [item.url componentsSeparatedByString:@"?"].firstObject;
    item.host = [[NSURL URLWithString:url] normalizedHost];
    
    return item;
}

@end
