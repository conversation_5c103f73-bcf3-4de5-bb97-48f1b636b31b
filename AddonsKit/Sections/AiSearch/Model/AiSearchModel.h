//
//  AiSearchModel.h
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/3/2.
//

#import "BaseModel.h"

/*
 自定义的uuid从50000开始
 */
@interface AiSearchModel : BaseModel
// id,或者优先级,都是同一个字段
@property (nonatomic, strong) NSString *uuid;
// 搜索引擎名称
@property (nonatomic, strong) NSString *name;
// 搜索引擎触发词(不保存数据库,换成字符串的形式保存)
@property (nonatomic, strong) NSArray *rule_keys;
// 搜索引擎q=%s之前的q,用于获取%s
@property (nonatomic, strong) NSString *query;
// 搜索引擎URL
@property (nonatomic, strong) NSString *url;
// 搜索引擎Host
@property (nonatomic, strong) NSString *host;
// 是否启用
@property (nonatomic, assign) BOOL isActive;
// icon
@property (nonatomic, strong) NSString *iconName;
// 更新时间
@property (nonatomic, strong) NSString *updateTime;

//保存到数据库时的字段
@property (nonatomic, strong) NSString *sql_rule_keys;

//辅助字段
@property (nonatomic, assign) BOOL isFirstInSection;
@property (nonatomic, assign) BOOL isLastInSection;

//是否是自定义的搜索引擎
- (BOOL)isCustomSearchModel;
//是否是自定义的搜索引擎
+ (BOOL)isCustomSearchModelWithUUID:(int)uuid;


//常用引擎
+ (instancetype)baidu;
+ (instancetype)google;
+ (instancetype)bing;
+ (instancetype)toutiao;
+ (instancetype)sougou;
+ (instancetype)_360;
+ (instancetype)duckduckgo;
+ (instancetype)ecosia;
+ (instancetype)yahoo;
+ (instancetype)yandex;

+ (instancetype)weibo;
+ (instancetype)taobao;
+ (instancetype)greasyfork;
+ (instancetype)xiaohongshu;
+ (instancetype)zhihu;

@end


@protocol AiSearchModel <NSObject>
@end
