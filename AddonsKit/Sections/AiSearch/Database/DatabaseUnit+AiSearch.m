//
//  DatabaseUnit+AiSearch.m
//  Saber
//
//  Created by q<PERSON><PERSON> on 2023/3/2.
//

#import "DatabaseUnit+AiSearch.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"
#import "ExtConfigItem.h"

@implementation DatabaseUnit (AiSearch)

+ (DatabaseUnit *)queryAllAiSearchItems
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_aisearch ORDER BY uuid DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AiSearchModel* item = [[AiSearchModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)queryAllAiSearchItemsAndConfig
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_aisearch ORDER BY uuid DESC"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            AiSearchModel* item = [[AiSearchModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        command = [NSString stringWithFormat:@"SELECT * FROM t_ext_config WHERE uuid = ?"];
        set = [db executeQuery:command, @(ExtConfigOptionAiSearch)];
        
        ExtConfigItem* config = nil;
        BOOL isPost = false;
        if([set next]) {
            config = [[ExtConfigItem alloc]initWithDictionary:[set resultDictionary] error:nil];
            isPost = config.option == 1;
        }
        
        NSDictionary* result = @{
            @"list" : array.count>0 ? array : @[],
            @"isPost" : @(isPost)
        };
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(result, YES);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateAiSearchWithId:(NSString*)uuid
                             isActive:(NSInteger)isActive
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_aisearch SET isActive = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), updateTime, uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新标题
+ (DatabaseUnit*)updateAiSearchWithId:(NSString*)uuid
                                 name:(NSString*)name
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_aisearch SET name = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, name?:@"", updateTime, uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 更新激活词
+ (DatabaseUnit*)updateAiSearchWithId:(NSString*)uuid
                              ruleKey:(NSString*)ruleKey
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_aisearch SET sql_rule_keys = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, ruleKey?:@"", updateTime, uuid];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

// 添加自定义搜索引擎
+ (DatabaseUnit*)addAiSearchWithItem:(AiSearchModel*)item
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        item.updateTime = updateTime;
        
        NSString* command = @"INSERT INTO t_aisearch(uuid, name, sql_rule_keys, url, host, query, isActive, iconName, updateTime) VALUES (?,?,?,?,?,?,?,?,?)\
            on CONFLICT(uuid) DO UPDATE SET name=excluded.name WHERE excluded.uuid=t_aisearch.uuid;\
            ";
            NSString* sql_rule_keys = [item.rule_keys componentsJoinedByString:@","];
            BOOL result = [db executeUpdate:command, item.uuid, item.name?:@"", sql_rule_keys?:@"", item.url?:@"", item.host?:@"", item.query?:@"", @(item.isActive), item.iconName?:@"", updateTime];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

// 删除自定义搜索引擎
+ (DatabaseUnit*)removeAiSearchWithId:(NSString*)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    //删除数据库记录
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
    
        NSString* command = [NSString stringWithFormat:@"DELETE FROM t_aisearch WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, uuid];

        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil, result);
            }
        });
    };
    
    return unit;
}

@end
