//
//  DatabaseUnit+AiSearch.h
//  Saber
//
//  Created by qingbin on 2023/3/2.
//

#import "DatabaseUnit.h"
#import "AiSearchModel.h"

@interface DatabaseUnit (AiSearch)

+ (DatabaseUnit *)queryAllAiSearchItems;

// 查询所有智能搜索列表和配置项
+ (DatabaseUnit *)queryAllAiSearchItemsAndConfig;

// 更新标题
+ (DatabaseUnit*)updateAiSearchWithId:(NSString*)uuid
                                 name:(NSString*)name;

// 更新激活词
+ (DatabaseUnit*)updateAiSearchWithId:(NSString*)uuid
                              ruleKey:(NSString*)ruleKey;

// 更新是否激活状态
+ (DatabaseUnit*)updateAiSearchWithId:(NSString*)uuid
                             isActive:(NSInteger)isActive;


// 添加自定义搜索引擎
+ (DatabaseUnit*)addAiSearchWithItem:(AiSearchModel*)item;

// 删除自定义搜索引擎
+ (DatabaseUnit*)removeAiSearchWithId:(NSString*)uuid;

@end

