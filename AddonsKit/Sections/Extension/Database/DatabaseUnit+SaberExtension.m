//
//  DatabaseUnit+SaberExtension.m
//  SaberKit
//
//  Created by q<PERSON><PERSON> on 2023/3/5.
//

#import "DatabaseUnit+SaberExtension.h"

#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"

#import "TagitManager.h"
#import "ExtConfigItem.h"
#import "AiSearchModel.h"
#import "UserScript.h"

@implementation DatabaseUnit (SaberExtension)

// 查询所有Saber Extension的数据
+ (DatabaseUnit *)queryAllDataForExtension
{
    DatabaseUnit* unit = [DatabaseUnit new];

//    double start = CFAbsoluteTimeGetCurrent();
//    __block double end = 0;
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        //查询配置表
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_ext_config"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *configItems = [[NSMutableArray alloc] init];
        while([set next]) {
            ExtConfigItem* item = [[ExtConfigItem alloc]initWithDictionary:[set resultDictionary] error:nil];
            [configItems addObject:item];
        }
        
        //查询所有脚本
        command = [NSString stringWithFormat:@"SELECT * FROM t_userscript ORDER BY ppOrder ASC, ctime DESC"];
        set = [db executeQuery:command];
        
        NSMutableArray *userScripts = [[NSMutableArray alloc] init];
        while([set next]) {
            UserScript* item = [[UserScript alloc]initWithDictionary:[set resultDictionary] error:nil];
            //从sql初始化
            [item jsonModelFromSql];
            
            [userScripts addObject:item];
        }
        
        //查询所有标记模式数据
        command = [NSString stringWithFormat:@"SELECT * FROM t_tagit ORDER BY ctime DESC"];
        set = [db executeQuery:command];
        
        NSMutableArray *tagits = [[NSMutableArray alloc] init];
        while([set next]) {
            TagitModel* item = [[TagitModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [tagits addObject:item];
        }
        
        //查询所有智能搜索引擎数据
        command = [NSString stringWithFormat:@"SELECT * FROM t_aisearch ORDER BY uuid DESC"];
        set = [db executeQuery:command];
        
        NSMutableArray *aiSearchModels = [[NSMutableArray alloc] init];
        while([set next]) {
            AiSearchModel* item = [[AiSearchModel alloc]initWithDictionary:[set resultDictionary] error:nil];
            [aiSearchModels addObject:item];
        }
        
        ExtensionModel* model = [ExtensionModel new];
        model.userScripts = [userScripts mutableCopy];
        model.tagits = [tagits mutableCopy];
        model.aiSearchModels = [aiSearchModels mutableCopy];
        
        ExtConfigModel* configModel = [ExtConfigModel new];
        [configModel updateWithModel:configItems];
        
        model.config = configModel;
        
        dispatch_async(dispatch_get_main_queue(), ^{
//            end = CFAbsoluteTimeGetCurrent();
//            NSLog(@"...........耗时时间为: %lf", end - start);
            
            if(unit.completeBlock) {
                unit.completeBlock(model,YES);
            }
        });
    };
    
    return unit;
}

@end
