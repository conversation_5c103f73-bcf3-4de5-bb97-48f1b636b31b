//
//  ExtensionModel.h
//  SaberKit
//
//  Created by q<PERSON><PERSON> on 2023/3/5.
//

#import "BaseModel.h"

#import "PPEnums.h"

#import "TagitManager.h"
#import "ExtConfigItem.h"
#import "AiSearchModel.h"
#import "UserScript.h"
#import "DarkConfigModel.h"

@interface ExtensionModel : BaseModel

@property (nonatomic, strong) UserScript* js_installHelper;

@property (nonatomic, strong) UserScript* js_tagit;

@property (nonatomic, strong) NSArray<UserScript>* userScripts;

@property (nonatomic, strong) NSArray<TagitModel>* tagits;

@property (nonatomic, strong) NSArray<AiSearchModel>* aiSearchModels;

@property (nonatomic, strong) ExtConfigModel* config;
// 记录上次选择的模块，打开的时候重新恢复
// 0-默认，用户脚本, 1-标记模式, 2-暗黑模式
@property (nonatomic, assign) int lastSelectModuleIndex;
// 暗黑模式
@property (nonatomic, strong) DarkConfigModel *darkModel;

@end

