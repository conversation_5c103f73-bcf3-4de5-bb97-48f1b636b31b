//
//  ExtConfig.h
//  SaberKit
//
//  Created by qingbin on 2023/3/5.
//

#import "BaseModel.h"
#import "PPEnums.h"

//包括是否启用油猴功能，是否启动视频嗅探，是否启用标记模式，是否启用智能搜索，是否启用屏蔽牛皮癣广告
//是否启用暗黑模式及其选择，是否启用长按探测元素

@interface ExtConfigItem : BaseModel

// 区分不同的配置项
@property (nonatomic, assign) ExtConfigOption uuid;
// 是否启用
@property (nonatomic, assign) BOOL isActive;
//暗黑模式下的选项, 0:跟随系统, 1-开启, 2-关闭， (已弃用)
//智能搜索的选项, 0:前置, 1-后置
@property (nonatomic, assign) int option;
// 更新时间
@property (nonatomic, strong) NSString *updateTime;

//默认开启
+ (instancetype)tampermonkey;
//默认开启
+ (instancetype)sniffer;
//默认开启
+ (instancetype)tagit;
//默认开启
+ (instancetype)adblock;
//默认开启
+ (instancetype)aiSearch;
//默认开启
+ (instancetype)blockTheFixedAds;
//默认开启
+ (instancetype)darkMode;
//默认开启
+ (instancetype)longPressDetect;
//默认开启
+ (instancetype)showSnifferButton;
//默认关闭
//+ (instancetype)noImage;

@end

@protocol ExtConfig <NSObject>
@end


//将上面7种情况整理成一个model，易于使用
@interface ExtConfigModel : BaseModel

@property (nonatomic, strong) ExtConfigItem* tampermonkey;

@property (nonatomic, strong) ExtConfigItem* sniffer;

@property (nonatomic, strong) ExtConfigItem* tagit;

@property (nonatomic, strong) ExtConfigItem* aiSearch;

@property (nonatomic, strong) ExtConfigItem* blockTheFixedAds;

@property (nonatomic, strong) ExtConfigItem* darkMode;

@property (nonatomic, strong) ExtConfigItem* longPressDetect;

@property (nonatomic, strong) ExtConfigItem* showSnifferButton;

//@property (nonatomic, strong) ExtConfigItem* noImage;

- (void)updateWithModel:(NSArray<ExtConfigItem*>*)models;

@end


@protocol ExtConfigModel <NSObject>
@end
