//
//  ExtConfigItem.m
//  SaberKit
//
//  Created by qingbin on 2023/3/5.
//

#import "ExtConfigItem.h"

@implementation ExtConfigItem

+ (instancetype)tampermonkey
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionTampermonkey;
    item.isActive = YES;
    
    return item;
}

+ (instancetype)sniffer
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionSniffer;
    item.isActive = YES;
    
    return item;
}

+ (instancetype)tagit
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionTagit;
    item.isActive = NO;
    
    return item;
}

+ (instancetype)adblock
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionAdblock;
    item.isActive = NO;
    
    return item;
}

+ (instancetype)aiSearch
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionAiSearch;
    item.isActive = NO;
    //默认前置
    item.option = 0;
    
    return item;
}

+ (instancetype)blockTheFixedAds
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionBlockTheFixedAds;
    item.isActive = NO;
    
    return item;
}

+ (instancetype)darkMode
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionDarkMode;
    item.isActive = NO;
    //默认关闭
    item.option = 2;
    
    return item;
}

+ (instancetype)longPressDetect
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionLongPressDetect;
    item.isActive = YES;
    
    return item;
}

//默认开启
+ (instancetype)showSnifferButton
{
    ExtConfigItem* item = [ExtConfigItem new];
    item.uuid = ExtConfigOptionShowSnifferButton;
    item.isActive = YES;
    
    return item;
}

//+ (instancetype)noImage
//{
//    ExtConfigItem* item = [ExtConfigItem new];
//    item.uuid = ExtConfigOptionNoImage;
//    item.isActive = NO;
//    
//    return item;
//}

@end


@interface ExtConfigModel ()

@end

@implementation ExtConfigModel

- (void)updateWithModel:(NSArray<ExtConfigItem*>*)models
{
    for(ExtConfigItem* item in models) {
        if(item.uuid == ExtConfigOptionTampermonkey) {
            self.tampermonkey = item;
        } else if(item.uuid == ExtConfigOptionTagit) {
            self.tagit = item;
        } else if(item.uuid == ExtConfigOptionSniffer) {
            self.sniffer = item;
        } else if(item.uuid == ExtConfigOptionAiSearch) {
            self.aiSearch = item;
        } else if(item.uuid == ExtConfigOptionBlockTheFixedAds) {
            self.blockTheFixedAds = item;
        } else if(item.uuid == ExtConfigOptionDarkMode) {
            self.darkMode = item;
        } else if(item.uuid == ExtConfigOptionLongPressDetect) {
            self.longPressDetect = item;
        } else if(item.uuid == ExtConfigOptionShowSnifferButton) {
            self.showSnifferButton = item;
        }
    }
}

@end
