//
//  DatabaseUnit+ExtConfig.h
//  SaberKit
//
//  Created by qingbin on 2023/3/5.
//

#import "DatabaseUnit.h"
#import "ExtConfigItem.h"

@interface DatabaseUnit (ExtConfig)

+ (DatabaseUnit *)queryAllExtConfigs;

// 更新是否激活状态
+ (DatabaseUnit*)updateExtConfigWithId:(ExtConfigOption)uuid
                              isActive:(NSInteger)isActive;

// 更新option
+ (DatabaseUnit*)updateExtConfigWithId:(ExtConfigOption)uuid
                                option:(NSInteger)option;

// 根据id查询指定配置
+ (DatabaseUnit *)queryExtConfigWithId:(ExtConfigOption)uuid;

@end

