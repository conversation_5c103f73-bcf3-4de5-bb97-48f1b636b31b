//
//  DatabaseUnit+ExtConfig.m
//  SaberKit
//
//  Created by q<PERSON><PERSON> on 2023/3/5.
//

#import "DatabaseUnit+ExtConfig.h"
#import "ReactiveCocoa.h"

#import "FMDatabaseQueue.h"
#import "FMDatabase.h"

#import "PPEnums.h"

@implementation DatabaseUnit (ExtConfig)

+ (DatabaseUnit *)queryAllExtConfigs
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_ext_config"];
        FMResultSet* set = [db executeQuery:command];
        
        NSMutableArray *array = [[NSMutableArray alloc] init];
        while([set next]) {
            ExtConfigItem* item = [[ExtConfigItem alloc]initWithDictionary:[set resultDictionary] error:nil];
            [array addObject:item];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(array,YES);
            }
        });
    };
    
    return unit;
}

// 更新是否激活状态
+ (DatabaseUnit*)updateExtConfigWithId:(ExtConfigOption)uuid
                              isActive:(NSInteger)isActive
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_ext_config SET isActive = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(isActive), updateTime, @(uuid)];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit*)updateExtConfigWithId:(ExtConfigOption)uuid
                                option:(NSInteger)option
{
    DatabaseUnit* unit = [DatabaseUnit new];
    
    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* updateTime = [NSString stringWithFormat:@"%ld", (long)[[NSDate date] timeIntervalSince1970]];
        
        NSString* command = [NSString stringWithFormat:@"UPDATE t_ext_config SET option = ?, updateTime = ? WHERE uuid=?;"];
        BOOL result = [db executeUpdate:command, @(option), updateTime, @(uuid)];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(nil,result);
            }
        });
    };
    
    return unit;
}

+ (DatabaseUnit *)queryExtConfigWithId:(ExtConfigOption)uuid
{
    DatabaseUnit* unit = [DatabaseUnit new];

    @weakify(unit)
    unit.executeBlock = ^(FMDatabase *db) {
        @strongify(unit)
        NSString* command = [NSString stringWithFormat:@"SELECT * FROM t_ext_config WHERE uuid = ?"];
        FMResultSet* set = [db executeQuery:command, @(uuid)];
        
        ExtConfigItem* result = nil;
        if([set next]) {
            result = [[ExtConfigItem alloc]initWithDictionary:[set resultDictionary] error:nil];
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if(unit.completeBlock) {
                unit.completeBlock(result, YES);
            }
        });
    };
    
    return unit;
}

@end
