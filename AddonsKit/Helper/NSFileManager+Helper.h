//
//  NSFileManager+Helper.h
//  PPBrowser
//
//  Created by qingbin on 2022/11/19.
//  Copyright © 2022 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSFileManager (Helper)

+ (NSURL *)downloadsPath;

/// https://www.jianshu.com/p/382b3ea37106
/// https://github.com/quoid/userscripts
/// 保存下载链接
+ (void)saveBookMark:(NSURL *)url;
/// 读取下载链接
+ (NSURL *)readBookMark;

+ (NSString *)groupPath;

/// CloudKit中CKAsset的保存目录
/// Documents/CloudKit
+ (NSURL *)cloudKitPath;

/// 解锁限制
+ (void)unlockLimit;
/// 获取解锁限制的值
+ (NSNumber *)valueForUnlockLimit;

@end

