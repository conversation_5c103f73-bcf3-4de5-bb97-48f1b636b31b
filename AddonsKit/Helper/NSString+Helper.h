//
//  NSString+Helper.h
//  QRCode
//
//  Created by qingbin on 2021/11/9.
//  Copyright © 2021 qingbin. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

@interface NSString (Helper)

+ (NSString *)convertToJsonString:(id)source options:(BOOL)plain;

+ (id)jsonConvertToObject:(NSString *)source;

+ (NSMutableAttributedString *)attributedStringWithText:(NSString *)text
                                                   font:(UIFont *)font
                                                  color:(UIColor *)color
                                                  title:(NSString *)title
                                              titleFont:(UIFont *)titleFont
                                             titleColor:(UIColor *)titleColor;

+ (CGSize)sizeWithText:(NSString *)text fontSize:(CGFloat)fontSize width:(CGFloat)width;
+ (CGSize)sizeWithAttributedText:(NSAttributedString *)text width:(CGFloat)width;

- (BOOL)isNumberValue;
- (BOOL)isIntValue;
- (BOOL)isFloatValue;
- (BOOL)isDoubleValue;

- (NSString*)trim:(NSString *)charactersInString;

// 移除脚本中的//# sourceMappingURL=xxx
// sourceMappingURL 可能以 //@ 或 //# 开头
+ (NSString *)removeSourceMappingURL:(NSString *)scriptContent;

@end
