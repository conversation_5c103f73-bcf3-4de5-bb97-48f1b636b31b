//
//  ReactiveViewModel.h
//  ReactiveViewModel
//
//  Created by <PERSON> on 2014-10-27.
//  Copyright (c) 2014 GitHub. All rights reserved.
//

#import <Foundation/Foundation.h>

//! Project version number for ReactiveViewModel.
FOUNDATION_EXPORT double ReactiveViewModelVersionNumber;

//! Project version string for ReactiveViewModel.
FOUNDATION_EXPORT const unsigned char ReactiveViewModelVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <ReactiveViewModel/PublicHeader.h>

#import <ReactiveViewModel/RVMViewModel.h>
