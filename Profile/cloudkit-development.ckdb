DEFINE SCHEMA

    RECORD TYPE AdBlockModel (
        "___createTime" TIMESTAMP,
        "___createdBy"  REFERENCE,
        "___etag"       STRING,
        "___modTime"    TIMESTAMP,
        "___modifiedBy" REFERENCE,
        "___recordID"   REFERENCE QUERYABLE,
        appCtime        STRING QUERYABLE SEARCHABLE SORTABLE,
        appType         INT64 QUERYABLE SORTABLE,
        appUserID       STRING QUERYABLE SEARCHABLE SORTABLE,
        appVersion      STRING QUERYABLE SEARCHABLE SORTABLE,
        ctime           STRING QUERYABLE SEARCHABLE SORTABLE,
        isActive        INT64 QUERYABLE SORTABLE,
        text            ASSET,
        title           STRING QUERYABLE SEARCHABLE SORTABLE,
        type            INT64 QUERYABLE SORTABLE,
        url             STRING QUERYABLE SEARCHABLE SORTABLE,
        uuid            STRING QUERYABLE SEARCHABLE SORTABLE,
        GRANT WRITE TO "_creator",
        GRANT CREATE TO "_icloud",
        <PERSON><PERSON><PERSON> READ TO "_world"
    );

    RECORD TYPE TagitModel (
        "___createTime" TIMESTAMP,
        "___createdBy"  REFERENCE,
        "___etag"       STRING,
        "___modTime"    TIMESTAMP,
        "___modifiedBy" REFERENCE,
        "___recordID"   REFERENCE QUERYABLE,
        appCtime        STRING QUERYABLE SEARCHABLE SORTABLE,
        appType         INT64 QUERYABLE SORTABLE,
        appUserID       STRING QUERYABLE SEARCHABLE SORTABLE,
        appVersion      STRING QUERYABLE SEARCHABLE SORTABLE,
        ctime           STRING QUERYABLE SEARCHABLE SORTABLE,
        host            STRING QUERYABLE SEARCHABLE SORTABLE,
        isActive        INT64 QUERYABLE SORTABLE,
        originUrl       STRING QUERYABLE SEARCHABLE SORTABLE,
        uuid            STRING QUERYABLE SEARCHABLE SORTABLE,
        xpath           STRING QUERYABLE SEARCHABLE SORTABLE,
        GRANT WRITE TO "_creator",
        GRANT CREATE TO "_icloud",
        GRANT READ TO "_world"
    );

    RECORD TYPE UserScript (
        "___createTime"  TIMESTAMP,
        "___createdBy"   REFERENCE,
        "___etag"        STRING,
        "___modTime"     TIMESTAMP,
        "___modifiedBy"  REFERENCE,
        "___recordID"    REFERENCE QUERYABLE,
        appCtime         STRING QUERYABLE SEARCHABLE SORTABLE,
        appType          INT64 QUERYABLE SORTABLE,
        appUserID        STRING QUERYABLE SEARCHABLE SORTABLE,
        appVersion       STRING QUERYABLE SEARCHABLE SORTABLE,
        author           STRING QUERYABLE SEARCHABLE SORTABLE,
        content          ASSET,
        desc             STRING QUERYABLE SEARCHABLE SORTABLE,
        downloadUrl      STRING QUERYABLE SEARCHABLE SORTABLE,
        iconUrl          ASSET,
        injectMode       INT64 QUERYABLE SORTABLE,
        isActive         INT64 QUERYABLE SORTABLE,
        isAutoUpdate     INT64 QUERYABLE SORTABLE,
        kNamespace       STRING QUERYABLE SEARCHABLE SORTABLE,
        name             STRING QUERYABLE SEARCHABLE SORTABLE,
        noframes         INT64 QUERYABLE SORTABLE,
        ppOrder          INT64 QUERYABLE SORTABLE,
        runAt            STRING QUERYABLE SEARCHABLE SORTABLE,
        sql_excludes     STRING QUERYABLE SEARCHABLE SORTABLE,
        sql_grants       STRING QUERYABLE SEARCHABLE SORTABLE,
        sql_includes     STRING QUERYABLE SEARCHABLE SORTABLE,
        sql_matches      STRING QUERYABLE SEARCHABLE SORTABLE,
        sql_requireUrls  STRING QUERYABLE SEARCHABLE SORTABLE,
        sql_resourceUrls STRING QUERYABLE SEARCHABLE SORTABLE,
        updateUrl        STRING QUERYABLE SEARCHABLE SORTABLE,
        uuid             STRING QUERYABLE SEARCHABLE SORTABLE,
        version          STRING QUERYABLE SEARCHABLE SORTABLE,
        GRANT WRITE TO "_creator",
        GRANT CREATE TO "_icloud",
        GRANT READ TO "_world"
    );

    RECORD TYPE Users (
        "___createTime" TIMESTAMP,
        "___createdBy"  REFERENCE,
        "___etag"       STRING,
        "___modTime"    TIMESTAMP,
        "___modifiedBy" REFERENCE,
        "___recordID"   REFERENCE,
        roles           LIST<INT64>,
        GRANT WRITE TO "_creator",
        GRANT READ TO "_world"
    );
